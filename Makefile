# Makefile for assistant-go

# Variables
BINARY_NAME=assistant
BUILD_DIR=./bin
COVERAGE_DIR=./coverage

.PHONY: help build test clean lint fmt wire

# Default target
help:
	@echo "Available targets:"
	@echo "  build              - Build the assistant binary"
	@echo "  test               - Run all tests"
	@echo "  test-short         - Run tests excluding integration tests"
	@echo "  test-integration   - Run integration tests only"
	@echo "  test-race          - Run tests with race detector"
	@echo "  coverage           - Generate test coverage report"
	@echo "  coverage-func      - Show test coverage by function"
	@echo "  benchmark          - Run benchmarks"
	@echo "  clean              - Clean build artifacts"
	@echo "  lint               - Run linters"
	@echo "  fmt                - Format code"
	@echo "  fmt-check          - Check if code is formatted"
	@echo "  vet                - Run go vet"
	@echo "  wire               - Generate Wire dependency injection code"
	@echo "  sqlc               - Generate sqlc code"
	@echo "  mocks              - Generate mock interfaces using gomock"
	@echo "  generate           - Generate all code (sqlc + mocks)"
	@echo "  ci                 - Run all CI checks"

# Build binary
build:
	go build -o $(BUILD_DIR)/$(BINARY_NAME) ./cmd/assistant

# Run all tests
test:
	go test -v ./...

# Run tests excluding integration tests
test-short:
	go test -short -v ./...

# Run integration tests only
test-integration:
	go test -tags=integration -v ./...

# Run tests with race detector
test-race:
	go test -race -v ./...

# Generate test coverage report
coverage:
	@mkdir -p $(COVERAGE_DIR)
	go test -coverprofile=$(COVERAGE_DIR)/coverage.out ./...
	go tool cover -html=$(COVERAGE_DIR)/coverage.out -o $(COVERAGE_DIR)/coverage.html
	@echo "Coverage report generated at $(COVERAGE_DIR)/coverage.html"

# Show test coverage by function
coverage-func: coverage
	go tool cover -func=$(COVERAGE_DIR)/coverage.out

# Run benchmarks
benchmark:
	go test -bench=. -benchmem ./...

# Clean build artifacts
clean:
	rm -rf $(BUILD_DIR)
	rm -rf $(COVERAGE_DIR)
	go clean -cache

# Run linters
lint:
	golangci-lint run

# Run all linters (comprehensive)
lint-all: lint lint-extra

# Run extra linters not included in golangci-lint
lint-extra: gosec staticcheck golint

# Run gosec security scanner
gosec:
	@echo "Running gosec..."
	@gosec -fmt=text -exclude=G101 -quiet ./...

# Run staticcheck
staticcheck:
	@echo "Running staticcheck..."
	@staticcheck ./...

# Run golint (deprecated but still useful)
golint:
	@echo "Running golint..."
	@golint ./... | grep -v "should have comment" | grep -v "should be of the form" || true

# Install all linter tools
lint-install:
	@echo "Installing linter tools..."
	@go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	@go install github.com/securego/gosec/v2/cmd/gosec@latest
	@go install honnef.co/go/tools/cmd/staticcheck@latest
	@go install golang.org/x/lint/golint@latest

# Format code
fmt:
	go fmt ./...

# Check if code is formatted
fmt-check:
	@test -z "$$(gofmt -l .)" || (echo "Please run 'make fmt' to format code" && exit 1)

# Run go vet
vet:
	go vet ./...



# Generate sqlc code
sqlc:
	sqlc generate -f configs/sqlc.yaml

# Generate mocks
mocks:
	@echo "Generating mocks..."
	@go install go.uber.org/mock/mockgen@latest
	@mockgen -source=internal/storage/database/sqlc/querier.go -destination=internal/storage/database/mocks/mock_querier.go -package=mocks
	@echo "Mocks generated successfully"

# Generate all code (sqlc + mocks)
generate: sqlc mocks

# Run database migrations
migrate:
	go run cmd/migrate/main.go -up

# Quick development rebuild
dev: sqlc build

# Run application
run: build
	$(BUILD_DIR)/$(BINARY_NAME)

# Install dependencies
deps:
	go mod download
	go mod tidy

# Update dependencies
update-deps:
	go get -u ./...
	go mod tidy

# Check code quality
check: fmt-check vet lint test

# Run all CI checks
ci: fmt-check vet lint-all test

# Quick check - run fast linters
check: fmt-check vet lint

# Pre-commit checks
pre-commit: fmt vet test-short

# Generate documentation
docs:
	godoc -http=:6060

# Database migrations
migrate-up:
	migrate -path internal/storage/database/migrations -database "$(DATABASE_URL)" up

migrate-down:
	migrate -path internal/storage/database/migrations -database "$(DATABASE_URL)" down

migrate-create:
	@read -p "Enter migration name: " name; \
	migrate create -ext sql -dir internal/storage/database/migrations -seq $$name

migrate-force:
	@read -p "Enter version to force: " version; \
	migrate -path internal/storage/database/migrations -database "$(DATABASE_URL)" force $$version

# Complete build flow
all: deps sqlc build

# Test specific package (usage: make test-pkg PKG=./internal/assistant)
test-pkg:
	go test -v $(PKG)

# Run specific test function (usage: make test-func FUNC=TestServiceCreate)
test-func:
	go test -v -run $(FUNC) ./...

# Run tests with verbose output
test-verbose:
	go test -v -count=1 ./...

# Run table-driven tests
test-table:
	go test -v -run ".*Table.*" ./...

# Run fuzzing tests
test-fuzz:
	go test -fuzz=Fuzz -fuzztime=10s ./...

# Generate test coverage and open in browser
test-coverage:
	@mkdir -p $(COVERAGE_DIR)
	go test -coverprofile=$(COVERAGE_DIR)/coverage.out ./...
	go tool cover -html=$(COVERAGE_DIR)/coverage.out
	@echo "Coverage report opened in browser"
