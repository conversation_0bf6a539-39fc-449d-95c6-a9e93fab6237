/*
Package memory implements the Cognitive Memory Architecture for Assistant-Go.

This package is the heart of the assistant, designed to provide a persistent, self-organizing, and context-aware long-term memory. It moves beyond simple chat history by creating a structured, queryable knowledge graph that evolves over time.

Key Concepts:

- Knowledge Lifecycle Management: The system is built around a full lifecycle for information: extraction, storage, retrieval, consolidation, and eventual archival. This ensures the knowledge base remains relevant and efficient.

- Typed Memories: Information is not just text; it's parsed into structured types like `Fact`, `Preference`, or `Schedule`. This allows for more intelligent storage and retrieval.

- Autonomous Consolidation: A background service (`Consolidator`) continuously works to improve the knowledge base. It performs several key tasks:
  - Tiered Semantic Deduplication: Merges or links similar memories based on their semantic similarity score, preventing data loss while eliminating true duplicates.
  - Conflict Resolution: Identifies and resolves contradictory information.
  - Archival: Deactivates old, irrelevant memories to keep the active set clean.

- Dynamic Knowledge Graph: Memories are not isolated. The system automatically builds relationships (`related`, `similar`, `contradicts`) between them, creating a rich graph that can be traversed during retrieval to gather deeper context.

- Resilient Architecture: All memory processing is handled asynchronously via a robust queue, ensuring that the main chat interaction is never blocked and that no information is lost during processing.
*/
package memory
