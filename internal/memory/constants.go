package memory

import "time"

// Search limits for different fact importance levels
const (
	// SearchLimitCritical is the limit for score >= 5 facts
	SearchLimitCritical = 200
	// SearchLimitImportant is the limit for score >= 4 facts
	SearchLimitImportant = 100
	// SearchLimitNormal is the limit for score >= 3 facts
	SearchLimitNormal = 50
	// SearchLimitLow is the limit for lower score facts
	SearchLimitLow = 30
)

// Similarity thresholds
const (
	// MinSimilarityThreshold is the minimum similarity score for search results
	MinSimilarityThreshold = 0.3
	// DefaultSimilarityThreshold is the default similarity threshold
	DefaultSimilarityThreshold = 0.75
	// HighSimilarityThreshold indicates very similar content
	HighSimilarityThreshold = 0.85
	// ExactMatchThreshold indicates nearly identical content
	ExactMatchThreshold = 0.95
)

// Processing configuration
const (
	// MinFactsForConcurrent is the minimum number of facts to trigger concurrent processing
	MinFactsForConcurrent = 2
	// MaxWorkersLimit is the maximum number of concurrent workers
	MaxWorkersLimit = 32
	// WorkerMultiplier is the multiplier for CPU cores to determine worker count
	WorkerMultiplier = 2
)

// Scoring weights
const (
	// VectorSimilarityWeight is the weight for vector similarity in scoring
	VectorSimilarityWeight = 0.40
	// TextRelevanceWeight is the weight for text relevance with embedding
	TextRelevanceWeight = 0.20
	// TextOnlyRelevanceWeight is the weight for text relevance without embedding
	TextOnlyRelevanceWeight = 0.60
	// RecencyWeight is the maximum weight for recency
	RecencyWeight = 0.15
	// AccessFrequencyWeight is the maximum weight for access frequency
	AccessFrequencyWeight = 0.10
	// ConfidenceWeight is the weight for confidence factor
	ConfidenceWeight = 0.05
)

// Time intervals for recency scoring
const (
	// RecencyHour is the recency score for updates within an hour
	RecencyHour = 0.15
	// RecencyDay is the recency score for updates within a day
	RecencyDay = 0.12
	// RecencyWeek is the recency score for updates within a week
	RecencyWeek = 0.08
	// RecencyMonth is the recency score for updates within a month
	RecencyMonth = 0.04
)

// Limits and defaults
const (
	// DefaultSearchLimit is the default number of search results
	DefaultSearchLimit = 10
	// MaxSearchResults is the maximum number of search results when no limit specified
	MaxSearchResults = 10000
	// DefaultBatchSize is the default batch size for processing
	DefaultBatchSize = 10
	// ConflictFilterLimit is the maximum memories to keep after conflict filtering
	ConflictFilterLimit = 50
)

// Confidence thresholds
const (
	// MinConfidenceThreshold is the minimum confidence to process a fact
	MinConfidenceThreshold = 0.3
	// DefaultConfidence is the default confidence when not specified
	DefaultConfidence = 0.5
)

// Decision-making configuration
const (
	// LengthRatioThreshold is the minimum ratio of content lengths to consider as same topic
	LengthRatioThreshold = 0.7
	// DecisionTemperature is the temperature for LLM decision making
	DecisionTemperature = 0.1
	// DecisionMaxTokens is the maximum tokens for decision responses
	DecisionMaxTokens = 500
	// DecisionLookAhead is the number of characters to look ahead when parsing
	DecisionLookAhead = 200
	// DecisionMoveOffset is the offset to move past current match when parsing
	DecisionMoveOffset = 20
	// ContentStartOffset is the offset after "content": when parsing
	ContentStartOffset = 10
)

// Memory service configuration
const (
	// HighPriority is the priority for important messages
	HighPriority = 10
	// DefaultMemoryTypeLimit is the default limit for GetMemoriesByType
	DefaultMemoryTypeLimit = 50
	// DefaultRecentMemoryLimit is the default limit for GetRecentMemories
	DefaultRecentMemoryLimit = 20
	// DefaultMinScore is the default minimum score for search
	DefaultMinScore = 0.5
	// DefaultHistoryLimit is the default limit for history queries
	DefaultHistoryLimit = 50
	// DefaultAllHistoryLimit is the default limit for all history
	DefaultAllHistoryLimit = 100
	// Tiered deduplication thresholds
	// DuplicateThreshold indicates exact duplicate (deactivate without merge)
	DuplicateThreshold float32 = 0.98
	// MergeSimilarityThreshold is the similarity threshold for merging
	MergeSimilarityThreshold float32 = 0.90
	// RelateThreshold indicates moderate similarity (create relationship)
	RelateThreshold float32 = 0.80
	// DefaultGraphDepth is the default depth for graph traversal
	DefaultGraphDepth = 2
)

// Relevance scoring configuration
const (
	// RecencyScoreDay is the recency score for memories within a day
	RecencyScoreDay = 0.2
	// RecencyScoreWeek is the recency score for memories within a week
	RecencyScoreWeek = 0.15
	// RecencyScoreMonth is the recency score for memories within a month
	RecencyScoreMonth = 0.1
	// RecencyScoreQuarter is the recency score for memories within 90 days
	RecencyScoreQuarter = 0.05
	// FrequentAccessThreshold is the threshold for frequent access
	FrequentAccessThreshold = 10
	// ModerateAccessThreshold is the threshold for moderate access
	ModerateAccessThreshold = 5
	// AccessScoreFrequent is the score for frequently accessed memories
	AccessScoreFrequent = 0.1
	// AccessScoreModerate is the score for moderately accessed memories
	AccessScoreModerate = 0.05
)

// Queue configuration
const (
	// QueueDefaultMaxWorkers is the default number of concurrent workers
	QueueDefaultMaxWorkers = 3
	// QueueDefaultBufferSize is the default job channel buffer size
	QueueDefaultBufferSize = 1000
	// QueueDefaultMaxRetries is the default maximum retries for failed jobs
	QueueDefaultMaxRetries = 2
	// QueueDefaultBatchSize is the default number of messages to batch
	QueueDefaultBatchSize = 5
	// QueueDefaultBatchTimeout is the default timeout for batch accumulation
	QueueDefaultBatchTimeout = 5 * time.Second
	// QueueProcessTimeout is the timeout for processing a single job
	QueueProcessTimeout = 60 * time.Second
)
