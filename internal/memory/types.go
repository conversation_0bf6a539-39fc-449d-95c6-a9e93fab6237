// Package memory provides types for the AI assistant memory system.
package memory

import (
	"context"
	"encoding/json"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/koopa0/assistant-go/internal/extract"
	"github.com/pgvector/pgvector-go"
)

// Memory represents a single memory with entities, relations, and context.
type Memory struct {
	ID         pgtype.UUID `json:"id"`
	SemanticID string      `json:"semantic_id"` // Preserved from existing system
	Type       MemoryType  `json:"type"`

	// Core content
	Content string `json:"content"` // Original content in original language
	Summary string `json:"summary"` // Standardized summary (optional)

	// Structured data
	Entities   []extract.Entity `json:"entities"`   // People, places, activities involved
	Attributes Attributes       `json:"attributes"` // Key-value pairs

	// Relations and context
	Relations []Relation `json:"relations"` // Links to other memories
	Context   Context    `json:"context"`   // When, where, etc.

	// Search and retrieval
	Embedding pgvector.Vector `json:"-"`        // Vector embedding
	Keywords  []string        `json:"keywords"` // Search keywords

	// Metadata
	Confidence  float32      `json:"confidence"` // 0.0 to 1.0
	Status      MemoryStatus `json:"status"`     // active, archived, deleted
	Version     int          `json:"version"`
	CreatedAt   time.Time    `json:"created_at"`
	UpdatedAt   time.Time    `json:"updated_at"`
	AccessedAt  time.Time    `json:"accessed_at"`
	AccessCount int32        `json:"access_count"`
	ArchivedAt  *time.Time   `json:"archived_at,omitempty"` // When memory was archived

	// Search result fields (not persisted)
	Similarity float32 `json:"similarity,omitempty"` // Vector similarity score when from search
}

// MemoryStatus defines the lifecycle status of a memory
type MemoryStatus string

const (
	StatusActive   MemoryStatus = "active"   // Normal, searchable memory
	StatusArchived MemoryStatus = "archived" // Old memory, not in default search
	StatusDeleted  MemoryStatus = "deleted"  // Soft deleted, pending cleanup
)

// MemoryType defines categories of memories
type MemoryType string

const (
	MemoryTypeFact         MemoryType = "fact"
	MemoryTypePreference   MemoryType = "preference"
	MemoryTypeSchedule     MemoryType = "schedule"
	MemoryTypeRelationship MemoryType = "relationship"
	MemoryTypeGoal         MemoryType = "goal"
	MemoryTypeSkill        MemoryType = "skill"
)

// Context provides temporal and spatial context for memories
type Context struct {
	// Temporal context
	OccurredAt *time.Time          `json:"occurred_at,omitempty"`
	StartTime  *time.Time          `json:"start_time,omitempty"`
	EndTime    *time.Time          `json:"end_time,omitempty"`
	Duration   *time.Duration      `json:"duration,omitempty"`
	Recurrence *extract.Recurrence `json:"recurrence,omitempty"`

	// Spatial context
	Location *Location `json:"location,omitempty"`

	// Source context
	Source    string `json:"source,omitempty"` // conversation, form, etc.
	SessionID string `json:"session_id,omitempty"`
}

// Location represents spatial information for memories
type Location struct {
	Name        string  `json:"name,omitempty"`
	Address     string  `json:"address,omitempty"`
	Latitude    float64 `json:"latitude,omitempty"`
	Longitude   float64 `json:"longitude,omitempty"`
	Description string  `json:"description,omitempty"`
}

// Attributes stores flexible key-value pairs
type Attributes map[string]interface{}

// Get retrieves an attribute value with type assertion
func (a Attributes) Get(key string) (interface{}, bool) {
	val, exists := a[key]
	return val, exists
}

// GetString retrieves a string attribute
func (a Attributes) GetString(key string) (string, bool) {
	val, exists := a[key]
	if !exists {
		return "", false
	}
	str, ok := val.(string)
	return str, ok
}

// GetTime retrieves a time attribute
func (a Attributes) GetTime(key string) (time.Time, bool) {
	val, exists := a[key]
	if !exists {
		return time.Time{}, false
	}

	switch v := val.(type) {
	case time.Time:
		return v, true
	case string:
		t, err := time.Parse(time.RFC3339, v)
		return t, err == nil
	default:
		return time.Time{}, false
	}
}

// Merge combines attributes, with other taking precedence
func (a Attributes) Merge(other Attributes) Attributes {
	result := make(Attributes)

	// Copy existing
	for k, v := range a {
		result[k] = v
	}

	// Override with new
	for k, v := range other {
		result[k] = v
	}

	return result
}

// Decision represents what to do with a memory
type Decision struct {
	Actions    []Action // Support multiple actions in sequence
	Reason     string
	Confidence float32 // Confidence in the decision (0-1)
}

// Action represents a single action within a decision
type Action struct {
	Type     ActionType
	TargetID *pgtype.UUID // For UPDATE/DELETE actions
	Content  string       // For ADD actions or modified content
}

// ActionType defines memory actions
type ActionType string

const (
	ActionAdd    ActionType = "ADD"
	ActionUpdate ActionType = "UPDATE"
	ActionDelete ActionType = "DELETE"
	ActionSkip   ActionType = "SKIP"
	ActionMerge  ActionType = "MERGE"
)

// Query defines search parameters
type Query struct {
	Text      string
	Types     []MemoryType
	Entities  []string
	TimeRange *TimeRange
	Limit     int
	MinScore  float32
}

// TimeRange for temporal queries
type TimeRange struct {
	Start time.Time
	End   time.Time
}

// DecisionMaker defines the interface for making memory decisions
type DecisionMaker interface {
	Decide(ctx context.Context, fact extract.Fact, existing []*Memory) (Decision, error)
}

// MarshalJSON implements custom JSON marshaling for Memory
func (m *Memory) MarshalJSON() ([]byte, error) {
	type Alias Memory
	return json.Marshal(&struct {
		*Alias
		ID string `json:"id"`
	}{
		Alias: (*Alias)(m),
		ID:    uuid.UUID(m.ID.Bytes).String(),
	})
}

// IsRecurring checks if this memory has recurrence
func (m *Memory) IsRecurring() bool {
	return m.Context.Recurrence != nil
}

// convertFromExtractContext converts extract.Context to memory.Context
func convertFromExtractContext(ec extract.Context) Context {
	var loc *Location
	if ec.Location != nil {
		loc = &Location{
			Name:    ec.Location.Name,
			Address: ec.Location.Address,
		}
	}

	return Context{
		OccurredAt: ec.OccurredAt,
		StartTime:  ec.StartTime,
		EndTime:    ec.EndTime,
		Recurrence: ec.Recurrence,
		Location:   loc,
	}
}

// GetNextOccurrence calculates next occurrence for recurring memories
func (m *Memory) GetNextOccurrence(after time.Time) *time.Time {
	if !m.IsRecurring() || m.Context.StartTime == nil {
		return nil
	}

	// TODO: Implement recurrence calculation
	return nil
}

// Score calculates memory relevance score
func (m *Memory) Score(query string, now time.Time) float32 {
	score := m.Confidence

	// Boost recent memories
	age := now.Sub(m.UpdatedAt)
	if age < 24*time.Hour {
		score += 0.2
	} else if age < 7*24*time.Hour {
		score += 0.1
	}

	// Boost frequently accessed
	if m.AccessCount > 10 {
		score += 0.1
	}

	// Cap at 1.0
	if score > 1.0 {
		score = 1.0
	}

	return score
}

// Metrics tracks pipeline performance
type Metrics struct {
	ExtractCount    int64                    `json:"extract_count"`
	ExtractErrors   int64                    `json:"extract_errors"`
	DecideCount     int64                    `json:"decide_count"`
	DecideErrors    int64                    `json:"decide_errors"`
	StoreCount      int64                    `json:"store_count"`
	StoreErrors     int64                    `json:"store_errors"`
	ProcessingTime  map[string]time.Duration `json:"processing_time"`
	LastProcessedAt time.Time                `json:"last_processed_at"`
}
