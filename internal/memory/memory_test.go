package memory

import (
	"context"
	"testing"
	"time"

	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/koopa0/assistant-go/internal/platform/logger"
)

// mockAIHybrid implements ai.Hybrid interface for testing
type mockAIHybrid struct {
	chatFunc  func(ctx context.Context, req *ai.Request) (*ai.Response, error)
	embedFunc func(ctx context.Context, text string) ([]float32, error)
}

func (m *mockAIHybrid) Chat(ctx context.Context, req *ai.Request) (*ai.Response, error) {
	if m.chatFunc != nil {
		return m.chatFunc(ctx, req)
	}
	return &ai.Response{Content: "mock response"}, nil
}

func (m *mockAIHybrid) Embed(ctx context.Context, text string) ([]float32, error) {
	if m.embedFunc != nil {
		return m.embedFunc(ctx, text)
	}
	// Return a mock embedding
	embedding := make([]float32, 768)
	for i := range embedding {
		embedding[i] = float32(i) / 768.0
	}
	return embedding, nil
}

func (m *mockAIHybrid) Stream(ctx context.Context, req *ai.Request) (<-chan ai.Stream, error) {
	ch := make(chan ai.Stream)
	close(ch)
	return ch, nil
}

func (m *mockAIHybrid) Provider() ai.Provider {
	return ai.Provider("mock")
}

func (m *mockAIHybrid) Close() error {
	return nil
}

func (m *mockAIHybrid) IsAvailable(ctx context.Context) bool {
	return true
}

func (m *mockAIHybrid) SupportsStreaming() bool {
	return false
}

func (m *mockAIHybrid) Config() interface{} {
	return nil
}

// TestNew tests the creation of a new Memory service
func TestNew(t *testing.T) {
	tests := []struct {
		name      string
		db        *pgxpool.Pool
		aiService ai.Hybrid
		logger    logger.Logger
		wantErr   bool
		errMsg    string
	}{
		{
			name:      "missing database",
			db:        nil,
			aiService: &mockAIHybrid{},
			logger:    logger.NewConsoleLogger(),
			wantErr:   true,
			errMsg:    "database is required",
		},
		{
			name:      "missing AI service",
			db:        &pgxpool.Pool{}, // mock pool
			aiService: nil,
			logger:    logger.NewConsoleLogger(),
			wantErr:   true,
			errMsg:    "AI service client is required",
		},
		{
			name:      "nil logger uses discard logger",
			db:        &pgxpool.Pool{}, // mock pool
			aiService: &mockAIHybrid{},
			logger:    nil,
			wantErr:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service, err := New(tt.db, tt.aiService, tt.logger)
			
			if tt.wantErr {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
				assert.Nil(t, service)
			} else {
				require.NoError(t, err)
				require.NotNil(t, service)
				// Service should be properly initialized
				assert.NotNil(t, service.processor)
				assert.NotNil(t, service.store)
				assert.NotNil(t, service.queryAnalyzer)
				assert.NotNil(t, service.merger)
				assert.NotNil(t, service.graph)
				assert.NotNil(t, service.consolidator)
				assert.NotNil(t, service.queue)
				assert.NotNil(t, service.smartSearcher)
				assert.NotNil(t, service.contextBuilder)
				
				// Clean up
				service.Shutdown()
			}
		})
	}
}

// TestService_ProcessMessages tests message processing
func TestService_ProcessMessages(t *testing.T) {
	// This test would require a real database connection
	// For unit testing, we would need to mock the database
	t.Skip("Requires database integration test setup")
}

// TestService_Search tests memory search functionality
func TestService_Search(t *testing.T) {
	// This test would require a real database connection
	t.Skip("Requires database integration test setup")
}

// TestService_BuildContext tests context building
func TestService_BuildContext(t *testing.T) {
	// This test would require a real database connection
	t.Skip("Requires database integration test setup")
}

// TestMemoryResult_Format tests memory result formatting
func TestMemoryResult_Format(t *testing.T) {
	tests := []struct {
		name   string
		result MemoryResult
		want   string
	}{
		{
			name: "basic memory",
			result: MemoryResult{
				ID:        "123",
				Content:   "User likes pizza",
				Type:      "preference",
				Category:  "food",
				Relevance: 0.95,
			},
			want: "User likes pizza",
		},
		{
			name: "memory with metadata",
			result: MemoryResult{
				ID:       "456",
				Content:  "Meeting at 3pm",
				Type:     "schedule",
				Category: "work",
				Metadata: map[string]interface{}{
					"location": "Conference Room A",
					"duration": "1 hour",
				},
				Relevance: 0.88,
			},
			want: "Meeting at 3pm",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test that Content is accessible
			assert.Equal(t, tt.want, tt.result.Content)
			assert.NotEmpty(t, tt.result.Type)
			assert.NotEmpty(t, tt.result.Category)
		})
	}
}

// Benchmark tests

// BenchmarkMemoryService_New benchmarks service creation
func BenchmarkMemoryService_New(b *testing.B) {
	// Mock dependencies
	db := &pgxpool.Pool{}
	aiService := &mockAIHybrid{}
	log := logger.NewDiscardLogger()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		service, err := New(db, aiService, log)
		if err != nil {
			b.Fatalf("failed to create service: %v", err)
		}
		service.Shutdown()
	}
}

// BenchmarkMemoryResult_Access benchmarks accessing memory results
func BenchmarkMemoryResult_Access(b *testing.B) {
	results := make([]MemoryResult, 100)
	for i := range results {
		results[i] = MemoryResult{
			ID:         string(rune(i)),
			Content:    "Benchmark memory content",
			Type:       "test",
			Category:   "benchmark",
			Similarity: float32(i) / 100.0,
			Relevance:  float32(i) / 100.0,
			Metadata: map[string]interface{}{
				"index": i,
				"test":  true,
			},
			CreatedAt: time.Now().Format(time.RFC3339),
		}
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		for _, result := range results {
			_ = result.ID
			_ = result.Content
			_ = result.Relevance
			_ = result.Metadata
		}
	}
}