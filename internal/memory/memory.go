// Package memory provides a simple, powerful memory system for AI assistants.
package memory

import (
	"context"
	"errors"
	"fmt"
	"math"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/jackc/pgx/v5/pgxpool"

	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/koopa0/assistant-go/internal/platform/logger"
	"github.com/koopa0/assistant-go/internal/storage/database/sqlc"
)

// Service provides persistent memory capabilities for AI assistants.
// It automatically extracts, stores, and retrieves relevant information
// from conversations, enabling context-aware interactions over time.
type Service struct {
	processor      *Processor
	store          *Store
	queryAnalyzer  *QueryAnalyzer
	merger         *Merger
	graph          *Graph
	consolidator   *Consolidator
	queue          *ProcessingQueue
	aiService      ai.Hybrid
	logger         logger.Logger
	smartSearcher  *SmartSearcher
	contextBuilder *ContextBuilder

	// Manage async operations
	wg sync.WaitGroup

	// Context for queue management
	ctx    context.Context
	cancel context.CancelFunc
}

// MemoryResult represents a retrieved memory with relevance score
type MemoryResult struct {
	ID         string
	Content    string
	Type       string
	Category   string
	Similarity float32
	Relevance  float32 // Combined score from similarity and other factors
	Metadata   map[string]interface{}
	CreatedAt  string
}

// New creates a new Memory service with simplified architecture.
func New(db *pgxpool.Pool, aiService ai.Hybrid, log logger.Logger) (*Service, error) {
	if db == nil {
		return nil, errors.New("database is required")
	}
	if aiService == nil {
		return nil, errors.New("AI service client is required")
	}
	if log == nil {
		log = logger.NewDiscardLogger()
	}

	// Create components with simplified architecture
	store := NewStore(db, aiService, log)
	// Use memory-specific extractor with custom prompts
	extractor := NewMemoryExtractor(aiService)
	// Create decider with AI support for semantic understanding
	decider := NewDecider(DefaultSimilarityThreshold, aiService)
	processor := NewProcessor(extractor, decider, store, log)
	// Use simple query analyzer
	queryAnalyzer := NewQueryAnalyzer()
	// Create merger for consolidating related memories
	merger := NewMerger(store, aiService)
	// Create graph for memory relationships
	memGraph := NewGraph(store)
	// Create consolidator for background optimization
	consolidator := NewConsolidator(store, merger, memGraph, log)
	// Create optimized processing queue
	queue := NewProcessingQueue(processor, DefaultQueueConfig(), log)
	// Create smart searcher for graph-based search
	smartSearcher := NewSmartSearcher(store, memGraph)
	// Create conflict detector for context builder
	conflictDetector := NewConflictDetector(store, aiService, log)
	// Create context builder for structured context
	contextBuilder := NewContextBuilder(smartSearcher, conflictDetector)

	// Create a context for the service
	ctx, cancel := context.WithCancel(context.Background())

	service := &Service{
		processor:      processor,
		store:          store,
		queryAnalyzer:  queryAnalyzer,
		merger:         merger,
		graph:          memGraph,
		consolidator:   consolidator,
		queue:          queue,
		aiService:      aiService,
		logger:         log.WithComponent("memory"),
		smartSearcher:  smartSearcher,
		contextBuilder: contextBuilder,
		ctx:            ctx,
		cancel:         cancel,
	}

	// Start the processing queue with the service context
	logger.Debug("About to start queue with context")
	queue.Start(ctx)
	logger.Debug("Queue started")

	return service, nil
}

// ProcessAsync processes messages and extracts memories asynchronously.
// This is the main entry point for storing new information.
func (s *Service) ProcessAsync(ctx context.Context, messages []ai.Message) {
	if s == nil {
		// Service is nil, can't proceed
		return
	}

	if s.logger == nil {
		// Logger is nil, can't log
		return
	}

	// Add immediate logging
	s.logger.Debug("ProcessAsync called", "message_count", len(messages))

	if len(messages) == 0 {
		s.logger.Info("ProcessAsync called with empty messages")
		return
	}

	s.logger.Info("Enqueuing messages for memory extraction", "messageCount", len(messages))

	// Determine priority based on message content
	priority := 0
	for _, msg := range messages {
		// Higher priority for messages with important keywords
		if strings.Contains(msg.Content, "生日") ||
			strings.Contains(msg.Content, "birthday") ||
			strings.Contains(msg.Content, "重要") ||
			strings.Contains(msg.Content, "important") {
			priority = HighPriority
			break
		}
	}

	// Check if queue is initialized
	if s.queue == nil {
		s.logger.Error("Queue is nil!")
		return
	}

	// Use optimized queue for processing
	if err := s.queue.Enqueue(messages, priority); err != nil {
		s.logger.Error("Failed to enqueue messages", "error", err)

		// Fallback to direct processing
		s.wg.Add(1)
		go func() {
			defer s.wg.Done()

			// Use background context for processing
			bgCtx := context.Background()

			// Recover from panics
			defer func() {
				if r := recover(); r != nil {
					s.logger.Error("panic in memory processing", "panic", r)
				}
			}()

			if err := s.processor.Process(bgCtx, messages); err != nil {
				s.logger.Error("Failed to process messages", "error", err)
			}
		}()
	}
}

// WaitForProcessing waits for all pending memory operations to complete.
// This is useful for testing and debugging.
func (s *Service) WaitForProcessing() {
	s.wg.Wait()
}

// Search finds memories relevant to the query using intelligent graph-based expansion.
func (s *Service) Search(ctx context.Context, query string, limit int) ([]*Memory, error) {
	if limit <= 0 {
		limit = DefaultSearchLimit
	}

	// Use smart searcher for graph-based search
	options := DefaultSmartSearchOptions()
	searchResults, err := s.smartSearcher.Search(ctx, query, limit, options)
	if err != nil {
		return nil, NewMemoryError("Search", KindProcessing, err, "smart search failed")
	}

	// Convert SearchResult to Memory
	memories := make([]*Memory, 0, len(searchResults))
	for _, result := range searchResults {
		memories = append(memories, result.Memory)
	}

	return memories, nil
}

// GetMemoriesByType retrieves memories of specific types with pagination.
func (s *Service) GetMemoriesByType(ctx context.Context, types []MemoryType, limit int) ([]*Memory, error) {
	if limit <= 0 {
		limit = DefaultMemoryTypeLimit
	}

	// Convert types to Query
	q := Query{
		Types:    types,
		Limit:    limit,
		MinScore: 0.0, // Get all memories of these types
	}

	return s.store.Search(ctx, q)
}

// GetRecentMemories retrieves the most recently updated memories.
func (s *Service) GetRecentMemories(ctx context.Context, limit int) ([]*Memory, error) {
	if limit <= 0 {
		limit = DefaultRecentMemoryLimit
	}

	return s.store.ListRecent(ctx, limit)
}

// SearchWithRelevance finds memories with enhanced relevance scoring.
func (s *Service) SearchWithRelevance(ctx context.Context, query string, limit int) ([]MemoryResult, error) {
	if limit <= 0 {
		limit = DefaultSearchLimit
	}

	// Search for memories
	searchQuery := Query{
		Text:     query,
		Limit:    limit * 2, // Get extra for filtering
		MinScore: DefaultMinScore,
	}
	memories, err := s.store.Search(ctx, searchQuery)
	if err != nil {
		return nil, fmt.Errorf("search memories: %w", err)
	}

	// Convert to MemoryResult with relevance scoring
	results := make([]MemoryResult, 0, len(memories))
	now := time.Now()

	for _, mem := range memories {
		// Use attributes as metadata
		metadata := make(map[string]interface{})
		for k, v := range mem.Attributes {
			metadata[k] = v
		}

		// Calculate base similarity (this would come from vector search)
		similarity := mem.Confidence

		// Calculate recency score (0-0.2 boost for recent memories)
		daysSinceCreation := now.Sub(mem.CreatedAt).Hours() / 24
		recencyScore := float32(0.0)
		if daysSinceCreation < 1 {
			recencyScore = RecencyScoreDay
		} else if daysSinceCreation < 7 {
			recencyScore = RecencyScoreWeek
		} else if daysSinceCreation < 30 {
			recencyScore = RecencyScoreMonth
		} else if daysSinceCreation < 90 {
			recencyScore = RecencyScoreQuarter
		}

		// Calculate access frequency score (0-0.1 boost)
		accessScore := float32(0.0)
		if mem.AccessCount > FrequentAccessThreshold {
			accessScore = AccessScoreFrequent
		} else if mem.AccessCount > ModerateAccessThreshold {
			accessScore = AccessScoreModerate
		}

		// Combined relevance score
		relevance := similarity + recencyScore + accessScore
		if relevance > 1.0 {
			relevance = 1.0
		}

		result := MemoryResult{
			ID:         fmt.Sprintf("%x", mem.ID.Bytes),
			Content:    mem.Content,
			Type:       string(mem.Type),
			Similarity: similarity,
			Relevance:  relevance,
			Metadata:   metadata,
			CreatedAt:  mem.CreatedAt.Format(time.RFC3339),
		}

		results = append(results, result)
	}

	// Sort by relevance score
	sort.Slice(results, func(i, j int) bool {
		return results[i].Relevance > results[j].Relevance
	})

	// Limit results
	if len(results) > limit {
		results = results[:limit]
	}

	return results, nil
}

// GetContext returns memories relevant for current conversation.
func (s *Service) GetContext(ctx context.Context, conversationPreview string, limit int) ([]*Memory, error) {
	if conversationPreview != "" {
		return s.Search(ctx, conversationPreview, limit)
	}
	return s.store.ListRecent(ctx, limit)
}

// GetStructuredContext returns organized memory context for LLM consumption.
func (s *Service) GetStructuredContext(ctx context.Context, query string, limit int) (*StructuredContext, error) {
	if limit <= 0 {
		limit = DefaultSearchLimit
	}

	// Build structured context with conflicts and relationships
	structuredCtx, err := s.contextBuilder.BuildContext(ctx, query, limit)
	if err != nil {
		return nil, fmt.Errorf("build context failed: %w", err)
	}

	return structuredCtx, nil
}

// GetContextForLLM returns formatted context string ready for LLM prompt.
func (s *Service) GetContextForLLM(ctx context.Context, query string, limit int) (string, error) {
	structuredCtx, err := s.GetStructuredContext(ctx, query, limit)
	if err != nil {
		return "", err
	}

	// Format for LLM consumption
	return s.contextBuilder.FormatForLLM(structuredCtx), nil
}

// Save stores a new memory directly (for testing/manual use).
func (s *Service) Save(ctx context.Context, content string, memType MemoryType) (*Memory, error) {
	memory := &Memory{
		Content: content,
		Type:    memType,
	}
	err := s.store.Save(ctx, memory)
	return memory, err
}

// GetRecent returns recently created memories.
func (s *Service) GetRecent(ctx context.Context, limit int) ([]*Memory, error) {
	return s.store.ListRecent(ctx, limit)
}

// Update modifies an existing memory.
func (s *Service) Update(ctx context.Context, id string, memory *Memory) (*Memory, error) {
	var pgUUID pgtype.UUID
	if err := pgUUID.Scan(id); err != nil {
		return nil, NewMemoryError("GetByID", KindValidation, err, "invalid memory ID")
	}
	err := s.store.Update(ctx, pgUUID, memory)
	return memory, err
}

// Delete removes a memory.
func (s *Service) Delete(ctx context.Context, id string) error {
	var pgUUID pgtype.UUID
	if err := pgUUID.Scan(id); err != nil {
		return NewMemoryError("UpdateAccess", KindValidation, err, "invalid memory ID")
	}
	return s.store.Delete(ctx, pgUUID)
}

// GetMemoryHistory returns the change history for a specific memory.
func (s *Service) GetMemoryHistory(ctx context.Context, memoryID string) ([]*sqlc.History, error) {
	var pgUUID pgtype.UUID
	if err := pgUUID.Scan(memoryID); err != nil {
		return nil, NewMemoryError("GetByID", KindValidation, err, "invalid memory ID")
	}

	return s.store.queries.GetHistory(ctx, sqlc.GetHistoryParams{
		MemoryID: pgUUID,
		Limit:    DefaultHistoryLimit,
	})
}

// GetHistory returns all recent memory changes for personal assistant.
func (s *Service) GetHistory(ctx context.Context, limit int) ([]*sqlc.History, error) {
	if limit <= 0 {
		limit = DefaultAllHistoryLimit
	}
	if limit > math.MaxInt32 {
		limit = math.MaxInt32
	}

	return s.store.queries.GetAllHistory(ctx, int32(limit)) // #nosec G115 -- already capped to MaxInt32 above
}

// GetHistoryByAction returns memory changes filtered by action type.
func (s *Service) GetHistoryByAction(ctx context.Context, action string) ([]*sqlc.History, error) {
	return s.store.queries.GetHistoryByAction(ctx, sqlc.GetHistoryByActionParams{
		Column1: sqlc.ActionType(action),
		Limit:   DefaultHistoryLimit,
	})
}

// MergeRelatedMemories triggers the merging of related memories.
// This can be called periodically or after significant updates.
func (s *Service) MergeRelatedMemories(ctx context.Context, category string) error {
	if s.merger == nil {
		return fmt.Errorf("merger not initialized")
	}

	s.logger.Info("Starting memory merge", "category", category)

	// Use a reasonable similarity threshold for merging
	// Lower than decision threshold to catch more related items
	minSimilarity := MergeSimilarityThreshold

	err := s.merger.MergeRelatedMemories(ctx, category, minSimilarity)
	if err != nil {
		s.logger.Error("Failed to merge memories", "error", err)
		return err
	}

	s.logger.Info("Memory merge completed", "category", category)
	return nil
}

// MergeBeveragePreferences is a convenience method to merge all beverage-related memories.
func (s *Service) MergeBeveragePreferences(ctx context.Context) error {
	return s.MergeRelatedMemories(ctx, "beverages")
}

// BuildMemoryGraph builds relationships between memories.
func (s *Service) BuildMemoryGraph(ctx context.Context) error {
	if s.graph == nil {
		return fmt.Errorf("graph not initialized")
	}

	s.logger.Info("Building memory graph")
	return s.graph.BuildCategoryGraph(ctx)
}

// GetRelatedMemories finds memories related to a specific memory.
func (s *Service) GetRelatedMemories(ctx context.Context, memoryID string) ([]*Memory, error) {
	var pgUUID pgtype.UUID
	if err := pgUUID.Scan(memoryID); err != nil {
		return nil, NewMemoryError("GetByID", KindValidation, err, "invalid memory ID")
	}

	return s.graph.FindRelated(ctx, pgUUID, nil, DefaultGraphDepth)
}

// GetMemoryNetwork returns the network view of a memory and its connections.
func (s *Service) GetMemoryNetwork(ctx context.Context, memoryID string) (*NetworkView, error) {
	var pgUUID pgtype.UUID
	if err := pgUUID.Scan(memoryID); err != nil {
		return nil, NewMemoryError("GetByID", KindValidation, err, "invalid memory ID")
	}

	return s.graph.GetMemoryNetwork(ctx, pgUUID)
}

// ConsolidateMemories runs memory consolidation strategies.
func (s *Service) ConsolidateMemories(ctx context.Context) error {
	if s.consolidator == nil {
		return fmt.Errorf("consolidator not initialized")
	}

	s.logger.Info("Running memory consolidation")
	return s.consolidator.ConsolidateAll(ctx)
}

// RunConsolidationStrategy runs a specific consolidation strategy.
func (s *Service) RunConsolidationStrategy(ctx context.Context, strategy ConsolidationStrategy) error {
	if s.consolidator == nil {
		return fmt.Errorf("consolidator not initialized")
	}

	return s.consolidator.Consolidate(ctx, strategy)
}

// GetQueueMetrics returns current processing queue metrics.
func (s *Service) GetQueueMetrics() QueueMetrics {
	if s.queue == nil {
		return QueueMetrics{}
	}

	return s.queue.GetMetrics()
}

// Close waits for all pending operations to complete.
func (s *Service) Close() error {
	s.logger.Info("Closing memory service")

	// Cancel the context to stop all background operations
	if s.cancel != nil {
		s.cancel()
	}

	// Stop the processing queue first
	if s.queue != nil {
		s.queue.Stop()
	}

	// Wait for all goroutines
	s.wg.Wait()

	s.logger.Info("Memory service closed")
	return nil
}
