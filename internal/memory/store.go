// Package memory provides simplified storage operations.
package memory

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/pgvector/pgvector-go"

	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/koopa0/assistant-go/internal/platform/logger"
	"github.com/koopa0/assistant-go/internal/storage/database/sqlc"
)

// Store manages memory persistence with PostgreSQL and pgvector
type Store struct {
	db              *pgxpool.Pool
	queries         sqlc.Querier
	embeddingClient ai.Embedding
	logger          logger.Logger
}

// NewStore creates a new memory store
func NewStore(db *pgxpool.Pool, embeddingClient ai.Embedding, log logger.Logger) *Store {
	return &Store{
		db:              db,
		queries:         sqlc.New(db),
		embeddingClient: embeddingClient,
		logger:          log.WithComponent("memory.store"),
	}
}

// Queries returns the underlying SQL queries interface
func (s *Store) Queries() *sqlc.Queries {
	q, _ := s.queries.(*sqlc.Queries)
	return q
}

// Save stores a new memory
func (s *Store) Save(ctx context.Context, memory *Memory) error {
	s.logger.Debug("Save called", "content", memory.Content, "type", memory.Type)
	s.logger.Info("[STORE] Save called",
		"content", memory.Content,
		"type", memory.Type,
		"keywords", memory.Keywords)

	// Generate embedding
	s.logger.Debug("Generating embedding")
	embedding, err := s.generateEmbedding(ctx, memory.Content)
	if err != nil {
		s.logger.Error("Embedding generation failed", "error", err)
		return NewMemoryError("save", KindEmbedding, err, "generate embedding failed")
	}
	s.logger.Debug("Embedding generated successfully")
	s.logger.Debug("[STORE] Generated embedding successfully")

	// Convert to database types
	entities, err := json.Marshal(memory.Entities)
	if err != nil {
		return NewMemoryError("save", KindValidation, err, "marshal entities failed")
	}

	attrs, err := json.Marshal(memory.Attributes)
	if err != nil {
		return NewMemoryError("save", KindValidation, err, "marshal attributes failed")
	}

	ctxData, err := json.Marshal(memory.Context)
	if err != nil {
		return NewMemoryError("save", KindValidation, err, "marshal context failed")
	}

	// Generate ID if not set
	if memory.ID.Bytes == [16]byte{} {
		memory.ID = pgtype.UUID{
			Bytes: uuid.New(),
			Valid: true,
		}
	}

	// Final validation of memory type before database insert
	validTypes := map[MemoryType]bool{
		"fact":         true,
		"preference":   true,
		"schedule":     true,
		"relationship": true,
		"goal":         true,
		"skill":        true,
	}

	if !validTypes[memory.Type] {
		return NewMemoryError("save", KindValidation,
			fmt.Errorf("invalid memory type: %s", memory.Type))
	}

	// Create memory
	params := sqlc.CreateMemoryParams{
		SemanticID: memory.SemanticID,
		Type:       sqlc.MemoryType(memory.Type),
		Content:    memory.Content,
		Summary:    pgtype.Text{String: memory.Summary, Valid: memory.Summary != ""},
		Entities:   entities,
		Attributes: attrs,
		Context:    ctxData,
		Embedding:  embedding,
		Keywords:   memory.Keywords,
		Confidence: pgtype.Float4{Float32: memory.Confidence, Valid: true},
	}

	s.logger.Debug("Calling CreateMemory with params")
	created, err := s.queries.CreateMemory(ctx, params)
	if err != nil {
		s.logger.Error("Database insert failed", "error", err)
		return NewMemoryError("save", KindStorage, err, "database insert failed")
	}

	// Update memory with database-generated values
	memory.ID = created.ID
	memory.CreatedAt = created.CreatedAt
	memory.UpdatedAt = created.UpdatedAt

	s.logger.Info("Memory saved successfully", "id", uuid.UUID(memory.ID.Bytes).String())
	s.logger.Info("[STORE] Memory saved successfully",
		"id", uuid.UUID(memory.ID.Bytes).String(),
		"content", memory.Content,
		"type", memory.Type)

	// Create history entry
	if err := s.queries.CreateHistory(ctx, sqlc.CreateHistoryParams{
		MemoryID:   memory.ID,
		Action:     sqlc.ActionTypeADD,
		Version:    1,
		Content:    memory.Content,
		Entities:   entities,
		Attributes: attrs,
		Context:    ctxData,
		Reason:     pgtype.Text{String: "Initial creation", Valid: true},
		CreatedBy:  pgtype.Text{String: "system", Valid: true},
	}); err != nil {
		// Non-fatal error - log but continue
		s.logger.Warn("[STORE] Failed to create history entry", "error", err)
	}

	return nil
}

// Update modifies an existing memory
func (s *Store) Update(ctx context.Context, id pgtype.UUID, memory *Memory) error {

	// Generate new embedding
	embedding, err := s.generateEmbedding(ctx, memory.Content)
	if err != nil {
		return NewMemoryError("update", KindEmbedding, err, "generate embedding failed")
	}

	// Marshal data
	entities, err := json.Marshal(memory.Entities)
	if err != nil {
		return NewMemoryError("update", KindValidation, err, "marshal entities failed")
	}

	attrs, err := json.Marshal(memory.Attributes)
	if err != nil {
		return NewMemoryError("update", KindValidation, err, "marshal attributes failed")
	}

	ctxData, err := json.Marshal(memory.Context)
	if err != nil {
		return NewMemoryError("update", KindValidation, err, "marshal context failed")
	}

	// Update memory
	err = s.queries.UpdateMemory(ctx, sqlc.UpdateMemoryParams{
		ID:         id,
		Content:    memory.Content,
		Summary:    pgtype.Text{String: memory.Summary, Valid: memory.Summary != ""},
		Entities:   entities,
		Attributes: attrs,
		Context:    ctxData,
		Confidence: pgtype.Float4{Float32: memory.Confidence, Valid: true},
	})
	if err != nil {
		return NewMemoryError("update", KindStorage, err, "update memory failed")
	}

	// Update embedding separately
	if err := s.queries.SetEmbedding(ctx, sqlc.SetEmbeddingParams{
		Embedding: embedding,
		ID:        id,
	}); err != nil {
		return NewMemoryError("update", KindStorage, err, "update embedding failed")
	}

	return nil
}

// Delete removes a memory
func (s *Store) Delete(ctx context.Context, id pgtype.UUID) error {
	return s.queries.DeactivateMemory(ctx, id)
}

// DeactivateMany deactivates multiple memories at once
func (s *Store) DeactivateMany(ctx context.Context, ids []pgtype.UUID) error {
	return s.queries.DeactivateMany(ctx, ids)
}

// Get retrieves a memory by ID
func (s *Store) Get(ctx context.Context, id pgtype.UUID) (*Memory, error) {
	row, err := s.queries.GetMemory(ctx, id)
	if err != nil {
		return nil, err
	}
	return s.rowToMemory(row)
}

// Search finds memories matching the query
func (s *Store) Search(ctx context.Context, query Query) ([]*Memory, error) {
	s.logger.Info("[STORE] Search called",
		"text", query.Text,
		"types", query.Types,
		"limit", query.Limit,
		"minScore", query.MinScore)

	// Generate embedding for query if text is provided
	var embedding pgvector.Vector
	if query.Text != "" {
		var err error
		embedding, err = s.generateEmbedding(ctx, query.Text)
		if err != nil {
			return nil, NewMemoryError("search", KindEmbedding, err, "generate query embedding failed")
		}
		s.logger.Debug("[STORE] Generated embedding for query")
	}

	// Convert types
	var types []sqlc.MemoryType
	if len(query.Types) > 0 {
		types = make([]sqlc.MemoryType, len(query.Types))
		for i, t := range query.Types {
			types[i] = sqlc.MemoryType(t)
		}
	}

	// Set time range
	var timeStart, timeEnd pgtype.Timestamptz
	if query.TimeRange != nil {
		timeStart = pgtype.Timestamptz{Time: query.TimeRange.Start, Valid: true}
		timeEnd = pgtype.Timestamptz{Time: query.TimeRange.End, Valid: true}
	}

	// Use the sqlc-generated SearchMemories method
	searchParams := sqlc.SearchMemoriesParams{
		Embedding:     embedding,
		Types:         types,
		Entities:      query.Entities,
		TimeStart:     timeStart,
		TimeEnd:       timeEnd,
		Text:          query.Text,
		MinSimilarity: query.MinScore,
	}

	// Call the sqlc-generated method
	s.logger.Debug("[STORE] Calling SearchMemories with params", "params", fmt.Sprintf("%+v", searchParams))
	searchResults, err := s.queries.SearchMemories(ctx, searchParams)
	if err != nil {
		s.logger.Error("[STORE] SearchMemories failed", "error", err)
		return nil, NewMemoryError("search", KindStorage, err, "search memories failed")
	}
	s.logger.Info("[STORE] SearchMemories returned results", "count", len(searchResults))

	// Apply limit if specified
	if query.Limit > 0 && len(searchResults) > query.Limit {
		searchResults = searchResults[:query.Limit]
	}

	// Convert SearchMemoriesRow to Memory structs
	memories := make([]*Memory, 0, len(searchResults))
	for _, row := range searchResults {
		memory := &Memory{
			ID:          row.ID,
			SemanticID:  row.SemanticID,
			Type:        MemoryType(row.Type),
			Content:     row.Content,
			Summary:     row.Summary.String,
			Keywords:    row.Keywords,
			Confidence:  row.Confidence.Float32,
			Version:     int(row.Version.Int32),
			AccessCount: row.AccessCount.Int32,
			CreatedAt:   row.CreatedAt,
			UpdatedAt:   row.UpdatedAt,
			Similarity:  row.Similarity,
		}

		// Parse entities
		if len(row.Entities) > 0 {
			_ = json.Unmarshal(row.Entities, &memory.Entities) // Ignore error, use empty if parsing fails
		}

		// Parse attributes
		if len(row.Attributes) > 0 {
			_ = json.Unmarshal(row.Attributes, &memory.Attributes) // Ignore error, use empty if parsing fails
		}

		// Parse context
		if len(row.Context) > 0 {
			_ = json.Unmarshal(row.Context, &memory.Context) // Ignore error, use empty if parsing fails
		}

		if row.AccessedAt.Valid {
			memory.AccessedAt = row.AccessedAt.Time
		}

		memories = append(memories, memory)
	}

	return memories, nil
}

// SearchSimilar finds memories similar to the given text using optimized vector search
func (s *Store) SearchSimilar(ctx context.Context, text string, limit int) ([]*Memory, error) {
	// Generate embedding for the search text
	embedding, err := s.generateEmbedding(ctx, text)
	if err != nil {
		return nil, NewMemoryError("searchSimilar", KindEmbedding, err, "generate query embedding failed")
	}

	// Use the optimized SearchSimilarWithLimit query that leverages the HNSW index
	params := sqlc.SearchSimilarWithLimitParams{
		Embedding:     embedding,
		Status:        "active", // Only search active memories
		MinSimilarity: MinSimilarityThreshold,
		LimitCount:    int32(min(limit, math.MaxInt32)), // #nosec G115 -- bounded by min()
	}

	rows, err := s.queries.SearchSimilarWithLimit(ctx, params)
	if err != nil {
		return nil, NewMemoryError("searchSimilar", KindStorage, err, "vector search failed")
	}

	// Convert rows to Memory structs
	memories := make([]*Memory, 0, len(rows))
	for _, row := range rows {
		memory := &Memory{
			ID:          row.ID,
			SemanticID:  row.SemanticID,
			Type:        MemoryType(row.Type),
			Content:     row.Content,
			Summary:     row.Summary.String,
			Keywords:    row.Keywords,
			Confidence:  row.Confidence.Float32,
			Version:     int(row.Version.Int32),
			AccessCount: row.AccessCount.Int32,
			CreatedAt:   row.CreatedAt,
			UpdatedAt:   row.UpdatedAt,
			Similarity:  row.Similarity,
		}

		// Parse entities
		if len(row.Entities) > 0 {
			_ = json.Unmarshal(row.Entities, &memory.Entities) // Ignore error, use empty if parsing fails
		}

		// Parse attributes
		if len(row.Attributes) > 0 {
			_ = json.Unmarshal(row.Attributes, &memory.Attributes) // Ignore error, use empty if parsing fails
		}

		// Parse context
		if len(row.Context) > 0 {
			_ = json.Unmarshal(row.Context, &memory.Context) // Ignore error, use empty if parsing fails
		}

		if row.AccessedAt.Valid {
			memory.AccessedAt = row.AccessedAt.Time
		}

		memories = append(memories, memory)
	}

	return memories, nil
}

// SaveBatch stores multiple memories efficiently using batch operations
func (s *Store) SaveBatch(ctx context.Context, memories []*Memory) error {
	if len(memories) == 0 {
		return nil
	}

	// Generate embeddings in batch for efficiency
	contents := make([]string, len(memories))
	for i, mem := range memories {
		contents[i] = mem.Content
	}

	embeddings, err := s.generateEmbeddingsBatch(ctx, contents)
	if err != nil {
		return NewMemoryError("saveBatch", KindEmbedding, err, "batch embedding generation failed")
	}

	// Use transaction for batch operations
	tx, err := s.db.Begin(ctx)
	if err != nil {
		return NewMemoryError("saveBatch", KindStorage, err, "failed to begin transaction")
	}
	defer func() {
		_ = tx.Rollback(ctx)
	}()

	queries := sqlc.New(tx)
	var savedCount int

	// Process memories in batch
	for i, memory := range memories {
		// Skip if embedding generation failed for this item
		if len(embeddings[i].Slice()) == 0 {
			continue
		}

		// Use transaction queries for better performance
		if err := s.saveSingleMemoryTx(ctx, queries, memory, embeddings[i]); err != nil {
			// Log error but continue with other memories for resilience
			continue
		}
		savedCount++
	}

	// Commit transaction
	if err := tx.Commit(ctx); err != nil {
		return NewMemoryError("saveBatch", KindStorage, err, "failed to commit transaction")
	}

	// Return error if no memories were saved
	if savedCount == 0 && len(memories) > 0 {
		return NewMemoryError("saveBatch", KindProcessing,
			errors.New("all memories failed to save"),
			fmt.Sprintf("attempted: %d, saved: %d", len(memories), savedCount))
	}

	return nil
}

// saveSingleMemoryTx saves a memory with pre-generated embedding using a transaction
func (s *Store) saveSingleMemoryTx(ctx context.Context, queries sqlc.Querier, memory *Memory, embedding pgvector.Vector) error {
	// Convert to database types
	entities, err := json.Marshal(memory.Entities)
	if err != nil {
		return NewMemoryError("saveSingleMemoryTx", KindValidation, err, "marshal entities failed")
	}

	attrs, err := json.Marshal(memory.Attributes)
	if err != nil {
		return NewMemoryError("saveSingleMemoryTx", KindValidation, err, "marshal attributes failed")
	}

	ctxData, err := json.Marshal(memory.Context)
	if err != nil {
		return NewMemoryError("saveSingleMemoryTx", KindValidation, err, "marshal context failed")
	}

	// Generate ID if not set
	if memory.ID.Bytes == [16]byte{} {
		memory.ID = pgtype.UUID{
			Bytes: uuid.New(),
			Valid: true,
		}
	}

	// Final validation of memory type before database insert
	validTypes := map[MemoryType]bool{
		"fact":         true,
		"preference":   true,
		"schedule":     true,
		"relationship": true,
		"goal":         true,
		"skill":        true,
	}

	if !validTypes[memory.Type] {
		return NewMemoryError("saveSingleMemoryTx", KindValidation,
			fmt.Errorf("invalid memory type: %s", memory.Type))
	}

	// Create memory
	params := sqlc.CreateMemoryParams{
		SemanticID: memory.SemanticID,
		Type:       sqlc.MemoryType(memory.Type),
		Content:    memory.Content,
		Summary:    pgtype.Text{String: memory.Summary, Valid: memory.Summary != ""},
		Entities:   entities,
		Attributes: attrs,
		Context:    ctxData,
		Embedding:  embedding,
		Keywords:   memory.Keywords,
		Confidence: pgtype.Float4{Float32: memory.Confidence, Valid: true},
	}

	created, err := queries.CreateMemory(ctx, params)
	if err != nil {
		return NewMemoryError("saveSingleMemoryTx", KindStorage, err, "database insert failed")
	}

	// Update memory with database-generated values
	memory.ID = created.ID
	memory.CreatedAt = created.CreatedAt
	memory.UpdatedAt = created.UpdatedAt

	// Create history entry
	_ = queries.CreateHistory(ctx, sqlc.CreateHistoryParams{
		MemoryID:   memory.ID,
		Action:     sqlc.ActionTypeADD,
		Version:    1,
		Content:    memory.Content,
		Entities:   entities,
		Attributes: attrs,
		Context:    ctxData,
		Reason:     pgtype.Text{String: "Initial creation", Valid: true},
		CreatedBy:  pgtype.Text{String: "system", Valid: true},
	}) // Non-fatal error - history is not critical for memory functionality

	return nil
}

// GetBySemanticID retrieves a memory by its semantic ID
func (s *Store) GetBySemanticID(ctx context.Context, semanticID string) (*Memory, error) {
	row, err := s.queries.GetMemoryBySemantic(ctx, semanticID)
	if err != nil {
		return nil, err
	}
	return s.rowToMemory(row)
}

// ListRecent returns recently accessed memories
func (s *Store) ListRecent(ctx context.Context, limit int) ([]*Memory, error) {
	// Ensure limit is within int32 bounds
	if limit > math.MaxInt32 {
		limit = math.MaxInt32
	}
	rows, err := s.queries.ListRecent(ctx, int32(limit)) // #nosec G115 -- bounded above
	if err != nil {
		return nil, err
	}

	memories := make([]*Memory, 0, len(rows))
	for _, row := range rows {
		mem, err := s.rowToMemory(row)
		if err != nil {
			continue
		}
		memories = append(memories, mem)
	}

	return memories, nil
}

// generateEmbedding creates a vector embedding for text
func (s *Store) generateEmbedding(ctx context.Context, text string) (pgvector.Vector, error) {
	if text == "" {
		return pgvector.Vector{}, nil
	}

	s.logger.Debug("[STORE] Generating embedding", "text", text)

	req := &ai.EmbedRequest{
		Text:  text,
		Model: "gemini-embedding-001",
	}

	// Use Embed method which automatically selects appropriate provider (Gemini)
	resp, err := s.embeddingClient.Embed(ctx, req)
	if err != nil {
		s.logger.Error("[STORE] Embedding generation failed", "error", err, "text", text)
		return pgvector.Vector{}, NewMemoryError("generateEmbedding", KindEmbedding, err, "embedding service failed")
	}

	s.logger.Debug("[STORE] Embedding generated successfully", "dimension", len(resp.Embedding))
	return pgvector.NewVector(resp.Embedding), nil
}

// TestEmbedding is a public method for testing embedding generation
func (s *Store) TestEmbedding(ctx context.Context, text string) ([]float32, error) {
	vec, err := s.generateEmbedding(ctx, text)
	if err != nil {
		return nil, err
	}
	return vec.Slice(), nil
}

// generateEmbeddingsBatch creates vector embeddings for multiple texts in batch.
// This is more efficient than generating embeddings one by one.
func (s *Store) generateEmbeddingsBatch(ctx context.Context, texts []string) ([]pgvector.Vector, error) {
	if len(texts) == 0 {
		return []pgvector.Vector{}, nil
	}

	// Filter out empty texts
	validTexts := make([]string, 0, len(texts))
	validIndices := make([]int, 0, len(texts))
	for i, text := range texts {
		if text != "" {
			validTexts = append(validTexts, text)
			validIndices = append(validIndices, i)
		}
	}

	if len(validTexts) == 0 {
		return make([]pgvector.Vector, len(texts)), nil
	}

	// Generate embeddings in batch
	// For batch embeddings, we need to make individual calls
	embeddings := make([][]float32, len(validTexts))
	for i, text := range validTexts {
		resp, err := s.embeddingClient.Embed(ctx, &ai.EmbedRequest{
			Text:  text,
			Model: "gemini-embedding-001",
		})
		if err != nil {
			return nil, NewMemoryError("generateEmbeddingsBatch", KindEmbedding, err, "batch embedding generation failed")
		}
		embeddings[i] = resp.Embedding
	}

	// Map back to original indices
	result := make([]pgvector.Vector, len(texts))
	for i, validIdx := range validIndices {
		result[validIdx] = pgvector.NewVector(embeddings[i])
	}

	return result, nil
}

// rowToMemory converts a database row to Memory
func (s *Store) rowToMemory(row *sqlc.Memory) (*Memory, error) {
	memory := &Memory{
		ID:          row.ID,
		SemanticID:  row.SemanticID,
		Type:        MemoryType(row.Type),
		Content:     row.Content,
		Summary:     row.Summary.String,
		Keywords:    row.Keywords,
		Confidence:  row.Confidence.Float32,
		Version:     int(row.Version.Int32),
		AccessCount: row.AccessCount.Int32,
		CreatedAt:   row.CreatedAt,
		UpdatedAt:   row.UpdatedAt,
	}

	// Parse entities
	if len(row.Entities) > 0 {
		if err := json.Unmarshal(row.Entities, &memory.Entities); err != nil {
			return nil, NewMemoryError("rowToMemory", KindValidation, err, "unmarshal entities failed")
		}
	}

	// Parse attributes
	if len(row.Attributes) > 0 {
		if err := json.Unmarshal(row.Attributes, &memory.Attributes); err != nil {
			return nil, NewMemoryError("rowToMemory", KindValidation, err, "unmarshal attributes failed")
		}
	}

	// Parse context
	if len(row.Context) > 0 {
		if err := json.Unmarshal(row.Context, &memory.Context); err != nil {
			return nil, NewMemoryError("rowToMemory", KindValidation, err, "unmarshal context failed")
		}
	}

	if row.AccessedAt.Valid {
		memory.AccessedAt = row.AccessedAt.Time
	}

	return memory, nil
}
