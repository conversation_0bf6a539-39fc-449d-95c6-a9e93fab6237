// Package memory defines standard errors for the memory system.
package memory

import (
	"errors"
	"fmt"
)

// Sentinel errors for common memory operations
var (
	// ErrNotFound indicates the requested memory doesn't exist
	ErrNotFound = errors.New("memory not found")

	// ErrInvalidType indicates an unsupported memory type
	ErrInvalidType = errors.New("invalid memory type")

	// ErrDuplicate indicates a memory with the same semantic ID already exists
	ErrDuplicate = errors.New("duplicate memory")

	// ErrEmptyContent indicates the memory content is empty
	ErrEmptyContent = errors.New("empty memory content")

	// ErrInvalidQuery indicates the search query is invalid
	ErrInvalidQuery = errors.New("invalid search query")

	// ErrEmbeddingFailed indicates embedding generation failed
	ErrEmbeddingFailed = errors.New("embedding generation failed")

	// ErrStorageUnavailable indicates the storage backend is unavailable
	ErrStorageUnavailable = errors.New("storage unavailable")

	// ErrProcessingTimeout indicates memory processing exceeded timeout
	ErrProcessingTimeout = errors.New("processing timeout")

	// ErrQueueFull indicates the processing queue is at capacity
	ErrQueueFull = errors.New("processing queue full")
)

// MemoryError represents a detailed error from memory operations
type MemoryError struct {
	Op      string // Operation that failed
	Kind    string // Type of error
	Err     error  // Underlying error
	Message string // Additional context
}

// Error implements the error interface
func (e *MemoryError) Error() string {
	if e.Message != "" {
		return fmt.Sprintf("memory %s failed (%s): %s: %v", e.Op, e.Kind, e.Message, e.Err)
	}
	return fmt.Sprintf("memory %s failed (%s): %v", e.Op, e.Kind, e.Err)
}

// Unwrap returns the underlying error
func (e *MemoryError) Unwrap() error {
	return e.Err
}

// Is checks if the error matches target
func (e *MemoryError) Is(target error) bool {
	return errors.Is(e.Err, target)
}

// NewMemoryError creates a new MemoryError
func NewMemoryError(op, kind string, err error, message ...string) *MemoryError {
	e := &MemoryError{
		Op:   op,
		Kind: kind,
		Err:  err,
	}
	if len(message) > 0 {
		e.Message = message[0]
	}
	return e
}

// Error kinds for categorizing errors
const (
	KindValidation = "validation"
	KindStorage    = "storage"
	KindEmbedding  = "embedding"
	KindProcessing = "processing"
	KindConflict   = "conflict"
	KindNotFound   = "not_found"
)

// IsNotFound checks if an error indicates a memory was not found
func IsNotFound(err error) bool {
	return errors.Is(err, ErrNotFound)
}

// IsValidation checks if an error is a validation error
func IsValidation(err error) bool {
	var memErr *MemoryError
	if errors.As(err, &memErr) {
		return memErr.Kind == KindValidation
	}
	return false
}

// IsStorage checks if an error is a storage error
func IsStorage(err error) bool {
	var memErr *MemoryError
	if errors.As(err, &memErr) {
		return memErr.Kind == KindStorage
	}
	return errors.Is(err, ErrStorageUnavailable)
}
