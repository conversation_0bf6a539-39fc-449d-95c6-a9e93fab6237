package memory

import (
	"context"
	"testing"

	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/koopa0/assistant-go/internal/extract"
	"github.com/koopa0/assistant-go/internal/platform/logger"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// mockExtractor implements Extractor interface for testing
type mockExtractor struct {
	extractFunc func(ctx context.Context, messages []ai.Message) ([]extract.Fact, error)
}

func (m *mockExtractor) Extract(ctx context.Context, messages []ai.Message) ([]extract.Fact, error) {
	if m.extractFunc != nil {
		return m.extractFunc(ctx, messages)
	}
	return []extract.Fact{}, nil
}

// mockDecider implements Decider interface for testing
type mockDecider struct {
	decideFunc func(ctx context.Context, fact extract.Fact, existing []*Memory) (Decision, error)
}

func (m *mockDecider) Decide(ctx context.Context, fact extract.Fact, existing []*Memory) (Decision, error) {
	if m.decideFunc != nil {
		return m.decideFunc(ctx, fact, existing)
	}
	return Decision{}, nil
}

// mockStore implements Store interface for testing
type mockStore struct {
	saveFunc   func(ctx context.Context, mem *Memory) error
	updateFunc func(ctx context.Context, mem *Memory) error
	deleteFunc func(ctx context.Context, id string) error
	searchFunc func(ctx context.Context, query Query) ([]*Memory, error)
}

func (m *mockStore) Save(ctx context.Context, mem *Memory) error {
	if m.saveFunc != nil {
		return m.saveFunc(ctx, mem)
	}
	return nil
}

func (m *mockStore) Update(ctx context.Context, mem *Memory) error {
	if m.updateFunc != nil {
		return m.updateFunc(ctx, mem)
	}
	return nil
}

func (m *mockStore) Delete(ctx context.Context, id string) error {
	if m.deleteFunc != nil {
		return m.deleteFunc(ctx, id)
	}
	return nil
}

func (m *mockStore) Search(ctx context.Context, query Query) ([]*Memory, error) {
	if m.searchFunc != nil {
		return m.searchFunc(ctx, query)
	}
	return []*Memory{}, nil
}

func (m *mockStore) SearchSimilar(ctx context.Context, userID string, embedding []float32, limit int) ([]*Memory, error) {
	return []*Memory{}, nil
}

func (m *mockStore) Get(ctx context.Context, id string) (*Memory, error) {
	return nil, nil
}

func (m *mockStore) GetByUser(ctx context.Context, userID string, limit int) ([]*Memory, error) {
	return []*Memory{}, nil
}

// TestNewProcessor tests processor creation
func TestNewProcessor(t *testing.T) {
	extractor := &mockExtractor{}
	decider := &mockDecider{}
	log := logger.NewConsoleLogger()

	processor := NewProcessor(extractor, decider, nil, log)

	require.NotNil(t, processor)
	assert.NotNil(t, processor.extractor)
	assert.NotNil(t, processor.decider)
	assert.Nil(t, processor.store)
	assert.NotNil(t, processor.logger)
}

// TestProcessor_Process tests the process method
func TestProcessor_Process(t *testing.T) {
	tests := []struct {
		name          string
		messages      []ai.Message
		extractResult []extract.Fact
		extractError  error
		decideResult  Decision
		decideError   error
		saveError     error
		wantError     bool
	}{
		{
			name: "successful processing",
			messages: []ai.Message{
				{Role: ai.User, Content: "I like pizza"},
				{Role: ai.Assistant, Content: "Pizza is great!"},
			},
			extractResult: []extract.Fact{
				{Content: "User likes pizza", Type: extract.FactTypePreference},
			},
			decideResult: Decision{
				Actions: []Action{{Type: ActionAdd}},
			},
			wantError: false,
		},
		{
			name: "extraction error",
			messages: []extract.Message{
				{Role: "user", Content: "Test"},
			},
			extractError: assert.AnError,
			wantError:    true,
		},
		{
			name: "no facts extracted",
			messages: []extract.Message{
				{Role: "user", Content: "Hello"},
			},
			extractResult: []extract.Fact{},
			wantError:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			extractor := &mockExtractor{
				extractFunc: func(ctx context.Context, messages []extract.Message) ([]extract.Fact, error) {
					return tt.extractResult, tt.extractError
				},
			}

			decider := &mockDecider{
				decideFunc: func(ctx context.Context, fact extract.Fact, existing []*Memory) (Decision, error) {
					return tt.decideResult, tt.decideError
				},
			}

			store := &mockStore{
				saveFunc: func(ctx context.Context, mem *Memory) error {
					return tt.saveError
				},
			}

			processor := NewProcessor(extractor, decider, store, logger.NewDiscardLogger())

			err := processor.Process(context.Background(), tt.messages)

			if tt.wantError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// BenchmarkProcessor_Process benchmarks the process method
func BenchmarkProcessor_Process(b *testing.B) {
	extractor := &mockExtractor{
		extractFunc: func(ctx context.Context, messages []extract.Message) ([]extract.Fact, error) {
			return []extract.Fact{
				{Fact: "Benchmark fact 1", Category: "test"},
				{Fact: "Benchmark fact 2", Category: "test"},
			}, nil
		},
	}

	decider := &mockDecider{
		decideFunc: func(ctx context.Context, fact extract.Fact, existing []*Memory) (Decision, error) {
			return Decision{
				Actions: []Action{{Type: ActionAdd}},
			}, nil
		},
	}

	store := &mockStore{
		saveFunc: func(ctx context.Context, mem *Memory) error {
			return nil
		},
	}

	processor := NewProcessor(extractor, decider, store, logger.NewDiscardLogger())

	messages := []extract.Message{
		{Role: "user", Content: "Benchmark message"},
		{Role: "assistant", Content: "Response"},
	}

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		err := processor.Process(ctx, "bench-user", messages)
		if err != nil {
			b.Fatalf("process failed: %v", err)
		}
	}
}
