# Memory Package

**Package:** `memory`
**Version:** 2.0.0

## Overview

The `memory` package provides the cognitive core for the <PERSON> assistant, transforming it from a stateless chatbot into a stateful companion with long-term, persistent memory. It implements a complete, sophisticated lifecycle for information, enabling the assistant to learn, reason about, and maintain knowledge over time.

This package receives structured `Fact` objects from the `extract` package and manages them within a dynamic knowledge base. Its architecture is designed for robustness, scalability, and intelligent information management.

## Core Features & Concepts

### 1. Asynchronous Processing Pipeline
-   **Resilient Queue**: All incoming facts are processed through a robust, asynchronous queue (`queue.go`) with batching and priority support.
-   **Dead-Letter Queue (DLQ)**: Failed jobs are automatically moved to a DLQ after multiple retries, ensuring no information is silently lost and allowing for manual inspection and reprocessing.

### 2. Intelligent Decision Making
-   **Semantic Decider**: The `Decider` (`decider.go`) uses vector similarity and LLM-based analysis to determine the correct action (ADD, UPDATE, DELETE, SKIP) for new information, preventing simple data duplication.
-   **Composite Actions**: The system can perform multiple actions in a single decision, such as deleting an old, contradictory memory while adding a new one.

### 3. Proactive Knowledge Curation
-   **The Consolidator**: A background service (`consolidator.go`) that actively maintains the health of the knowledge base.
-   **Graded Deduplication**: A key innovation. Instead of a binary duplicate/not-duplicate check, it uses a graded approach based on semantic similarity:
    -   **> 0.98 Similarity (Duplicate)**: The old memory is deactivated.
    -   **0.90 - 0.98 Similarity (Merge)**: The memories are merged, combining their metadata and relationships.
    -   **0.80 - 0.90 Similarity (Relate)**: A `similar_to` relationship is created in the knowledge graph, preserving both memories while acknowledging their connection.
-   **Information Archiving**: Old, irrelevant memories are automatically archived and eventually soft-deleted, keeping the active memory set clean and relevant.

### 4. Conflict & Contradiction Management
-   **Conflict Detector**: The `ConflictDetector` (`conflict.go`) proactively scans for contradictory memories (e.g., "I like coffee" vs. "I don't drink coffee").
-   **Intelligent Resolution**: It uses a weighted scoring system—considering recency, confidence, and access frequency—to suggest which memory to keep.
-   **AI-Powered Topic Extraction**: Uses LLMs to dynamically identify the topics of memories, allowing it to find conflicts beyond simple keyword matching.

### 5. Knowledge Graph
-   **Relationship Management**: The `Graph` (`graph.go`) component manages relationships between memories (e.g., `similar_to`, `related_activity`).
-   **Entity-Based Relations**: Automatically builds a graph of relationships based on shared entities and topics, enabling more advanced contextual retrieval.

## Architecture

The system is composed of several specialized, cooperating components:

-   `service.go`: The public API for the memory system.
-   `processor.go`: Orchestrates the processing of a single fact.
-   `store.go`: Handles all database interactions (PostgreSQL + pgvector).
-   `queue.go`: Manages the asynchronous job queue and DLQ.
-   `decider.go`: Decides how to integrate new information.
-   `consolidator.go`: Performs long-term memory maintenance.
-   `deduplicator.go`: Implements the graded semantic deduplication logic.
-   `conflict.go`: Detects and resolves contradictions.
-   `graph.go`: Manages the knowledge graph.

## Usage

```go
// Service initialization
memService, err := memory.New(db, aiService, logger)

// --- Storing Information (Asynchronous) ---
// This is the primary way to add information. It's non-blocking.
messages := []ai.Message{...}
memService.ProcessAsync(context.Background(), messages)

// --- Retrieving Information ---
// Simple search
results, err := memService.Search(ctx, "my coffee preferences", 10)

// Search with advanced contextual retrieval (future)
// This would leverage the knowledge graph
// richContext, err := memService.GetRichContext(ctx, "what's new with my beverage preferences?")

// --- Maintenance ---
// The consolidator runs in the background, but can be triggered manually
err := memService.ConsolidateMemories(ctx)
```

## Key Design Decisions

-   **Knowledge as a Spectrum**: The graded deduplication strategy treats similarity as a spectrum, not a binary state. This allows for a more nuanced and human-like handling of information.
-   **Resilience by Default**: The asynchronous queue with its DLQ ensures that the system is resilient to transient errors and that valuable information is preserved.
-   **Active Curation, Not Passive Storage**: The `Consolidator` makes the system proactive. It doesn't wait for the memory to become messy; it actively organizes, merges, and archives information to maintain its health.
