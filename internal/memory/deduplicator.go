// Package memory provides semantic deduplication capabilities.
package memory

import (
	"context"
	"math"
	"sort"

	"github.com/pgvector/pgvector-go"
)

// DuplicateGroup represents a group of semantically similar memories
type DuplicateGroup struct {
	Primary    *Memory   // The memory to keep
	Duplicates []*Memory // Memories to remove/merge
	Similarity float32   // Average similarity within group
}

// SemanticDeduplicator handles memory deduplication using vector embeddings
type SemanticDeduplicator struct {
	store               *Store
	similarityThreshold float32
}

// NewSemanticDeduplicator creates a new semantic deduplicator
func NewSemanticDeduplicator(store *Store, threshold float32) *SemanticDeduplicator {
	if threshold <= 0 || threshold > 1 {
		threshold = ExactMatchThreshold // 0.95 from constants
	}
	return &SemanticDeduplicator{
		store:               store,
		similarityThreshold: threshold,
	}
}

// FindDuplicates finds semantically similar memories using vector embeddings
func (d *SemanticDeduplicator) FindDuplicates(ctx context.Context, memories []*Memory) ([]DuplicateGroup, error) {
	if len(memories) < 2 {
		return nil, nil
	}

	// Build similarity matrix
	similarities := d.computeSimilarityMatrix(memories)

	// Cluster similar memories
	groups := d.clusterBySimilarity(memories, similarities)

	// Convert to DuplicateGroups
	var duplicateGroups []DuplicateGroup
	for _, group := range groups {
		if len(group) < 2 {
			continue
		}

		dg := d.createDuplicateGroup(group, similarities)
		duplicateGroups = append(duplicateGroups, dg)
	}

	return duplicateGroups, nil
}

// computeSimilarityMatrix calculates pairwise similarities
func (d *SemanticDeduplicator) computeSimilarityMatrix(memories []*Memory) [][]float32 {
	n := len(memories)
	matrix := make([][]float32, n)
	for i := range matrix {
		matrix[i] = make([]float32, n)
		matrix[i][i] = 1.0 // Self-similarity
	}

	// Compute pairwise similarities
	for i := 0; i < n; i++ {
		for j := i + 1; j < n; j++ {
			sim := d.cosineSimilarity(memories[i].Embedding, memories[j].Embedding)
			matrix[i][j] = sim
			matrix[j][i] = sim
		}
	}

	return matrix
}

// cosineSimilarity computes cosine similarity between two vectors
func (d *SemanticDeduplicator) cosineSimilarity(v1, v2 pgvector.Vector) float32 {
	vec1 := v1.Slice()
	vec2 := v2.Slice()

	if len(vec1) != len(vec2) || len(vec1) == 0 {
		return 0
	}

	var dotProduct, norm1, norm2 float64
	for i := range vec1 {
		dotProduct += float64(vec1[i]) * float64(vec2[i])
		norm1 += float64(vec1[i]) * float64(vec1[i])
		norm2 += float64(vec2[i]) * float64(vec2[i])
	}

	if norm1 == 0 || norm2 == 0 {
		return 0
	}

	return float32(dotProduct / (math.Sqrt(norm1) * math.Sqrt(norm2)))
}

// clusterBySimilarity groups memories based on similarity threshold
func (d *SemanticDeduplicator) clusterBySimilarity(memories []*Memory, similarities [][]float32) [][]*Memory {
	n := len(memories)
	assigned := make([]bool, n)
	var groups [][]*Memory

	// Greedy clustering
	for i := 0; i < n; i++ {
		if assigned[i] {
			continue
		}

		// Start new group
		group := []*Memory{memories[i]}
		assigned[i] = true

		// Add similar memories to group
		for j := i + 1; j < n; j++ {
			if assigned[j] {
				continue
			}

			// Check if similar to any member of the group
			similar := false
			for _, mem := range group {
				idx1 := d.findMemoryIndex(memories, mem)
				if similarities[idx1][j] >= d.similarityThreshold {
					similar = true
					break
				}
			}

			if similar {
				group = append(group, memories[j])
				assigned[j] = true
			}
		}

		groups = append(groups, group)
	}

	return groups
}

// createDuplicateGroup selects primary memory and organizes duplicates
func (d *SemanticDeduplicator) createDuplicateGroup(group []*Memory, similarities [][]float32) DuplicateGroup {
	// Sort by multiple criteria to select the best primary
	sort.Slice(group, func(i, j int) bool {
		// 1. Prefer higher confidence
		if group[i].Confidence != group[j].Confidence {
			return group[i].Confidence > group[j].Confidence
		}
		// 2. Prefer more recent access
		if !group[i].AccessedAt.Equal(group[j].AccessedAt) {
			return group[i].AccessedAt.After(group[j].AccessedAt)
		}
		// 3. Prefer higher access count
		if group[i].AccessCount != group[j].AccessCount {
			return group[i].AccessCount > group[j].AccessCount
		}
		// 4. Prefer more recent update
		return group[i].UpdatedAt.After(group[j].UpdatedAt)
	})

	// Calculate average similarity
	var totalSim float32
	count := 0
	for i := 0; i < len(group); i++ {
		for j := i + 1; j < len(group); j++ {
			idx1 := d.findMemoryIndex(group, group[i])
			idx2 := d.findMemoryIndex(group, group[j])
			if idx1 >= 0 && idx2 >= 0 {
				totalSim += similarities[idx1][idx2]
				count++
			}
		}
	}

	avgSim := float32(0)
	if count > 0 {
		avgSim = totalSim / float32(count)
	}

	return DuplicateGroup{
		Primary:    group[0],
		Duplicates: group[1:],
		Similarity: avgSim,
	}
}

// findMemoryIndex finds the index of a memory in the slice
func (d *SemanticDeduplicator) findMemoryIndex(memories []*Memory, target *Memory) int {
	for i, mem := range memories {
		if mem.ID == target.ID {
			return i
		}
	}
	return -1
}

// MergeDuplicates creates a merged memory from a duplicate group
func (d *SemanticDeduplicator) MergeDuplicates(group DuplicateGroup) *Memory {
	// Start with primary memory
	merged := *group.Primary

	// Merge entities from duplicates
	entityMap := make(map[string]bool)
	for _, entity := range merged.Entities {
		entityMap[entity.Name] = true
	}

	for _, dup := range group.Duplicates {
		for _, entity := range dup.Entities {
			if !entityMap[entity.Name] {
				merged.Entities = append(merged.Entities, entity)
				entityMap[entity.Name] = true
			}
		}

		// Merge keywords
		for _, keyword := range dup.Keywords {
			if !contains(merged.Keywords, keyword) {
				merged.Keywords = append(merged.Keywords, keyword)
			}
		}

		// Update access count
		merged.AccessCount += dup.AccessCount

		// Keep highest confidence
		if dup.Confidence > merged.Confidence {
			merged.Confidence = dup.Confidence
		}
	}

	return &merged
}

// contains checks if a string slice contains a value
func contains(slice []string, value string) bool {
	for _, v := range slice {
		if v == value {
			return true
		}
	}
	return false
}
