package memory

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/koopa0/assistant-go/internal/extract"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestSearchOption tests search option functions
func TestSearchOption(t *testing.T) {
	tests := []struct {
		name   string
		option SearchOption
		check  func(t *testing.T, opts *searchOptions)
	}{
		{
			name:   "WithType",
			option: WithType(MemoryTypeFact),
			check: func(t *testing.T, opts *searchOptions) {
				assert.Equal(t, MemoryTypeFact, opts.memoryType)
			},
		},
		{
			name:   "WithTextSearch",
			option: WithTextSearch("test query"),
			check: func(t *testing.T, opts *searchOptions) {
				assert.Equal(t, "test query", opts.text)
			},
		},
		{
			name:   "WithLimit",
			option: WithLimit(50),
			check: func(t *testing.T, opts *searchOptions) {
				assert.Equal(t, 50, opts.limit)
			},
		},
		{
			name:   "WithTimeRange",
			option: WithTimeRange(time.Now().Add(-24*time.Hour), time.Now()),
			check: func(t *testing.T, opts *searchOptions) {
				assert.NotNil(t, opts.startTime)
				assert.NotNil(t, opts.endTime)
			},
		},
		{
			name:   "WithMinScore",
			option: WithMinScore(0.8),
			check: func(t *testing.T, opts *searchOptions) {
				assert.Equal(t, float32(0.8), opts.minScore)
			},
		},
		{
			name:   "WithActive",
			option: WithActive(),
			check: func(t *testing.T, opts *searchOptions) {
				assert.True(t, opts.activeOnly)
			},
		},
		{
			name:   "WithOrdering",
			option: WithOrdering(OrderByRelevance),
			check: func(t *testing.T, opts *searchOptions) {
				assert.Equal(t, OrderByRelevance, opts.orderBy)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			opts := &searchOptions{
				limit: defaultSearchLimit,
			}
			tt.option(opts)
			tt.check(t, opts)
		})
	}
}

// TestMemory_Score tests memory scoring
func TestMemory_Score(t *testing.T) {
	now := time.Now()

	tests := []struct {
		name     string
		memory   Memory
		query    string
		expected float32
	}{
		{
			name: "recent memory with high confidence",
			memory: Memory{
				Confidence: 0.9,
				UpdatedAt:  now.Add(-1 * time.Hour),
			},
			expected: 1.0, // 0.9 + 0.2 (recent) = 1.1, capped at 1.0
		},
		{
			name: "week old memory",
			memory: Memory{
				Confidence:  0.8,
				UpdatedAt:   now.Add(-3 * 24 * time.Hour),
				AccessCount: 5,
			},
			expected: 0.9, // 0.8 + 0.1 (within week)
		},
		{
			name: "frequently accessed old memory",
			memory: Memory{
				Confidence:  0.7,
				UpdatedAt:   now.Add(-30 * 24 * time.Hour),
				AccessCount: 20,
			},
			expected: 0.8, // 0.7 + 0.1 (frequently accessed)
		},
		{
			name: "low confidence old memory",
			memory: Memory{
				Confidence:  0.3,
				UpdatedAt:   now.Add(-60 * 24 * time.Hour),
				AccessCount: 0,
			},
			expected: 0.3, // No bonuses
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			score := tt.memory.Score(tt.query, now)
			assert.Equal(t, tt.expected, score)
		})
	}
}

// TestMemory_IsRecurring tests recurrence checking
func TestMemory_IsRecurring(t *testing.T) {
	tests := []struct {
		name      string
		memory    Memory
		recurring bool
	}{
		{
			name: "memory with recurrence",
			memory: Memory{
				Context: Context{
					Recurrence: &extract.Recurrence{
						Type:     "daily",
						Interval: 1,
					},
				},
			},
			recurring: true,
		},
		{
			name: "memory without recurrence",
			memory: Memory{
				Context: Context{
					Recurrence: nil,
				},
			},
			recurring: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.recurring, tt.memory.IsRecurring())
		})
	}
}

// TestMemory_MarshalJSON tests JSON marshaling
func TestMemory_MarshalJSON(t *testing.T) {
	id := uuid.New()
	memory := &Memory{
		ID: pgtype.UUID{
			Bytes: id,
			Valid: true,
		},
		Content:    "Test memory",
		Confidence: 0.95,
		Status:     StatusActive,
	}

	data, err := memory.MarshalJSON()
	require.NoError(t, err)
	assert.Contains(t, string(data), id.String())
	assert.Contains(t, string(data), "Test memory")
}

// TestAttributes tests attribute methods
func TestAttributes(t *testing.T) {
	attrs := Attributes{
		"name":      "John",
		"age":       30,
		"timestamp": "2024-01-01T00:00:00Z",
	}

	t.Run("Get", func(t *testing.T) {
		val, exists := attrs.Get("name")
		assert.True(t, exists)
		assert.Equal(t, "John", val)

		_, exists = attrs.Get("missing")
		assert.False(t, exists)
	})

	t.Run("GetString", func(t *testing.T) {
		str, ok := attrs.GetString("name")
		assert.True(t, ok)
		assert.Equal(t, "John", str)

		_, ok = attrs.GetString("age")
		assert.False(t, ok)
	})

	t.Run("GetTime", func(t *testing.T) {
		tm, ok := attrs.GetTime("timestamp")
		assert.True(t, ok)
		assert.NotZero(t, tm)

		_, ok = attrs.GetTime("name")
		assert.False(t, ok)
	})

	t.Run("Merge", func(t *testing.T) {
		other := Attributes{
			"age":  35,
			"city": "New York",
		}

		merged := attrs.Merge(other)
		assert.Equal(t, "John", merged["name"])
		assert.Equal(t, 35, merged["age"]) // Overridden
		assert.Equal(t, "New York", merged["city"])
	})
}

// Benchmark tests

// BenchmarkMemory_Score benchmarks memory scoring
func BenchmarkMemory_Score(b *testing.B) {
	now := time.Now()
	memory := Memory{
		Confidence:  0.85,
		UpdatedAt:   now.Add(-2 * time.Hour),
		AccessCount: 10,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = memory.Score("benchmark query", now)
	}
}

// BenchmarkAttributes_Operations benchmarks attribute operations
func BenchmarkAttributes_Operations(b *testing.B) {
	attrs := make(Attributes)
	for i := 0; i < 20; i++ {
		attrs[string(rune('a'+i))] = i
	}

	b.Run("Get", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, _ = attrs.Get("j")
		}
	})

	b.Run("GetString", func(b *testing.B) {
		attrs["test"] = "value"
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, _ = attrs.GetString("test")
		}
	})

	b.Run("Merge", func(b *testing.B) {
		other := Attributes{"x": 1, "y": 2, "z": 3}
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = attrs.Merge(other)
		}
	})
}
