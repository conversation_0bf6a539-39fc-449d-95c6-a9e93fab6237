// Package memory implements memory merging capabilities.
package memory

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/koopa0/assistant-go/internal/ai"
)

// MergeStrategy defines how memories should be merged.
type MergeStrategy string

const (
	// MergeStrategyConcat concatenates related memories
	MergeStrategyConcat MergeStrategy = "concat"
	// MergeStrategyReplace replaces old with new
	MergeStrategyReplace MergeStrategy = "replace"
	// MergeStrategyLLM uses LLM to intelligently merge
	MergeStrategyLLM MergeStrategy = "llm"
)

// Merger handles the consolidation of related memories.
type Merger struct {
	store    *Store
	aiClient ai.Client // Optional: for LLM-based merging
}

// NewMerger creates a new memory merger.
func NewMerger(store *Store, aiClient ai.Client) *Merger {
	return &Merger{
		store:    store,
		aiClient: aiClient,
	}
}

// MergeRelatedMemories finds and merges related memories based on category and similarity.
func (m *Merger) MergeRelatedMemories(ctx context.Context, category string, minSimilarity float32) error {
	// Search for all memories in the category
	memories, err := m.findRelatedMemories(ctx, category)
	if err != nil {
		return NewMemoryError("MergeRelatedMemories", KindStorage, err, "find related memories")
	}

	if len(memories) < 2 {
		// Nothing to merge
		return nil
	}

	// Group memories by their semantic relationship
	groups := m.groupBySimilarity(memories, minSimilarity)

	// Merge each group
	for _, group := range groups {
		if len(group) < 2 {
			continue
		}

		// Determine merge strategy based on memory type
		strategy := m.determineMergeStrategy(group)

		// Perform merge
		merged, err := m.mergeGroup(ctx, group, strategy)
		if err != nil {
			// Skip this group and continue with others
			continue
		}

		// Save merged memory
		if err := m.saveMergedMemory(ctx, merged, group); err != nil {
			// Skip this group and continue with others
			continue
		}
	}

	return nil
}

// findRelatedMemories searches for memories that might be related.
func (m *Merger) findRelatedMemories(ctx context.Context, category string) ([]*Memory, error) {
	// For now, search by type
	// In future, could use category field or attributes
	query := Query{
		Types: []MemoryType{MemoryTypePreference, MemoryTypeFact},
		Limit: 100,
	}

	return m.store.Search(ctx, query)
}

// groupBySimilarity groups memories by their semantic similarity.
func (m *Merger) groupBySimilarity(memories []*Memory, minSimilarity float32) [][]*Memory {
	// Simple grouping by content similarity
	// In production, would use more sophisticated clustering

	var groups [][]*Memory
	used := make(map[int]bool)

	for i, mem1 := range memories {
		if used[i] {
			continue
		}

		group := []*Memory{mem1}
		used[i] = true

		// Find similar memories
		for j := i + 1; j < len(memories); j++ {
			if used[j] {
				continue
			}

			// Check if memories are related (simplified logic)
			if m.areRelated(mem1, memories[j]) {
				group = append(group, memories[j])
				used[j] = true
			}
		}

		if len(group) > 1 {
			groups = append(groups, group)
		}
	}

	return groups
}

// areRelated determines if two memories are related enough to merge.
func (m *Merger) areRelated(mem1, mem2 *Memory) bool {
	// Same type is a basic requirement
	if mem1.Type != mem2.Type {
		return false
	}

	// For preferences, check if they're in the same category
	if mem1.Type == MemoryTypePreference {
		// Simple heuristic: check if both mention beverages
		beverageKeywords := []string{"咖啡", "茶", "紅茶", "綠茶", "coffee", "tea", "喝", "drink"}

		mem1HasBeverage := false
		mem2HasBeverage := false

		for _, keyword := range beverageKeywords {
			if strings.Contains(strings.ToLower(mem1.Content), keyword) {
				mem1HasBeverage = true
			}
			if strings.Contains(strings.ToLower(mem2.Content), keyword) {
				mem2HasBeverage = true
			}
		}

		return mem1HasBeverage && mem2HasBeverage
	}

	return false
}

// determineMergeStrategy decides how to merge a group of memories.
func (m *Merger) determineMergeStrategy(group []*Memory) MergeStrategy {
	// If we have AI client, prefer LLM-based merging
	if m.aiClient != nil {
		return MergeStrategyLLM
	}

	// Otherwise, use simple concatenation for preferences
	if len(group) > 0 && group[0].Type == MemoryTypePreference {
		return MergeStrategyConcat
	}

	return MergeStrategyReplace
}

// mergeGroup merges a group of memories using the specified strategy.
func (m *Merger) mergeGroup(ctx context.Context, group []*Memory, strategy MergeStrategy) (*Memory, error) {
	switch strategy {
	case MergeStrategyLLM:
		return m.mergeLLM(ctx, group)
	case MergeStrategyConcat:
		return m.mergeConcat(group)
	case MergeStrategyReplace:
		return m.mergeReplace(group)
	default:
		return nil, fmt.Errorf("unknown merge strategy: %s", strategy)
	}
}

// mergeConcat concatenates memories into a comprehensive one.
func (m *Merger) mergeConcat(group []*Memory) (*Memory, error) {
	// Sort by creation time
	sort.Slice(group, func(i, j int) bool {
		return group[i].CreatedAt.Before(group[j].CreatedAt)
	})

	// Extract unique content items
	items := make([]string, 0, len(group))
	seen := make(map[string]bool)

	for _, mem := range group {
		// Extract the core item (e.g., "紅茶" from "喜歡喝紅茶")
		item := m.extractCoreItem(mem.Content)
		if item != "" && !seen[item] {
			items = append(items, item)
			seen[item] = true
		}
	}

	// Build merged content
	var content string
	if strings.Contains(group[0].Content, "喜歡") || strings.Contains(group[0].Content, "like") {
		if strings.Contains(group[0].Content, "喝") {
			content = fmt.Sprintf("喜歡喝的飲料：%s", strings.Join(items, "、"))
		} else {
			content = fmt.Sprintf("喜歡：%s", strings.Join(items, "、"))
		}
	} else {
		content = strings.Join(items, "、")
	}

	// Create merged memory
	merged := &Memory{
		Type:       group[0].Type,
		Content:    content,
		SemanticID: fmt.Sprintf("merged_%s_%d", group[0].Type, time.Now().Unix()),
		Confidence: 0.95, // High confidence for merged memories
		Attributes: map[string]interface{}{
			"merged_from": len(group),
			"merge_type":  "concatenation",
		},
	}

	return merged, nil
}

// mergeReplace uses the most recent memory.
func (m *Merger) mergeReplace(group []*Memory) (*Memory, error) {
	// Sort by update time, most recent first
	sort.Slice(group, func(i, j int) bool {
		return group[i].UpdatedAt.After(group[j].UpdatedAt)
	})

	return group[0], nil
}

// mergeLLM uses AI to intelligently merge memories.
func (m *Merger) mergeLLM(ctx context.Context, group []*Memory) (*Memory, error) {
	// Build prompt
	prompt := m.buildMergePrompt(group)

	// Call LLM
	resp, err := m.aiClient.Chat(ctx, &ai.Request{
		Messages: []ai.Message{
			{Role: ai.System, Content: m.getMergeSystemPrompt()},
			{Role: ai.User, Content: prompt},
		},
		Temperature: 0.3,
		MaxTokens:   300,
	})
	if err != nil {
		// Fall back to concatenation
		return m.mergeConcat(group)
	}

	// Parse response and create merged memory
	content := m.parseMergeResponse(resp.Content)
	if content == "" {
		// Fall back to concatenation
		return m.mergeConcat(group)
	}

	merged := &Memory{
		Type:       group[0].Type,
		Content:    content,
		SemanticID: fmt.Sprintf("merged_%s_%d", group[0].Type, time.Now().Unix()),
		Confidence: 0.95,
		Attributes: map[string]interface{}{
			"merged_from": len(group),
			"merge_type":  "llm",
		},
	}

	return merged, nil
}

// getMergeSystemPrompt returns the system prompt for merging.
func (m *Merger) getMergeSystemPrompt() string {
	return `You are a memory consolidation system. Merge related memories into a comprehensive, organized memory.

Guidelines:
1. Preserve the original language (don't translate)
2. Keep all unique information
3. Remove redundancy
4. Create a clear, structured summary
5. For preferences, list all items clearly

Return ONLY the merged content, no explanation.`
}

// buildMergePrompt creates the prompt for LLM merging.
func (m *Merger) buildMergePrompt(group []*Memory) string {
	var parts []string
	for i, mem := range group {
		parts = append(parts, fmt.Sprintf("%d. %s", i+1, mem.Content))
	}

	return fmt.Sprintf("Merge these related memories into one comprehensive memory:\n\n%s\n\nMerged content:",
		strings.Join(parts, "\n"))
}

// parseMergeResponse extracts the merged content from LLM response.
func (m *Merger) parseMergeResponse(response string) string {
	// Simple extraction - just use the response as is
	return strings.TrimSpace(response)
}

// extractCoreItem extracts the main item from a preference.
func (m *Merger) extractCoreItem(content string) string {
	// Remove common prefixes
	content = strings.TrimPrefix(content, "喜歡喝")
	content = strings.TrimPrefix(content, "喜歡")
	content = strings.TrimPrefix(content, "likes ")
	content = strings.TrimPrefix(content, "like ")

	// Remove common suffixes
	content = strings.TrimSuffix(content, " coffee")
	content = strings.TrimSuffix(content, " tea")

	return strings.TrimSpace(content)
}

// saveMergedMemory saves the merged memory and deactivates the originals.
func (m *Merger) saveMergedMemory(ctx context.Context, merged *Memory, originals []*Memory) error {
	// Save the merged memory
	if err := m.store.Save(ctx, merged); err != nil {
		return NewMemoryError("saveMergedMemory", KindStorage, err, "save merged memory")
	}

	// Deactivate original memories
	ids := make([]pgtype.UUID, len(originals))
	for i, mem := range originals {
		ids[i] = mem.ID
	}

	if err := m.store.DeactivateMany(ctx, ids); err != nil {
		return fmt.Errorf("deactivate originals: %w", err)
	}

	return nil
}
