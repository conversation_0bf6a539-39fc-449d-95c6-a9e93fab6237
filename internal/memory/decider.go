// Package memory implements intelligent decision making with optional LLM support.
package memory

import (
	"context"
	"fmt"
	"strings"

	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/koopa0/assistant-go/internal/extract"
)

// Decider makes memory decisions using either simple rules or LLM-based semantic analysis.
type Decider struct {
	similarityThreshold float32
	aiClient            ai.Client // Optional: if nil, use simple rules
	useSemanticMode     bool
}

// NewDecider creates a new decider with optional AI support.
func NewDecider(similarityThreshold float32, aiClient ai.Client) *Decider {
	if similarityThreshold <= 0 {
		similarityThreshold = HighSimilarityThreshold
	}
	return &Decider{
		similarityThreshold: similarityThreshold,
		aiClient:            aiClient,
		useSemanticMode:     aiClient != nil,
	}
}

// Decide determines what action to take for a given fact.
func (d *Decider) Decide(ctx context.Context, fact extract.Fact, similar []*Memory) (Decision, error) {
	// No similar memories found - always add
	if len(similar) == 0 {
		return Decision{
			Actions: []Action{
				{Type: ActionAdd},
			},
			Reason:     "No similar memories found",
			Confidence: fact.Confidence,
		}, nil
	}

	// Use semantic analysis if AI client is available
	// We prioritize semantic analysis to catch all cases including identical content
	if d.useSemanticMode && d.aiClient != nil {
		// Pass more memories to LLM for better context
		numToAnalyze := min(DefaultSearchLimit, len(similar))
		decision, err := d.analyzeWithLLM(ctx, fact, similar[:numToAnalyze])
		if err != nil {
			// Log error and fall back to simple rules
			// Don't skip automatically - let simpleDecide handle it
			return d.simpleDecide(fact, similar)
		}
		return decision, nil
	}

	// Fall back to simple rules when no AI service
	return d.simpleDecide(fact, similar)
}

// simpleDecide uses basic similarity rules without semantic understanding.
func (d *Decider) simpleDecide(fact extract.Fact, similar []*Memory) (Decision, error) {
	mostSimilar := similar[0]
	similarity := mostSimilar.Similarity

	// Only update if very high similarity AND same type AND similar content length
	// This prevents tea from being considered an update to coffee
	if similarity >= d.similarityThreshold && string(fact.Type) == string(mostSimilar.Type) {
		// Check if content is actually about the same thing
		factLen := len(fact.Content)
		memLen := len(mostSimilar.Content)
		lengthRatio := float32(min(factLen, memLen)) / float32(max(factLen, memLen))

		// If content lengths are very different, it's probably not the same thing
		if lengthRatio < LengthRatioThreshold {
			return Decision{
				Actions: []Action{
					{Type: ActionAdd},
				},
				Reason:     "Different content despite high similarity",
				Confidence: fact.Confidence,
			}, nil
		}

		// Very high similarity with same type and similar length
		if similarity >= ExactMatchThreshold {
			return Decision{
				Actions: []Action{
					{Type: ActionUpdate, TargetID: &mostSimilar.ID},
				},
				Reason:     "Very high similarity with same type",
				Confidence: similarity * fact.Confidence,
			}, nil
		}
	}

	// Default to add for most cases
	return Decision{
		Actions: []Action{
			{Type: ActionAdd},
		},
		Reason:     "New information",
		Confidence: fact.Confidence,
	}, nil
}

// analyzeWithLLM uses LLM to understand the semantic relationship.
func (d *Decider) analyzeWithLLM(ctx context.Context, fact extract.Fact, similar []*Memory) (Decision, error) {
	prompt := d.buildAnalysisPrompt(fact, similar)

	resp, err := d.aiClient.Chat(ctx, &ai.Request{
		Messages: []ai.Message{
			{Role: ai.System, Content: d.getSystemPrompt()},
			{Role: ai.User, Content: prompt},
		},
		Temperature: DecisionTemperature,
		MaxTokens:   DecisionMaxTokens,
		Model:       ai.Claude35Sonnet,
	})
	if err != nil {
		return Decision{}, NewMemoryError("decide", KindProcessing, err, "LLM call failed")
	}

	return d.parseDecision(resp.Content, fact, similar)
}

// getSystemPrompt returns the system prompt for semantic analysis.
func (d *Decider) getSystemPrompt() string {
	return `You are a memory decision system. Analyze the relationship between a new fact and existing memories.

Your task:
1. Understand the semantic meaning regardless of language (English, Chinese, Japanese, etc.)
2. Detect if the new fact contradicts, updates, or adds to existing memories
3. Consider context like "我現在不只喜歡紅茶" (I now like not just red tea) vs "我不喜歡紅茶" (I don't like red tea)
4. Handle complex negations and partial negations correctly
5. IMPORTANT: Different items in the same category should be treated as ADDITIONAL information, not duplicates
6. CRITICAL: Recognize negation patterns:
   - Chinese: "不喝X了", "不喜歡X", "不再X", "沒有X", "停止X"
   - English: "don't X anymore", "no longer X", "stopped X", "quit X"
   - These should UPDATE or DELETE the positive preference, not be added as new
7. SPECIAL CASE: When you find an IDENTICAL memory (same content), you must:
   - First check if there are OTHER related memories that contradict the new fact
   - If the identical memory is "不喜歡紅茶" and there's also "喜歡紅茶", DELETE the contradictory one
   - Only SKIP if there are no contradictions to resolve

Return a JSON decision with MULTIPLE ACTIONS if needed:
{
  "actions": [
    {
      "type": "DELETE",
      "targetIndex": 0,  // Index of memory to delete (0-based)
      "reason": "Deleting old preference"
    },
    {
      "type": "ADD",
      "content": "new content",  // Optional: specify exact content for ADD
      "reason": "Adding negation"
    }
  ],
  "reason": "Overall reason for composite action",
  "isContradiction": true/false,
  "isAdditionalInfo": true/false,
  "category": "string"  // Category of the memory (e.g., "beverages", "food", "activities")
}

Or for single action:
{
  "actions": [
    {
      "type": "ADD|UPDATE|DELETE|SKIP",
      "targetIndex": 0,  // For UPDATE/DELETE
      "content": "modified content",  // For ADD with specific content
      "reason": "Action-specific reason"
    }
  ],
  "reason": "Overall reason"
}

Guidelines:
- SKIP only if the information is truly identical in BOTH form AND meaning (verified semantically)
- UPDATE if new fact modifies or extends existing memory about the SAME item (partial change)
- DELETE if new fact completely negates or invalidates the existing memory
- ADD if it's new information or additional preference in the same category
- Different items in same category = ADD (e.g., coffee + tea = two separate preferences)

IMPORTANT SKIP criteria:
- Do NOT skip just because strings are similar or identical
- Must verify semantic equivalence: same meaning, same context, same intent
- Consider: "喜歡喝咖啡" vs "喜歡喝咖啡" (morning context vs general) → might be ADD if context differs
- Even identical text might have different meanings based on context

Examples with careful analysis:
- "I like red tea" + "I don't like red tea anymore" → DELETE (complete negation)
- "喜歡喝綠茶" + "不喝綠茶了" → DELETE (stopped drinking)
- "喜歡喝紅茶" + "不喜歡喝紅茶" → DELETE (negation of preference)
- "不喜歡喝紅茶" + "不喜歡喝紅茶" → SKIP only if truly redundant with same context
- "I like coffee" + "I prefer tea over coffee" → UPDATE (preference ranking change)
- "每天運動" + "不運動了" → DELETE (stopped behavior)
- "I drink coffee daily" + "I drink coffee occasionally" → UPDATE (frequency change)
- "I like coffee" + "I also like tea" → ADD (different beverages)
- "I like coffee" + "I still like coffee" → SKIP (truly duplicate intent)
- "喜歡咖啡" + "喜歡紅茶" → ADD (different beverages)
- "喜歡純黑咖啡" + "喜歡拿鐵" → ADD (different coffee types)

Category Examples:
- Beverages: coffee, tea, juice, water, etc.
- Food: pasta, pizza, sushi, etc.
- Activities: running, swimming, reading, etc.`
}

// buildAnalysisPrompt creates the analysis prompt.
func (d *Decider) buildAnalysisPrompt(fact extract.Fact, similar []*Memory) string {
	prompt := fmt.Sprintf("New fact:\nType: %s\nContent: %s\nConfidence: %.2f\n\nExisting memories:\n",
		fact.Type, fact.Content, fact.Confidence)

	for i, mem := range similar {
		prompt += fmt.Sprintf("\n%d. Type: %s\nContent: %s\nSimilarity: %.2f\n",
			i+1, mem.Type, mem.Content, mem.Similarity)
	}

	prompt += "\nAnalyze the relationship and decide what action to take."
	return prompt
}

// parseDecision parses the LLM response into a Decision.
func (d *Decider) parseDecision(response string, fact extract.Fact, similar []*Memory) (Decision, error) {
	// Try to parse new format with multiple actions first
	if strings.Contains(response, `"actions"`) {
		return d.parseCompositeDecision(response, fact, similar)
	}

	// Fall back to old single-action format for backward compatibility
	if strings.Contains(response, `"action": "SKIP"`) || strings.Contains(response, `"action": "skip"`) {
		return Decision{
			Actions: []Action{
				{Type: ActionSkip},
			},
			Reason:     d.extractReason(response),
			Confidence: fact.Confidence,
		}, nil
	}

	if strings.Contains(response, `"action": "UPDATE"`) || strings.Contains(response, `"action": "update"`) {
		targetIdx := d.extractTargetIndex(response)
		if targetIdx >= 0 && targetIdx < len(similar) {
			return Decision{
				Actions: []Action{
					{Type: ActionUpdate, TargetID: &similar[targetIdx].ID},
				},
				Reason:     d.extractReason(response),
				Confidence: fact.Confidence,
			}, nil
		}
	}

	if strings.Contains(response, `"action": "DELETE"`) || strings.Contains(response, `"action": "delete"`) {
		targetIdx := d.extractTargetIndex(response)
		if targetIdx >= 0 && targetIdx < len(similar) {
			return Decision{
				Actions: []Action{
					{Type: ActionDelete, TargetID: &similar[targetIdx].ID},
				},
				Reason:     d.extractReason(response),
				Confidence: fact.Confidence,
			}, nil
		}
	}

	// Default to ADD
	return Decision{
		Actions: []Action{
			{Type: ActionAdd},
		},
		Reason:     d.extractReason(response),
		Confidence: fact.Confidence,
	}, nil
}

// parseCompositeDecision parses the new format with multiple actions
func (d *Decider) parseCompositeDecision(response string, fact extract.Fact, similar []*Memory) (Decision, error) {
	actions := []Action{}

	// Extract actions array manually (simple parsing)
	// Look for patterns like: "type": "DELETE", "targetIndex": 0
	// and "type": "ADD", "content": "..."

	// Find all action blocks
	actionsStart := strings.Index(response, `"actions"`)
	if actionsStart == -1 {
		// Fall back to single ADD action
		return Decision{
			Actions:    []Action{{Type: ActionAdd}},
			Reason:     d.extractReason(response),
			Confidence: fact.Confidence,
		}, nil
	}

	// Simple pattern matching for actions
	remaining := response[actionsStart:]

	// Look for DELETE actions
	deleteIdx := 0
	for {
		deletePos := strings.Index(remaining[deleteIdx:], `"type": "DELETE"`)
		if deletePos == -1 {
			deletePos = strings.Index(remaining[deleteIdx:], `"type": "delete"`)
		}
		if deletePos == -1 {
			break
		}
		deleteIdx += deletePos

		// Find targetIndex after this DELETE
		targetIdxStr := remaining[deleteIdx : deleteIdx+DecisionLookAhead] // Look ahead for targetIndex
		targetIdx := d.extractTargetIndexFromString(targetIdxStr)

		if targetIdx >= 0 && targetIdx < len(similar) {
			actions = append(actions, Action{
				Type:     ActionDelete,
				TargetID: &similar[targetIdx].ID,
			})
		}
		deleteIdx += DecisionMoveOffset // Move past current match
	}

	// Look for ADD actions
	if strings.Contains(response, `"type": "ADD"`) || strings.Contains(response, `"type": "add"`) {
		action := Action{Type: ActionAdd}

		// Check if there's specific content for ADD
		contentStart := strings.Index(response, `"content":`)
		if contentStart != -1 {
			contentEnd := strings.Index(response[contentStart+ContentStartOffset:], `"`)
			if contentEnd != -1 {
				content := d.extractContentFromPosition(response, contentStart)
				if content != "" {
					action.Content = content
				}
			}
		}

		actions = append(actions, action)
	}

	// Look for UPDATE actions
	updateIdx := d.extractTargetIndexForAction(response, "UPDATE")
	if updateIdx == -1 {
		updateIdx = d.extractTargetIndexForAction(response, "update")
	}
	if updateIdx >= 0 && updateIdx < len(similar) {
		actions = append(actions, Action{
			Type:     ActionUpdate,
			TargetID: &similar[updateIdx].ID,
		})
	}

	// Look for SKIP action
	if strings.Contains(response, `"type": "SKIP"`) || strings.Contains(response, `"type": "skip"`) {
		actions = []Action{{Type: ActionSkip}} // SKIP overrides other actions
	}

	// If no actions found, default to ADD
	if len(actions) == 0 {
		actions = append(actions, Action{Type: ActionAdd})
	}

	return Decision{
		Actions:    actions,
		Reason:     d.extractReason(response),
		Confidence: fact.Confidence,
	}, nil
}

// Helper methods
func (d *Decider) extractReason(response string) string {
	// Simple extraction - in production would use proper JSON parsing
	start := strings.Index(response, `"reason":`)
	if start == -1 {
		return "Based on semantic analysis"
	}
	// Find the content between quotes after "reason":
	start = strings.Index(response[start:], `"`) + start + 1
	end := strings.Index(response[start:], `"`)
	if end > 0 {
		return response[start : start+end]
	}
	return "Based on semantic analysis"
}

func (d *Decider) extractTargetIndex(response string) int {
	// Simple extraction - look for targetIndex value
	start := strings.Index(response, `"targetIndex":`)
	if start == -1 {
		return 0
	}
	// Extract the number
	var idx int
	if _, err := fmt.Sscanf(response[start:], `"targetIndex": %d`, &idx); err != nil {
		return 0
	}
	return idx
}

func (d *Decider) extractTargetIndexFromString(str string) int {
	// Simple extraction - look for targetIndex value in a string
	start := strings.Index(str, `"targetIndex":`)
	if start == -1 {
		return -1
	}
	// Extract the number
	var idx int
	if _, err := fmt.Sscanf(str[start:], `"targetIndex": %d`, &idx); err != nil {
		return -1
	}
	return idx
}

func (d *Decider) extractTargetIndexForAction(response string, actionType string) int {
	// Find the action type first
	actionPos := strings.Index(response, fmt.Sprintf(`"type": "%s"`, actionType))
	if actionPos == -1 {
		return -1
	}

	// Look for targetIndex after this action
	searchStr := response[actionPos:min(actionPos+DecisionLookAhead, len(response))]
	return d.extractTargetIndexFromString(searchStr)
}

func (d *Decider) extractContentFromPosition(response string, startPos int) string {
	// Extract content value after "content": position
	if startPos == -1 {
		return ""
	}

	// Find the opening quote
	openQuote := strings.Index(response[startPos:], `"`)
	if openQuote == -1 {
		return ""
	}

	start := startPos + openQuote + 1

	// Find the closing quote (handling escaped quotes)
	for i := start; i < len(response); i++ {
		if response[i] == '"' && (i == 0 || response[i-1] != '\\') {
			return response[start:i]
		}
	}

	return ""
}
