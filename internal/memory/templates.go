package memory

import (
	"bytes"
	"fmt"
	"text/template"
)

// ContextTemplate defines the template for generating memory context.
type ContextTemplate struct {
	Name     string
	Template *template.Template
}

// DefaultContextTemplate is the default template for memory context.
const DefaultContextTemplate = `<retrieved_memory>
{{- if .DirectMatches }}
  <direct_matches>
  {{- range .DirectMatches }}
    <memory id="{{.ID}}" type="{{.Type}}" confidence="{{.Confidence}}" created="{{.CreatedAt}}">
      {{- if .Keywords }}
      <keywords>{{range .Keywords}}{{.}}, {{end}}</keywords>
      {{- end }}
      <content>{{.Content}}</content>
    </memory>
  {{- end }}
  </direct_matches>
{{- end }}

{{- if .RelatedMemories }}
  <related_memories>
  {{- range .RelatedMemories }}
    <memory id="{{.ID}}" type="{{.Type}}" relation="{{.Relation}}" from="{{.RelationFrom}}">
      <content>{{.Content}}</content>
    </memory>
  {{- end }}
  </related_memories>
{{- end }}

{{- if .Conflicts }}
  <conflicts>
  {{- range .Conflicts }}
    <conflict topic="{{.Topic}}">
    {{- range .Statements }}
      <statement status="{{.Status}}" created="{{.CreatedAt}}">{{.Content}}</statement>
    {{- end }}
    </conflict>
  {{- end }}
  </conflicts>
{{- end }}

{{- if .Timeline }}
  <timeline>
  {{- range .Timeline }}
    <event date="{{.Date}}" type="{{.Type}}">{{.Content}}</event>
  {{- end }}
  </timeline>
{{- end }}

{{- if .RelevantEntities }}
  <entities>
  {{- range .RelevantEntities }}
    <entity name="{{.Name}}" type="{{.Type}}" occurrences="{{.Occurrences}}">
    {{- range .Contexts }}
      <context>{{.}}</context>
    {{- end }}
    </entity>
  {{- end }}
  </entities>
{{- end }}

{{- if .Summary }}
  <summary>{{.Summary}}</summary>
{{- end }}
</retrieved_memory>`

// MinimalContextTemplate is a minimal template for simple queries.
const MinimalContextTemplate = `<memory_context>
{{- range .DirectMatches }}
- {{.Content}} ({{.Type}}, confidence: {{.Confidence}})
{{- end }}
{{- range .RelatedMemories }}
- Related: {{.Content}} (via {{.Relation}})
{{- end }}
</memory_context>`

// DetailedContextTemplate includes all available information.
const DetailedContextTemplate = `<comprehensive_memory_context>
  <metadata>
    <query_time>{{.QueryTime}}</query_time>
    <total_memories>{{.TotalMemories}}</total_memories>
    <search_depth>{{.SearchDepth}}</search_depth>
  </metadata>
  
  <primary_results count="{{len .DirectMatches}}">
  {{- range .DirectMatches }}
    <memory>
      <id>{{.ID}}</id>
      <semantic_id>{{.SemanticID}}</semantic_id>
      <type>{{.Type}}</type>
      <content>{{.Content}}</content>
      <confidence>{{.Confidence}}</confidence>
      <keywords>{{range .Keywords}}{{.}} {{end}}</keywords>
      <created_at>{{.CreatedAt}}</created_at>
      <updated_at>{{.UpdatedAt}}</updated_at>
      <access_count>{{.AccessCount}}</access_count>
      {{- if .Metadata }}
      <metadata>
        {{- range $key, $value := .Metadata }}
        <{{$key}}>{{$value}}</{{$key}}>
        {{- end }}
      </metadata>
      {{- end }}
    </memory>
  {{- end }}
  </primary_results>
  
  <expanded_results count="{{len .RelatedMemories}}">
  {{- range .RelatedMemories }}
    <memory>
      <id>{{.ID}}</id>
      <content>{{.Content}}</content>
      <relation>
        <type>{{.Relation}}</type>
        <from_memory>{{.RelationFrom}}</from_memory>
        <confidence>{{.RelationConfidence}}</confidence>
        <path_length>{{.PathLength}}</path_length>
      </relation>
    </memory>
  {{- end }}
  </expanded_results>
  
  {{- if .Conflicts }}
  <detected_conflicts count="{{len .Conflicts}}">
  {{- range .Conflicts }}
    <conflict>
      <topic>{{.Topic}}</topic>
      <conflict_type>{{.Type}}</conflict_type>
      <statements>
      {{- range .Statements }}
        <statement>
          <content>{{.Content}}</content>
          <created_at>{{.CreatedAt}}</created_at>
          <status>{{.Status}}</status>
          <confidence>{{.Confidence}}</confidence>
        </statement>
      {{- end }}
      </statements>
      <resolution>{{.Resolution}}</resolution>
    </conflict>
  {{- end }}
  </detected_conflicts>
  {{- end }}
  
  {{- if .Timeline }}
  <chronological_view>
  {{- range .Timeline }}
    <event>
      <timestamp>{{.Date}}</timestamp>
      <type>{{.Type}}</type>
      <content>{{.Content}}</content>
      <significance>{{.Significance}}</significance>
    </event>
  {{- end }}
  </chronological_view>
  {{- end }}
  
  {{- if .RelevantEntities }}
  <entity_analysis>
  {{- range .RelevantEntities }}
    <entity>
      <name>{{.Name}}</name>
      <type>{{.Type}}</type>
      <frequency>{{.Occurrences}}</frequency>
      <first_seen>{{.FirstSeen}}</first_seen>
      <last_seen>{{.LastSeen}}</last_seen>
      <contexts>
      {{- range .Contexts }}
        <occurrence>{{.}}</occurrence>
      {{- end }}
      </contexts>
      <relationships>
      {{- range .Relationships }}
        <related_to>{{.}}</related_to>
      {{- end }}
      </relationships>
    </entity>
  {{- end }}
  </entity_analysis>
  {{- end }}
  
  <analysis>
    <summary>{{.Summary}}</summary>
    <key_themes>{{range .KeyThemes}}{{.}} {{end}}</key_themes>
    <sentiment>{{.Sentiment}}</sentiment>
    <completeness_score>{{.CompletenessScore}}</completeness_score>
  </analysis>
</comprehensive_memory_context>`

// TemplateManager manages context templates.
type TemplateManager struct {
	templates map[string]*ContextTemplate
	current   string
}

// NewTemplateManager creates a new template manager with default templates.
func NewTemplateManager() *TemplateManager {
	tm := &TemplateManager{
		templates: make(map[string]*ContextTemplate),
		current:   "default",
	}

	// Register default templates
	if err := tm.RegisterTemplate("default", DefaultContextTemplate); err != nil {
		panic(fmt.Sprintf("failed to register default template: %v", err))
	}
	if err := tm.RegisterTemplate("minimal", MinimalContextTemplate); err != nil {
		panic(fmt.Sprintf("failed to register minimal template: %v", err))
	}
	if err := tm.RegisterTemplate("detailed", DetailedContextTemplate); err != nil {
		panic(fmt.Sprintf("failed to register detailed template: %v", err))
	}

	return tm
}

// RegisterTemplate registers a new template.
func (tm *TemplateManager) RegisterTemplate(name, tmplStr string) error {
	tmpl, err := template.New(name).Parse(tmplStr)
	if err != nil {
		return err
	}

	tm.templates[name] = &ContextTemplate{
		Name:     name,
		Template: tmpl,
	}

	return nil
}

// SetCurrentTemplate sets the active template.
func (tm *TemplateManager) SetCurrentTemplate(name string) error {
	if _, exists := tm.templates[name]; !exists {
		return fmt.Errorf("template %s not found", name)
	}
	tm.current = name
	return nil
}

// RenderContext renders the context using the current template.
func (tm *TemplateManager) RenderContext(data interface{}) (string, error) {
	tmpl, exists := tm.templates[tm.current]
	if !exists {
		return "", fmt.Errorf("current template %s not found", tm.current)
	}

	var buf bytes.Buffer
	err := tmpl.Template.Execute(&buf, data)
	if err != nil {
		return "", err
	}

	return buf.String(), nil
}

// RenderWithTemplate renders the context using a specific template.
func (tm *TemplateManager) RenderWithTemplate(name string, data interface{}) (string, error) {
	tmpl, exists := tm.templates[name]
	if !exists {
		return "", fmt.Errorf("template %s not found", name)
	}

	var buf bytes.Buffer
	err := tmpl.Template.Execute(&buf, data)
	if err != nil {
		return "", err
	}

	return buf.String(), nil
}
