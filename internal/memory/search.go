// Package memory implements smart search with graph-based expansion.
package memory

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"
)

// SmartSearcher implements intelligent search with graph expansion.
type SmartSearcher struct {
	store *Store
	graph *Graph
}

// NewSmartSearcher creates a new smart searcher.
func NewSmartSearcher(store *Store, graph *Graph) *SmartSearcher {
	return &SmartSearcher{
		store: store,
		graph: graph,
	}
}

// SearchResult represents an enhanced search result with relationship info.
type SearchResult struct {
	Memory       *Memory
	Score        float32
	Source       SearchSource
	RelationPath []RelationInfo
	ExpandedFrom *pgtype.UUID // ID of seed memory this was expanded from
}

// SearchSource indicates how a memory was found.
type SearchSource string

// Search source constants
const (
	SourceDirect   SearchSource = "direct"   // SourceDirect indicates direct vector/text match
	SourceExpanded SearchSource = "expanded" // SourceExpanded indicates found through graph expansion
	SourceInferred SearchSource = "inferred" // SourceInferred indicates found through semantic inference
)

// RelationInfo describes a relationship in the expansion path.
type RelationInfo struct {
	Type     RelationType
	Strength float32
	FromID   pgtype.UUID
	ToID     pgtype.UUID
}

// SmartSearchOptions configures the smart search behavior.
type SmartSearchOptions struct {
	MaxDepth         int     // Maximum graph traversal depth (default: 2)
	MinRelationScore float32 // Minimum relation strength to follow (default: 0.5)
	ExpansionLimit   int     // Max memories to expand per seed (default: 5)
	IncludeConflicts bool    // Include conflicting memories in results
	PreferRecent     bool    // Boost recent memories in ranking
}

// DefaultSmartSearchOptions returns sensible defaults.
func DefaultSmartSearchOptions() SmartSearchOptions {
	return SmartSearchOptions{
		MaxDepth:         2,
		MinRelationScore: 0.5,
		ExpansionLimit:   5,
		IncludeConflicts: true,
		PreferRecent:     true,
	}
}

// Search performs an intelligent, multi-stage search to retrieve the most relevant context.
// It goes beyond simple vector search by leveraging the knowledge graph to expand the
// initial results, providing a richer, more context-aware set of memories.
// The process involves: 1. Finding initial seed memories. 2. Expanding them via graph
// relationships. 3. Merging and re-ranking the combined results.
func (s *SmartSearcher) Search(ctx context.Context, query string, limit int, options SmartSearchOptions) ([]SearchResult, error) {
	s.store.logger.Info("[SEARCH] SmartSearcher.Search called",
		"query", query,
		"limit", limit,
		"options", fmt.Sprintf("%+v", options))

	// Step 1: Find seed memories through vector search
	seeds, err := s.findSeedMemories(ctx, query, limit)
	if err != nil {
		return nil, fmt.Errorf("find seed memories: %w", err)
	}
	s.store.logger.Info("[SEARCH] Found seed memories", "count", len(seeds))

	// Step 2: Expand seeds through graph relationships
	expanded, err := s.expandSeeds(ctx, seeds, options)
	if err != nil {
		return nil, fmt.Errorf("expand seeds: %w", err)
	}
	s.store.logger.Info("[SEARCH] Expanded memories", "count", len(expanded))

	// Step 3: Merge and deduplicate results
	allResults := s.mergeResults(seeds, expanded)
	s.store.logger.Info("[SEARCH] Merged results", "count", len(allResults))

	// Step 4: Re-rank based on multiple factors
	rankedResults := s.rankResults(allResults, query, options)

	// Step 5: Apply final limit
	if len(rankedResults) > limit {
		rankedResults = rankedResults[:limit]
	}

	s.store.logger.Info("[SEARCH] Final results", "count", len(rankedResults))

	return rankedResults, nil
}

// findSeedMemories performs initial vector search to find seed memories.
func (s *SmartSearcher) findSeedMemories(ctx context.Context, query string, limit int) ([]SearchResult, error) {
	s.store.logger.Debug("[SEARCH] Finding seed memories", "query", query)

	// Use existing search with higher limit to get good seeds
	searchQuery := Query{
		Text:     query,
		Limit:    limit * 3, // Get extra for better seed selection
		MinScore: 0.5,       // Lower threshold for seeds
	}

	memories, err := s.store.Search(ctx, searchQuery)
	if err != nil {
		s.store.logger.Error("[SEARCH] Failed to search memories", "error", err)
		return nil, err
	}

	s.store.logger.Debug("[SEARCH] Store.Search returned memories", "count", len(memories))
	for i, mem := range memories {
		s.store.logger.Debug("[SEARCH] Memory found",
			"index", i,
			"id", uuid.UUID(mem.ID.Bytes).String(),
			"content", mem.Content,
			"confidence", mem.Confidence)
	}

	// Convert to SearchResults
	results := make([]SearchResult, 0, len(memories))
	for _, mem := range memories {
		results = append(results, SearchResult{
			Memory: mem,
			Score:  mem.Confidence, // Initial score from vector similarity
			Source: SourceDirect,
		})
	}

	return results, nil
}

// expandSeeds expands seed memories through graph relationships.
func (s *SmartSearcher) expandSeeds(ctx context.Context, seeds []SearchResult, options SmartSearchOptions) ([]SearchResult, error) {
	expandedMap := make(map[string]SearchResult) // Use map to avoid duplicates

	for _, seed := range seeds {
		// Only expand high-scoring seeds
		if seed.Score < options.MinRelationScore {
			continue
		}

		// Find related memories through graph with relationship info
		relatedWithInfo, err := s.graph.FindRelatedWithInfo(ctx, seed.Memory.ID, nil, options.MaxDepth)
		if err != nil {
			// Log but continue with other seeds
			continue
		}

		// Convert related memories to search results
		count := 0
		for _, relMem := range relatedWithInfo {
			if count >= options.ExpansionLimit {
				break
			}

			// Skip if already in results
			key := uuid.UUID(relMem.Memory.ID.Bytes).String()
			if _, exists := expandedMap[key]; exists {
				continue
			}

			// Create expanded result with actual relation info
			expandedResult := SearchResult{
				Memory:       relMem.Memory,
				Score:        seed.Score * relMem.RelationStrength * 0.8, // Score based on actual relation strength
				Source:       SourceExpanded,
				ExpandedFrom: &seed.Memory.ID,
				RelationPath: []RelationInfo{
					{
						Type:     relMem.RelationType,     // Actual relation type
						Strength: relMem.RelationStrength, // Actual strength
						FromID:   seed.Memory.ID,
						ToID:     relMem.Memory.ID,
					},
				},
			}

			expandedMap[key] = expandedResult
			count++
		}
	}

	// Convert map to slice
	expanded := make([]SearchResult, 0, len(expandedMap))
	for _, result := range expandedMap {
		expanded = append(expanded, result)
	}

	return expanded, nil
}

// mergeResults combines direct and expanded results, removing duplicates.
func (s *SmartSearcher) mergeResults(seeds, expanded []SearchResult) []SearchResult {
	resultMap := make(map[string]SearchResult)

	// Add seeds first (they have priority)
	for _, seed := range seeds {
		key := uuid.UUID(seed.Memory.ID.Bytes).String()
		resultMap[key] = seed
	}

	// Add expanded results if not already present
	for _, exp := range expanded {
		key := uuid.UUID(exp.Memory.ID.Bytes).String()
		if _, exists := resultMap[key]; !exists {
			resultMap[key] = exp
		}
	}

	// Convert to slice
	results := make([]SearchResult, 0, len(resultMap))
	for _, result := range resultMap {
		results = append(results, result)
	}

	return results
}

// rankResults applies sophisticated ranking based on multiple factors.
func (s *SmartSearcher) rankResults(results []SearchResult, query string, options SmartSearchOptions) []SearchResult {
	now := time.Now()

	// Calculate enhanced scores
	for i := range results {
		result := &results[i]
		mem := result.Memory

		// Base score from search
		score := result.Score

		// Boost for source type
		switch result.Source {
		case SourceDirect:
			score *= 1.2 // Direct matches are more relevant
		case SourceExpanded:
			score *= 0.9 // Slight penalty for expanded results
		}

		// Recency boost (if enabled)
		if options.PreferRecent {
			daysSince := now.Sub(mem.CreatedAt).Hours() / 24
			if daysSince < 1 {
				score *= 1.3
			} else if daysSince < 7 {
				score *= 1.2
			} else if daysSince < 30 {
				score *= 1.1
			}
		}

		// Access frequency boost
		if mem.AccessCount > 10 {
			score *= 1.1
		} else if mem.AccessCount > 5 {
			score *= 1.05
		}

		// Memory type relevance
		switch mem.Type {
		case MemoryTypeFact:
			score *= 1.15 // Facts are generally more important
		case MemoryTypeSchedule:
			score *= 1.1 // Schedules are time-sensitive
		}

		// Confidence adjustment
		score *= mem.Confidence

		// Ensure score doesn't exceed 1.0
		if score > 1.0 {
			score = 1.0
		}

		result.Score = score
	}

	// Sort by final score
	sort.Slice(results, func(i, j int) bool {
		// Primary sort by score
		if results[i].Score != results[j].Score {
			return results[i].Score > results[j].Score
		}
		// Secondary sort by recency
		return results[i].Memory.CreatedAt.After(results[j].Memory.CreatedAt)
	})

	return results
}

// FindConflicts identifies conflicting memories in search results.
func (s *SmartSearcher) FindConflicts(results []SearchResult) map[string][]SearchResult {
	conflicts := make(map[string][]SearchResult)

	// Group by keywords/topics
	topicGroups := make(map[string][]SearchResult)
	for _, result := range results {
		for _, keyword := range result.Memory.Keywords {
			topicGroups[keyword] = append(topicGroups[keyword], result)
		}
	}

	// Check for conflicts within each topic group
	for topic, group := range topicGroups {
		if len(group) < 2 {
			continue
		}

		// Simple conflict detection based on negation patterns
		for i := 0; i < len(group); i++ {
			for j := i + 1; j < len(group); j++ {
				if s.areConflicting(group[i].Memory, group[j].Memory) {
					conflicts[topic] = append(conflicts[topic], group[i], group[j])
				}
			}
		}
	}

	return conflicts
}

// areConflicting checks if two memories conflict.
func (s *SmartSearcher) areConflicting(mem1, mem2 *Memory) bool {
	// Simple implementation - check for negation patterns
	content1 := mem1.Content
	content2 := mem2.Content

	// Check if one negates the other
	negationPairs := [][]string{
		{"喜歡", "不喜歡"},
		{"like", "don't like"},
		{"要", "不要"},
		{"can", "cannot"},
	}

	for _, pair := range negationPairs {
		if (smartContains(content1, pair[0]) && smartContains(content2, pair[1])) ||
			(smartContains(content1, pair[1]) && smartContains(content2, pair[0])) {
			return true
		}
	}

	return false
}

// smartContains is a simple string contains helper.
func smartContains(s, substr string) bool {
	return strings.Contains(s, substr)
}
