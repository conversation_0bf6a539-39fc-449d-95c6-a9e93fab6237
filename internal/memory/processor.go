// Package memory implements a simplified memory processing system.
package memory

import (
	"context"
	"errors"
	"fmt"
	"runtime"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/koopa0/assistant-go/internal/extract"
	"github.com/koopa0/assistant-go/internal/platform/logger"
)

// Processor handles the complete memory processing flow in a simple, linear manner.
type Processor struct {
	extractor Extractor
	decider   DecisionMaker
	store     *Store
	logger    logger.Logger
}

// NewProcessor creates a new memory processor.
func NewProcessor(extractor Extractor, decider DecisionMaker, store *Store, log logger.Logger) *Processor {
	return &Processor{
		extractor: extractor,
		decider:   decider,
		store:     store,
		logger:    log.WithComponent("memory.processor"),
	}
}

// Process handles messages through the complete memory processing flow.
// This is a simple, synchronous method that can be called asynchronously if needed.
func (p *Processor) Process(ctx context.Context, messages []ai.Message) error {
	if len(messages) == 0 {
		return nil
	}

	// Step 1: Extract facts from messages
	facts, err := p.extractor.Extract(ctx, messages)
	if err != nil {
		return fmt.Errorf("extract facts: %w", err)
	}

	p.logger.Debug("Extracted facts", "count", len(facts))

	if len(facts) == 0 {
		p.logger.Debug("No facts extracted - nothing to process")
		return nil
	}

	// Skip preprocessing for better performance and simplicity
	// Let the normal decision-making process handle conflicts

	// Step 2: Process each fact
	successCount := 0
	errorCount := 0

	p.logger.Info("Processing facts", "count", len(facts))

	// Always use concurrent processing for better performance
	if len(facts) >= MinFactsForConcurrent {
		p.logger.Debug("Using concurrent processing")
		successCount, errorCount = p.processFactsConcurrently(ctx, facts)
	} else {
		// Process sequentially for small number of facts
		p.logger.Debug("Using sequential processing")
		for i, fact := range facts {
			p.logger.Debug("Processing fact", "index", i+1, "content", fact.Content)
			if err := p.processFact(ctx, fact); err != nil {
				p.logger.Error("Failed to process fact",
					"index", i,
					"factType", fact.Type,
					"content", fact.Content,
					"error", err,
				)
				errorCount++
			} else {
				p.logger.Debug("Fact processed successfully")
				successCount++
			}
		}
	}

	p.logger.Info("Memory processing completed",
		"totalFacts", len(facts),
		"successful", successCount,
		"failed", errorCount,
	)

	return nil
}

// processFact handles a single fact through decision and storage.
func (p *Processor) processFact(ctx context.Context, fact extract.Fact) error {
	p.logger.Debug("Processing fact",
		"content", fact.Content,
		"type", fact.Type,
		"score", fact.Score)

	// Early filtering: Skip low-value or meaningless content
	if extract.ShouldSkipFact(fact) {
		p.logger.Debug("Skipping low-value fact", "content", fact.Content)
		return nil
	}

	// Step 1: Find similar memories
	similar, err := p.findSimilarMemories(ctx, fact)
	if err != nil {
		// Don't fail the whole process, just log and continue with empty similar list
		p.logger.Warn("Failed to search similar memories", "error", err)
		similar = []*Memory{}
	}
	p.logger.Debug("Found similar memories", "count", len(similar))

	// Step 2: Make decision
	p.logger.Debug("Making decision", "similarCount", len(similar))
	decision, err := p.decider.Decide(ctx, fact, similar)
	if err != nil {
		p.logger.Error("Decision failed", "error", err)
		return fmt.Errorf("make decision: %w", err)
	}
	actionSummary := "SKIP"
	if len(decision.Actions) > 0 {
		types := []string{}
		for _, action := range decision.Actions {
			types = append(types, string(action.Type))
		}
		actionSummary = strings.Join(types, "+")
	}
	p.logger.Debug("Decision made", "actions", actionSummary, "reason", decision.Reason)

	// Check score threshold for memory decisions
	p.checkLowScoreAdd(fact, decision)

	// Step 3: Execute decision (now supports multiple actions)
	if len(decision.Actions) == 0 {
		// Backward compatibility: treat as SKIP if no actions
		p.logger.Debug("No actions in decision, skipping",
			"reason", decision.Reason,
			"content", fact.Content,
		)
		return nil
	}

	// Execute each action in sequence
	return p.executeActions(ctx, fact, decision)
}

// processFactsConcurrently processes multiple facts in parallel for better performance.
func (p *Processor) processFactsConcurrently(ctx context.Context, facts []extract.Fact) (successCount, errorCount int) {
	p.logger.Debug("Starting concurrent processing", "fact_count", len(facts))

	// Optimize worker count based on CPU cores and batch size
	maxWorkers := runtime.NumCPU() * WorkerMultiplier // Use 2x CPU cores for I/O-bound operations

	// Adjust based on batch size
	if len(facts) < maxWorkers {
		maxWorkers = len(facts)
	}

	// Cap at reasonable maximum to avoid resource exhaustion
	if maxWorkers > MaxWorkersLimit {
		maxWorkers = MaxWorkersLimit
	}

	p.logger.Debug("Concurrent processing setup", "workers", maxWorkers)

	type result struct {
		index int
		err   error
	}

	// Create channels
	factChan := make(chan struct {
		index int
		fact  extract.Fact
	}, len(facts))
	resultChan := make(chan result, len(facts))

	// Start workers
	var wg sync.WaitGroup
	for i := 0; i < maxWorkers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			p.logger.Debug("Worker started", "worker_id", workerID)
			for item := range factChan {
				p.logger.Debug("Worker processing fact", "worker_id", workerID, "index", item.index, "content", item.fact.Content)
				select {
				case <-ctx.Done():
					resultChan <- result{index: item.index, err: ctx.Err()}
					return
				default:
					err := p.processFact(ctx, item.fact)
					if err != nil {
						p.logger.Error("Worker failed to process fact", "worker_id", workerID, "error", err)
					} else {
						p.logger.Debug("Worker successfully processed fact", "worker_id", workerID)
					}
					resultChan <- result{index: item.index, err: err}
				}
			}
			p.logger.Debug("Worker finished", "worker_id", workerID)
		}(i)
	}

	// Send facts to workers
	p.logger.Debug("Sending facts to workers")
	for i, fact := range facts {
		factChan <- struct {
			index int
			fact  extract.Fact
		}{index: i, fact: fact}
	}
	close(factChan)

	// Wait for workers to complete in a separate goroutine
	go func() {
		wg.Wait()
		close(resultChan)
		p.logger.Debug("All workers finished, result channel closed")
	}()

	// Collect results
	p.logger.Debug("Collecting results")
	resultsReceived := 0
	for res := range resultChan {
		resultsReceived++
		p.logger.Debug("Received result", "received", resultsReceived, "total", len(facts))
		if res.err != nil {
			errorCount++
			if res.index < len(facts) {
				fact := facts[res.index]
				p.logger.Error("Failed to process fact",
					"index", res.index,
					"factType", fact.Type,
					"content", fact.Content,
					"error", res.err,
				)
			}
		} else {
			successCount++
		}
	}

	p.logger.Info("Concurrent processing completed", "successful", successCount, "failed", errorCount)
	return successCount, errorCount
}

// addMemory adds a new memory to the store.
func (p *Processor) addMemory(ctx context.Context, fact extract.Fact) error {
	memory := p.factToMemory(fact)

	p.logger.Info("Adding memory",
		"type", memory.Type,
		"content", memory.Content,
		"keywords", memory.Keywords,
		"confidence", memory.Confidence,
	)

	if err := p.store.Save(ctx, memory); err != nil {
		p.logger.Error("Failed to save memory", "error", err)
		return err // Store already wraps errors properly
	}

	p.logger.Info("Successfully added new memory",
		"id", uuid.UUID(memory.ID.Bytes).String(),
		"type", memory.Type,
		"content", memory.Content,
		"keywords", memory.Keywords,
		"entities", len(memory.Entities),
	)

	return nil
}

// updateMemory updates an existing memory.
func (p *Processor) updateMemory(ctx context.Context, fact extract.Fact, targetID pgtype.UUID) error {
	memory := p.factToMemory(fact)

	if err := p.store.Update(ctx, targetID, memory); err != nil {
		return err // Store already wraps errors properly
	}

	p.logger.Debug("Updated memory",
		"targetID", targetID,
		"type", memory.Type,
	)

	return nil
}

// factToMemory converts a Fact to a Memory.
func (p *Processor) factToMemory(fact extract.Fact) *Memory {
	return &Memory{
		Type:       MemoryType(fact.Type),
		Content:    fact.Content,
		Entities:   fact.Entities,
		Attributes: Attributes(fact.Attributes),
		Context:    convertFromExtractContext(fact.Context),
		Keywords:   fact.Keywords,
		Confidence: fact.Confidence,
		Status:     StatusActive,
		Version:    1,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}
}

// getSearchLimit returns the dynamic search limit based on fact importance.
func (p *Processor) getSearchLimit(fact extract.Fact) int {
	switch {
	case fact.Score >= 5:
		return SearchLimitCritical
	case fact.Score >= 4:
		return SearchLimitImportant
	case fact.Score >= 3:
		return SearchLimitNormal
	default:
		return SearchLimitLow
	}
}

// findSimilarMemories searches for similar memories including handling negation patterns.
func (p *Processor) findSimilarMemories(ctx context.Context, fact extract.Fact) ([]*Memory, error) {
	searchLimit := p.getSearchLimit(fact)

	similar, err := p.store.SearchSimilar(ctx, fact.Content, searchLimit)
	if err != nil {
		return nil, err
	}

	// For negations, do ONE additional search to find the positive form
	if extract.IsNegationPattern(fact.Content) {
		p.logger.Debug("Detected negation pattern, searching for positive form")
		positiveForm := extract.PositiveForm(fact.Content)
		if positiveForm != "" && positiveForm != fact.Content {
			additionalSimilar, searchErr := p.store.SearchSimilar(ctx, positiveForm, 20) // Search more for negations to find conflicts
			if searchErr == nil && len(additionalSimilar) > 0 {
				// Track existing IDs to avoid duplicates
				existingIDs := make(map[string]bool)
				for _, mem := range similar {
					existingIDs[uuid.UUID(mem.ID.Bytes).String()] = true
				}

				// Only add the most relevant additional results
				added := 0
				for _, mem := range additionalSimilar {
					memID := uuid.UUID(mem.ID.Bytes).String()
					if !existingIDs[memID] {
						similar = append(similar, mem)
						added++
						if added >= 3 { // Limit additional results
							break
						}
					}
				}
			}
		}
		p.logger.Debug("Total similar memories after negation search", "count", len(similar))
	}

	return similar, nil
}

// checkLowScoreAdd checks if a low score fact is being added and logs a warning.
func (p *Processor) checkLowScoreAdd(fact extract.Fact, decision Decision) {
	hasAddAction := false
	for _, action := range decision.Actions {
		if action.Type == ActionAdd {
			hasAddAction = true
			break
		}
	}
	if fact.Score <= 2 && hasAddAction {
		p.logger.Warn("Low score fact being added", "score", fact.Score)
	}
}

// executeActions executes all the actions in a decision.
func (p *Processor) executeActions(ctx context.Context, fact extract.Fact, decision Decision) error {
	p.logger.Debug("Executing actions", "count", len(decision.Actions))

	for i, action := range decision.Actions {
		p.logger.Debug("Executing action", "index", i+1, "type", action.Type)

		if err := p.executeAction(ctx, fact, action, decision.Reason); err != nil {
			return err
		}

		// SKIP means don't process further actions
		if action.Type == ActionSkip {
			return nil
		}
	}

	return nil
}

// executeAction executes a single action.
func (p *Processor) executeAction(ctx context.Context, fact extract.Fact, action Action, reason string) error {
	switch action.Type {
	case ActionSkip:
		p.logger.Debug("Skipping fact",
			"reason", reason,
			"content", fact.Content,
		)
		return nil

	case ActionAdd:
		p.logger.Debug("Adding new memory")
		// For ADD actions with specific content, use that instead of fact content
		if action.Content != "" {
			modifiedFact := fact
			modifiedFact.Content = action.Content
			return p.addMemory(ctx, modifiedFact)
		}
		return p.addMemory(ctx, fact)

	case ActionUpdate:
		if action.TargetID == nil {
			return NewMemoryError("processFact", KindValidation, errors.New("update action requires target ID"))
		}
		p.logger.Debug("Updating memory", "id", uuid.UUID(action.TargetID.Bytes).String())
		return p.updateMemory(ctx, fact, *action.TargetID)

	case ActionDelete:
		if action.TargetID == nil {
			return NewMemoryError("processFact", KindValidation, errors.New("delete action requires target ID"))
		}
		p.logger.Debug("Deleting memory", "id", uuid.UUID(action.TargetID.Bytes).String())
		if err := p.store.Delete(ctx, *action.TargetID); err != nil {
			return NewMemoryError("processFact", KindStorage, err, "delete failed")
		}
		return nil

	case ActionMerge:
		// TODO: Implement merge logic when needed
		return NewMemoryError("processFact", KindProcessing, errors.New("merge action not yet implemented"))

	default:
		return NewMemoryError("processFact", KindValidation, fmt.Errorf("unknown action: %s", action.Type))
	}
}
