// Package memory implements memory consolidation capabilities.
package memory

import (
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/koopa0/assistant-go/internal/platform/logger"
)

// ConsolidationStrategy defines how memories should be consolidated.
type ConsolidationStrategy string

const (
	// StrategyDeduplicate removes duplicate memories
	StrategyDeduplicate ConsolidationStrategy = "deduplicate"
	// StrategyMergeRelated merges related memories
	StrategyMergeRelated ConsolidationStrategy = "merge_related"
	// StrategyArchiveOld archives old, unused memories
	StrategyArchiveOld ConsolidationStrategy = "archive_old"
	// StrategyOptimizeGraph optimizes memory relationships
	StrategyOptimizeGraph ConsolidationStrategy = "optimize_graph"
)

// Consolidator manages background memory optimization and cleanup.
type Consolidator struct {
	store        *Store
	merger       *Merger
	graph        *Graph
	deduplicator *SemanticDeduplicator
	logger       logger.Logger
}

// NewConsolidator creates a new memory consolidator.
func NewConsolidator(store *Store, merger *Merger, graph *Graph, logger logger.Logger) *Consolidator {
	return &Consolidator{
		store:        store,
		merger:       merger,
		graph:        graph,
		deduplicator: NewSemanticDeduplicator(store, RelateThreshold), // Use lowest threshold to find all potential relations
		logger:       logger.WithComponent("consolidator"),
	}
}

// ConsolidateAll runs all consolidation strategies.
func (c *Consolidator) ConsolidateAll(ctx context.Context) error {
	c.logger.Info("Starting full memory consolidation")

	strategies := []ConsolidationStrategy{
		StrategyDeduplicate,
		StrategyMergeRelated,
		StrategyArchiveOld,
		StrategyOptimizeGraph,
	}

	for _, strategy := range strategies {
		if err := c.Consolidate(ctx, strategy); err != nil {
			c.logger.Error("Consolidation strategy failed",
				"strategy", strategy,
				"error", err)
			// Continue with other strategies
		}
	}

	c.logger.Info("Memory consolidation completed")
	return nil
}

// Consolidate runs a specific consolidation strategy.
func (c *Consolidator) Consolidate(ctx context.Context, strategy ConsolidationStrategy) error {
	c.logger.Info("Running consolidation strategy", "strategy", strategy)

	switch strategy {
	case StrategyDeduplicate:
		return c.deduplicateMemories(ctx)
	case StrategyMergeRelated:
		return c.mergeRelatedMemories(ctx)
	case StrategyArchiveOld:
		return c.archiveOldMemories(ctx)
	case StrategyOptimizeGraph:
		return c.optimizeGraph(ctx)
	default:
		return NewMemoryError("consolidate", KindValidation,
			fmt.Errorf("unknown strategy: %s", strategy))
	}
}

// deduplicateMemories finds and removes duplicate memories using semantic similarity.
func (c *Consolidator) deduplicateMemories(ctx context.Context) error {
	c.logger.Info("Deduplicating memories using tiered strategy")

	// Get all recent memories
	memories, err := c.store.ListRecent(ctx, 1000)
	if err != nil {
		return NewMemoryError("deduplicateMemories", KindStorage, err, "list memories failed")
	}

	// Find duplicates using semantic similarity
	duplicateGroups, err := c.deduplicator.FindDuplicates(ctx, memories)
	if err != nil {
		return NewMemoryError("deduplicateMemories", KindProcessing, err, "find duplicates failed")
	}

	// Process each duplicate group with tiered strategy
	deactivated := 0
	merged := 0
	relationsCreated := 0

	for _, group := range duplicateGroups {
		c.logger.Debug("Processing duplicate group",
			"primary", group.Primary.Content,
			"duplicates", len(group.Duplicates),
			"similarity", group.Similarity)

		// Apply tiered deduplication strategy
		if err := c.processGroup(ctx, group, &deactivated, &merged, &relationsCreated); err != nil {
			c.logger.Error("Failed to process duplicate group",
				"primary", group.Primary.Content,
				"error", err)
			continue
		}
	}

	c.logger.Info("Tiered deduplication completed",
		"groups", len(duplicateGroups),
		"deactivated", deactivated,
		"merged", merged,
		"relations_created", relationsCreated)
	return nil
}

// processGroup applies a tiered deduplication strategy to a group of similar memories.
// This is a core part of the knowledge curation process, treating similarity as a spectrum
// rather than a binary state. It avoids data loss by creating relationships for moderately
// similar items, while still efficiently cleaning up true duplicates.
// processGroup applies a tiered deduplication strategy to a group of similar memories.
// This is a core part of the knowledge curation process, treating similarity as a spectrum
// rather than a binary state. It avoids data loss by creating relationships for moderately
// similar items, while still efficiently cleaning up true duplicates.
func (c *Consolidator) processGroup(ctx context.Context, group DuplicateGroup, deactivated, merged, relationsCreated *int) error {
	similarity := group.Similarity

	switch {
	case similarity >= DuplicateThreshold:
		// Exact duplicate - deactivate without merging
		return c.deactivateDuplicates(ctx, group, deactivated)

	case similarity >= MergeSimilarityThreshold:
		// High similarity - merge then deactivate
		return c.mergeDuplicates(ctx, group, deactivated, merged)

	case similarity >= RelateThreshold:
		// Medium similarity - create relationships
		return c.createSimilarityRelations(ctx, group, relationsCreated)

	default:
		// Low similarity - do nothing
		c.logger.Debug("Similarity below threshold, skipping",
			"similarity", similarity,
			"threshold", RelateThreshold)
		return nil
	}
}

// deactivateDuplicates handles exact duplicates by deactivating them
func (c *Consolidator) deactivateDuplicates(ctx context.Context, group DuplicateGroup, deactivated *int) error {
	// Collect IDs to deactivate
	idsToDelete := make([]pgtype.UUID, 0, len(group.Duplicates))
	for _, dup := range group.Duplicates {
		idsToDelete = append(idsToDelete, dup.ID)
	}

	// Batch deactivate duplicates
	if len(idsToDelete) > 0 {
		if err := c.store.DeactivateMany(ctx, idsToDelete); err != nil {
			return NewMemoryError("deactivateDuplicates", KindStorage, err, "deactivate failed")
		}
		*deactivated += len(idsToDelete)
		c.logger.Debug("Deactivated exact duplicates",
			"count", len(idsToDelete),
			"primary", group.Primary.Content)
	}

	return nil
}

// mergeDuplicates handles high similarity by merging then deactivating
func (c *Consolidator) mergeDuplicates(ctx context.Context, group DuplicateGroup, deactivated, merged *int) error {
	// Merge duplicates into primary
	mergedMemory := c.deduplicator.MergeDuplicates(group)

	// Sum access counts from all duplicates
	totalAccessCount := group.Primary.AccessCount
	for _, dup := range group.Duplicates {
		totalAccessCount += dup.AccessCount
	}
	mergedMemory.AccessCount = totalAccessCount

	// Update the primary memory
	if err := c.store.Update(ctx, group.Primary.ID, mergedMemory); err != nil {
		return NewMemoryError("mergeDuplicates", KindStorage, err, "update merged memory failed")
	}
	*merged++

	// Transfer relationships from duplicates to primary
	for _, dup := range group.Duplicates {
		if err := c.transferRelations(ctx, dup.ID, group.Primary.ID); err != nil {
			c.logger.Warn("Failed to transfer relations",
				"from", dup.ID,
				"to", group.Primary.ID,
				"error", err)
		}
	}

	// Deactivate duplicates after merging
	return c.deactivateDuplicates(ctx, group, deactivated)
}

// createSimilarityRelations creates relationships between moderately similar memories
func (c *Consolidator) createSimilarityRelations(ctx context.Context, group DuplicateGroup, relationsCreated *int) error {
	// Create bidirectional "similar_to" relationships
	strength := group.Similarity // Use similarity score as relationship strength

	for _, dup := range group.Duplicates {
		// Create forward relation: primary -> duplicate
		if err := c.graph.CreateRelation(ctx, group.Primary.ID, dup.ID, RelationSimilar, strength); err != nil {
			c.logger.Warn("Failed to create forward similarity relation",
				"from", group.Primary.ID,
				"to", dup.ID,
				"error", err)
			continue
		}

		// Create reverse relation: duplicate -> primary
		if err := c.graph.CreateRelation(ctx, dup.ID, group.Primary.ID, RelationSimilar, strength); err != nil {
			c.logger.Warn("Failed to create reverse similarity relation",
				"from", dup.ID,
				"to", group.Primary.ID,
				"error", err)
			continue
		}

		*relationsCreated += 2 // Count both directions
		c.logger.Debug("Created similarity relations",
			"memory1", group.Primary.Content,
			"memory2", dup.Content,
			"strength", strength)
	}

	return nil
}

// transferRelations transfers all relationships from one memory to another
func (c *Consolidator) transferRelations(ctx context.Context, fromID, toID pgtype.UUID) error {
	// Get all relations involving the source memory
	relations, err := c.store.queries.GetRelationsByMemory(ctx, fromID)
	if err != nil {
		return NewMemoryError("transferRelations", KindStorage, err, "get relations failed")
	}

	transferred := 0
	for _, rel := range relations {
		// Determine the new relation direction
		var newFromID, newToID pgtype.UUID

		if rel.FromID == fromID {
			// Outgoing relation: fromID -> X becomes toID -> X
			newFromID = toID
			newToID = rel.ToID
		} else {
			// Incoming relation: X -> fromID becomes X -> toID
			newFromID = rel.FromID
			newToID = toID
		}

		// Skip if this would create a self-relation
		if newFromID == newToID {
			c.logger.Debug("Skipping self-relation during transfer",
				"from", newFromID,
				"to", newToID,
				"type", rel.Type)
			continue
		}

		// Create the new relation
		err := c.graph.CreateRelation(ctx, newFromID, newToID, RelationType(rel.Type), rel.Strength.Float32)
		if err != nil {
			c.logger.Warn("Failed to transfer relation",
				"from", newFromID,
				"to", newToID,
				"type", rel.Type,
				"error", err)
			continue
		}
		transferred++
	}

	c.logger.Debug("Relations transferred",
		"from", fromID,
		"to", toID,
		"count", transferred)

	return nil
}

// mergeRelatedMemories finds and merges related memories.
func (c *Consolidator) mergeRelatedMemories(ctx context.Context) error {
	c.logger.Info("Merging related memories")

	// Run merger for different categories
	categories := []string{"beverages", "food", "sports", "schedule"}

	for _, category := range categories {
		if err := c.merger.MergeRelatedMemories(ctx, category, 0.7); err != nil {
			c.logger.Error("Failed to merge category",
				"category", category,
				"error", err)
		}
	}

	return nil
}

// archiveOldMemories archives memories that haven't been accessed recently.
func (c *Consolidator) archiveOldMemories(ctx context.Context) error {
	c.logger.Info("Archiving old memories")

	// Define thresholds
	archiveThreshold := time.Now().AddDate(0, 0, -90) // Not accessed in 90 days
	deleteThreshold := time.Now().AddDate(-1, 0, 0)   // Archived over 1 year ago

	// Get all active memories
	memories, err := c.store.ListRecent(ctx, 10000)
	if err != nil {
		return NewMemoryError("archiveOldMemories", KindStorage, err, "list memories failed")
	}

	archived := 0
	deleted := 0

	for _, mem := range memories {
		// Process based on current status
		switch mem.Status {
		case StatusActive:
			// Check if should be archived
			if mem.AccessedAt.Before(archiveThreshold) {
				// Skip critical memory types
				if mem.Type == MemoryTypeFact || mem.Type == MemoryTypeSchedule {
					continue
				}

				// Archive the memory
				mem.Status = StatusArchived
				mem.ArchivedAt = &[]time.Time{time.Now()}[0]
				mem.Confidence = mem.Confidence * 0.7 // Reduce but not too much

				if err := c.store.Update(ctx, mem.ID, mem); err != nil {
					c.logger.Error("Failed to archive memory",
						"id", mem.ID,
						"error", err)
				} else {
					archived++
				}
			}

		case StatusArchived:
			// Check if should be deleted
			if mem.ArchivedAt != nil && mem.ArchivedAt.Before(deleteThreshold) {
				// Never accessed after archiving
				if mem.AccessCount == 0 || mem.AccessedAt.Before(*mem.ArchivedAt) {
					mem.Status = StatusDeleted

					if err := c.store.Update(ctx, mem.ID, mem); err != nil {
						c.logger.Error("Failed to mark memory as deleted",
							"id", mem.ID,
							"error", err)
					} else {
						deleted++
					}
				}
			}
		}
	}

	c.logger.Info("Archiving completed",
		"archived", archived,
		"marked_for_deletion", deleted)
	return nil
}

// optimizeGraph optimizes memory relationship graph.
func (c *Consolidator) optimizeGraph(ctx context.Context) error {
	c.logger.Info("Optimizing memory graph")

	// Build entity-based relationships (new strategy)
	c.logger.Debug("Building entity-based relationships")
	if err := c.graph.BuildEntityRelationships(ctx); err != nil {
		c.logger.Warn("Failed to build entity relationships", "error", err)
		// Continue with other optimizations
	}

	// Rebuild category relationships (legacy approach)
	c.logger.Debug("Building category-based relationships")
	if err := c.graph.BuildCategoryGraph(ctx); err != nil {
		return fmt.Errorf("rebuild category graph: %w", err)
	}

	// Get relationship suggestions
	suggestions, err := c.graph.SuggestRelations(ctx, 20)
	if err != nil {
		return fmt.Errorf("get suggestions: %w", err)
	}

	// Create high-confidence relationships
	created := 0
	for _, suggestion := range suggestions {
		if suggestion.Confidence < 0.8 {
			continue
		}

		err := c.graph.CreateRelation(ctx,
			suggestion.From.ID,
			suggestion.To.ID,
			suggestion.Type,
			suggestion.Strength)

		if err != nil {
			c.logger.Error("Failed to create relation",
				"from", suggestion.From.Content,
				"to", suggestion.To.Content,
				"error", err)
		} else {
			created++
		}
	}

	c.logger.Info("Graph optimization completed", "relations_created", created)
	return nil
}

// ConsolidationStats represents consolidation statistics.
type ConsolidationStats struct {
	TotalMemories    int
	Duplicates       int
	Merged           int
	Archived         int
	RelationsCreated int
	LastRun          time.Time
}

// GetStats returns current consolidation statistics.
func (c *Consolidator) GetStats(ctx context.Context) (*ConsolidationStats, error) {
	// Get total memory count
	memories, err := c.store.ListRecent(ctx, 10000)
	if err != nil {
		return nil, fmt.Errorf("list memories: %w", err)
	}

	// Find duplicates using semantic similarity
	duplicateGroups, err := c.deduplicator.FindDuplicates(ctx, memories)
	if err != nil {
		c.logger.Warn("Failed to find duplicates for stats", "error", err)
		duplicateGroups = nil
	}

	duplicateCount := 0
	for _, group := range duplicateGroups {
		duplicateCount += len(group.Duplicates)
	}

	stats := &ConsolidationStats{
		TotalMemories: len(memories),
		Duplicates:    duplicateCount,
		LastRun:       time.Now(),
	}

	return stats, nil
}
