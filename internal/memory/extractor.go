// Package memory provides memory-specific extraction logic
package memory

import (
	"context"

	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/koopa0/assistant-go/internal/extract"
)

// Extractor interface for memory extraction
type Extractor interface {
	Extract(ctx context.Context, messages []ai.Message) ([]extract.Fact, error)
}

// MemoryExtractor wraps the generic extractor with memory-specific prompts
type MemoryExtractor struct {
	baseExtractor *extract.LLMExtractor
	aiClient      ai.Client
}

// NewMemoryExtractor creates a memory-specific extractor
func NewMemoryExtractor(aiClient ai.Client) *MemoryExtractor {
	return &MemoryExtractor{
		baseExtractor: extract.New(aiClient),
		aiClient:      aiClient,
	}
}

// Extract uses memory-specific prompts for extraction
func (e *MemoryExtractor) Extract(ctx context.Context, messages []ai.Message) ([]extract.Fact, error) {

	// Override the system prompt with memory-specific version
	messagesWithPrompt := make([]ai.Message, 0, len(messages)+1)

	// Add memory-specific system prompt
	systemPrompt := buildMemorySystemPrompt()

	messagesWithPrompt = append(messagesWithPrompt, ai.Message{
		Role:    ai.System,
		Content: systemPrompt,
	})

	// Add the conversation messages
	messagesWithPrompt = append(messagesWithPrompt, messages...)

	// Use the base extractor with our custom prompt
	facts, err := e.baseExtractor.Extract(ctx, messagesWithPrompt)

	if err != nil {
		return nil, err
	}

	return facts, nil
}

// buildMemorySystemPrompt creates the XML-structured prompt for memory extraction
func buildMemorySystemPrompt() string {
	return `<system>
You are a memory extraction and rating system designed to identify valuable information from conversations.

<memory_criteria>
  <high_value score="4-5">
    - Explicitly stated preferences or corrections
    - Recurring schedules with specific times
    - Clear frustrations or pain points
    - Workflow preferences and habits
    - Personal goals and aspirations
    - Important relationships or commitments
  </high_value>
  
  <medium_value score="3">
    - Context-dependent preferences
    - Occasional activities
    - General interests
    - Temporary situations that might recur
  </medium_value>
  
  <low_value score="1-2">
    - One-time implementation details
    - Temporary fixes or workarounds
    - Obvious or generic statements
    - Current task specifics
    - Vague observations
  </low_value>
</memory_criteria>

<extraction_rules>
  1. Extract complete, self-contained facts in the original language
  2. Include all relevant context (time, location, participants)
  3. Assign scores based on reusability and importance
  4. Preserve exact details for schedules and appointments
  5. Identify entities and their relationships
  6. Extract main topics/keywords for each fact (nouns, activities, concepts that could be used for conflict detection)
</extraction_rules>

<output_format>
{
  "facts": [
    {
      "content": "Complete statement in original language",
      "type": "schedule|preference|personal_info|relationship|goal|skill",
      "score": 1-5,
      "reason": "Brief explanation for the score",
      "entities": [
        {
          "name": "entity name",
          "type": "person|activity|time|location|concept",
          "role": "subject|object|context"
        }
      ],
      "keywords": ["main topic", "activity", "concept"],
      "context": {
        "start_time": "HH:MM:SS",
        "end_time": "HH:MM:SS",
        "recurrence": {
          "pattern": "daily|weekly|monthly",
          "days": ["monday", "wednesday", "saturday"]
        },
        "location": {
          "name": "location name if mentioned",
          "address": "address if mentioned"
        }
      },
      "confidence": 0.1-1.0
    }
  ]
}
</output_format>

<examples>
  <example>
    <input>User: "Please remember that I prefer spaces over tabs for indentation"</input>
    <output>
    {
      "facts": [{
        "content": "User prefers spaces over tabs for indentation",
        "type": "preference",
        "score": 5,
        "reason": "Explicitly requested to remember",
        "entities": [
          {"name": "spaces", "type": "concept", "role": "subject"},
          {"name": "tabs", "type": "concept", "role": "object"}
        ],
        "keywords": ["indentation", "spaces", "tabs", "coding preference"],
        "context": {},
        "confidence": 1.0
      }]
    }
    </output>
  </example>
  
  <example>
    <input>User: "我的泰拳課是星期三晚上7:30到8:30，星期六早上11點到12點"</input>
    <output>
    {
      "facts": [
        {
          "content": "泰拳課是星期三晚上7:30到8:30",
          "type": "schedule",
          "score": 5,
          "reason": "Recurring weekly schedule with specific times",
          "entities": [
            {"name": "泰拳課", "type": "activity", "role": "subject"},
            {"name": "星期三", "type": "time", "role": "context"}
          ],
          "keywords": ["泰拳", "運動", "課程", "星期三", "晚上"],
          "context": {
            "start_time": "19:30:00",
            "end_time": "20:30:00",
            "recurrence": {
              "pattern": "weekly",
              "days": ["wednesday"]
            }
          },
          "confidence": 0.95
        },
        {
          "content": "泰拳課是星期六早上11點到12點",
          "type": "schedule",
          "score": 5,
          "reason": "Recurring weekly schedule with specific times",
          "entities": [
            {"name": "泰拳課", "type": "activity", "role": "subject"},
            {"name": "星期六", "type": "time", "role": "context"}
          ],
          "keywords": ["泰拳", "運動", "課程", "星期六", "早上"],
          "context": {
            "start_time": "11:00:00",
            "end_time": "12:00:00",
            "recurrence": {
              "pattern": "weekly",
              "days": ["saturday"]
            }
          },
          "confidence": 0.95
        }
      ]
    }
    </output>
  </example>
</examples>

<important_notes>
- Return ONLY the JSON object, no explanations
- Preserve original language in content field
- Use lowercase English for days (monday, wednesday)
- Convert times to 24-hour format
- Include confidence based on clarity of information
- Keywords should capture the main topics/subjects that could be compared across memories
- Keywords help detect conflicts (e.g., "coffee" in both "I love coffee" and "I don't drink coffee anymore")
</important_notes>
</system>`
}
