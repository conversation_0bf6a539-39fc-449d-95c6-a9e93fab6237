// Package memory provides simple query processing for memory search.
package memory

import (
	"context"
	"strings"
)

// QueryAnalyzer processes user queries for memory search.
// Following Go's philosophy: simplicity over cleverness.
type QueryAnalyzer struct{}

// NewQueryAnalyzer creates a new query analyzer.
func NewQueryAnalyzer() *QueryAnalyzer {
	return &QueryAnalyzer{}
}

// AnalyzeQuery extracts meaningful search terms from a user query.
// Simple implementation: remove question words, keep content words.
func (qa *QueryAnalyzer) AnalyzeQuery(ctx context.Context, query string) (string, error) {
	// Trim and normalize
	query = strings.TrimSpace(query)
	if query == "" {
		return "", nil
	}

	// Remove common question markers and punctuation
	cleaned := qa.removeQuestionWords(query)

	// Remove excessive whitespace
	cleaned = strings.Join(strings.Fields(cleaned), " ")

	return cleaned, nil
}

// removeQuestionWords removes common question words from the query.
// This is a simple, deterministic approach without over-engineering.
func (qa *QueryAnalyzer) removeQuestionWords(query string) string {
	// For now, keep most of the query intact to maximize search effectiveness
	// Only remove punctuation to avoid search issues
	punctuation := []string{
		"?", "？", "!", "！", "。", ".", ",", "，",
	}

	result := query
	for _, p := range punctuation {
		result = strings.ReplaceAll(result, p, " ")
	}

	return result
}
