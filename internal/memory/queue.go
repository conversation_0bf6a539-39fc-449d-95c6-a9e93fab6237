// Package memory implements an optimized async processing queue.
package memory

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/koopa0/assistant-go/internal/platform/logger"
)

// ProcessingQueue manages async memory processing with optimizations.
type ProcessingQueue struct {
	processor  *Processor
	jobChan    chan *ProcessingJob
	workerPool chan struct{} // Worker pool for concurrency control
	dlq        *DeadLetterQueue
	logger     logger.Logger
	wg         sync.WaitGroup
	mu         sync.Mutex

	// Metrics
	processed int64
	failed    int64
	avgTime   time.Duration
}

// ProcessingJob represents a memory processing task.
type ProcessingJob struct {
	ID        string
	Messages  []ai.Message
	Priority  int // Higher priority = process first
	CreatedAt time.Time
	Retries   int
	LastError error // Track last processing error
}

// DeadLetterQueue stores permanently failed jobs for manual inspection.
type DeadLetterQueue struct {
	mu        sync.RWMutex
	jobs      []*<PERSON>Letter<PERSON>ob
	maxSize   int
	onEnqueue func(*DeadLetterJob) // Callback for alerting
}

// DeadLetterJob represents a job that failed permanently.
type DeadLetterJob struct {
	Job          *ProcessingJob
	FailedAt     time.Time
	FailureCount int
	LastError    error
	Reason       string
}

// QueueConfig defines configuration for the processing queue.
type QueueConfig struct {
	MaxWorkers   int           // Maximum concurrent workers
	BufferSize   int           // Job channel buffer size
	MaxRetries   int           // Maximum retries for failed jobs
	BatchSize    int           // Number of messages to batch
	BatchTimeout time.Duration // Timeout for batch accumulation
}

// DefaultQueueConfig returns sensible defaults.
func DefaultQueueConfig() QueueConfig {
	return QueueConfig{
		MaxWorkers:   QueueDefaultMaxWorkers, // Limit concurrent AI calls
		BufferSize:   QueueDefaultBufferSize, // Buffer for burst handling
		MaxRetries:   QueueDefaultMaxRetries,
		BatchSize:    QueueDefaultBatchSize,
		BatchTimeout: QueueDefaultBatchTimeout,
	}
}

// NewProcessingQueue creates an optimized processing queue.
func NewProcessingQueue(processor *Processor, cfg QueueConfig, logger logger.Logger) *ProcessingQueue {
	if cfg.MaxWorkers <= 0 {
		cfg.MaxWorkers = QueueDefaultMaxWorkers
	}
	if cfg.BufferSize <= 0 {
		cfg.BufferSize = QueueDefaultBufferSize
	}

	// Create DLQ with alert callback
	dlq := &DeadLetterQueue{
		maxSize: 1000, // Store up to 1000 failed jobs
		onEnqueue: func(dlj *DeadLetterJob) {
			logger.Error("Job moved to Dead Letter Queue",
				"job_id", dlj.Job.ID,
				"failures", dlj.FailureCount,
				"reason", dlj.Reason,
				"error", dlj.LastError)
		},
	}

	return &ProcessingQueue{
		processor:  processor,
		jobChan:    make(chan *ProcessingJob, cfg.BufferSize),
		workerPool: make(chan struct{}, cfg.MaxWorkers),
		dlq:        dlq,
		logger:     logger.WithComponent("memory.queue"),
	}
}

// Start begins processing jobs from the queue.
func (q *ProcessingQueue) Start(ctx context.Context) {
	// Add immediate logging
	q.logger.Debug("Starting processing queue with context")
	q.logger.Debug("Queue configuration", "workers", cap(q.workerPool), "buffer", cap(q.jobChan))

	q.logger.Info("Starting processing queue",
		"workers", cap(q.workerPool),
		"bufferSize", cap(q.jobChan))

	// Initialize worker pool
	for i := 0; i < cap(q.workerPool); i++ {
		q.workerPool <- struct{}{}
	}

	// Start queue processor
	q.wg.Add(1)
	go func() {
		q.logger.Debug("Starting processQueue goroutine")
		q.processQueue(ctx)
		q.logger.Debug("processQueue goroutine ended")
	}()

	// Log that queue is ready
	q.logger.Info("Processing queue started and ready to receive jobs")
	q.logger.Debug("Queue Start() completed")
}

// Stop gracefully shuts down the queue.
func (q *ProcessingQueue) Stop() {
	q.logger.Info("Stopping processing queue")
	close(q.jobChan)
	q.wg.Wait()
	q.logger.Info("Processing queue stopped",
		"processed", q.processed,
		"failed", q.failed,
		"avg_time", q.avgTime)
}

// Enqueue adds a job to the processing queue.
func (q *ProcessingQueue) Enqueue(messages []ai.Message, priority int) error {
	job := &ProcessingJob{
		ID:        fmt.Sprintf("job_%d", time.Now().UnixNano()),
		Messages:  messages,
		Priority:  priority,
		CreatedAt: time.Now(),
	}

	select {
	case q.jobChan <- job:
		q.logger.Info("Successfully enqueued job", "id", job.ID, "priority", priority, "messages", len(messages))
		return nil
	default:
		q.logger.Error("Queue is full, cannot enqueue", "channelCap", cap(q.jobChan), "channelLen", len(q.jobChan))
		return NewMemoryError("enqueue", KindProcessing, ErrQueueFull)
	}
}

// processQueue processes jobs from the queue.
func (q *ProcessingQueue) processQueue(ctx context.Context) {
	defer q.wg.Done()

	q.logger.Debug("processQueue goroutine started")
	q.logger.Info("processQueue goroutine started", "channelCap", cap(q.jobChan))

	// Batch accumulator
	batch := make([]*ProcessingJob, 0, QueueDefaultBatchSize)
	batchTimer := time.NewTimer(QueueDefaultBatchTimeout)
	defer batchTimer.Stop()

	// Log that we're ready to process
	q.logger.Info("Queue processor ready and waiting for jobs")
	q.logger.Debug("Ready to receive jobs from channel")

	for {
		select {
		case <-ctx.Done():
			q.logger.Debug("Context canceled")
			q.logger.Info("Queue processor stopping due to context cancellation")
			// Process remaining batch
			if len(batch) > 0 {
				q.processBatch(ctx, batch)
			}
			return

		case job, ok := <-q.jobChan:
			if !ok {
				// Channel closed, process remaining batch
				if len(batch) > 0 {
					q.processBatch(ctx, batch)
				}
				return
			}

			q.logger.Info("Queue received job", "id", job.ID, "batchSize", len(batch)+1)
			// Add to batch
			batch = append(batch, job)

			// Process if batch is full
			if len(batch) >= QueueDefaultBatchSize {
				q.processBatch(ctx, batch)
				batch = batch[:0]
				batchTimer.Reset(QueueDefaultBatchTimeout)
			}

		case <-batchTimer.C:
			// Process accumulated batch
			if len(batch) > 0 {
				q.processBatch(ctx, batch)
				batch = batch[:0]
			}
			batchTimer.Reset(QueueDefaultBatchTimeout)
		}
	}
}

// processBatch processes a batch of jobs.
func (q *ProcessingQueue) processBatch(ctx context.Context, batch []*ProcessingJob) {
	if len(batch) == 0 {
		return
	}

	q.logger.Info("Processing batch", "batchSize", len(batch))

	// Sort by priority (higher first)
	// Simple insertion sort for small batches
	for i := 1; i < len(batch); i++ {
		j := i
		for j > 0 && batch[j].Priority > batch[j-1].Priority {
			batch[j], batch[j-1] = batch[j-1], batch[j]
			j--
		}
	}

	// Process each job in the batch
	for _, job := range batch {
		// Acquire worker slot
		<-q.workerPool

		// Process asynchronously
		q.wg.Add(1)
		go func(j *ProcessingJob) {
			defer q.wg.Done()
			defer func() { q.workerPool <- struct{}{} }()

			q.processJob(ctx, j)
		}(job)
	}
}

// processJob processes a single job.
func (q *ProcessingQueue) processJob(ctx context.Context, job *ProcessingJob) {
	start := time.Now()

	q.logger.Debug("Processing job",
		"id", job.ID,
		"priority", job.Priority,
		"messages", len(job.Messages))

	// Create timeout context with longer timeout
	processCtx, cancel := context.WithTimeout(ctx, QueueProcessTimeout)
	defer cancel()

	// Process messages - the processor will handle concurrency internally
	err := q.processor.Process(processCtx, job.Messages)

	duration := time.Since(start)

	// Update metrics
	q.mu.Lock()
	if err != nil {
		q.failed++
		job.LastError = err

		q.logger.Error("Job processing failed",
			"job_id", job.ID,
			"error", err,
			"duration", duration,
			"retries", job.Retries)

		// Retry if below limit
		if job.Retries < QueueDefaultMaxRetries {
			job.Retries++
			// Re-enqueue with lower priority
			_ = q.Enqueue(job.Messages, job.Priority-1)
		} else {
			// Max retries exceeded - move to DLQ
			q.dlq.Enqueue(&DeadLetterJob{
				Job:          job,
				FailedAt:     time.Now(),
				FailureCount: job.Retries + 1,
				LastError:    err,
				Reason:       fmt.Sprintf("Max retries (%d) exceeded", QueueDefaultMaxRetries),
			})
		}
	} else {
		q.processed++
		// Update average time
		if q.avgTime == 0 {
			q.avgTime = duration
		} else {
			q.avgTime = (q.avgTime + duration) / 2
		}

		q.logger.Debug("Job completed", "id", job.ID, "duration", duration)
	}
	q.mu.Unlock()
}

// GetMetrics returns current queue metrics.
func (q *ProcessingQueue) GetMetrics() QueueMetrics {
	q.mu.Lock()
	defer q.mu.Unlock()

	return QueueMetrics{
		Processed:     q.processed,
		Failed:        q.failed,
		AverageTime:   q.avgTime,
		QueueLength:   len(q.jobChan),
		ActiveWorkers: cap(q.workerPool) - len(q.workerPool),
		DLQSize:       q.dlq.Size(),
	}
}

// QueueMetrics represents queue performance metrics.
type QueueMetrics struct {
	Processed     int64
	Failed        int64
	AverageTime   time.Duration
	QueueLength   int
	ActiveWorkers int
	DLQSize       int
}

// DLQ Methods

// Enqueue adds a failed job to the dead letter queue.
func (dlq *DeadLetterQueue) Enqueue(job *DeadLetterJob) {
	dlq.mu.Lock()
	defer dlq.mu.Unlock()

	// If at capacity, remove oldest
	if len(dlq.jobs) >= dlq.maxSize {
		dlq.jobs = dlq.jobs[1:]
	}

	dlq.jobs = append(dlq.jobs, job)

	// Trigger alert callback
	if dlq.onEnqueue != nil {
		dlq.onEnqueue(job)
	}
}

// GetJobs returns all jobs in the DLQ.
func (dlq *DeadLetterQueue) GetJobs() []*DeadLetterJob {
	dlq.mu.RLock()
	defer dlq.mu.RUnlock()

	// Return a copy to prevent external modifications
	jobs := make([]*DeadLetterJob, len(dlq.jobs))
	copy(jobs, dlq.jobs)
	return jobs
}

// Size returns the number of jobs in the DLQ.
func (dlq *DeadLetterQueue) Size() int {
	dlq.mu.RLock()
	defer dlq.mu.RUnlock()
	return len(dlq.jobs)
}

// Clear removes all jobs from the DLQ.
func (dlq *DeadLetterQueue) Clear() {
	dlq.mu.Lock()
	defer dlq.mu.Unlock()
	dlq.jobs = nil
}

// ReprocessDLQJob attempts to reprocess a job from the DLQ.
func (q *ProcessingQueue) ReprocessDLQJob(jobID string) error {
	dlqJobs := q.dlq.GetJobs()

	for i, dlj := range dlqJobs {
		if dlj.Job.ID == jobID {
			// Reset retry counter and re-enqueue
			dlj.Job.Retries = 0
			dlj.Job.LastError = nil

			// Remove from DLQ
			q.dlq.mu.Lock()
			q.dlq.jobs = append(q.dlq.jobs[:i], q.dlq.jobs[i+1:]...)
			q.dlq.mu.Unlock()

			// Re-enqueue with original priority
			return q.Enqueue(dlj.Job.Messages, dlj.Job.Priority)
		}
	}

	return NewMemoryError("reprocessDLQJob", KindNotFound,
		fmt.Errorf("job %s not found in DLQ", jobID))
}

// GetDLQJobs returns all jobs currently in the Dead Letter Queue.
func (q *ProcessingQueue) GetDLQJobs() []*DeadLetterJob {
	return q.dlq.GetJobs()
}

// ClearDLQ removes all jobs from the Dead Letter Queue.
func (q *ProcessingQueue) ClearDLQ() {
	q.dlq.Clear()
	q.logger.Info("Dead Letter Queue cleared")
}
