// Package memory implements memory relationship graph capabilities.
package memory

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"sort"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/koopa0/assistant-go/internal/storage/database/sqlc"
)

// RelationType defines the type of relationship between memories.
type RelationType string

const (
	// RelationSimilar indicates memories about similar topics
	RelationSimilar RelationType = "similar"
	// RelationContrast indicates contrasting or opposing memories
	RelationContrast RelationType = "contrast"
	// RelationTemporal indicates time-based relationships
	RelationTemporal RelationType = "temporal"
	// RelationCausal indicates cause-effect relationships
	RelationCausal RelationType = "causal"
	// RelationCategory indicates same category (e.g., beverages)
	RelationCategory RelationType = "category"
	// RelationSharedEntity indicates memories sharing important entities
	RelationSharedEntity RelationType = "shared_entity"
	// RelationRelatedActivity indicates memories about related activities
	RelationRelatedActivity RelationType = "related_activity"
)

// Relation represents a connection between two memories.
type Relation struct {
	FromID   pgtype.UUID
	ToID     pgtype.UUID
	Type     RelationType
	Strength float32 // 0.0 to 1.0
	Metadata map[string]interface{}
}

// Graph manages memory relationships and provides graph operations.
type Graph struct {
	store *Store
}

// NewGraph creates a new memory graph manager.
func NewGraph(store *Store) *Graph {
	return &Graph{
		store: store,
	}
}

// CreateRelation creates or updates a relationship between memories.
func (g *Graph) CreateRelation(ctx context.Context, fromID, toID pgtype.UUID, relType RelationType, strength float32) error {
	// Validate strength
	if strength < 0 || strength > 1 {
		strength = 0.5 // Default moderate strength
	}

	metadata := map[string]interface{}{
		"created_at": time.Now().Format(time.RFC3339),
	}
	metadataJSON, err := json.Marshal(metadata)
	if err != nil {
		return fmt.Errorf("marshal metadata: %w", err)
	}

	params := sqlc.CreateRelationParams{
		FromID:   fromID,
		ToID:     toID,
		Type:     sqlc.RelationType(relType),
		Strength: pgtype.Float4{Float32: strength, Valid: true},
		Metadata: metadataJSON,
	}

	_, err = g.store.queries.CreateRelation(ctx, params)
	if err != nil {
		return fmt.Errorf("create relation: %w", err)
	}

	return nil
}

// RelatedMemory represents a memory with its relationship information
type RelatedMemory struct {
	*Memory
	RelationType     RelationType
	RelationStrength float32
	RelationDepth    int
}

// FindRelated finds memories related to a given memory ID.
func (g *Graph) FindRelated(ctx context.Context, memoryID pgtype.UUID, relType *RelationType, maxDepth int) ([]*Memory, error) {
	if maxDepth <= 0 {
		maxDepth = 1
	}
	if maxDepth > 3 {
		maxDepth = 3 // Limit recursion depth
	}

	var relationFilter sqlc.RelationType
	if relType != nil {
		relationFilter = sqlc.RelationType(*relType)
	}

	params := sqlc.GetRelatedParams{
		Column1: memoryID,
		Column2: relationFilter,
		Column3: int32(min(maxDepth, math.MaxInt32)), // #nosec G115 -- bounded by min()
	}

	rows, err := g.store.queries.GetRelated(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("get related: %w", err)
	}

	// Convert rows to Memory objects
	memories := make([]*Memory, 0, len(rows))
	for _, row := range rows {
		memory := &Memory{
			ID:          row.ID,
			SemanticID:  row.SemanticID,
			Type:        MemoryType(row.Type),
			Content:     row.Content,
			Summary:     row.Summary.String,
			Keywords:    row.Keywords,
			Confidence:  row.Confidence.Float32,
			Version:     int(row.Version.Int32),
			AccessCount: row.AccessCount.Int32,
			CreatedAt:   row.CreatedAt,
			UpdatedAt:   row.UpdatedAt,
		}

		// Parse JSON fields
		if len(row.Entities) > 0 {
			_ = json.Unmarshal(row.Entities, &memory.Entities)
		}
		if len(row.Attributes) > 0 {
			_ = json.Unmarshal(row.Attributes, &memory.Attributes)
		}
		if len(row.Context) > 0 {
			_ = json.Unmarshal(row.Context, &memory.Context)
		}

		memories = append(memories, memory)
	}

	return memories, nil
}

// FindRelatedWithInfo finds memories related to a given memory ID with relationship information.
func (g *Graph) FindRelatedWithInfo(ctx context.Context, memoryID pgtype.UUID, relType *RelationType, maxDepth int) ([]*RelatedMemory, error) {
	if maxDepth <= 0 {
		maxDepth = 1
	}
	if maxDepth > 3 {
		maxDepth = 3 // Limit recursion depth
	}

	var relationFilter sqlc.RelationType
	if relType != nil {
		relationFilter = sqlc.RelationType(*relType)
	}

	params := sqlc.GetRelatedParams{
		Column1: memoryID,
		Column2: relationFilter,
		Column3: int32(min(maxDepth, math.MaxInt32)), // #nosec G115 -- bounded by min()
	}

	rows, err := g.store.queries.GetRelated(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("get related: %w", err)
	}

	// Convert rows to RelatedMemory objects
	relatedMemories := make([]*RelatedMemory, 0, len(rows))
	for _, row := range rows {
		memory := &Memory{
			ID:          row.ID,
			SemanticID:  row.SemanticID,
			Type:        MemoryType(row.Type),
			Content:     row.Content,
			Summary:     row.Summary.String,
			Keywords:    row.Keywords,
			Confidence:  row.Confidence.Float32,
			Version:     int(row.Version.Int32),
			AccessCount: row.AccessCount.Int32,
			CreatedAt:   row.CreatedAt,
			UpdatedAt:   row.UpdatedAt,
			AccessedAt:  row.AccessedAt.Time,
			Status:      MemoryStatus(row.Status),
		}

		// Parse entities
		if len(row.Entities) > 0 {
			_ = json.Unmarshal(row.Entities, &memory.Entities)
		}

		// Parse attributes
		if len(row.Attributes) > 0 {
			_ = json.Unmarshal(row.Attributes, &memory.Attributes)
		}

		// Parse context
		if len(row.Context) > 0 {
			_ = json.Unmarshal(row.Context, &memory.Context)
		}

		relatedMem := &RelatedMemory{
			Memory:           memory,
			RelationType:     RelationType(row.RelationType),
			RelationStrength: row.RelationStrength.Float32,
			RelationDepth:    int(row.RelationDepth),
		}

		relatedMemories = append(relatedMemories, relatedMem)
	}

	return relatedMemories, nil
}

// BuildCategoryGraph automatically creates category relationships.
func (g *Graph) BuildCategoryGraph(ctx context.Context) error {
	// Building category relationships

	// Define categories and their keywords
	categories := map[string][]string{
		"beverages": {"咖啡", "茶", "紅茶", "綠茶", "coffee", "tea", "喝", "drink", "飲料"},
		"food":      {"吃", "食", "eat", "food", "餐", "meal"},
		"sports":    {"運動", "sport", "exercise", "泰拳", "跑步", "游泳"},
		"schedule":  {"時間", "點", "週", "每天", "time", "schedule", "when"},
	}

	// Get all active memories
	memories, err := g.store.ListRecent(ctx, 1000)
	if err != nil {
		return fmt.Errorf("list memories: %w", err)
	}

	// Group memories by category
	categoryMemories := make(map[string][]*Memory)
	for _, mem := range memories {
		content := strings.ToLower(mem.Content)
		for cat, keywords := range categories {
			for _, keyword := range keywords {
				if strings.Contains(content, keyword) {
					categoryMemories[cat] = append(categoryMemories[cat], mem)
					break
				}
			}
		}
	}

	// Create relationships within each category
	var batchParams []sqlc.CreateRelationsBatchParams

	for _, mems := range categoryMemories {
		// Create relationships between memories in same category
		for i := 0; i < len(mems); i++ {
			for j := i + 1; j < len(mems); j++ {
				// Calculate relationship strength based on type similarity
				strength := float32(0.5) // Base strength
				if mems[i].Type == mems[j].Type {
					strength = 0.7 // Higher for same type
				}

				// Empty metadata for now
				metadata, _ := json.Marshal(map[string]interface{}{})

				batchParams = append(batchParams, sqlc.CreateRelationsBatchParams{
					FromID:   mems[i].ID,
					ToID:     mems[j].ID,
					Type:     sqlc.RelationType(RelationCategory),
					Strength: pgtype.Float4{Float32: strength, Valid: true},
					Metadata: metadata,
				})
			}
		}
	}

	// Batch insert all relations
	if len(batchParams) > 0 {
		if _, err := g.store.queries.CreateRelationsBatch(ctx, batchParams); err != nil {
			return fmt.Errorf("batch create relations: %w", err)
		}
	}

	return nil
}

// GetMemoryNetwork returns the network of memories connected to a given memory.
func (g *Graph) GetMemoryNetwork(ctx context.Context, memoryID pgtype.UUID) (*NetworkView, error) {
	// Get the central memory
	central, err := g.store.Get(ctx, memoryID)
	if err != nil {
		return nil, NewMemoryError("buildContextGraph", KindStorage, err, "get central memory")
	}

	// Get all related memories
	related, err := g.FindRelated(ctx, memoryID, nil, 2)
	if err != nil {
		return nil, fmt.Errorf("find related: %w", err)
	}

	// Build network view
	network := &NetworkView{
		Central: central,
		Nodes:   make(map[string]*NetworkNode),
		Edges:   make([]NetworkEdge, 0),
	}

	// Add central node
	centralNode := &NetworkNode{
		Memory: central,
		Depth:  0,
	}
	centralIDStr := uuid.UUID(central.ID.Bytes).String()
	network.Nodes[centralIDStr] = centralNode

	// Add related nodes
	for _, mem := range related {
		node := &NetworkNode{
			Memory: mem,
			Depth:  1, // Simplified - would need to calculate actual depth
		}
		memIDStr := uuid.UUID(mem.ID.Bytes).String()
		network.Nodes[memIDStr] = node

		// Add edge
		edge := NetworkEdge{
			From:     centralIDStr,
			To:       memIDStr,
			Type:     "related",
			Strength: 0.5, // Would need to get actual strength from DB
		}
		network.Edges = append(network.Edges, edge)
	}

	return network, nil
}

// SuggestRelations analyzes memories and suggests potential relationships.
func (g *Graph) SuggestRelations(ctx context.Context, limit int) ([]RelationSuggestion, error) {
	if limit <= 0 {
		limit = 10
	}

	suggestions := []RelationSuggestion{}

	// Get recent memories
	memories, err := g.store.ListRecent(ctx, 50)
	if err != nil {
		return nil, fmt.Errorf("list memories: %w", err)
	}

	// Analyze pairs for potential relationships
	for i := 0; i < len(memories); i++ {
		for j := i + 1; j < len(memories); j++ {
			mem1, mem2 := memories[i], memories[j]

			// Skip if same type and very similar (likely duplicates)
			if mem1.Type == mem2.Type && g.calculateSimilarity(mem1.Content, mem2.Content) > 0.9 {
				continue
			}

			// Check for temporal relationships
			if g.hasTemporalRelation(mem1, mem2) {
				suggestions = append(suggestions, RelationSuggestion{
					From:       mem1,
					To:         mem2,
					Type:       RelationTemporal,
					Strength:   0.7,
					Reason:     "Temporal connection detected",
					Confidence: 0.8,
				})
			}

			// Check for category relationships
			if g.inSameCategory(mem1, mem2) {
				suggestions = append(suggestions, RelationSuggestion{
					From:       mem1,
					To:         mem2,
					Type:       RelationCategory,
					Strength:   0.6,
					Reason:     "Same category items",
					Confidence: 0.9,
				})
			}

			if len(suggestions) >= limit {
				break
			}
		}
		if len(suggestions) >= limit {
			break
		}
	}

	// Sort by confidence
	sort.Slice(suggestions, func(i, j int) bool {
		return suggestions[i].Confidence > suggestions[j].Confidence
	})

	return suggestions, nil
}

// BuildEntityRelationships analyzes shared entities and creates appropriate relationships.
func (g *Graph) BuildEntityRelationships(ctx context.Context) error {
	// Get all active memories
	memories, err := g.store.ListRecent(ctx, 1000)
	if err != nil {
		return fmt.Errorf("list memories: %w", err)
	}

	// Build entity index
	entityIndex := make(map[string][]*Memory)
	for _, mem := range memories {
		for _, entity := range mem.Entities {
			key := fmt.Sprintf("%s:%s", entity.Type, entity.Name)
			entityIndex[key] = append(entityIndex[key], mem)
		}
	}

	// Create relationships based on shared entities
	var batchParams []sqlc.CreateRelationsBatchParams
	processedPairs := make(map[string]bool)

	for entityKey, mems := range entityIndex {
		// Skip if only one memory has this entity
		if len(mems) < 2 {
			continue
		}

		// Extract entity type from key
		var entityType string
		if idx := strings.Index(entityKey, ":"); idx > 0 {
			entityType = entityKey[:idx]
		}

		// Create relationships between all memories sharing this entity
		for i := 0; i < len(mems); i++ {
			for j := i + 1; j < len(mems); j++ {
				// Create pair key to avoid duplicates
				mem1ID := uuid.UUID(mems[i].ID.Bytes).String()
				mem2ID := uuid.UUID(mems[j].ID.Bytes).String()
				pairKey := fmt.Sprintf("%s-%s", mem1ID, mem2ID)
				reversePairKey := fmt.Sprintf("%s-%s", mem2ID, mem1ID)

				if processedPairs[pairKey] || processedPairs[reversePairKey] {
					continue
				}
				processedPairs[pairKey] = true

				// Determine relationship type based on entity type
				relType := g.getRelationTypeForEntity(entityType)

				// Calculate strength based on entity importance and overlap
				strength := g.calculateEntityRelationStrength(mems[i], mems[j], entityType)

				// Create bidirectional relationships
				metadata, _ := json.Marshal(map[string]interface{}{
					"shared_entity": entityKey,
					"entity_type":   entityType,
				})

				// Forward relation
				batchParams = append(batchParams, sqlc.CreateRelationsBatchParams{
					FromID:   mems[i].ID,
					ToID:     mems[j].ID,
					Type:     sqlc.RelationType(relType),
					Strength: pgtype.Float4{Float32: strength, Valid: true},
					Metadata: metadata,
				})

				// Reverse relation
				batchParams = append(batchParams, sqlc.CreateRelationsBatchParams{
					FromID:   mems[j].ID,
					ToID:     mems[i].ID,
					Type:     sqlc.RelationType(relType),
					Strength: pgtype.Float4{Float32: strength, Valid: true},
					Metadata: metadata,
				})
			}
		}
	}

	// Batch insert all relations
	if len(batchParams) > 0 {
		if _, err := g.store.queries.CreateRelationsBatch(ctx, batchParams); err != nil {
			return fmt.Errorf("batch create relations: %w", err)
		}
	}

	return nil
}

// getRelationTypeForEntity maps entity types to relationship types.
func (g *Graph) getRelationTypeForEntity(entityType string) RelationType {
	switch entityType {
	case "activity":
		return RelationRelatedActivity
	case "person":
		return RelationSharedEntity
	case "place":
		return RelationSharedEntity
	case "thing":
		return RelationCategory
	case "concept":
		return RelationCategory
	case "time":
		return RelationTemporal
	default:
		return RelationSimilar
	}
}

// calculateEntityRelationStrength calculates relationship strength based on entity overlap.
func (g *Graph) calculateEntityRelationStrength(mem1, mem2 *Memory, sharedEntityType string) float32 {
	// Higher strength for certain entity types
	var strength float32
	switch sharedEntityType {
	case "person":
		strength = 0.8 // Strong connection for shared people
	case "activity":
		strength = 0.7 // Strong connection for shared activities
	case "place":
		strength = 0.6 // Moderate connection for shared places
	case "time":
		strength = 0.5 // Moderate connection for temporal relations
	default:
		strength = 0.4 // Base connection for other types
	}

	// Bonus for multiple shared entities
	sharedCount := 0
	entityMap1 := make(map[string]bool)
	for _, e := range mem1.Entities {
		entityMap1[fmt.Sprintf("%s:%s", e.Type, e.Name)] = true
	}

	for _, e := range mem2.Entities {
		if entityMap1[fmt.Sprintf("%s:%s", e.Type, e.Name)] {
			sharedCount++
		}
	}

	// Increase strength based on shared entity count
	if sharedCount > 1 {
		strength += float32(sharedCount-1) * 0.1
		if strength > 1.0 {
			strength = 1.0
		}
	}

	// Factor in memory importance (confidence)
	avgConfidence := (mem1.Confidence + mem2.Confidence) / 2
	strength = strength * (0.7 + 0.3*avgConfidence)

	return strength
}

// Helper methods

func (g *Graph) calculateSimilarity(content1, content2 string) float32 {
	// Simple character overlap similarity (would use better algorithm in production)
	chars1 := strings.Split(content1, "")
	chars2 := strings.Split(content2, "")

	common := 0
	for _, c1 := range chars1 {
		for _, c2 := range chars2 {
			if c1 == c2 {
				common++
				break
			}
		}
	}

	maxLen := len(chars1)
	if len(chars2) > maxLen {
		maxLen = len(chars2)
	}

	return float32(common) / float32(maxLen)
}

func (g *Graph) hasTemporalRelation(mem1, mem2 *Memory) bool {
	// Check if memories mention time-related content
	timeKeywords := []string{"時間", "點", "週", "每", "morning", "evening", "daily", "weekly"}

	hasTime1 := false
	hasTime2 := false

	for _, keyword := range timeKeywords {
		if strings.Contains(mem1.Content, keyword) {
			hasTime1 = true
		}
		if strings.Contains(mem2.Content, keyword) {
			hasTime2 = true
		}
	}

	return hasTime1 && hasTime2
}

func (g *Graph) inSameCategory(mem1, mem2 *Memory) bool {
	// Simple category detection
	if mem1.Type == mem2.Type {
		return true
	}

	// Check for common category keywords
	beverageKeywords := []string{"咖啡", "茶", "coffee", "tea", "喝", "drink"}

	mem1IsBeverage := false
	mem2IsBeverage := false

	for _, keyword := range beverageKeywords {
		if strings.Contains(strings.ToLower(mem1.Content), keyword) {
			mem1IsBeverage = true
		}
		if strings.Contains(strings.ToLower(mem2.Content), keyword) {
			mem2IsBeverage = true
		}
	}

	return mem1IsBeverage && mem2IsBeverage
}

// NetworkView represents a memory and its connections.
type NetworkView struct {
	Central *Memory
	Nodes   map[string]*NetworkNode
	Edges   []NetworkEdge
}

// NetworkNode represents a memory in the network.
type NetworkNode struct {
	Memory *Memory
	Depth  int
}

// NetworkEdge represents a connection between memories.
type NetworkEdge struct {
	From     string
	To       string
	Type     string
	Strength float32
}

// RelationSuggestion represents a suggested relationship.
type RelationSuggestion struct {
	From       *Memory
	To         *Memory
	Type       RelationType
	Strength   float32
	Reason     string
	Confidence float32
}
