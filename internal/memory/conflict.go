package memory

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/koopa0/assistant-go/internal/platform/logger"
)

// ConflictDetector detects and suggests resolutions for memory conflicts
type ConflictDetector struct {
	store    *Store
	aiClient ai.Client
	logger   logger.Logger
}

// NewConflictDetector creates a new conflict detector
func NewConflictDetector(store *Store, aiClient ai.Client, log logger.Logger) *ConflictDetector {
	return &ConflictDetector{
		store:    store,
		aiClient: aiClient,
		logger:   log.WithComponent("memory.conflict"),
	}
}

// ConflictSet represents a set of conflicting memories
type ConflictSet struct {
	Topic      string
	Type       string // "negation", "duplicate", "contradiction"
	Memories   []*Memory
	Resolution ConflictResolution
}

// ConflictResolution suggests how to resolve a conflict
type ConflictResolution struct {
	Action      string // "keep_newest", "delete_old", "merge"
	ToDelete    []pgtype.UUID
	ToKeep      *Memory
	Explanation string
}

// SearchWithConflictDetection searches for memories and detects conflicts
func (s *Service) SearchWithConflictDetection(ctx context.Context, query string, limit int) ([]*Memory, []ConflictSet, error) {
	// Regular search
	memories, err := s.Search(ctx, query, limit)
	if err != nil {
		return nil, nil, err
	}

	// Detect conflicts
	detector := NewConflictDetector(s.store, s.aiService, s.logger)
	conflicts := detector.DetectConflicts(ctx, memories)

	return memories, conflicts, nil
}

// ResolveConflicts resolves detected conflicts
func (s *Service) ResolveConflicts(ctx context.Context, conflicts []ConflictSet) error {
	if len(conflicts) == 0 {
		return nil
	}

	detector := NewConflictDetector(s.store, s.aiService, s.logger)
	return detector.ResolveConflicts(ctx, conflicts)
}

// AutoResolveConflicts automatically resolves conflicts when found
func (s *Service) AutoResolveConflicts(ctx context.Context) error {
	s.logger.Info("Starting automatic conflict resolution")

	// Search for all preference memories
	query := Query{
		Types: []MemoryType{MemoryTypePreference},
		Limit: 100,
	}

	memories, err := s.store.Search(ctx, query)
	if err != nil {
		return fmt.Errorf("search preferences: %w", err)
	}

	// Detect and resolve conflicts
	detector := NewConflictDetector(s.store, s.aiService, s.logger)
	conflicts := detector.DetectConflicts(ctx, memories)

	if len(conflicts) > 0 {
		s.logger.Info("Found conflict sets", "count", len(conflicts))
		return detector.ResolveConflicts(ctx, conflicts)
	}

	s.logger.Debug("No conflicts found")
	return nil
}

// DetectConflicts finds conflicting memories related to a query
func (cd *ConflictDetector) DetectConflicts(ctx context.Context, memories []*Memory) []ConflictSet {
	if len(memories) < 2 {
		return nil
	}

	// Group by topic
	topicGroups := make(map[string][]*Memory)
	for _, mem := range memories {
		topics := cd.extractTopicsFromMemory(ctx, mem)
		for _, topic := range topics {
			topicGroups[topic] = append(topicGroups[topic], mem)
		}
	}

	// Find conflicts within each topic group
	var conflicts []ConflictSet
	for topic, group := range topicGroups {
		if len(group) < 2 {
			continue
		}

		// Check for conflicts
		conflictSet := ConflictSet{
			Topic:    topic,
			Memories: group,
		}

		// Look for negation patterns
		hasPositive := false
		hasNegative := false

		for _, mem := range group {
			if isNegative(mem.Content) {
				hasNegative = true
			} else {
				hasPositive = true
			}
		}

		if hasPositive && hasNegative {
			conflictSet.Type = "negation"
			conflictSet.Resolution = cd.suggestResolution(group)
			conflicts = append(conflicts, conflictSet)
		}
	}

	return conflicts
}

// suggestResolution suggests how to resolve conflicts
func (cd *ConflictDetector) suggestResolution(memories []*Memory) ConflictResolution {
	if len(memories) == 0 {
		return ConflictResolution{}
	}

	// Score each memory based on multiple factors
	type scoredMemory struct {
		memory *Memory
		score  float32
	}

	scored := make([]scoredMemory, len(memories))
	now := time.Now()

	for i, mem := range memories {
		score := float32(0)

		// 1. Confidence score (0-1, weight: 0.3)
		score += mem.Confidence * 0.3

		// 2. Access frequency (normalized, weight: 0.2)
		maxAccess := int32(0)
		for _, m := range memories {
			if m.AccessCount > maxAccess {
				maxAccess = m.AccessCount
			}
		}
		if maxAccess > 0 {
			score += float32(mem.AccessCount) / float32(maxAccess) * 0.2
		}

		// 3. Recency (weight: 0.3)
		age := now.Sub(mem.UpdatedAt)
		if age < 24*time.Hour {
			score += 0.3
		} else if age < 7*24*time.Hour {
			score += 0.2
		} else if age < 30*24*time.Hour {
			score += 0.1
		}

		// 4. Completeness (weight: 0.2)
		completeness := float32(0)
		if len(mem.Entities) > 0 {
			completeness += 0.5
		}
		if len(mem.Keywords) > 0 {
			completeness += 0.3
		}
		if mem.Context.OccurredAt != nil || mem.Context.Location != nil {
			completeness += 0.2
		}
		score += completeness * 0.2

		scored[i] = scoredMemory{memory: mem, score: score}
	}

	// Find the best memory
	best := scored[0]
	for _, sm := range scored[1:] {
		if sm.score > best.score {
			best = sm
		}
	}

	// Mark all others for deletion
	var toDelete []pgtype.UUID
	for _, mem := range memories {
		if mem.ID != best.memory.ID {
			toDelete = append(toDelete, mem.ID)
		}
	}

	// Generate explanation
	explanation := fmt.Sprintf("保留記憶：%s (信心度: %.1f%%, 存取次數: %d, 完整度: 高)",
		best.memory.Content,
		best.memory.Confidence*100,
		best.memory.AccessCount,
	)

	return ConflictResolution{
		Action:      "keep_best",
		ToDelete:    toDelete,
		ToKeep:      best.memory,
		Explanation: explanation,
	}
}

// IsConflicting checks if two memories conflict with each other.
func (cd *ConflictDetector) IsConflicting(mem1, mem2 *Memory) bool {
	// Simple conflict detection based on negation patterns
	content1 := strings.ToLower(mem1.Content)
	content2 := strings.ToLower(mem2.Content)

	// Check if one negates the other
	negationPairs := [][]string{
		{"喜歡", "不喜歡"},
		{"like", "don't like"},
		{"要", "不要"},
		{"can", "cannot"},
		{"是", "不是"},
		{"有", "沒有"},
	}

	for _, pair := range negationPairs {
		if (strings.Contains(content1, pair[0]) && strings.Contains(content2, pair[1])) ||
			(strings.Contains(content1, pair[1]) && strings.Contains(content2, pair[0])) {
			// Also check if they're about the same topic
			for _, keyword1 := range mem1.Keywords {
				for _, keyword2 := range mem2.Keywords {
					if keyword1 == keyword2 {
						return true
					}
				}
			}
		}
	}

	return false
}

// ResolveConflicts executes the suggested resolutions
func (cd *ConflictDetector) ResolveConflicts(ctx context.Context, conflicts []ConflictSet) error {
	// Collect all IDs to delete
	var idsToDelete []pgtype.UUID
	for _, conflict := range conflicts {
		idsToDelete = append(idsToDelete, conflict.Resolution.ToDelete...)
	}

	// Batch delete if there are any IDs
	if len(idsToDelete) > 0 {
		if err := cd.store.DeactivateMany(ctx, idsToDelete); err != nil {
			return fmt.Errorf("batch delete conflicts: %w", err)
		}
	}

	return nil
}

// GenerateConflictReport creates a human-readable conflict report
func GenerateConflictReport(conflicts []ConflictSet) string {
	if len(conflicts) == 0 {
		return ""
	}

	var report strings.Builder
	report.WriteString("\n💡 發現記憶衝突：\n")

	for _, conflict := range conflicts {
		report.WriteString(fmt.Sprintf("\n📌 關於「%s」的衝突：\n", conflict.Topic))

		for _, mem := range conflict.Memories {
			report.WriteString(fmt.Sprintf("  - %s (建立於 %s)\n",
				mem.Content,
				mem.CreatedAt.Format("2006-01-02 15:04")))
		}

		report.WriteString(fmt.Sprintf("  💭 建議：%s\n", conflict.Resolution.Explanation))
	}

	report.WriteString("\n❓ 需要我幫您清理這些衝突的記憶嗎？")

	return report.String()
}

// extractTopicsFromMemory extracts topics from memory's keywords or entities
func (cd *ConflictDetector) extractTopicsFromMemory(ctx context.Context, mem *Memory) []string {
	// First try: Use keywords if available (extracted during fact extraction)
	if len(mem.Keywords) > 0 {
		return mem.Keywords
	}

	// Second try: Use entities if available
	if len(mem.Entities) > 0 {
		topics := make([]string, 0, len(mem.Entities))
		for _, entity := range mem.Entities {
			topics = append(topics, entity.Name)
		}
		return topics
	}

	// Fallback to basic extraction from content (no LLM call needed)
	return extractBasicTopics(mem.Content)
}

// extractBasicTopics extracts topics without hardcoded keywords
func extractBasicTopics(content string) []string {
	// Extract noun phrases and key terms
	var topics []string
	words := strings.Fields(content)

	// Look for nouns and noun phrases
	for i, word := range words {
		// Skip common particles and verbs in Chinese
		skipWords := []string{"我", "的", "了", "嗎", "是", "有", "在", "和", "與", "或", "也", "都", "很", "非常", "不", "沒"}
		skip := false
		for _, sw := range skipWords {
			if word == sw {
				skip = true
				break
			}
		}

		if !skip && len(word) >= 6 { // At least 2 Chinese characters
			topics = append(topics, word)

			// Try to capture compound nouns
			if i+1 < len(words) && !contains(skipWords, words[i+1]) {
				compound := word + words[i+1]
				if len(compound) <= 15 { // Reasonable length for compound terms
					topics = append(topics, compound)
				}
			}
		}
	}

	// Deduplicate
	seen := make(map[string]bool)
	unique := []string{}
	for _, topic := range topics {
		if !seen[topic] {
			seen[topic] = true
			unique = append(unique, topic)
		}
	}

	return unique
}

func isNegative(content string) bool {
	negativePatterns := []string{
		"不喜歡", "不喝", "不愛", "討厭", "不要",
		"don't", "dislike", "hate", "never",
	}

	lowerContent := strings.ToLower(content)
	for _, pattern := range negativePatterns {
		if strings.Contains(lowerContent, pattern) {
			return true
		}
	}
	return false
}
