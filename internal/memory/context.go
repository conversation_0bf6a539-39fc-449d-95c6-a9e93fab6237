// Package memory implements context building for LLM prompts.
package memory

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/google/uuid"
)

// ContextBuilder creates structured context from memory search results.
type ContextBuilder struct {
	searcher  *SmartSearcher
	conflict  *ConflictDetector
	templates *TemplateManager
}

// NewContextBuilder creates a new context builder.
func NewContextBuilder(searcher *SmartSearcher, conflict *ConflictDetector) *ContextBuilder {
	return &ContextBuilder{
		searcher:  searcher,
		conflict:  conflict,
		templates: NewTemplateManager(),
	}
}

// StructuredContext represents organized memory context for LLM.
type StructuredContext struct {
	DirectMatches    []MemoryContext   `json:"direct_matches"`
	RelatedMemories  []MemoryContext   `json:"related_memories"`
	Conflicts        []ConflictContext `json:"conflicts,omitempty"`
	Timeline         []TimelineEntry   `json:"timeline,omitempty"`
	Summary          string            `json:"summary"`
	RelevantEntities []EntityContext   `json:"entities,omitempty"`
}

// MemoryContext represents a memory in the context.
type MemoryContext struct {
	ID           string            `json:"id"`
	Content      string            `json:"content"`
	Type         string            `json:"type"`
	Confidence   float32           `json:"confidence"`
	CreatedAt    string            `json:"created_at"`
	Relation     string            `json:"relation,omitempty"`
	RelationFrom string            `json:"relation_from,omitempty"`
	Keywords     []string          `json:"keywords,omitempty"`
	Metadata     map[string]string `json:"metadata,omitempty"`
}

// ConflictContext represents conflicting information.
type ConflictContext struct {
	Topic      string          `json:"topic"`
	Statements []ConflictEntry `json:"statements"`
}

// ConflictEntry represents one side of a conflict.
type ConflictEntry struct {
	Content   string `json:"content"`
	CreatedAt string `json:"created_at"`
	Status    string `json:"status"` // "current" or "outdated"
}

// TimelineEntry represents a memory in chronological context.
type TimelineEntry struct {
	Date    string `json:"date"`
	Content string `json:"content"`
	Type    string `json:"type"`
}

// EntityContext represents an important entity across memories.
type EntityContext struct {
	Name        string   `json:"name"`
	Type        string   `json:"type"`
	Occurrences int      `json:"occurrences"`
	Contexts    []string `json:"contexts"`
}

// BuildContext creates structured context from search results.
func (b *ContextBuilder) BuildContext(ctx context.Context, query string, limit int) (*StructuredContext, error) {
	b.searcher.store.logger.Info("[CONTEXT] BuildContext called", "query", query, "limit", limit)

	// Perform search with graph expansion
	options := DefaultSmartSearchOptions()
	results, err := b.searcher.Search(ctx, query, limit, options)
	if err != nil {
		return nil, fmt.Errorf("smart search failed: %w", err)
	}

	b.searcher.store.logger.Info("[CONTEXT] Search results", "totalResults", len(results))

	// Separate direct and expanded results
	var directMatches, relatedMemories []SearchResult
	for _, result := range results {
		switch result.Source {
		case SourceDirect:
			directMatches = append(directMatches, result)
		case SourceExpanded:
			relatedMemories = append(relatedMemories, result)
		}
	}

	// Build structured context
	context := &StructuredContext{
		DirectMatches:   b.convertToMemoryContexts(directMatches),
		RelatedMemories: b.convertToMemoryContexts(relatedMemories),
	}

	// Find and add conflicts
	conflicts := b.findConflicts(results)
	if len(conflicts) > 0 {
		context.Conflicts = conflicts
	}

	// Build timeline for temporal context
	context.Timeline = b.buildTimeline(results)

	// Extract relevant entities
	context.RelevantEntities = b.extractEntities(results)

	// Generate summary
	context.Summary = b.generateSummary(query, results)

	return context, nil
}

// convertToMemoryContexts converts search results to memory contexts.
func (b *ContextBuilder) convertToMemoryContexts(results []SearchResult) []MemoryContext {
	contexts := make([]MemoryContext, 0, len(results))

	for _, result := range results {
		mem := result.Memory
		mc := MemoryContext{
			ID:         uuid.UUID(mem.ID.Bytes).String(),
			Content:    mem.Content,
			Type:       string(mem.Type),
			Confidence: mem.Confidence,
			CreatedAt:  mem.CreatedAt.Format(time.RFC3339),
			Keywords:   mem.Keywords,
		}

		// Add relation info for expanded results
		if result.Source == SourceExpanded && result.ExpandedFrom != nil {
			mc.Relation = "similar_to"
			mc.RelationFrom = uuid.UUID(result.ExpandedFrom.Bytes).String()
		}

		// Add metadata
		if len(mem.Attributes) > 0 {
			mc.Metadata = make(map[string]string)
			for k, v := range mem.Attributes {
				mc.Metadata[k] = fmt.Sprintf("%v", v)
			}
		}

		contexts = append(contexts, mc)
	}

	return contexts
}

// findConflicts identifies conflicts in the results.
func (b *ContextBuilder) findConflicts(results []SearchResult) []ConflictContext {
	// Group memories by topic
	topicGroups := make(map[string][]*Memory)
	for _, result := range results {
		for _, keyword := range result.Memory.Keywords {
			topicGroups[keyword] = append(topicGroups[keyword], result.Memory)
		}
	}

	// Find conflicts within each topic
	var conflicts []ConflictContext
	for topic, memories := range topicGroups {
		if len(memories) < 2 {
			continue
		}

		// Use conflict detector to find conflicts
		conflictPairs := b.detectConflictsInGroup(memories)
		if len(conflictPairs) > 0 {
			conflict := ConflictContext{
				Topic:      topic,
				Statements: b.formatConflictPairs(conflictPairs),
			}
			conflicts = append(conflicts, conflict)
		}
	}

	return conflicts
}

// detectConflictsInGroup finds conflicts within a group of memories.
func (b *ContextBuilder) detectConflictsInGroup(memories []*Memory) [][]*Memory {
	var conflicts [][]*Memory

	for i := 0; i < len(memories); i++ {
		for j := i + 1; j < len(memories); j++ {
			if b.conflict != nil {
				// Simple conflict detection
				if b.conflict != nil && b.conflict.IsConflicting(memories[i], memories[j]) {
					conflicts = append(conflicts, []*Memory{memories[i], memories[j]})
				}
			} else {
				// Simple conflict detection based on content
				if b.searcher.areConflicting(memories[i], memories[j]) {
					conflicts = append(conflicts, []*Memory{memories[i], memories[j]})
				}
			}
		}
	}

	return conflicts
}

// formatConflictPairs formats conflict pairs into entries.
func (b *ContextBuilder) formatConflictPairs(pairs [][]*Memory) []ConflictEntry {
	entries := make([]ConflictEntry, 0)
	seen := make(map[string]bool)

	for _, pair := range pairs {
		for _, mem := range pair {
			key := uuid.UUID(mem.ID.Bytes).String()
			if seen[key] {
				continue
			}
			seen[key] = true

			status := "current"
			if len(pair) > 1 {
				// Mark older memory as outdated
				if mem.CreatedAt.Before(pair[0].CreatedAt) || mem.CreatedAt.Before(pair[1].CreatedAt) {
					status = "outdated"
				}
			}

			entries = append(entries, ConflictEntry{
				Content:   mem.Content,
				CreatedAt: mem.CreatedAt.Format(time.RFC3339),
				Status:    status,
			})
		}
	}

	// Sort by creation time (newest first)
	sort.Slice(entries, func(i, j int) bool {
		ti, _ := time.Parse(time.RFC3339, entries[i].CreatedAt)
		tj, _ := time.Parse(time.RFC3339, entries[j].CreatedAt)
		return ti.After(tj)
	})

	return entries
}

// buildTimeline creates a chronological view of memories.
func (b *ContextBuilder) buildTimeline(results []SearchResult) []TimelineEntry {
	// Sort by creation time
	sorted := make([]SearchResult, len(results))
	copy(sorted, results)
	sort.Slice(sorted, func(i, j int) bool {
		return sorted[i].Memory.CreatedAt.Before(sorted[j].Memory.CreatedAt)
	})

	// Create timeline entries
	entries := make([]TimelineEntry, 0, len(sorted))
	for _, result := range sorted {
		mem := result.Memory
		entries = append(entries, TimelineEntry{
			Date:    mem.CreatedAt.Format("2006-01-02"),
			Content: mem.Content,
			Type:    string(mem.Type),
		})
	}

	return entries
}

// extractEntities extracts important entities from results.
func (b *ContextBuilder) extractEntities(results []SearchResult) []EntityContext {
	entityMap := make(map[string]*EntityContext)

	for _, result := range results {
		mem := result.Memory
		for _, entity := range mem.Entities {
			key := fmt.Sprintf("%s:%s", entity.Type, entity.Name)

			if ec, exists := entityMap[key]; exists {
				ec.Occurrences++
				ec.Contexts = append(ec.Contexts, mem.Content)
			} else {
				entityMap[key] = &EntityContext{
					Name:        entity.Name,
					Type:        string(entity.Type),
					Occurrences: 1,
					Contexts:    []string{mem.Content},
				}
			}
		}
	}

	// Convert to slice and sort by occurrences
	entities := make([]EntityContext, 0, len(entityMap))
	for _, ec := range entityMap {
		// Limit contexts to avoid too much detail
		if len(ec.Contexts) > 3 {
			ec.Contexts = ec.Contexts[:3]
		}
		entities = append(entities, *ec)
	}

	sort.Slice(entities, func(i, j int) bool {
		return entities[i].Occurrences > entities[j].Occurrences
	})

	// Return top entities
	if len(entities) > 10 {
		entities = entities[:10]
	}

	return entities
}

// generateSummary creates a brief summary of the context.
func (b *ContextBuilder) generateSummary(query string, results []SearchResult) string {
	if len(results) == 0 {
		return "No relevant memories found for the query."
	}

	// Count by type
	typeCounts := make(map[MemoryType]int)
	for _, result := range results {
		typeCounts[result.Memory.Type]++
	}

	// Build summary
	parts := []string{
		fmt.Sprintf("Found %d relevant memories for query '%s'", len(results), query),
	}

	// Add type breakdown
	if len(typeCounts) > 0 {
		typeStrs := []string{}
		for typ, count := range typeCounts {
			typeStrs = append(typeStrs, fmt.Sprintf("%d %s", count, typ))
		}
		parts = append(parts, fmt.Sprintf("Types: %s", strings.Join(typeStrs, ", ")))
	}

	// Add time range
	if len(results) > 1 {
		earliest := results[0].Memory.CreatedAt
		latest := results[0].Memory.CreatedAt

		for _, result := range results[1:] {
			if result.Memory.CreatedAt.Before(earliest) {
				earliest = result.Memory.CreatedAt
			}
			if result.Memory.CreatedAt.After(latest) {
				latest = result.Memory.CreatedAt
			}
		}

		if !earliest.Equal(latest) {
			parts = append(parts, fmt.Sprintf("Time range: %s to %s",
				earliest.Format("2006-01-02"),
				latest.Format("2006-01-02")))
		}
	}

	return strings.Join(parts, ". ") + "."
}

// FormatForLLM converts structured context to LLM-friendly format.
func (b *ContextBuilder) FormatForLLM(ctx *StructuredContext) string {
	if ctx == nil {
		return ""
	}

	// Use template to render context
	result, err := b.templates.RenderContext(ctx)
	if err != nil {
		// Fallback to simple format
		b.searcher.store.logger.Warn("Failed to render template", "error", err)
		return b.formatSimple(ctx)
	}

	return result
}

// SetTemplate sets the active template for context rendering.
func (b *ContextBuilder) SetTemplate(name string) error {
	return b.templates.SetCurrentTemplate(name)
}

// RegisterCustomTemplate allows registration of custom templates.
func (b *ContextBuilder) RegisterCustomTemplate(name, template string) error {
	return b.templates.RegisterTemplate(name, template)
}

// formatSimple provides the original format as fallback.
func (b *ContextBuilder) formatSimple(ctx *StructuredContext) string {
	var sb strings.Builder

	// Summary
	sb.WriteString("<context>\n")
	sb.WriteString(fmt.Sprintf("<summary>%s</summary>\n\n", ctx.Summary))

	// Direct matches
	if len(ctx.DirectMatches) > 0 {
		sb.WriteString("<direct_matches>\n")
		for _, mem := range ctx.DirectMatches {
			sb.WriteString(fmt.Sprintf("  <memory id=\"%s\" type=\"%s\" confidence=\"%.2f\">%s</memory>\n",
				mem.ID, mem.Type, mem.Confidence, mem.Content))
		}
		sb.WriteString("</direct_matches>\n\n")
	}

	// Related memories
	if len(ctx.RelatedMemories) > 0 {
		sb.WriteString("<related_memories>\n")
		for _, mem := range ctx.RelatedMemories {
			relation := mem.Relation
			if relation == "" {
				relation = "related"
			}
			sb.WriteString(fmt.Sprintf("  <memory id=\"%s\" type=\"%s\" relation=\"%s\">%s</memory>\n",
				mem.ID, mem.Type, relation, mem.Content))
		}
		sb.WriteString("</related_memories>\n\n")
	}

	// Conflicts
	if len(ctx.Conflicts) > 0 {
		sb.WriteString("<conflicts>\n")
		for _, conflict := range ctx.Conflicts {
			sb.WriteString(fmt.Sprintf("  <conflict topic=\"%s\">\n", conflict.Topic))
			for _, stmt := range conflict.Statements {
				sb.WriteString(fmt.Sprintf("    <statement status=\"%s\" created=\"%s\">%s</statement>\n",
					stmt.Status, stmt.CreatedAt, stmt.Content))
			}
			sb.WriteString("  </conflict>\n")
		}
		sb.WriteString("</conflicts>\n\n")
	}

	// Key entities
	if len(ctx.RelevantEntities) > 0 {
		sb.WriteString("<entities>\n")
		for _, entity := range ctx.RelevantEntities {
			if entity.Occurrences > 1 {
				sb.WriteString(fmt.Sprintf("  <entity name=\"%s\" type=\"%s\" occurrences=\"%d\" />\n",
					entity.Name, entity.Type, entity.Occurrences))
			}
		}
		sb.WriteString("</entities>\n\n")
	}

	sb.WriteString("</context>")
	return sb.String()
}
