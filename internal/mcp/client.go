// Package mcp provides Model Context Protocol client implementation.
// This package enables integration with external MCP servers for enhanced tool capabilities.
package mcp

import (
	"context"
	"fmt"
	"os/exec"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/modelcontextprotocol/go-sdk/mcp"

	"github.com/koopa0/assistant-go/internal/platform/logger"
)

// Client manages connections to MCP servers.
type Client struct {
	// Core components
	mcpClient *mcp.Client
	sessions  map[string]*Session // serverName -> session
	mu        sync.RWMutex

	// Configuration
	config Config
	logger logger.Logger
}

// Session represents an active connection to an MCP server.
type Session struct {
	Server    ServerConfig
	Session   *mcp.ClientSession
	Transport mcp.Transport
	Connected bool
	LastPing  time.Time
}

// Config holds MCP client configuration.
type Config struct {
	ClientName    string         // Name of this client
	ClientVersion string         // Version of this client
	Servers       []ServerConfig // List of MCP servers to connect to
	Timeout       time.Duration  // Connection timeout
}

// ServerConfig defines an MCP server connection.
type ServerConfig struct {
	Name        string            // Unique server name
	Command     string            // Command to start server
	Args        []string          // Command arguments
	Env         map[string]string // Environment variables
	AutoConnect bool              // Connect automatically on startup
}

// NewClient creates a new MCP client.
func NewClient(cfg Config, log logger.Logger) (*Client, error) {
	if cfg.ClientName == "" {
		cfg.ClientName = "assistant-go"
	}
	if cfg.ClientVersion == "" {
		cfg.ClientVersion = "1.0.0"
	}
	if cfg.Timeout == 0 {
		cfg.Timeout = 30 * time.Second
	}

	// Create MCP client instance with new API
	implementation := &mcp.Implementation{
		Name:    cfg.ClientName,
		Version: cfg.ClientVersion,
	}

	clientOptions := &mcp.ClientOptions{
		// Add any client options if needed
	}

	mcpClient := mcp.NewClient(implementation, clientOptions)

	client := &Client{
		mcpClient: mcpClient,
		sessions:  make(map[string]*Session),
		config:    cfg,
		logger:    log.WithComponent("mcp"),
	}

	// Auto-connect to configured servers
	if err := client.initializeServers(context.Background()); err != nil {
		return nil, fmt.Errorf("initialize servers: %w", err)
	}

	return client, nil
}

// initializeServers connects to servers with AutoConnect enabled.
func (c *Client) initializeServers(ctx context.Context) error {
	for _, server := range c.config.Servers {
		if !server.AutoConnect {
			continue
		}

		if err := c.ConnectToServer(ctx, server.Name); err != nil {
			c.logger.Warn("failed to connect to server",
				"server", server.Name,
				"error", err)
			// Continue with other servers
		}
	}
	return nil
}

// ConnectToServer establishes connection to a specific MCP server.
func (c *Client) ConnectToServer(ctx context.Context, serverName string) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	// Check if already connected
	if session, exists := c.sessions[serverName]; exists && session.Connected {
		return nil
	}

	// Find server configuration
	var serverConfig *ServerConfig
	for _, srv := range c.config.Servers {
		if srv.Name == serverName {
			serverConfig = &srv
			break
		}
	}
	if serverConfig == nil {
		return fmt.Errorf("server %s not found in configuration", serverName)
	}

	// Validate command against whitelist for security
	if !isCommandAllowed(serverConfig.Command) {
		return fmt.Errorf("command %s is not in the allowed list", serverConfig.Command)
	}

	// Create command
	cmd := exec.Command(serverConfig.Command, serverConfig.Args...) // #nosec G204 - command is validated against whitelist above

	// Set environment variables
	for k, v := range serverConfig.Env {
		cmd.Env = append(cmd.Env, fmt.Sprintf("%s=%s", k, v))
	}

	// Create transport
	transport := mcp.NewCommandTransport(cmd)

	// Connect with timeout
	connectCtx, cancel := context.WithTimeout(ctx, c.config.Timeout)
	defer cancel()

	session, err := c.mcpClient.Connect(connectCtx, transport)
	if err != nil {
		return fmt.Errorf("connect to server %s: %w", serverName, err)
	}

	// Store session
	c.sessions[serverName] = &Session{
		Server:    *serverConfig,
		Session:   session,
		Transport: transport,
		Connected: true,
		LastPing:  time.Now(),
	}

	c.logger.Info("connected to MCP server",
		"server", serverName,
		"command", serverConfig.Command)

	return nil
}

// isCommandAllowed checks if a command is in the allowed list for security
func isCommandAllowed(command string) bool {
	// Extract the base command name
	baseCmd := filepath.Base(command)

	// List of allowed MCP server commands
	allowedCommands := []string{
		"mcp-server-filesystem",
		"mcp-server-github",
		"mcp-server-memory",
		"mcp-server-sqlite",
		"npx",
		"node",
		"python",
		"python3",
	}

	// Check if command is in allowed list
	for _, allowed := range allowedCommands {
		if baseCmd == allowed || strings.HasPrefix(baseCmd, allowed) {
			return true
		}
	}

	return strings.HasPrefix(baseCmd, "mcp-server-")
}

// GetSession returns the session for a specific server.
func (c *Client) GetSession(serverName string) (*mcp.ClientSession, error) {
	c.mu.RLock()
	defer c.mu.RUnlock()

	session, exists := c.sessions[serverName]
	if !exists {
		return nil, fmt.Errorf("server %s not connected", serverName)
	}

	if !session.Connected {
		return nil, fmt.Errorf("server %s is disconnected", serverName)
	}

	return session.Session, nil
}

// ListConnectedServers returns list of connected server names.
func (c *Client) ListConnectedServers() []string {
	c.mu.RLock()
	defer c.mu.RUnlock()

	var servers []string
	for name, session := range c.sessions {
		if session.Connected {
			servers = append(servers, name)
		}
	}
	return servers
}

// Disconnect closes connection to a specific server.
func (c *Client) Disconnect(serverName string) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	session, exists := c.sessions[serverName]
	if !exists {
		return nil // Already disconnected
	}

	// Close transport if it supports closing
	if closer, ok := session.Transport.(interface{ Close() error }); ok {
		if err := closer.Close(); err != nil {
			c.logger.Warn("error closing transport",
				"server", serverName,
				"error", err)
		}
	}

	session.Connected = false
	delete(c.sessions, serverName)

	c.logger.Info("disconnected from MCP server", "server", serverName)
	return nil
}

// Close disconnects from all servers.
func (c *Client) Close() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	for name := range c.sessions {
		c.mu.Unlock()
		if err := c.Disconnect(name); err != nil {
			c.logger.Warn("error disconnecting from server",
				"server", name,
				"error", err)
		}
		c.mu.Lock()
	}

	return nil
}

// Ping checks if a server is responsive.
func (c *Client) Ping(ctx context.Context, serverName string) error {
	session, err := c.GetSession(serverName)
	if err != nil {
		return err
	}

	// MCP has a Ping method
	err = session.Ping(ctx, &mcp.PingParams{})
	if err != nil {
		return fmt.Errorf("server not responsive: %w", err)
	}

	// Update last ping time
	c.mu.Lock()
	if s, exists := c.sessions[serverName]; exists {
		s.LastPing = time.Now()
	}
	c.mu.Unlock()

	return nil
}

// MonitorConnections periodically checks server connections.
func (c *Client) MonitorConnections(ctx context.Context, interval time.Duration) {
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			c.checkConnections(ctx)
		}
	}
}

// checkConnections verifies all server connections.
func (c *Client) checkConnections(ctx context.Context) {
	servers := c.ListConnectedServers()

	for _, server := range servers {
		if err := c.Ping(ctx, server); err != nil {
			c.logger.Warn("server ping failed",
				"server", server,
				"error", err)

			// Attempt reconnection
			c.mu.Lock()
			if session, exists := c.sessions[server]; exists {
				session.Connected = false
			}
			c.mu.Unlock()

			// Try to reconnect
			if err := c.ConnectToServer(ctx, server); err != nil {
				c.logger.Error("failed to reconnect to server",
					"server", server,
					"error", err)
			}
		}
	}
}
