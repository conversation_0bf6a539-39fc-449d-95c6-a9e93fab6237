package mcp

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/koopa0/assistant-go/internal/platform/logger"
	"github.com/stretchr/testify/assert"
)

// TestNewClient tests Client creation
func TestNewClient(t *testing.T) {
	tests := []struct {
		name    string
		config  Config
		wantErr bool
	}{
		{
			name: "valid config",
			config: Config{
				ClientName:    "test-client",
				ClientVersion: "1.0.0",
				Timeout:       30 * time.Second,
			},
			wantErr: false,
		},
		{
			name: "empty client name uses default",
			config: Config{
				ClientName:    "",
				ClientVersion: "1.0.0",
			},
			wantErr: false, // Should use default name
		},
		{
			name: "default timeout",
			config: Config{
				ClientName:    "test-client",
				ClientVersion: "1.0.0",
				Timeout:       0, // Should use default
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client, err := NewClient(tt.config, logger.NewConsoleLogger())
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, client)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, client)
			}
		})
	}
}

// TestServerConfig tests ServerConfig validation
func TestServerConfig(t *testing.T) {
	tests := []struct {
		name   string
		server ServerConfig
		valid  bool
	}{
		{
			name: "valid server",
			server: ServerConfig{
				Name:        "test-server",
				Command:     "node",
				Args:        []string{"server.js"},
				Env:         map[string]string{"NODE_ENV": "production"},
				AutoConnect: true,
			},
			valid: true,
		},
		{
			name: "minimal server",
			server: ServerConfig{
				Name:    "minimal",
				Command: "python",
				Args:    []string{"server.py"},
			},
			valid: true,
		},
		{
			name: "missing name",
			server: ServerConfig{
				Command: "node",
			},
			valid: false,
		},
		{
			name: "missing command",
			server: ServerConfig{
				Name: "test",
			},
			valid: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.valid {
				assert.NotEmpty(t, tt.server.Name)
				assert.NotEmpty(t, tt.server.Command)
			} else {
				// Validation would fail
				assert.True(t, tt.server.Name == "" || tt.server.Command == "")
			}
		})
	}
}

// TestSession tests Session management
func TestSession(t *testing.T) {
	session := &Session{
		Server: ServerConfig{
			Name:    "test-server",
			Command: "node",
		},
		Connected: false,
		LastPing:  time.Time{},
	}

	// Initial state
	assert.False(t, session.Connected)
	assert.True(t, session.LastPing.IsZero())

	// Update state
	session.Connected = true
	session.LastPing = time.Now()

	assert.True(t, session.Connected)
	assert.False(t, session.LastPing.IsZero())
}

// TestClientClose tests Client.Close
func TestClientClose(t *testing.T) {
	client := &Client{
		sessions: make(map[string]*Session),
		logger:   logger.NewConsoleLogger(),
		config:   Config{ClientName: "test"},
	}

	// Close should not panic
	err := client.Close()
	assert.NoError(t, err)
}

// TestListConnectedServers tests listing connected servers
func TestListConnectedServers(t *testing.T) {
	client := &Client{
		sessions: map[string]*Session{
			"server1": {Connected: true},
			"server2": {Connected: false},
			"server3": {Connected: true},
		},
		logger: logger.NewConsoleLogger(),
	}

	servers := client.ListConnectedServers()
	assert.Len(t, servers, 2)
	assert.Contains(t, servers, "server1")
	assert.Contains(t, servers, "server3")
	assert.NotContains(t, servers, "server2")
}

// TestConnectToServer tests connecting to a server
func TestConnectToServer(t *testing.T) {
	cfg := Config{
		ClientName:    "test-client",
		ClientVersion: "1.0.0",
		Servers: []ServerConfig{
			{
				Name:        "test-server",
				Command:     "echo",
				Args:        []string{"test"},
				AutoConnect: false, // Don't auto-connect
			},
		},
	}

	client, err := NewClient(cfg, logger.NewConsoleLogger())
	assert.NoError(t, err)
	assert.NotNil(t, client)

	// ConnectToServer will fail when trying to connect
	// because echo is not a real MCP server
	ctx := context.Background()
	err = client.ConnectToServer(ctx, "test-server")
	assert.Error(t, err)
}

// TestDisconnect tests disconnecting from a server
func TestDisconnect(t *testing.T) {
	client := &Client{
		sessions: map[string]*Session{
			"server1": {
				Connected: true,
				Server:    ServerConfig{Name: "server1"},
			},
		},
		logger: logger.NewConsoleLogger(),
	}

	// Disconnect existing server
	err := client.Disconnect("server1")
	assert.NoError(t, err)

	// Disconnect non-existent server should return nil (already disconnected)
	err = client.Disconnect("non-existent")
	assert.NoError(t, err)
}

// TestConfigDefaults tests default configuration values
func TestConfigDefaults(t *testing.T) {
	cfg := Config{
		ClientName:    "test",
		ClientVersion: "1.0.0",
	}

	// Test the provided values
	assert.Equal(t, "test", cfg.ClientName)
	assert.Equal(t, "1.0.0", cfg.ClientVersion)

	// When timeout is not set, it should use a default
	if cfg.Timeout == 0 {
		cfg.Timeout = 30 * time.Second
	}

	assert.Equal(t, 30*time.Second, cfg.Timeout)
}

// TestConcurrentAccess tests concurrent access to Client
func TestConcurrentAccess(t *testing.T) {
	client := &Client{
		sessions: make(map[string]*Session),
		logger:   logger.NewConsoleLogger(),
	}

	// Test concurrent reads and writes
	done := make(chan bool)
	for i := 0; i < 10; i++ {
		go func(id int) {
			// Write
			client.mu.Lock()
			client.sessions[fmt.Sprintf("server%d", id)] = &Session{
				Connected: true,
			}
			client.mu.Unlock()

			// Read
			client.mu.RLock()
			_ = len(client.sessions)
			client.mu.RUnlock()

			done <- true
		}(i)
	}

	// Wait for all goroutines
	for i := 0; i < 10; i++ {
		<-done
	}

	assert.Len(t, client.sessions, 10)
}

// TestDiscoverTools tests tool discovery (will fail without real server)
func TestDiscoverTools(t *testing.T) {
	client := &Client{
		sessions: make(map[string]*Session),
		logger:   logger.NewConsoleLogger(),
	}

	ctx := context.Background()
	tools, err := client.DiscoverTools(ctx)

	// Should return empty since no servers connected
	assert.NoError(t, err)
	assert.Empty(t, tools)
}
