package mcp

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/koopa0/assistant-go/internal/platform/logger"
	"github.com/modelcontextprotocol/go-sdk/mcp"
	"github.com/stretchr/testify/assert"
)

// TestNewTool tests Tool creation
func TestNewTool(t *testing.T) {
	client := &Client{
		sessions: make(map[string]*Session),
		logger:   logger.NewConsoleLogger(),
	}

	schema := &mcp.Tool{
		Name:        "test-tool",
		Description: "A test tool",
		InputSchema: nil, // Will test with nil schema for simplicity
	}

	tool := NewTool(client, "server1", schema)

	assert.NotNil(t, tool)
	assert.Equal(t, "server1", tool.serverName)
	assert.Equal(t, "test-tool", tool.toolName)
	assert.Equal(t, client, tool.client)
	assert.Equal(t, schema, tool.schema)
}

// TestToolName tests Tool.Name with server prefix
func TestToolName(t *testing.T) {
	tests := []struct {
		serverName string
		toolName   string
		expected   string
	}{
		{
			serverName: "server1",
			toolName:   "search",
			expected:   "server1.search",
		},
		{
			serverName: "my-server",
			toolName:   "calculate",
			expected:   "my-server.calculate",
		},
		{
			serverName: "server.with.dots",
			toolName:   "tool.with.dots",
			expected:   "server.with.dots.tool.with.dots",
		},
	}

	for _, tt := range tests {
		t.Run(tt.expected, func(t *testing.T) {
			tool := &Tool{
				serverName: tt.serverName,
				toolName:   tt.toolName,
			}
			assert.Equal(t, tt.expected, tool.Name())
		})
	}
}

// TestToolDescription tests Tool.Description
func TestToolDescription(t *testing.T) {
	tests := []struct {
		name        string
		description string
	}{
		{
			name:        "simple description",
			description: "This tool does something",
		},
		{
			name:        "empty description",
			description: "",
		},
		{
			name:        "long description",
			description: "This is a very long description that explains in detail what the tool does and how it works",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tool := &Tool{
				schema: &mcp.Tool{
					Description: tt.description,
				},
			}
			assert.Equal(t, tt.description, tool.Description())
		})
	}
}

// TestToolExecute tests Tool.Execute
func TestToolExecute(t *testing.T) {
	// Create a mock client with no sessions
	client := &Client{
		sessions: make(map[string]*Session),
		logger:   logger.NewConsoleLogger(),
	}

	tool := &Tool{
		serverName: "test-server",
		toolName:   "test-tool",
		client:     client,
		schema: &mcp.Tool{
			Name: "test-tool",
		},
	}

	// Execute should fail because no session exists
	ctx := context.Background()
	result, err := tool.Execute(ctx, map[string]any{"test": "input"})

	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "get session")
}

// TestToolInputSchema tests Tool.InputSchema
func TestToolInputSchema(t *testing.T) {
	// Test with nil schema
	tool1 := &Tool{
		schema: &mcp.Tool{
			Name:        "test1",
			InputSchema: nil,
		},
	}

	schema1 := tool1.InputSchema()
	assert.Equal(t, json.RawMessage("{}"), schema1)

	// Test conversion of valid schema
	// Since we can't create jsonschema.Schema directly in tests,
	// we'll test the general behavior
	tool2 := &Tool{
		schema: &mcp.Tool{
			Name:        "test2",
			InputSchema: nil, // Would normally have a schema object
		},
	}

	schema2 := tool2.InputSchema()
	assert.NotNil(t, schema2)
}

// TestInputSchemaWithNilSchema tests InputSchema with nil schema
func TestInputSchemaWithNilSchema(t *testing.T) {
	tool := &Tool{
		schema: &mcp.Tool{
			Name:        "test",
			InputSchema: nil,
		},
	}

	schema := tool.InputSchema()
	assert.Equal(t, json.RawMessage("{}"), schema)
}

// TestToolExecuteEdgeCases tests Tool execution edge cases
func TestToolExecuteEdgeCases(t *testing.T) {
	// Test with disconnected server
	client1 := &Client{
		sessions: map[string]*Session{
			"test-server": {
				Connected: false, // Disconnected
				Server: ServerConfig{
					Name: "test-server",
				},
			},
		},
		logger: logger.NewConsoleLogger(),
	}

	tool1 := &Tool{
		serverName: "test-server",
		toolName:   "test",
		client:     client1,
		schema:     &mcp.Tool{Name: "test"},
	}

	ctx := context.Background()
	_, err := tool1.Execute(ctx, map[string]any{"test": "input"})
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "disconnected")

	// Test with non-existent server
	client2 := &Client{
		sessions: make(map[string]*Session),
		logger:   logger.NewConsoleLogger(),
	}

	tool2 := &Tool{
		serverName: "non-existent",
		toolName:   "test",
		client:     client2,
		schema:     &mcp.Tool{Name: "test"},
	}

	_, err = tool2.Execute(ctx, map[string]any{"test": "input"})
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not connected")
}

// TestToolValidation tests various tool configurations
func TestToolValidation(t *testing.T) {
	tests := []struct {
		name   string
		tool   *Tool
		valid  bool
		reason string
	}{
		{
			name: "valid tool",
			tool: &Tool{
				serverName: "server",
				toolName:   "tool",
				client:     &Client{},
				schema:     &mcp.Tool{Name: "tool"},
			},
			valid:  true,
			reason: "All required fields present",
		},
		{
			name: "missing client",
			tool: &Tool{
				serverName: "server",
				toolName:   "tool",
				client:     nil,
				schema:     &mcp.Tool{Name: "tool"},
			},
			valid:  false,
			reason: "Client is required for execution",
		},
		{
			name: "missing schema",
			tool: &Tool{
				serverName: "server",
				toolName:   "tool",
				client:     &Client{},
				schema:     nil,
			},
			valid:  false,
			reason: "Schema is required for tool definition",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.valid {
				assert.NotNil(t, tt.tool.client)
				assert.NotNil(t, tt.tool.schema)
			} else {
				assert.True(t, tt.tool.client == nil || tt.tool.schema == nil)
			}
		})
	}
}
