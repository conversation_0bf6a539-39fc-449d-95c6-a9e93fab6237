# Model Context Protocol (MCP) Integration

## Overview

The Model Context Protocol (MCP) is a standardized protocol for integrating external tools and data sources with AI models. This package provides a Go implementation that allows the assistant to dynamically extend its capabilities through external MCP servers.

## Architecture

```
┌─────────────────────┐
│   Assistant Core    │
│  (ai.Client/Service)│
└──────────┬──────────┘
           │
           │ uses
           ▼
┌─────────────────────┐      stdio/websocket      ┌─────────────────────┐
│    MCP Integration  │◄──────────────────────────►│   MCP Servers       │
│  (mcp.Integration)  │                            │ (External Processes)│
└─────────────────────┘                            └─────────────────────┘
           │
           │ provides
           ▼
┌─────────────────────┐
│    Tool Manager     │
│  (tool.Manager)     │
└─────────────────────┘
```

## Key Components

### 1. MCP Client (`client.go`)
- Manages communication with MCP servers via stdio or WebSocket
- Handles protocol negotiation and capability discovery
- Implements automatic reconnection and error recovery

### 2. Tool Bridge (`tool.go`)
- Converts MCP tool definitions to internal tool format
- Marshals/unmarshals parameters between formats
- Handles tool execution lifecycle

### 3. Resource Manager (`resource.go`)
- Provides access to external data sources
- Implements caching for frequently accessed resources
- Supports various resource types (files, APIs, databases)

### 4. Integration Layer (`integration.go`)
- Orchestrates multiple MCP servers
- Manages server lifecycle (start/stop)
- Provides unified interface to the assistant

## Configuration

MCP servers are configured via environment variables:

```yaml
# Example configuration
MCP_SERVERS: |
  {
    "servers": {
      "filesystem": {
        "command": "mcp-server-filesystem",
        "args": ["--root", "/path/to/data"]
      },
      "github": {
        "command": "mcp-server-github",
        "env": {
          "GITHUB_TOKEN": "${GITHUB_TOKEN}"
        }
      }
    }
  }
```

## Usage Example

```go
// Create MCP integration
mcpIntegration := mcp.NewIntegration(logger)

// Initialize from configuration
config := &mcp.Config{
    Servers: map[string]ServerConfig{
        "filesystem": {
            Command: "mcp-server-filesystem",
            Args:    []string{"--root", "/data"},
        },
    },
}

// Start MCP servers
if err := mcpIntegration.Initialize(ctx, config); err != nil {
    log.Fatal(err)
}

// Register tools with tool manager
toolManager := tool.NewManager()
tools, _ := mcpIntegration.GetTools(ctx)
for _, t := range tools {
    toolManager.Register(t)
}

// Use in assistant
assistant := assistant.New(
    aiClient,
    assistant.WithTools(toolManager),
)
```

## Protocol Details

### Message Format
MCP uses JSON-RPC 2.0 over stdio or WebSocket:

```json
{
  "jsonrpc": "2.0",
  "method": "tools/call",
  "params": {
    "name": "read_file",
    "arguments": {
      "path": "/example.txt"
    }
  },
  "id": 1
}
```

### Capability Negotiation
Servers advertise their capabilities during initialization:

```json
{
  "capabilities": {
    "tools": true,
    "resources": true,
    "prompts": false
  }
}
```

## Error Handling

The integration implements comprehensive error handling:

1. **Connection Errors**: Automatic retry with exponential backoff
2. **Protocol Errors**: Graceful degradation, disabling faulty servers
3. **Execution Errors**: Returned to the AI model for handling

## Security Considerations

1. **Process Isolation**: Each MCP server runs in a separate process
2. **Resource Limits**: CPU and memory limits can be configured
3. **Access Control**: Servers only access explicitly allowed resources
4. **Input Validation**: All inputs are validated before execution

## Extending MCP

To add a new MCP server:

1. Implement the MCP protocol specification
2. Define tools/resources in the server
3. Add server configuration
4. The integration will automatically discover and use it

## Common MCP Servers

- `mcp-server-filesystem`: File system access
- `mcp-server-github`: GitHub API integration
- `mcp-server-sqlite`: SQLite database access
- `mcp-server-playwright`: Web browser automation

## Debugging

Enable debug logging:

```bash
export LOG_LEVEL=debug
export MCP_DEBUG=true
```

View MCP communication:
```bash
tail -f ~/.assistant/logs/mcp.log
```

## References

- [MCP Specification](https://modelcontextprotocol.io/docs)
- [MCP SDK](https://github.com/modelcontextprotocol/sdk)
- [Example Servers](https://github.com/modelcontextprotocol/servers)