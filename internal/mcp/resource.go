package mcp

import (
	"context"
	"fmt"
	"sync"

	"github.com/modelcontextprotocol/go-sdk/mcp"
)

// Resource represents an MCP resource with server association.
type Resource struct {
	ServerName  string `json:"server_name"`
	URI         string `json:"uri"`
	Name        string `json:"name"`
	MimeType    string `json:"mime_type"`
	Description string `json:"description,omitempty"`
}

// ResourceContent represents the actual content of a resource.
type ResourceContent struct {
	URI      string `json:"uri"`
	MimeType string `json:"mime_type"`
	Text     string `json:"text,omitempty"`
	Blob     []byte `json:"blob,omitempty"`
}

// ResourceManager handles resource operations across MCP servers.
type ResourceManager struct {
	client *Client
	cache  map[string]*ResourceContent // URI -> content cache
	mu     sync.RWMutex
}

// ListResources lists all resources from connected servers.
func (c *Client) ListResources(ctx context.Context) ([]Resource, error) {
	var resources []Resource

	c.mu.RLock()
	defer c.mu.RUnlock()

	for serverName, session := range c.sessions {
		if !session.Connected {
			continue
		}

		// List resources from this server
		result, err := session.Session.ListResources(ctx, &mcp.ListResourcesParams{})
		if err != nil {
			c.logger.Warn("failed to list resources from server",
				"server", serverName,
				"error", err)
			continue
		}

		// Convert to our resource type
		for _, res := range result.Resources {
			resources = append(resources, Resource{
				ServerName: serverName,
				URI:        res.URI,
				Name:       res.Name,
				MimeType:   res.MIMEType,
			})
		}
	}

	return resources, nil
}

// ReadResource reads a specific resource.
func (c *Client) ReadResource(ctx context.Context, serverName, uri string) (*mcp.Resource, error) {
	session, err := c.GetSession(serverName)
	if err != nil {
		return nil, err
	}

	params := &mcp.ReadResourceParams{
		URI: uri,
	}
	result, err := session.ReadResource(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("read resource %s: %w", uri, err)
	}

	// Return first resource content if available
	if len(result.Contents) > 0 {
		return &mcp.Resource{
			URI:      result.Contents[0].URI,
			Name:     "", // Name not available in ResourceContents
			MIMEType: result.Contents[0].MIMEType,
		}, nil
	}
	return nil, fmt.Errorf("no resource content available")
}

// Note: Subscribe/Unsubscribe methods would need to be implemented
// based on the MCP SDK capabilities for resource subscriptions

// GetResourceTemplates lists available resource templates.
func (c *Client) GetResourceTemplates(ctx context.Context, serverName string) ([]*mcp.ResourceTemplate, error) {
	session, err := c.GetSession(serverName)
	if err != nil {
		return nil, err
	}

	result, err := session.ListResourceTemplates(ctx, &mcp.ListResourceTemplatesParams{})
	if err != nil {
		return nil, fmt.Errorf("list resource templates: %w", err)
	}

	return result.ResourceTemplates, nil
}

// NewResourceManager creates a new resource manager.
func NewResourceManager(client *Client) *ResourceManager {
	return &ResourceManager{
		client: client,
		cache:  make(map[string]*ResourceContent),
	}
}

// GetResource retrieves a resource, checking cache first.
func (rm *ResourceManager) GetResource(ctx context.Context, serverName, uri string) (*ResourceContent, error) {
	// Check cache first
	rm.mu.RLock()
	if cached, exists := rm.cache[uri]; exists {
		rm.mu.RUnlock()
		return cached, nil
	}
	rm.mu.RUnlock()

	// Fetch from server
	session, err := rm.client.GetSession(serverName)
	if err != nil {
		return nil, err
	}

	params := &mcp.ReadResourceParams{
		URI: uri,
	}
	result, err := session.ReadResource(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("read resource %s: %w", uri, err)
	}

	if len(result.Contents) == 0 {
		return nil, fmt.Errorf("no content for resource %s", uri)
	}

	// Convert first content to our format
	content := &ResourceContent{
		URI:      result.Contents[0].URI,
		MimeType: result.Contents[0].MIMEType,
	}

	// Handle text or blob content
	// The MCP SDK uses string and []byte directly, not pointers
	if result.Contents[0].Text != "" {
		content.Text = result.Contents[0].Text
	}
	if len(result.Contents[0].Blob) > 0 {
		content.Blob = result.Contents[0].Blob
	}

	// Cache the result
	rm.mu.Lock()
	rm.cache[uri] = content
	rm.mu.Unlock()

	return content, nil
}

// ClearCache clears the resource cache.
func (rm *ResourceManager) ClearCache() {
	rm.mu.Lock()
	rm.cache = make(map[string]*ResourceContent)
	rm.mu.Unlock()
}

// Note: Resource subscription functionality would need to be implemented
// when the MCP SDK supports it. For now, these are placeholder methods.

// SubscribeToResource subscribes to resource changes (placeholder).
func (c *Client) SubscribeToResource(ctx context.Context, serverName, uri string) error {
	// TODO: Implement when MCP SDK supports resource subscriptions
	c.logger.Debug("resource subscription requested",
		"server", serverName,
		"uri", uri)
	return nil
}

// UnsubscribeFromResource unsubscribes from resource changes (placeholder).
func (c *Client) UnsubscribeFromResource(ctx context.Context, serverName, uri string) error {
	// TODO: Implement when MCP SDK supports resource subscriptions
	c.logger.Debug("resource unsubscription requested",
		"server", serverName,
		"uri", uri)
	return nil
}
