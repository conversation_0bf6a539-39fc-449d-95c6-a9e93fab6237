package mcp

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/modelcontextprotocol/go-sdk/mcp"

	"github.com/koopa0/assistant-go/internal/platform/logger"
	"github.com/koopa0/assistant-go/internal/tool"
)

// Integration manages the integration between MCP and Tool Manager.
type Integration struct {
	client          *Client
	toolManager     *tool.Manager
	resourceManager *ResourceManager
	healthChecker   *HealthChecker
	logger          logger.Logger

	// Track registered tools with their server associations
	registeredTools map[string]string // tool name -> server name
	mu              sync.RWMutex

	// Auto-discovery settings
	autoDiscover      bool
	discoveryInterval time.Duration
	discoveryCancel   context.CancelFunc // Cancel function for auto-discovery
	discoveryRunning  bool               // Flag to prevent duplicate StartAutoDiscovery

	// Capabilities tracking
	capabilities map[string]*ServerCapabilities // server name -> capabilities
}

// IntegrationConfig configures MCP integration.
type IntegrationConfig struct {
	MCPConfig         Config
	ToolManager       *tool.Manager
	AutoDiscover      bool          // Automatically discover and register tools
	DiscoveryInterval time.Duration // How often to re-discover tools
}

// ServerCapabilities tracks what features a server supports.
type ServerCapabilities struct {
	Tools     bool `json:"tools"`
	Resources bool `json:"resources"`
	Prompts   bool `json:"prompts"`
}

// NewIntegration creates a new MCP integration.
func NewIntegration(cfg IntegrationConfig, log logger.Logger) (*Integration, error) {
	// Create MCP client
	client, err := NewClient(cfg.MCPConfig, log)
	if err != nil {
		return nil, fmt.Errorf("create MCP client: %w", err)
	}

	// Set defaults
	if cfg.DiscoveryInterval == 0 {
		cfg.DiscoveryInterval = 5 * time.Minute
	}

	// Create health checker
	healthChecker := NewHealthChecker(client, 30*time.Second)

	integration := &Integration{
		client:            client,
		toolManager:       cfg.ToolManager,
		resourceManager:   NewResourceManager(client),
		healthChecker:     healthChecker,
		logger:            log.WithComponent("mcp-integration"),
		registeredTools:   make(map[string]string),
		autoDiscover:      cfg.AutoDiscover,
		discoveryInterval: cfg.DiscoveryInterval,
		discoveryRunning:  false,
		capabilities:      make(map[string]*ServerCapabilities),
	}

	// Initial discovery of all capabilities
	if err := integration.DiscoverCapabilities(context.Background()); err != nil {
		integration.logger.Warn("initial capability discovery failed", "error", err)
	}

	// Initial tool discovery
	if err := integration.DiscoverAndRegisterTools(context.Background()); err != nil {
		integration.logger.Warn("initial tool discovery failed", "error", err)
	}

	// Start health monitoring
	integration.healthChecker.Start(context.Background())

	return integration, nil
}

// DiscoverAndRegisterTools discovers MCP tools and registers them with Tool Manager.
func (i *Integration) DiscoverAndRegisterTools(ctx context.Context) error {
	// Discover tools from all connected servers
	tools, err := i.client.DiscoverTools(ctx)
	if err != nil {
		return fmt.Errorf("discover tools: %w", err)
	}

	i.mu.Lock()
	defer i.mu.Unlock()

	// Register each tool
	registered := 0
	for _, t := range tools {
		toolName := t.Name()

		// Check if already registered
		if _, exists := i.registeredTools[toolName]; exists {
			continue
		}

		// Register with tool manager
		if err := i.toolManager.Register(t); err != nil {
			i.logger.Warn("failed to register MCP tool",
				"tool", toolName,
				"error", err)
			continue
		}

		// Extract server name from MCP tool
		if mcpTool, ok := t.(*Tool); ok {
			i.registeredTools[toolName] = mcpTool.serverName
		} else {
			// Fallback: extract server name from tool name pattern
			if idx := len(toolName); idx > 0 {
				for j := 0; j < len(toolName); j++ {
					if toolName[j] == '.' {
						i.registeredTools[toolName] = toolName[:j]
						break
					}
				}
			}
		}
		registered++

		i.logger.Info("registered MCP tool",
			"tool", toolName,
			"description", t.Description())
	}

	i.logger.Info("MCP tool discovery complete",
		"discovered", len(tools),
		"registered", registered,
		"total", len(i.registeredTools))

	return nil
}

// StartAutoDiscovery starts automatic tool discovery.
func (i *Integration) StartAutoDiscovery(ctx context.Context) {
	i.mu.Lock()
	defer i.mu.Unlock()

	if !i.autoDiscover {
		return
	}

	// Check if already running
	if i.discoveryRunning {
		i.logger.Debug("auto-discovery already running")
		return
	}

	// Create a cancellable context
	discoveryCtx, cancel := context.WithCancel(ctx)
	i.discoveryCancel = cancel
	i.discoveryRunning = true

	go i.runAutoDiscovery(discoveryCtx)
}

// runAutoDiscovery periodically discovers and registers new tools.
func (i *Integration) runAutoDiscovery(ctx context.Context) {
	defer func() {
		i.mu.Lock()
		i.discoveryRunning = false
		i.discoveryCancel = nil
		i.mu.Unlock()
	}()

	ticker := time.NewTicker(i.discoveryInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			if err := i.DiscoverAndRegisterTools(ctx); err != nil {
				i.logger.Warn("auto-discovery failed", "error", err)
			}
		}
	}
}

// ConnectServer connects to a new MCP server and discovers its tools.
func (i *Integration) ConnectServer(ctx context.Context, serverName string) error {
	// Connect to server
	if err := i.client.ConnectToServer(ctx, serverName); err != nil {
		return fmt.Errorf("connect to server: %w", err)
	}

	// Discover and register tools
	if err := i.DiscoverAndRegisterTools(ctx); err != nil {
		return fmt.Errorf("discover tools: %w", err)
	}

	return nil
}

// DisconnectServer disconnects from an MCP server and unregisters its tools.
func (i *Integration) DisconnectServer(serverName string) error {
	// Get tools from this server before disconnecting
	i.mu.Lock()
	var toolsToRemove []string
	for toolName, associatedServer := range i.registeredTools {
		// Check if tool belongs to this server using stored association
		if associatedServer == serverName {
			toolsToRemove = append(toolsToRemove, toolName)
		}
	}
	i.mu.Unlock()

	// Disconnect from server
	if err := i.client.Disconnect(serverName); err != nil {
		return fmt.Errorf("disconnect from server: %w", err)
	}

	// Unregister tools
	i.mu.Lock()
	defer i.mu.Unlock()

	for _, toolName := range toolsToRemove {
		if err := i.toolManager.Unregister(toolName); err != nil {
			i.logger.Warn("failed to unregister tool",
				"tool", toolName,
				"error", err)
		}
		delete(i.registeredTools, toolName)
	}

	i.logger.Info("disconnected from MCP server",
		"server", serverName,
		"tools_removed", len(toolsToRemove))

	return nil
}

// GetClient returns the underlying MCP client.
func (i *Integration) GetClient() *Client {
	return i.client
}

// ListServers returns list of connected servers.
func (i *Integration) ListServers() []string {
	return i.client.ListConnectedServers()
}

// ListMCPTools returns list of registered MCP tools.
func (i *Integration) ListMCPTools() []string {
	i.mu.RLock()
	defer i.mu.RUnlock()

	tools := make([]string, 0, len(i.registeredTools))
	for tool := range i.registeredTools {
		tools = append(tools, tool)
	}
	return tools
}

// DiscoverCapabilities discovers what capabilities each server supports.
func (i *Integration) DiscoverCapabilities(ctx context.Context) error {
	servers := i.client.ListConnectedServers()

	i.mu.Lock()
	defer i.mu.Unlock()

	for _, serverName := range servers {
		// Get server info to determine capabilities
		session, err := i.client.GetSession(serverName)
		if err != nil {
			i.logger.Warn("failed to get session for capability discovery",
				"server", serverName,
				"error", err)
			continue
		}

		// Check capabilities by trying to list each type
		caps := &ServerCapabilities{}

		// Check tools capability
		if _, err := session.ListTools(ctx, &mcp.ListToolsParams{}); err == nil {
			caps.Tools = true
		}

		// Check resources capability
		if _, err := session.ListResources(ctx, &mcp.ListResourcesParams{}); err == nil {
			caps.Resources = true
		}

		// Check prompts capability
		if _, err := session.ListPrompts(ctx, &mcp.ListPromptsParams{}); err == nil {
			caps.Prompts = true
		}

		i.capabilities[serverName] = caps
		i.logger.Info("discovered server capabilities",
			"server", serverName,
			"tools", caps.Tools,
			"resources", caps.Resources,
			"prompts", caps.Prompts)
	}

	return nil
}

// GetServerCapabilities returns the capabilities for a specific server.
func (i *Integration) GetServerCapabilities(serverName string) (*ServerCapabilities, bool) {
	i.mu.RLock()
	defer i.mu.RUnlock()

	caps, exists := i.capabilities[serverName]
	return caps, exists
}

// GetResources returns all available resources from MCP servers.
func (i *Integration) GetResources(ctx context.Context) ([]Resource, error) {
	return i.client.ListResources(ctx)
}

// GetResource retrieves a specific resource.
func (i *Integration) GetResource(ctx context.Context, serverName, uri string) (*ResourceContent, error) {
	return i.resourceManager.GetResource(ctx, serverName, uri)
}

// GetPrompts returns all available prompts from MCP servers.
func (i *Integration) GetPrompts(ctx context.Context) ([]Prompt, error) {
	return i.client.ListPrompts(ctx)
}

// ExecutePrompt executes a prompt and returns the result.
func (i *Integration) ExecutePrompt(ctx context.Context, serverName, promptName string, args map[string]string) (*PromptResult, error) {
	return i.client.GetPrompt(ctx, serverName, promptName, args)
}

// GetHealthStatus returns the health status of all MCP servers.
func (i *Integration) GetHealthStatus() map[string]*HealthStatus {
	return i.healthChecker.GetAllStatuses()
}

// IsHealthy returns true if all MCP servers are healthy.
func (i *Integration) IsHealthy() bool {
	return i.healthChecker.IsHealthy()
}

// WaitForHealthy waits for all servers to become healthy.
func (i *Integration) WaitForHealthy(ctx context.Context, timeout time.Duration) error {
	return i.healthChecker.WaitForHealthy(ctx, timeout)
}

// Close shuts down the integration.
func (i *Integration) Close() error {
	// Stop health checker
	if i.healthChecker != nil {
		i.healthChecker.Stop()
	}

	// Stop auto-discovery if running
	i.mu.Lock()
	if i.discoveryCancel != nil {
		i.discoveryCancel()
	}
	i.mu.Unlock()

	// Wait a bit for auto-discovery to stop
	time.Sleep(100 * time.Millisecond)

	// Clear resource cache
	if i.resourceManager != nil {
		i.resourceManager.ClearCache()
	}

	// Unregister all MCP tools
	i.mu.Lock()
	for toolName := range i.registeredTools {
		if err := i.toolManager.Unregister(toolName); err != nil {
			i.logger.Warn("failed to unregister tool on shutdown",
				"tool", toolName,
				"error", err)
		}
	}
	i.registeredTools = make(map[string]string)
	i.mu.Unlock()

	// Close MCP client
	return i.client.Close()
}
