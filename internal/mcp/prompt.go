package mcp

import (
	"context"
	"fmt"

	"github.com/modelcontextprotocol/go-sdk/mcp"
)

// Prompt represents an MCP prompt template.
type Prompt struct {
	ServerName  string           `json:"server_name"`
	Name        string           `json:"name"`
	Description string           `json:"description,omitempty"`
	Arguments   []PromptArgument `json:"arguments,omitempty"`
}

// PromptArgument represents a prompt argument definition.
type PromptArgument struct {
	Name        string `json:"name"`
	Description string `json:"description,omitempty"`
	Required    bool   `json:"required"`
}

// PromptResult contains the generated prompt content.
type PromptResult struct {
	Messages []PromptMessage `json:"messages"`
}

// PromptMessage represents a message in a prompt result.
type PromptMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// ListPrompts lists all prompts from connected servers.
func (c *Client) ListPrompts(ctx context.Context) ([]Prompt, error) {
	var prompts []Prompt

	c.mu.RLock()
	defer c.mu.RUnlock()

	for serverName, session := range c.sessions {
		if !session.Connected {
			continue
		}

		// List prompts from this server
		result, err := session.Session.ListPrompts(ctx, &mcp.ListPromptsParams{})
		if err != nil {
			c.logger.Warn("failed to list prompts from server",
				"server", serverName,
				"error", err)
			continue
		}

		// Convert to our prompt type
		for _, p := range result.Prompts {
			prompt := Prompt{
				ServerName:  serverName,
				Name:        p.Name,
				Description: p.Description,
			}

			// Convert arguments if available
			if p.Arguments != nil {
				for _, arg := range p.Arguments {
					prompt.Arguments = append(prompt.Arguments, PromptArgument{
						Name:        arg.Name,
						Description: arg.Description,
						Required:    arg.Required,
					})
				}
			}

			prompts = append(prompts, prompt)
		}
	}

	return prompts, nil
}

// GetPrompt retrieves and executes a specific prompt.
func (c *Client) GetPrompt(ctx context.Context, serverName, promptName string, args map[string]string) (*PromptResult, error) {
	session, err := c.GetSession(serverName)
	if err != nil {
		return nil, err
	}

	params := &mcp.GetPromptParams{
		Name:      promptName,
		Arguments: args,
	}

	result, err := session.GetPrompt(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("get prompt %s: %w", promptName, err)
	}

	// Convert messages to our format
	promptResult := &PromptResult{
		Messages: make([]PromptMessage, 0, len(result.Messages)),
	}

	for _, msg := range result.Messages {
		// Convert message to our format
		// Extract text content from the Content interface
		contentStr := ""
		if msg.Content != nil {
			// Marshal content to JSON to extract text
			if data, err := msg.Content.MarshalJSON(); err == nil {
				// For now, use the JSON representation as content
				// In a real implementation, we'd parse this to extract text
				contentStr = string(data)
			}
		}

		promptResult.Messages = append(promptResult.Messages, PromptMessage{
			Role:    string(msg.Role),
			Content: contentStr,
		})
	}

	return promptResult, nil
}

// DiscoverPrompts discovers all prompts and returns them in a structured format.
func (c *Client) DiscoverPrompts(ctx context.Context) (map[string][]Prompt, error) {
	prompts, err := c.ListPrompts(ctx)
	if err != nil {
		return nil, err
	}

	// Group prompts by server
	grouped := make(map[string][]Prompt)
	for _, prompt := range prompts {
		grouped[prompt.ServerName] = append(grouped[prompt.ServerName], prompt)
	}

	return grouped, nil
}
