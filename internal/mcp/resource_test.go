package mcp

import (
	"context"
	"testing"

	"github.com/koopa0/assistant-go/internal/platform/logger"
	"github.com/stretchr/testify/assert"
)

// TestResource tests Resource struct
func TestResource(t *testing.T) {
	resource := Resource{
		ServerName: "test-server",
		URI:        "file:///path/to/resource",
		Name:       "test-resource",
		MimeType:   "text/plain",
	}

	assert.Equal(t, "test-server", resource.ServerName)
	assert.Equal(t, "file:///path/to/resource", resource.URI)
	assert.Equal(t, "test-resource", resource.Name)
	assert.Equal(t, "text/plain", resource.MimeType)
}

// TestListResources tests ListResources with no connected servers
func TestListResources(t *testing.T) {
	client := &Client{
		sessions: make(map[string]*Session),
		logger:   logger.NewConsoleLogger(),
	}

	ctx := context.Background()
	resources, err := client.ListResources(ctx)

	assert.NoError(t, err)
	assert.Empty(t, resources)
}

// TestListResourcesWithDisconnectedServers tests ListResources with disconnected servers
func TestListResourcesWithDisconnectedServers(t *testing.T) {
	client := &Client{
		sessions: map[string]*Session{
			"server1": {Connected: false},
			"server2": {Connected: false},
		},
		logger: logger.NewConsoleLogger(),
	}

	ctx := context.Background()
	resources, err := client.ListResources(ctx)

	assert.NoError(t, err)
	assert.Empty(t, resources)
}

// TestReadResource tests ReadResource
func TestReadResource(t *testing.T) {
	client := &Client{
		sessions: make(map[string]*Session),
		logger:   logger.NewConsoleLogger(),
	}

	ctx := context.Background()

	// Should fail with no session
	content, err := client.ReadResource(ctx, "test-server", "file:///test")

	assert.Error(t, err)
	assert.Nil(t, content)
	assert.Contains(t, err.Error(), "not connected")
}

// TestResourceTypes tests different resource types
func TestResourceTypes(t *testing.T) {
	tests := []struct {
		name     string
		resource Resource
	}{
		{
			name: "text file",
			resource: Resource{
				ServerName: "server1",
				URI:        "file:///docs/readme.txt",
				Name:       "readme.txt",
				MimeType:   "text/plain",
			},
		},
		{
			name: "JSON file",
			resource: Resource{
				ServerName: "server2",
				URI:        "file:///config/settings.json",
				Name:       "settings.json",
				MimeType:   "application/json",
			},
		},
		{
			name: "HTTP resource",
			resource: Resource{
				ServerName: "web-server",
				URI:        "https://example.com/data",
				Name:       "Remote Data",
				MimeType:   "application/json",
			},
		},
		{
			name: "custom protocol",
			resource: Resource{
				ServerName: "custom",
				URI:        "custom://resource/path",
				Name:       "Custom Resource",
				MimeType:   "application/octet-stream",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.NotEmpty(t, tt.resource.ServerName)
			assert.NotEmpty(t, tt.resource.URI)
			assert.NotEmpty(t, tt.resource.Name)
			assert.NotEmpty(t, tt.resource.MimeType)
		})
	}
}

// TestResourceValidation tests resource validation
func TestResourceValidation(t *testing.T) {
	tests := []struct {
		name     string
		resource Resource
		valid    bool
		reason   string
	}{
		{
			name: "valid resource",
			resource: Resource{
				ServerName: "server",
				URI:        "file:///path",
				Name:       "resource",
				MimeType:   "text/plain",
			},
			valid:  true,
			reason: "All required fields present",
		},
		{
			name: "missing URI",
			resource: Resource{
				ServerName: "server",
				Name:       "resource",
				MimeType:   "text/plain",
			},
			valid:  false,
			reason: "URI is required",
		},
		{
			name: "missing server name",
			resource: Resource{
				URI:      "file:///path",
				Name:     "resource",
				MimeType: "text/plain",
			},
			valid:  false,
			reason: "Server name is required",
		},
		{
			name: "empty mime type allowed",
			resource: Resource{
				ServerName: "server",
				URI:        "file:///path",
				Name:       "resource",
				MimeType:   "",
			},
			valid:  true,
			reason: "Empty MIME type is allowed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.valid {
				assert.NotEmpty(t, tt.resource.ServerName)
				assert.NotEmpty(t, tt.resource.URI)
			} else {
				assert.True(t, tt.resource.ServerName == "" || tt.resource.URI == "")
			}
		})
	}
}

// TestResourceContent represents content returned from reading a resource
type TestResourceContent struct {
	Text     string
	MimeType string
}

// TestReadResourceScenarios tests various resource reading scenarios
func TestReadResourceScenarios(t *testing.T) {
	scenarios := []struct {
		name         string
		serverName   string
		uri          string
		expectError  bool
		errorMessage string
	}{
		{
			name:         "valid resource",
			serverName:   "test-server",
			uri:          "file:///test/file.txt",
			expectError:  true, // Will fail without real session
			errorMessage: "not connected",
		},
		{
			name:         "empty URI",
			serverName:   "test-server",
			uri:          "",
			expectError:  true,
			errorMessage: "not connected",
		},
		{
			name:         "empty server name",
			serverName:   "",
			uri:          "file:///test",
			expectError:  true,
			errorMessage: "not connected",
		},
	}

	client := &Client{
		sessions: make(map[string]*Session),
		logger:   logger.NewConsoleLogger(),
	}

	for _, scenario := range scenarios {
		t.Run(scenario.name, func(t *testing.T) {
			ctx := context.Background()
			content, err := client.ReadResource(ctx, scenario.serverName, scenario.uri)

			if scenario.expectError {
				assert.Error(t, err)
				assert.Nil(t, content)
				if scenario.errorMessage != "" {
					assert.Contains(t, err.Error(), scenario.errorMessage)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, content)
			}
		})
	}
}
