package mcp

import (
	"context"
	"testing"
	"time"

	"github.com/koopa0/assistant-go/internal/platform/logger"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestHealthChecker(t *testing.T) {
	// Create a mock client
	client := &Client{
		sessions: make(map[string]*Session),
		logger:   logger.NewConsoleLogger(),
	}

	// Create health checker
	checker := NewHealthChecker(client, 100*time.Millisecond)
	assert.NotNil(t, checker)
	assert.Equal(t, 100*time.Millisecond, checker.interval)

	// Test GetStatus for non-existent server
	status, exists := checker.GetStatus("non-existent")
	assert.False(t, exists)
	assert.Nil(t, status)

	// Test GetAllStatuses when empty
	statuses := checker.GetAllStatuses()
	assert.Empty(t, statuses)

	// Test IsHealthy when no servers
	assert.False(t, checker.IsHealthy())
}

func TestHealthStatus(t *testing.T) {
	status := &HealthStatus{
		ServerName:    "test-server",
		Connected:     true,
		LastPing:      time.Now(),
		ResponseTime:  50 * time.Millisecond,
		ToolCount:     5,
		ResourceCount: 10,
		Capabilities: &ServerCapabilities{
			Tools:     true,
			Resources: true,
			Prompts:   false,
		},
	}

	assert.Equal(t, "test-server", status.ServerName)
	assert.True(t, status.Connected)
	assert.NotZero(t, status.LastPing)
	assert.Equal(t, 50*time.Millisecond, status.ResponseTime)
	assert.Equal(t, 5, status.ToolCount)
	assert.Equal(t, 10, status.ResourceCount)
	assert.True(t, status.Capabilities.Tools)
	assert.True(t, status.Capabilities.Resources)
	assert.False(t, status.Capabilities.Prompts)
}

func TestHealthCheckerLifecycle(t *testing.T) {
	client := &Client{
		sessions: make(map[string]*Session),
		logger:   logger.NewConsoleLogger(),
	}

	checker := NewHealthChecker(client, time.Second)

	// Start the checker
	ctx := context.Background()
	checker.Start(ctx)

	// Stop should not panic
	checker.Stop()

	// Multiple stops should be safe
	checker.Stop()
}

func TestWaitForHealthy(t *testing.T) {
	client := &Client{
		sessions: make(map[string]*Session),
		logger:   logger.NewConsoleLogger(),
	}

	checker := NewHealthChecker(client, time.Second)

	ctx := context.Background()

	// Should timeout when no servers are healthy
	err := checker.WaitForHealthy(ctx, 100*time.Millisecond)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "timeout")
}

func TestHealthCheckerConcurrency(t *testing.T) {
	client := &Client{
		sessions: make(map[string]*Session),
		logger:   logger.NewConsoleLogger(),
	}

	checker := NewHealthChecker(client, time.Second)

	// Simulate concurrent access
	done := make(chan bool)

	// Multiple readers
	for i := 0; i < 5; i++ {
		go func() {
			_ = checker.GetAllStatuses()
			_ = checker.IsHealthy()
			done <- true
		}()
	}

	// Wait for all goroutines
	for i := 0; i < 5; i++ {
		<-done
	}

	// Should not panic or deadlock
	require.NotNil(t, checker)
}
