package mcp

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/modelcontextprotocol/go-sdk/mcp"
)

// HealthStatus represents the health status of an MCP server.
type HealthStatus struct {
	ServerName    string              `json:"server_name"`
	Connected     bool                `json:"connected"`
	LastPing      time.Time           `json:"last_ping"`
	ResponseTime  time.Duration       `json:"response_time"`
	Error         string              `json:"error,omitempty"`
	Capabilities  *ServerCapabilities `json:"capabilities,omitempty"`
	ToolCount     int                 `json:"tool_count"`
	ResourceCount int                 `json:"resource_count"`
}

// HealthChecker monitors MCP server health.
type HealthChecker struct {
	client   *Client
	interval time.Duration
	mu       sync.RWMutex
	statuses map[string]*HealthStatus
	cancel   context.CancelFunc
}

// NewHealthChecker creates a new health checker.
func NewHealthChecker(client *Client, interval time.Duration) *HealthChecker {
	if interval == 0 {
		interval = 30 * time.Second
	}
	return &HealthChecker{
		client:   client,
		interval: interval,
		statuses: make(map[string]*HealthStatus),
	}
}

// Start begins health checking.
func (h *HealthChecker) Start(ctx context.Context) {
	ctx, h.cancel = context.WithCancel(ctx)
	go h.run(ctx)
}

// Stop stops health checking.
func (h *HealthChecker) Stop() {
	if h.cancel != nil {
		h.cancel()
	}
}

// run performs periodic health checks.
func (h *HealthChecker) run(ctx context.Context) {
	// Initial check
	h.checkAll(ctx)

	ticker := time.NewTicker(h.interval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			h.checkAll(ctx)
		}
	}
}

// checkAll checks health of all servers.
func (h *HealthChecker) checkAll(ctx context.Context) {
	servers := h.client.ListConnectedServers()

	var wg sync.WaitGroup
	for _, server := range servers {
		wg.Add(1)
		go func(serverName string) {
			defer wg.Done()
			h.checkServer(ctx, serverName)
		}(server)
	}
	wg.Wait()
}

// checkServer checks health of a single server.
func (h *HealthChecker) checkServer(ctx context.Context, serverName string) {
	status := &HealthStatus{
		ServerName: serverName,
		Connected:  false,
	}

	// Time the ping
	start := time.Now()
	err := h.client.Ping(ctx, serverName)
	status.ResponseTime = time.Since(start)

	if err != nil {
		status.Error = err.Error()
	} else {
		status.Connected = true
		status.LastPing = time.Now()

		// Get additional server info
		session, err := h.client.GetSession(serverName)
		if err == nil {
			// Count tools
			if tools, err := session.ListTools(ctx, &mcp.ListToolsParams{}); err == nil {
				status.ToolCount = len(tools.Tools)
			}

			// Count resources
			if resources, err := session.ListResources(ctx, &mcp.ListResourcesParams{}); err == nil {
				status.ResourceCount = len(resources.Resources)
			}

			// Check capabilities
			status.Capabilities = &ServerCapabilities{
				Tools:     status.ToolCount > 0,
				Resources: status.ResourceCount > 0,
			}

			// Check prompts capability
			if _, err := session.ListPrompts(ctx, &mcp.ListPromptsParams{}); err == nil {
				status.Capabilities.Prompts = true
			}
		}
	}

	// Store status
	h.mu.Lock()
	h.statuses[serverName] = status
	h.mu.Unlock()

	// Log status change
	if status.Connected {
		h.client.logger.Debug("MCP server health check passed",
			"server", serverName,
			"response_time", status.ResponseTime,
			"tools", status.ToolCount,
			"resources", status.ResourceCount)
	} else {
		h.client.logger.Warn("MCP server health check failed",
			"server", serverName,
			"error", status.Error)
	}
}

// GetStatus returns the health status for a server.
func (h *HealthChecker) GetStatus(serverName string) (*HealthStatus, bool) {
	h.mu.RLock()
	defer h.mu.RUnlock()
	status, exists := h.statuses[serverName]
	return status, exists
}

// GetAllStatuses returns health status for all servers.
func (h *HealthChecker) GetAllStatuses() map[string]*HealthStatus {
	h.mu.RLock()
	defer h.mu.RUnlock()

	// Return a copy to prevent concurrent modification
	result := make(map[string]*HealthStatus)
	for k, v := range h.statuses {
		result[k] = v
	}
	return result
}

// IsHealthy returns true if all servers are healthy.
func (h *HealthChecker) IsHealthy() bool {
	h.mu.RLock()
	defer h.mu.RUnlock()

	for _, status := range h.statuses {
		if !status.Connected {
			return false
		}
	}
	return len(h.statuses) > 0
}

// WaitForHealthy waits for all servers to become healthy.
func (h *HealthChecker) WaitForHealthy(ctx context.Context, timeout time.Duration) error {
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	ticker := time.NewTicker(time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return fmt.Errorf("timeout waiting for servers to become healthy")
		case <-ticker.C:
			if h.IsHealthy() {
				return nil
			}
		}
	}
}
