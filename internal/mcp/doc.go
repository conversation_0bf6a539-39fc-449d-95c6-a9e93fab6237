// Package mcp provides Model Context Protocol client implementation.
//
// This package enables <PERSON><PERSON><PERSON> to connect to external MCP servers,
// expanding its capabilities through a standardized protocol for AI tool
// integration.
//
// # Architecture
//
// The MCP client acts as a bridge between <PERSON><PERSON><PERSON>'s internal tool
// system and external MCP servers:
//
//	Assistant-Go
//	     │
//	Tool Manager
//	     │
//	┌────┴────┐
//	│   MCP   │
//	│ Client  │
//	└────┬────┘
//	     │
//	┌────┴────────────┬───────────────┐
//	│                 │               │
//	MCP Server 1   MCP Server 2   MCP Server N
//
// # Core Components
//
// Client: Manages connections to multiple MCP servers, handles
// discovery, and maintains session lifecycle.
//
// Tool: Wraps MCP tools to implement <PERSON>-<PERSON>'s tool.Tool
// interface, enabling seamless integration.
//
// Resource: Provides access to MCP resources, which can be
// documents, data sources, or any content exposed by servers.
//
// # Usage Example
//
//	// Configure MCP client
//	config := mcp.Config{
//	    ClientName: "assistant-go",
//	    Servers: []mcp.ServerConfig{
//	        {
//	            Name:    "github",
//	            Command: "npx",
//	            Args:    []string{"@modelcontextprotocol/server-github"},
//	            AutoConnect: true,
//	        },
//	    },
//	}
//
//	// Create client
//	client, err := mcp.NewClient(config, logger)
//	if err != nil {
//	    return err
//	}
//	defer client.Close()
//
//	// Discover available tools
//	tools, err := client.DiscoverTools(ctx)
//	if err != nil {
//	    return err
//	}
//
//	// Use tools through standard interface
//	for _, tool := range tools {
//	    result, err := tool.Execute(ctx, args)
//	    // Handle result...
//	}
//
// # Server Types
//
// MCP servers can provide various capabilities:
//
// - File System Access: Read/write files, navigate directories
// - Database Access: Query and modify databases
// - API Integration: Call external APIs and services
// - Search: Web search, code search, documentation search
// - Computation: Run calculations, data analysis
// - Memory: Store and retrieve contextual information
//
// # Benefits
//
// 1. Extensibility: Add new capabilities without modifying core code
// 2. Isolation: Servers run in separate processes for security
// 3. Language Agnostic: Servers can be written in any language
// 4. Standardized: Common protocol for all AI tool integrations
// 5. Dynamic: Discover and use tools at runtime
package mcp
