package mcp

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestPromptStructures(t *testing.T) {
	// Test Prompt creation
	prompt := Prompt{
		ServerName:  "test-server",
		Name:        "code-review",
		Description: "Reviews code for best practices",
		Arguments: []PromptArgument{
			{
				Name:        "language",
				Description: "Programming language",
				Required:    true,
			},
			{
				Name:        "code",
				Description: "Code to review",
				Required:    true,
			},
		},
	}

	assert.Equal(t, "test-server", prompt.ServerName)
	assert.Equal(t, "code-review", prompt.Name)
	assert.Equal(t, "Reviews code for best practices", prompt.Description)
	assert.Len(t, prompt.Arguments, 2)
	assert.True(t, prompt.Arguments[0].Required)
}

func TestPromptResult(t *testing.T) {
	// Test PromptResult
	result := PromptResult{
		Messages: []PromptMessage{
			{
				Role:    "system",
				Content: "You are a code reviewer",
			},
			{
				Role:    "user",
				Content: "Review this Go code",
			},
		},
	}

	assert.Len(t, result.Messages, 2)
	assert.Equal(t, "system", result.Messages[0].Role)
	assert.Equal(t, "user", result.Messages[1].Role)
}

func TestClientListPrompts(t *testing.T) {
	// This test would require a mock MCP server
	// For now, we test the method signature and basic flow

	client := &Client{
		sessions: make(map[string]*Session),
	}

	ctx := context.Background()
	prompts, err := client.ListPrompts(ctx)

	// Should not error with no sessions
	require.NoError(t, err)
	assert.Empty(t, prompts)
}

func TestClientDiscoverPrompts(t *testing.T) {
	client := &Client{
		sessions: make(map[string]*Session),
	}

	ctx := context.Background()
	grouped, err := client.DiscoverPrompts(ctx)

	require.NoError(t, err)
	assert.NotNil(t, grouped)
	assert.Empty(t, grouped)
}
