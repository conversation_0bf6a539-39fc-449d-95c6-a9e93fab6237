package mcp

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/modelcontextprotocol/go-sdk/mcp"

	"github.com/koopa0/assistant-go/internal/tool"
)

// Tool wraps an MCP tool to implement the tool.Tool interface.
// This bridges external MCP tools with our internal tool system.
type Tool struct {
	serverName string
	toolName   string
	client     *Client
	schema     *mcp.Tool
}

// NewTool creates a tool wrapper for an MCP tool.
func NewTool(client *Client, serverName string, schema *mcp.Tool) *Tool {
	return &Tool{
		serverName: serverName,
		toolName:   schema.Name,
		client:     client,
		schema:     schema,
	}
}

// Name returns the tool name.
func (t *Tool) Name() string {
	// Prefix with server name to avoid conflicts
	return fmt.Sprintf("%s.%s", t.serverName, t.toolName)
}

// ServerName returns the server name this tool belongs to.
func (t *Tool) ServerName() string {
	return t.serverName
}

// Description returns the tool description.
func (t *Tool) Description() string {
	return t.schema.Description
}

// Execute calls the MCP tool.
func (t *Tool) Execute(ctx context.Context, input any) (any, error) {
	session, err := t.client.GetSession(t.serverName)
	if err != nil {
		return nil, fmt.Errorf("get session: %w", err)
	}

	// Convert input to map if needed
	var args map[string]any
	switch v := input.(type) {
	case map[string]any:
		args = v
	case string:
		// Try to parse as JSON
		if parseErr := json.Unmarshal([]byte(v), &args); parseErr != nil {
			// If not JSON, wrap as single argument
			args = map[string]any{"input": v}
		}
	default:
		// Convert to JSON then to map
		data, marshalErr := json.Marshal(input)
		if marshalErr != nil {
			return nil, fmt.Errorf("marshal input: %w", marshalErr)
		}
		if unmarshalErr := json.Unmarshal(data, &args); unmarshalErr != nil {
			return nil, fmt.Errorf("unmarshal input: %w", unmarshalErr)
		}
	}

	// Call MCP tool
	params := &mcp.CallToolParams{
		Name:      t.toolName,
		Arguments: args,
	}
	result, err := session.CallTool(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("call tool %s: %w", t.toolName, err)
	}

	return result.Content, nil
}

// InputSchema returns the tool's input schema.
func (t *Tool) InputSchema() json.RawMessage {
	// Convert MCP schema to JSON
	if t.schema.InputSchema == nil {
		return json.RawMessage("{}")
	}

	data, err := json.Marshal(t.schema.InputSchema)
	if err != nil {
		// Return empty schema on error
		return json.RawMessage("{}")
	}

	return data
}

// DiscoverTools discovers all tools from connected MCP servers.
func (c *Client) DiscoverTools(ctx context.Context) ([]tool.Tool, error) {
	var tools []tool.Tool

	c.mu.RLock()
	defer c.mu.RUnlock()

	for serverName, session := range c.sessions {
		if !session.Connected {
			continue
		}

		// List tools from this server
		result, err := session.Session.ListTools(ctx, &mcp.ListToolsParams{})
		if err != nil {
			c.logger.Warn("failed to list tools from server",
				"server", serverName,
				"error", err)
			continue
		}

		// Wrap each tool
		for _, toolInfo := range result.Tools {
			mcpTool := NewTool(c, serverName, toolInfo)
			tools = append(tools, mcpTool)

			c.logger.Debug("discovered MCP tool",
				"server", serverName,
				"tool", toolInfo.Name,
				"description", toolInfo.Description)
		}
	}

	return tools, nil
}

// CallTool directly calls a tool on a specific server.
func (c *Client) CallTool(ctx context.Context, serverName, toolName string, args map[string]any) (any, error) {
	session, err := c.GetSession(serverName)
	if err != nil {
		return nil, err
	}

	params := &mcp.CallToolParams{
		Name:      toolName,
		Arguments: args,
	}
	result, err := session.CallTool(ctx, params)
	if err != nil {
		return nil, err
	}
	return result.Content, nil
}

// ListTools lists all tools from a specific server.
func (c *Client) ListTools(ctx context.Context, serverName string) ([]*mcp.Tool, error) {
	session, err := c.GetSession(serverName)
	if err != nil {
		return nil, err
	}

	result, err := session.ListTools(ctx, &mcp.ListToolsParams{})
	if err != nil {
		return nil, err
	}
	return result.Tools, nil
}

// GetToolInfo retrieves detailed information about a specific tool.
func (c *Client) GetToolInfo(ctx context.Context, serverName, toolName string) (*mcp.Tool, error) {
	tools, err := c.ListTools(ctx, serverName)
	if err != nil {
		return nil, err
	}

	for _, tool := range tools {
		if tool.Name == toolName {
			return tool, nil
		}
	}

	return nil, fmt.Errorf("tool %s not found on server %s", toolName, serverName)
}
