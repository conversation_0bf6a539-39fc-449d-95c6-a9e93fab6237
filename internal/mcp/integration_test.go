//go:build integration
// +build integration

package mcp

import (
	"fmt"
	"testing"
	"time"

	"github.com/koopa0/assistant-go/internal/platform/logger"
	"github.com/koopa0/assistant-go/internal/tool"
	"github.com/stretchr/testify/assert"
)

// Test IntegrationConfig validation
func TestIntegrationConfig(t *testing.T) {
	tests := []struct {
		name                 string
		cfg                  IntegrationConfig
		expectedInterval     time.Duration
		expectedAutoDiscover bool
	}{
		{
			name: "default discovery interval",
			cfg: IntegrationConfig{
				MCPConfig:   Config{},
				ToolManager: tool.NewManager(tool.Config{}),
			},
			expectedInterval:     5 * time.Minute,
			expectedAutoDiscover: false,
		},
		{
			name: "custom discovery interval",
			cfg: IntegrationConfig{
				MCPConfig:         Config{},
				ToolManager:       tool.NewManager(tool.Config{}),
				DiscoveryInterval: 10 * time.Minute,
				AutoDiscover:      true,
			},
			expectedInterval:     10 * time.Minute,
			expectedAutoDiscover: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Try to create integration
			integration, err := NewIntegration(tt.cfg, logger.NewConsoleLogger())

			// Should succeed since we're not requiring real MCP servers
			assert.NoError(t, err)
			assert.NotNil(t, integration)

			// Verify the configuration was applied correctly
			// Note: We can't directly access private fields, but we can verify behavior
		})
	}
}

// Test tool registration
func TestToolRegistration(t *testing.T) {
	// Create a simple integration with mocked components
	integration := &Integration{
		registeredTools: make(map[string]string),
		logger:          logger.NewConsoleLogger(),
	}

	// Test tool registration tracking
	_, exists := integration.registeredTools["test-tool"]
	assert.False(t, exists)

	// Mark as registered with server association
	integration.mu.Lock()
	integration.registeredTools["test-tool"] = "test-server"
	integration.mu.Unlock()

	integration.mu.RLock()
	serverName, exists := integration.registeredTools["test-tool"]
	assert.True(t, exists)
	assert.Equal(t, "test-server", serverName)
	integration.mu.RUnlock()

	// Test concurrent access
	done := make(chan bool)
	for i := 0; i < 10; i++ {
		go func(id int) {
			toolName := fmt.Sprintf("tool-%d", id)
			serverName := fmt.Sprintf("server-%d", id)
			integration.mu.Lock()
			integration.registeredTools[toolName] = serverName
			integration.mu.Unlock()

			integration.mu.RLock()
			actualServer, exists := integration.registeredTools[toolName]
			assert.True(t, exists)
			assert.Equal(t, serverName, actualServer)
			integration.mu.RUnlock()
			done <- true
		}(i)
	}

	// Wait for all goroutines
	for i := 0; i < 10; i++ {
		<-done
	}
}

// Test Close method
func TestIntegration_Close(t *testing.T) {
	// Create a proper integration through NewIntegration
	cfg := IntegrationConfig{
		MCPConfig:   Config{ClientName: "test"},
		ToolManager: tool.NewManager(tool.Config{}),
	}

	integration, err := NewIntegration(cfg, logger.NewConsoleLogger())
	assert.NoError(t, err)
	assert.NotNil(t, integration)

	// Close should work without errors
	err = integration.Close()
	assert.NoError(t, err)
}

// Test Close with registered tools
func TestIntegration_CloseWithRegisteredTools(t *testing.T) {
	// Create a proper integration
	cfg := IntegrationConfig{
		MCPConfig:   Config{ClientName: "test"},
		ToolManager: tool.NewManager(tool.Config{}),
	}

	integration, err := NewIntegration(cfg, logger.NewConsoleLogger())
	assert.NoError(t, err)

	// Manually add some registered tools to test cleanup
	integration.mu.Lock()
	integration.registeredTools["tool1"] = "server1"
	integration.registeredTools["tool2"] = "server2"
	integration.mu.Unlock()

	// Close should work and attempt to unregister tools
	err = integration.Close()
	assert.NoError(t, err)

	// Verify tools were cleared
	assert.Empty(t, integration.registeredTools)
}

// Test ListMCPTools method
func TestIntegration_ListMCPTools(t *testing.T) {
	integration := &Integration{
		registeredTools: map[string]string{
			"tool1": "server1",
			"tool2": "server2",
			"tool3": "server3",
		},
		logger: logger.NewConsoleLogger(),
	}

	tools := integration.ListMCPTools()
	assert.Len(t, tools, 3)
	assert.Contains(t, tools, "tool1")
	assert.Contains(t, tools, "tool2")
	assert.Contains(t, tools, "tool3")
}

// Test GetClient method
func TestIntegration_GetClient(t *testing.T) {
	integration := &Integration{
		client: nil, // Client is nil
		logger: logger.NewConsoleLogger(),
	}

	client := integration.GetClient()
	assert.Nil(t, client)
}

// Test ListServers method
func TestIntegration_ListServers(t *testing.T) {
	integration := &Integration{
		client: nil, // Client is nil, so this should handle gracefully
		logger: logger.NewConsoleLogger(),
	}

	// This will panic with nil client, so we test that scenario
	assert.Panics(t, func() {
		integration.ListServers()
	})
}
