package assistant

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/koopa0/assistant-go/internal/tool"
)

// getAvailableTools returns the list of available tools as AI tool parameters.
func (a *assistant) getAvailableTools() []ai.Tool {
	// Check if tools manager is nil
	if a.tools == nil {
		return []ai.Tool{}
	}

	// Get all tools from tool manager (includes both local and MCP tools)
	tools := a.tools.List()
	params := make([]ai.Tool, 0, len(tools))

	// Convert all tools to AI parameters
	for _, t := range tools {
		// Parse the JSON schema
		var schema ai.Schema
		if err := json.Unmarshal(t.InputSchema(), &schema); err != nil {
			a.logger.Warn("failed to parse tool schema",
				"tool", t.Name(),
				"error", err,
				"raw_schema", string(t.InputSchema()))
			continue
		}

		params = append(params, ai.Tool{
			Name:        t.Name(),
			Description: t.Description(),
			InputSchema: schema,
		})
	}

	return params
}

// executeToolsAndGetFinalResponse executes tool calls and gets the final response.
func (a *assistant) executeToolsAndGetFinalResponse(
	ctx context.Context,
	resp *ai.Response,
	messages []ai.Message,
	systemPrompt string,
	req Request,
) (*ai.Response, []string, error) {
	// Execute tools
	toolResults, toolNames, _ := a.executeToolsWithRawResults(ctx, resp.ToolCalls)

	// Build messages with tool results
	// Only add assistant message if it has content to avoid API errors
	if resp.Content != "" {
		messages = append(messages, ai.Message{
			Role:    ai.Assistant,
			Content: resp.Content,
		})
	}

	// Add tool results as user message
	if len(toolResults) > 0 {
		toolMsg := "Tool Results:\n"
		for _, result := range toolResults {
			toolMsg += fmt.Sprintf("%s: %s\n", result.ID, result.Content)
		}
		messages = append(messages, ai.Message{
			Role:    ai.User,
			Content: toolMsg,
		})
	}

	// Get final response with tool results
	finalReq := &ai.Request{
		Model:       a.config.AI.DefaultModel,
		Messages:    messages,
		System:      systemPrompt,
		Temperature: req.Options.Temperature,
		MaxTokens:   req.Options.MaxTokens,
		Stream:      false,
		Tools:       a.getAvailableTools(), // Include tools again for potential follow-up
	}

	finalResp, err := a.ai.Chat(ctx, finalReq)
	if err != nil {
		return nil, nil, fmt.Errorf("final response failed: %w", err)
	}

	// Check if more tools need to be executed (recursive)
	if len(finalResp.ToolCalls) > 0 {
		recursiveResp, recursiveToolNames, err := a.executeToolsAndGetFinalResponse(ctx, finalResp, messages, systemPrompt, req)
		if err != nil {
			return nil, nil, err
		}
		// Accumulate tool names from recursive calls
		toolNames = append(toolNames, recursiveToolNames...)
		return recursiveResp, toolNames, nil
	}

	return finalResp, toolNames, nil
}

// executeToolsWithRawResults executes the requested tool calls and returns both formatted and raw results.
func (a *assistant) executeToolsWithRawResults(ctx context.Context, toolCalls []ai.ToolCall) ([]tool.ToolResult, []string, []any) {
	results := make([]tool.ToolResult, 0, len(toolCalls))
	toolNames := make([]string, 0, len(toolCalls))
	rawResults := make([]any, 0, len(toolCalls))

	for _, tc := range toolCalls {
		toolNames = append(toolNames, tc.Name)

		// Log tool call at Info level
		a.logger.Info("calling tool", "name", tc.Name, "call_id", tc.ID)

		// Convert arguments
		args := make(map[string]any)
		for k, v := range tc.Args {
			var val any
			if err := json.Unmarshal(v, &val); err != nil {
				// If unmarshal fails, use the raw value
				args[k] = string(v)
			} else {
				args[k] = val
			}
		}

		// Record the tool call
		exec := ToolExecution{
			Timestamp: time.Now(),
			Name:      tc.Name,
			Args:      args,
			CallID:    tc.ID,
		}

		// Check if tools manager is nil
		if a.tools == nil {
			exec.Success = false
			exec.Error = "Tool manager not available"
			a.recordToolExecution(exec)
			results = append(results, tool.ToolResult{
				ID:      tc.ID,
				Content: "Tool manager not available",
				Error:   fmt.Errorf("tool manager not initialized"),
			})
			rawResults = append(rawResults, nil)
			continue
		}

		// Get the tool
		t, exists := a.tools.Get(tc.Name)
		if !exists {
			exec.Success = false
			exec.Error = fmt.Sprintf("Tool %s not found", tc.Name)
			a.recordToolExecution(exec)
			results = append(results, tool.ToolResult{
				ID:      tc.ID,
				Content: fmt.Sprintf("Tool %s not found", tc.Name),
				Error:   fmt.Errorf("tool not found: %s", tc.Name),
			})
			rawResults = append(rawResults, nil)
			continue
		}

		// Execute the tool
		result, err := t.Execute(ctx, args)
		if err != nil {
			exec.Success = false
			exec.Error = err.Error()
			a.recordToolExecution(exec)
			results = append(results, tool.ToolResult{
				ID:      tc.ID,
				Content: fmt.Sprintf("Tool execution failed: %v", err),
				Error:   err,
			})
			rawResults = append(rawResults, nil)
			continue
		}

		// Capture the raw result for memory extraction
		rawResults = append(rawResults, result)

		// Format result
		var content string
		switch v := result.(type) {
		case string:
			content = v
		case map[string]any:
			if formatted, jsonErr := json.MarshalIndent(v, "", "  "); jsonErr == nil {
				content = string(formatted)
			} else {
				content = fmt.Sprintf("%v", v)
			}
		default:
			if formatted, jsonErr := json.MarshalIndent(v, "", "  "); jsonErr == nil {
				content = string(formatted)
			} else {
				content = fmt.Sprintf("%v", v)
			}
		}

		results = append(results, tool.ToolResult{
			ID:      tc.ID,
			Content: content,
		})

		// Record successful execution
		exec.Success = true
		exec.ResultLen = len(content)
		a.recordToolExecution(exec)

		// Log tool execution at Info level for visibility
		if strings.Contains(tc.Name, ".") {
			a.logger.Info("executed MCP tool",
				"name", tc.Name,
				"args", args,
				"success", true,
				"result_length", len(content))
		} else {
			a.logger.Info("executed built-in tool",
				"name", tc.Name,
				"args", args,
				"success", true,
				"result_length", len(content))
		}
	}

	return results, toolNames, rawResults
}

// executeTools is a wrapper for backward compatibility that only returns formatted results.
func (a *assistant) executeTools(ctx context.Context, toolCalls []ai.ToolCall) ([]tool.ToolResult, []string) {
	results, toolNames, _ := a.executeToolsWithRawResults(ctx, toolCalls)
	return results, toolNames
}
