package assistant

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/koopa0/assistant-go/internal/conversation"
	"github.com/koopa0/assistant-go/internal/extract"
	"github.com/koopa0/assistant-go/internal/memory"
	"github.com/koopa0/assistant-go/internal/prompt"
)

// buildContext constructs the messages and system prompt for the AI request.
func (a *assistant) buildContext(
	ctx context.Context,
	req Request,
	conv *conversation.Conversation,
) ([]ai.Message, string, error) {
	messages := []ai.Message{}

	// Load conversation history if not a new conversation
	if conv != nil && a.conv != nil {
		history, err := a.conv.GetMessages(ctx, conv.ID, 100) // Get last 100 messages for context
		if err != nil {
			a.logger.Warn("failed to load conversation history", "error", err)
		} else {
			for _, msg := range history {
				messages = append(messages, ai.Message{
					Role:    msg.Role,
					Content: msg.Content,
					Time:    msg.CreatedAt,
				})
			}
		}
	}

	// Add current message
	messages = append(messages, ai.Message{
		Role:    ai.User,
		Content: req.Query,
	})

	// Memory context is now handled in buildSystemPrompt

	// Build system prompt
	systemPrompt := a.buildSystemPrompt(ctx, req)

	return messages, systemPrompt, nil
}

// buildSystemPrompt constructs the system prompt for the AI.
func (a *assistant) buildSystemPrompt(ctx context.Context, req Request) string {
	// Use custom prompt if provided
	if req.Context.SystemPrompt != "" {
		return req.Context.SystemPrompt
	}

	// Build user info from config
	userInfo := prompt.UserInfo{
		Name:     a.config.Owner.Name,
		Email:    a.config.Owner.Email,
		Language: a.config.Owner.Preferences["language"],
		Timezone: a.config.Owner.Preferences["timezone"],
		Theme:    a.config.Owner.Preferences["theme"],
	}

	// Set defaults if not configured
	if userInfo.Name == "" {
		userInfo.Name = "User"
	}
	if userInfo.Language == "" {
		// Use app language if set, otherwise default to English
		if a.config.App.Language != "" {
			userInfo.Language = a.config.App.Language
		} else {
			userInfo.Language = "en"
		}
	}
	if userInfo.Timezone == "" {
		userInfo.Timezone = "UTC"
	}
	if userInfo.Theme == "" {
		userInfo.Theme = "dark"
	}

	// Use system prompt from internal/prompt package
	systemPrompt := prompt.GetSystemPrompt(userInfo, "")

	// Enhance with memory-specific instructions
	systemPrompt = prompt.EnhanceSystemPromptWithMemoryInstructions(systemPrompt)

	// Add temporal context
	loc, _ := time.LoadLocation(userInfo.Timezone)
	now := time.Now().In(loc)
	temporalContext := prompt.GenerateTemporalContext(now, userInfo.Timezone)
	systemPrompt += "\n\n" + temporalContext

	// Add memory context if available
	if a.memory != nil {
		memoryContent := a.buildMemoryContext(ctx, req)
		if memoryContent != "" {
			systemPrompt += "\n\n" + memoryContent
		}
	}

	// Add MCP capabilities to system prompt
	if req.Options.EnableMCPContext && a.mcp != nil {
		mcpContext := a.buildMCPCapabilitiesContext()
		if mcpContext != "" {
			systemPrompt += "\n\n" + mcpContext
		}
	}

	return systemPrompt
}

// memorySection represents a section of memory content to be included in context.
type memorySection struct {
	tag      string
	priority int // higher priority sections appear first
	content  string
}

// buildMemoryContext constructs structured memory content with XML tags.
// This provides clear structure for the AI to distinguish between different types of memories.
func (a *assistant) buildMemoryContext(ctx context.Context, req Request) string {
	a.logger.Info("Building memory context", "query", req.Query)

	if a.memory == nil {
		a.logger.Warn("Memory service is nil")
		return ""
	}

	// Collect all relevant memory sections
	sections := a.collectMemorySections(ctx, req)
	if len(sections) == 0 {
		a.logger.Info("No memory sections collected")
		return ""
	}

	// Sort by priority (higher first)
	for i := 0; i < len(sections); i++ {
		for j := i + 1; j < len(sections); j++ {
			if sections[j].priority > sections[i].priority {
				sections[i], sections[j] = sections[j], sections[i]
			}
		}
	}

	// Build final XML
	var result strings.Builder
	result.WriteString("<retrieved_memory>\n")
	for _, section := range sections {
		if section.content != "" {
			result.WriteString(section.content)
			result.WriteString("\n")
		}
	}
	result.WriteString("</retrieved_memory>")

	return result.String()
}

// collectMemorySections gathers all relevant memory sections based on query type.
func (a *assistant) collectMemorySections(ctx context.Context, req Request) []memorySection {
	var sections []memorySection
	query := strings.ToLower(req.Query)

	a.logger.Info("Collecting memory sections",
		"query", req.Query,
		"isTimeQuery", isTimeQuery(query),
		"isFactQuery", isFactQuery(query))

	// Determine query type and collect appropriate sections
	if isTimeQuery(query) {
		a.logger.Debug("Processing as time query")
		if content := a.buildScheduleSection(ctx, req); content != "" {
			sections = append(sections, memorySection{
				tag:      "schedules",
				priority: 10,
				content:  content,
			})
		}
	}

	if isFactQuery(query) {
		a.logger.Debug("Processing as fact query")
		if content := a.buildOwnerProfileSection(ctx); content != "" {
			sections = append(sections, memorySection{
				tag:      "owner_profile",
				priority: 9,
				content:  content,
			})
		}
	}

	// Always search for relevant memories
	a.logger.Debug("Searching for relevant memories")
	if content := a.buildRelevantMemoriesSection(ctx, req.Query); content != "" {
		a.logger.Info("Added relevant memories section")
		sections = append(sections, memorySection{
			tag:      "relevant_memories",
			priority: 8,
			content:  content,
		})
	}

	// For general queries, also include broader context
	if isGeneralQuery(query) || isPersonalQuery(query) {
		a.logger.Debug("Including broader memory context")
		if content := a.buildBroaderMemoryContext(ctx); content != "" {
			sections = append(sections, memorySection{
				tag:      "broader_context",
				priority: 7,
				content:  content,
			})
		}
	}

	// Add recent conversation with lower priority
	if content := a.buildConversationSection(); content != "" {
		sections = append(sections, memorySection{
			tag:      "recent_conversation",
			priority: 2,
			content:  content,
		})
	}

	// Add MCP resources if enabled
	if a.mcp != nil && req.Options.EnableMCPResources {
		if content := a.buildMCPSection(ctx, query); content != "" {
			sections = append(sections, memorySection{
				tag:      "mcp_resources",
				priority: 3,
				content:  content,
			})
		}
	}

	return sections
}

// Query type detection helpers
func isTimeQuery(query string) bool {
	timePatterns := []string{
		"tomorrow", "today", "yesterday", "next week", "schedule", "when",
		"明天", "後天", "星期", "週", "什麼時候", "行程", "安排",
	}
	for _, pattern := range timePatterns {
		if strings.Contains(query, pattern) {
			return true
		}
	}
	return false
}

func isFactQuery(query string) bool {
	factPatterns := []string{
		"remember", "like", "prefer", "what", "who", "favorite",
		"記得", "喜歡", "什麼", "時候", "今天", "昨天", "上次",
	}
	for _, pattern := range factPatterns {
		if strings.Contains(query, pattern) {
			return true
		}
	}
	return false
}

// buildScheduleSection creates schedule section for time queries.
func (a *assistant) buildScheduleSection(ctx context.Context, req Request) string {
	a.logger.Info("Building schedule section")

	// Search for schedule memories
	memories, err := a.memory.Search(ctx, "schedule 行程 時間 星期 週", 50)
	if err != nil {
		a.logger.Error("Failed to search for schedules", "error", err)
		return ""
	}

	// Filter only schedule type memories
	var scheduleMemories []*memory.Memory
	for _, mem := range memories {
		if mem.Type == memory.MemoryTypeSchedule {
			scheduleMemories = append(scheduleMemories, mem)
		}
	}

	if len(scheduleMemories) == 0 {
		a.logger.Info("No schedule memories found")
		return ""
	}

	a.logger.Info("Found schedule memories", "count", len(scheduleMemories))

	// Group schedules by type (one-time vs recurring)
	var oneTimeSchedules []*memory.Memory
	var recurringSchedules []*memory.Memory

	for _, mem := range scheduleMemories {
		content := strings.ToLower(mem.Content)
		// Check if it's a recurring schedule
		if containsAny(content, []string{"每週", "每周", "星期", "週一", "週二", "週三", "週四", "週五", "週六", "週日",
			"every week", "weekly", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"}) {
			recurringSchedules = append(recurringSchedules, mem)
		} else {
			oneTimeSchedules = append(oneTimeSchedules, mem)
		}
	}

	var parts []string
	parts = append(parts, "<schedules>")

	// Add one-time schedules
	if len(oneTimeSchedules) > 0 {
		parts = append(parts, "  <one_time_events>")
		for _, sched := range oneTimeSchedules {
			parts = append(parts, fmt.Sprintf("    <event id=\"%s\">%s</event>",
				sched.ID.String(), sched.Content))
		}
		parts = append(parts, "  </one_time_events>")
	}

	// Add recurring schedules
	if len(recurringSchedules) > 0 {
		parts = append(parts, "  <recurring_events>")
		for _, sched := range recurringSchedules {
			parts = append(parts, fmt.Sprintf("    <event id=\"%s\">%s</event>",
				sched.ID.String(), sched.Content))
		}
		parts = append(parts, "  </recurring_events>")
	}

	parts = append(parts, "</schedules>")
	return strings.Join(parts, "\n")
}

// containsAny checks if the text contains any of the patterns
func containsAny(text string, patterns []string) bool {
	for _, pattern := range patterns {
		if strings.Contains(text, pattern) {
			return true
		}
	}
	return false
}

// isGeneralQuery checks if the query is asking for general information.
func isGeneralQuery(query string) bool {
	generalPatterns := []string{
		"tell me about", "what do you know", "你知道", "告訴我", "關於我",
		"my information", "我的資訊", "我的資料",
	}
	for _, pattern := range generalPatterns {
		if strings.Contains(query, pattern) {
			return true
		}
	}
	return false
}

// isPersonalQuery checks if the query is asking about personal information.
func isPersonalQuery(query string) bool {
	personalPatterns := []string{
		"我是誰", "who am i", "about me", "我的", "my",
		"personal", "個人",
	}
	for _, pattern := range personalPatterns {
		if strings.Contains(query, pattern) {
			return true
		}
	}
	return false
}

// buildOwnerProfileSection creates owner profile section with facts and preferences.
func (a *assistant) buildOwnerProfileSection(ctx context.Context) string {
	// TODO: This function needs to be updated for the new memory system
	// The new API uses GetContext(ctx, conversationPreview, limit) and returns []*sqlc.Memory
	return ""
}

// buildBroaderMemoryContext retrieves a wider range of memories for comprehensive context.
func (a *assistant) buildBroaderMemoryContext(ctx context.Context) string {
	a.logger.Info("Building broader memory context")

	// Search for various categories of memories
	categories := []string{
		"preference 喜歡 like",
		"schedule 行程 時間",
		"fact 資訊 information",
		"personal 個人 生日",
		"work 工作 專長",
	}

	allMemories := make(map[string]*memory.Memory)

	for _, category := range categories {
		memories, err := a.memory.Search(ctx, category, 20)
		if err != nil {
			a.logger.Warn("Failed to search category", "category", category, "error", err)
			continue
		}

		// Deduplicate by ID
		for _, mem := range memories {
			id := mem.ID.String()
			if _, exists := allMemories[id]; !exists {
				allMemories[id] = mem
			}
		}
	}

	if len(allMemories) == 0 {
		return ""
	}

	// Convert map to slice and sort by type and recency
	memories := make([]*memory.Memory, 0, len(allMemories))
	for _, mem := range allMemories {
		memories = append(memories, mem)
	}

	sort.Slice(memories, func(i, j int) bool {
		// First sort by type
		if memories[i].Type != memories[j].Type {
			return memories[i].Type < memories[j].Type
		}
		// Then by update time
		return memories[i].UpdatedAt.After(memories[j].UpdatedAt)
	})

	// Build formatted output
	var parts []string
	parts = append(parts, "<broader_context>")

	currentType := memory.MemoryType("")
	for _, mem := range memories {
		// Add type header when type changes
		if mem.Type != currentType {
			if currentType != "" {
				parts = append(parts, fmt.Sprintf("  </%s>", currentType))
			}
			parts = append(parts, fmt.Sprintf("  <%s>", mem.Type))
			currentType = mem.Type
		}

		parts = append(parts, fmt.Sprintf("    <memory id=\"%s\">%s</memory>",
			mem.ID.String(), mem.Content))
	}

	if currentType != "" {
		parts = append(parts, fmt.Sprintf("  </%s>", currentType))
	}

	parts = append(parts, "</broader_context>")
	return strings.Join(parts, "\n")
}

// buildRelevantMemoriesSection performs memory search and formats results.
func (a *assistant) buildRelevantMemoriesSection(ctx context.Context, query string) string {
	a.logger.Info("Building relevant memories section", "query", query)

	// Use GetContextForLLM to get structured, formatted context
	llmContext, err := a.memory.GetContextForLLM(ctx, query, 50) // Use higher limit for richer context
	if err != nil {
		// Fallback to basic search if new method fails
		a.logger.Warn("Failed to get structured context, falling back to basic search", "error", err)
		return a.buildRelevantMemoriesFallback(ctx, query)
	}

	if llmContext == "" {
		a.logger.Info("No relevant context found for query", "query", query)
		return ""
	}

	a.logger.Info("Retrieved structured context", "length", len(llmContext))
	return llmContext
}

// buildRelevantMemoriesFallback is the old implementation for backwards compatibility.
func (a *assistant) buildRelevantMemoriesFallback(ctx context.Context, query string) string {
	// Search without limit to get ALL relevant memories
	memories, err := a.memory.Search(ctx, query, 0) // 0 means no limit
	if err != nil {
		a.logger.Error("Memory search failed", "error", err)
		return ""
	}

	if len(memories) == 0 {
		a.logger.Info("No memories found for query", "query", query)
		return ""
	}

	a.logger.Info("Found memories before filtering", "count", len(memories))

	// Filter out conflicting memories
	filteredMemories := a.filterConflictingMemories(memories)
	a.logger.Info("Memories after conflict resolution", "count", len(filteredMemories))

	if len(filteredMemories) == 0 {
		return ""
	}

	var parts []string
	parts = append(parts, "<relevant_memories>")

	for i, mem := range filteredMemories {
		// Format memory with ID, type and content for better traceability
		a.logger.Debug("Including memory",
			"index", i,
			"id", mem.ID,
			"type", mem.Type,
			"content", truncateContent(mem.Content, 50))

		parts = append(parts, fmt.Sprintf("  <memory id=\"%s\" type=\"%s\">%s</memory>",
			mem.ID.String(), mem.Type, mem.Content))
	}

	parts = append(parts, "</relevant_memories>")
	return strings.Join(parts, "\n")
}

// buildConversationSection gets recent conversation messages.
func (a *assistant) buildConversationSection() string {
	// TODO: This function needs to be updated for the new memory system
	return ""
	/*
		convContext := a.memory.GetConversationContext(5)
		if len(convContext) == 0 {
			return ""
		}

		var parts []string
		parts = append(parts, "<recent_conversation>")
		for _, msg := range convContext {
			parts = append(parts, fmt.Sprintf("  <message role=\"%s\">%s</message>", msg.Role, msg.Content))
		}
		parts = append(parts, "</recent_conversation>")
		return strings.Join(parts, "\n")
	*/
}

// buildMCPSection adds relevant MCP resources to the context.
func (a *assistant) buildMCPSection(ctx context.Context, query string) string {
	resources, err := a.mcp.GetResources(ctx)
	if err != nil || len(resources) == 0 {
		return ""
	}

	var parts []string
	parts = append(parts, "<mcp_resources>")

	// Add server capabilities summary
	serverSummary := make(map[string][]string)
	for _, res := range resources {
		serverSummary[res.ServerName] = append(serverSummary[res.ServerName], res.Name)
	}

	for server, resNames := range serverSummary {
		parts = append(parts, fmt.Sprintf("  <server name=\"%s\" resources=\"%d\"/>", server, len(resNames)))
	}

	// Add detailed resource info for query-relevant resources
	queryLower := strings.ToLower(query)
	for _, res := range resources {
		// Check if resource might be relevant to the query
		if strings.Contains(strings.ToLower(res.Name), queryLower) ||
			strings.Contains(strings.ToLower(res.Description), queryLower) {
			parts = append(parts, fmt.Sprintf("  <resource server=\"%s\" uri=\"%s\" type=\"%s\">%s</resource>",
				res.ServerName, res.URI, res.MimeType, res.Name))
		}
	}

	parts = append(parts, "</mcp_resources>")
	return strings.Join(parts, "\n")
}

// buildMCPCapabilitiesContext returns a summary of available MCP capabilities.
func (a *assistant) buildMCPCapabilitiesContext() string {
	if a.mcp == nil {
		return ""
	}

	var parts []string
	servers := a.mcp.ListServers()

	for _, server := range servers {
		caps, exists := a.mcp.GetServerCapabilities(server)
		if !exists {
			continue
		}

		var capabilities []string
		if caps.Tools {
			capabilities = append(capabilities, "tools")
		}
		if caps.Resources {
			capabilities = append(capabilities, "resources")
		}
		if caps.Prompts {
			capabilities = append(capabilities, "prompts")
		}

		if len(capabilities) > 0 {
			parts = append(parts, fmt.Sprintf("- %s: %s", server, strings.Join(capabilities, ", ")))
		}
	}

	if len(parts) == 0 {
		return ""
	}

	return "Connected MCP servers:\n" + strings.Join(parts, "\n")
}

// Formatting helpers
// TODO: These functions need to be updated for the new memory system
/*
func (a *assistant) formatSchedule(sched memory.Schedule, indent int) string {
	indentStr := strings.Repeat(" ", indent)
	var parts []string
	parts = append(parts, fmt.Sprintf("%s<schedule type=\"%s\">", indentStr, sched.EventType))
	parts = append(parts, fmt.Sprintf("%s  <event>%s</event>", indentStr, sched.EventName))
	if sched.TimeOfDay != "" {
		parts = append(parts, fmt.Sprintf("%s  <time>%s</time>", indentStr, sched.TimeOfDay))
	}
	parts = append(parts, fmt.Sprintf("%s  <description>%s</description>", indentStr, sched.Description))
	parts = append(parts, fmt.Sprintf("%s</schedule>", indentStr))
	return strings.Join(parts, "\n")
}

func (a *assistant) formatFact(fact memory.Fact, indent int) string {
	indentStr := strings.Repeat(" ", indent)
	timeStr := ""
	if factMeta, ok := fact.Metadata.(knowledge.FactMetadata); ok && factMeta.Context != "" {
		if strings.Contains(factMeta.Context, "Time context:") {
			timeStr = strings.TrimPrefix(factMeta.Context, "Time context: ")
		}
	}
	if timeStr != "" {
		return fmt.Sprintf("%s<fact time=\"%s\">%s</fact>", indentStr, timeStr, fact.Content)
	}
	return fmt.Sprintf("%s<fact>%s</fact>", indentStr, fact.Content)
}

func (a *assistant) formatMemory(mem *knowledge.Node, indent int) string {
	indentStr := strings.Repeat(" ", indent)
	timeStr := ""
	if factMeta, ok := mem.Metadata.(knowledge.FactMetadata); ok && factMeta.Context != "" {
		if strings.Contains(factMeta.Context, "Time context:") {
			timeStr = strings.TrimPrefix(factMeta.Context, "Time context: ")
		}
	}
	if timeStr != "" {
		return fmt.Sprintf("%s<memory time=\"%s\">%s</memory>", indentStr, timeStr, mem.Content)
	}
	return fmt.Sprintf("%s<memory>%s</memory>", indentStr, mem.Content)
}
*/

// filterConflictingMemories removes conflicting memories, keeping only the most recent/relevant.
func (a *assistant) filterConflictingMemories(memories []*memory.Memory) []*memory.Memory {
	// Group memories by activity/topic
	groups := make(map[string][]*memory.Memory)

	for _, mem := range memories {
		// Extract key for grouping
		key := a.extractMemoryGroupKey(mem)
		if key == "" {
			// No specific key, include as-is
			key = fmt.Sprintf("_unique_%s", mem.ID)
		}
		groups[key] = append(groups[key], mem)
	}

	// For each group, keep only the best memory
	var result []*memory.Memory
	for key, group := range groups {
		if len(group) == 1 {
			// No conflict
			result = append(result, group[0])
		} else {
			// Multiple memories for same topic - choose best
			a.logger.Debug("Resolving conflict", "key", key, "memories", len(group))
			best := a.selectBestMemory(group)
			result = append(result, best)
		}
	}

	// Sort by relevance/recency
	sort.Slice(result, func(i, j int) bool {
		// More recent memories first
		return result[i].UpdatedAt.After(result[j].UpdatedAt)
	})

	// Limit to reasonable number but keep more for comprehensive context
	if len(result) > memory.ConflictFilterLimit {
		result = result[:memory.ConflictFilterLimit]
	}

	return result
}

// extractMemoryGroupKey extracts a key for grouping related memories.
func (a *assistant) extractMemoryGroupKey(mem *memory.Memory) string {
	// For schedules, include time info to avoid grouping different time slots
	if mem.Type == memory.MemoryTypeSchedule {
		// Extract both activity and time from content
		content := strings.ToLower(mem.Content)

		// Try to extract day and time info
		var timeInfo string
		dayPatterns := []string{"星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日",
			"週一", "週二", "週三", "週四", "週五", "週六", "週日",
			"monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"}
		for _, day := range dayPatterns {
			if strings.Contains(content, day) {
				timeInfo += "_" + day
				break
			}
		}

		// Extract time patterns
		timePatterns := []string{"早上", "上午", "下午", "晚上", "morning", "afternoon", "evening", "night"}
		for _, timeP := range timePatterns {
			if strings.Contains(content, timeP) {
				timeInfo += "_" + timeP
				break
			}
		}

		// Get activity
		activity := ""
		for _, entity := range mem.Entities {
			if entity.Type == extract.EntityActivity {
				activity = strings.ToLower(entity.Name)
				break
			}
		}
		if activity == "" {
			activity = a.extractActivityFromContent(mem.Content)
		}

		if activity != "" {
			return fmt.Sprintf("schedule_%s%s", activity, timeInfo)
		}

		// If no activity found, use full content hash to ensure uniqueness
		return fmt.Sprintf("schedule_%s", mem.ID.String()[:8])
	}

	// For preferences, check for negations and positive/negative patterns
	if mem.Type == memory.MemoryTypePreference {
		content := strings.ToLower(mem.Content)
		// Extract subject of preference
		subject := ""
		if strings.Contains(content, "喜歡") || strings.Contains(content, "like") {
			// Extract what comes after 喜歡/like
			if idx := strings.Index(content, "喜歡"); idx != -1 {
				subject = strings.TrimSpace(content[idx+6:])
			} else if idx := strings.Index(content, "like"); idx != -1 {
				subject = strings.TrimSpace(content[idx+4:])
			}
		} else if strings.Contains(content, "不喜歡") || strings.Contains(content, "don't like") {
			if idx := strings.Index(content, "不喜歡"); idx != -1 {
				subject = "not_" + strings.TrimSpace(content[idx+9:])
			} else if idx := strings.Index(content, "don't like"); idx != -1 {
				subject = "not_" + strings.TrimSpace(content[idx+10:])
			}
		}

		if subject != "" {
			// Normalize common subjects
			subject = strings.ReplaceAll(subject, "喝", "")
			subject = strings.ReplaceAll(subject, "吃", "")
			subject = strings.TrimSpace(subject)
			return fmt.Sprintf("preference_%s", subject)
		}
	}

	// For other types, use semantic ID prefix
	if parts := strings.Split(mem.SemanticID, "_"); len(parts) >= 2 {
		return strings.Join(parts[:2], "_")
	}

	return ""
}

// selectBestMemory selects the best memory from a conflicting group.
func (a *assistant) selectBestMemory(memories []*memory.Memory) *memory.Memory {
	if len(memories) == 0 {
		return nil
	}

	// Sort by multiple criteria
	sort.Slice(memories, func(i, j int) bool {
		// 1. Prefer higher confidence
		if memories[i].Confidence != memories[j].Confidence {
			return memories[i].Confidence > memories[j].Confidence
		}

		// 2. Prefer more recent
		if !memories[i].UpdatedAt.Equal(memories[j].UpdatedAt) {
			return memories[i].UpdatedAt.After(memories[j].UpdatedAt)
		}

		// 3. Prefer more accessed
		return memories[i].AccessCount > memories[j].AccessCount
	})

	return memories[0]
}

// extractActivityFromContent extracts activity name from content.
func (a *assistant) extractActivityFromContent(content string) string {
	content = strings.ToLower(content)

	// Common activity mappings
	activityMap := map[string]string{
		"泰拳":        "muay_thai",
		"muay thai": "muay_thai",
		"boxing":    "boxing",
		"瑜珈":        "yoga",
		"yoga":      "yoga",
	}

	for key, value := range activityMap {
		if strings.Contains(content, key) {
			return value
		}
	}

	return ""
}
