package assistant

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"testing"

	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/koopa0/assistant-go/internal/platform/config"
	"github.com/koopa0/assistant-go/internal/platform/logger"
	"github.com/koopa0/assistant-go/internal/tool"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// Mock tool for testing
type mockTool struct {
	name        string
	description string
	schema      json.RawMessage
	executeFunc func(ctx context.Context, args any) (any, error)
}

func (m *mockTool) Name() string                 { return m.name }
func (m *mockTool) Description() string          { return m.description }
func (m *mockTool) InputSchema() json.RawMessage { return m.schema }
func (m *mockTool) Execute(ctx context.Context, args any) (any, error) {
	if m.executeFunc != nil {
		return m.executeFunc(ctx, args)
	}
	return "mock result", nil
}

func TestGetAvailableTools(t *testing.T) {
	tests := []struct {
		name          string
		setupTools    func() *tool.Manager
		expectedCount int
		validateTools func(t *testing.T, tools []ai.Tool)
	}{
		{
			name: "single valid tool",
			setupTools: func() *tool.Manager {
				mgr := tool.NewManager(tool.Config{})
				schema := ai.Schema{
					Type: "object",
					Properties: map[string]ai.Property{
						"query": {Type: "string", Description: "Search query"},
					},
					Required: []string{"query"},
				}
				schemaBytes, _ := json.Marshal(schema)
				mgr.Register(&mockTool{
					name:        "search",
					description: "Search the web",
					schema:      schemaBytes,
				})
				return mgr
			},
			expectedCount: 1,
			validateTools: func(t *testing.T, tools []ai.Tool) {
				assert.Equal(t, "search", tools[0].Name)
				assert.Equal(t, "Search the web", tools[0].Description)
				assert.NotNil(t, tools[0].InputSchema)
			},
		},
		{
			name: "multiple tools",
			setupTools: func() *tool.Manager {
				mgr := tool.NewManager(tool.Config{})
				schema1 := ai.Schema{
					Type:       "object",
					Properties: map[string]ai.Property{"path": {Type: "string"}},
				}
				schema2 := ai.Schema{
					Type:       "object",
					Properties: map[string]ai.Property{"content": {Type: "string"}},
				}
				schema1Bytes, _ := json.Marshal(schema1)
				schema2Bytes, _ := json.Marshal(schema2)

				mgr.Register(&mockTool{
					name:        "read_file",
					description: "Read a file",
					schema:      schema1Bytes,
				})
				mgr.Register(&mockTool{
					name:        "write_file",
					description: "Write a file",
					schema:      schema2Bytes,
				})
				return mgr
			},
			expectedCount: 2,
			validateTools: func(t *testing.T, tools []ai.Tool) {
				names := make(map[string]bool)
				for _, tool := range tools {
					names[tool.Name] = true
				}
				assert.True(t, names["read_file"])
				assert.True(t, names["write_file"])
			},
		},
		{
			name: "invalid schema skipped",
			setupTools: func() *tool.Manager {
				mgr := tool.NewManager(tool.Config{})
				mgr.Register(&mockTool{
					name:        "bad_tool",
					description: "Tool with bad schema",
					schema:      []byte("invalid json"),
				})
				// Add a valid tool too
				validSchema, _ := json.Marshal(ai.Schema{Type: "object"})
				mgr.Register(&mockTool{
					name:        "good_tool",
					description: "Valid tool",
					schema:      validSchema,
				})
				return mgr
			},
			expectedCount: 1, // Only valid tool should be returned
			validateTools: func(t *testing.T, tools []ai.Tool) {
				assert.Equal(t, "good_tool", tools[0].Name)
			},
		},
		{
			name: "empty tool list",
			setupTools: func() *tool.Manager {
				return tool.NewManager(tool.Config{})
			},
			expectedCount: 0,
			validateTools: func(t *testing.T, tools []ai.Tool) {
				assert.Empty(t, tools)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := &assistant{
				tools:  tt.setupTools(),
				logger: logger.NewConsoleLogger(),
			}

			tools := a.getAvailableTools()
			assert.Len(t, tools, tt.expectedCount)

			if tt.validateTools != nil {
				tt.validateTools(t, tools)
			}
		})
	}
}

func TestExecuteTools(t *testing.T) {
	tests := []struct {
		name           string
		toolCalls      []ai.ToolCall
		setupTools     func() *tool.Manager
		expectedCount  int
		validateResult func(t *testing.T, results []tool.ToolResult, names []string)
	}{
		{
			name: "single tool execution",
			toolCalls: []ai.ToolCall{
				{
					ID:   "call_123",
					Name: "search",
					Args: ai.Arguments{"query": json.RawMessage(`"Go tutorials"`)},
				},
			},
			setupTools: func() *tool.Manager {
				mgr := tool.NewManager(tool.Config{})
				schema, _ := json.Marshal(ai.Schema{Type: "object"})
				mgr.Register(&mockTool{
					name:        "search",
					description: "Search tool",
					schema:      schema,
					executeFunc: func(ctx context.Context, args any) (any, error) {
						argsMap := args.(map[string]any)
						query := argsMap["query"].(string)
						return map[string]any{
							"results": []string{
								"Tutorial 1: " + query,
								"Tutorial 2: " + query,
							},
						}, nil
					},
				})
				return mgr
			},
			expectedCount: 1,
			validateResult: func(t *testing.T, results []tool.ToolResult, names []string) {
				assert.Equal(t, "call_123", results[0].ID)
				assert.Contains(t, results[0].Content, "Tutorial 1")
				assert.NoError(t, results[0].Error)
				assert.Contains(t, names, "search")
			},
		},
		{
			name: "multiple tool executions",
			toolCalls: []ai.ToolCall{
				{
					ID:   "call_1",
					Name: "tool1",
					Args: ai.Arguments{},
				},
				{
					ID:   "call_2",
					Name: "tool2",
					Args: ai.Arguments{},
				},
			},
			setupTools: func() *tool.Manager {
				mgr := tool.NewManager(tool.Config{})
				schema, _ := json.Marshal(ai.Schema{Type: "object"})
				mgr.Register(&mockTool{
					name:   "tool1",
					schema: schema,
					executeFunc: func(ctx context.Context, args any) (any, error) {
						return "result1", nil
					},
				})
				mgr.Register(&mockTool{
					name:   "tool2",
					schema: schema,
					executeFunc: func(ctx context.Context, args any) (any, error) {
						return "result2", nil
					},
				})
				return mgr
			},
			expectedCount: 2,
			validateResult: func(t *testing.T, results []tool.ToolResult, names []string) {
				assert.Len(t, results, 2)
				assert.Equal(t, "result1", results[0].Content)
				assert.Equal(t, "result2", results[1].Content)
				assert.Equal(t, []string{"tool1", "tool2"}, names)
			},
		},
		{
			name: "tool not found",
			toolCalls: []ai.ToolCall{
				{
					ID:   "call_404",
					Name: "nonexistent",
					Args: ai.Arguments{},
				},
			},
			setupTools: func() *tool.Manager {
				return tool.NewManager(tool.Config{})
			},
			expectedCount: 1,
			validateResult: func(t *testing.T, results []tool.ToolResult, names []string) {
				assert.Error(t, results[0].Error)
				assert.Contains(t, results[0].Content, "not found")
				assert.Contains(t, names, "nonexistent")
			},
		},
		{
			name: "tool execution error",
			toolCalls: []ai.ToolCall{
				{
					ID:   "call_err",
					Name: "failing_tool",
					Args: ai.Arguments{},
				},
			},
			setupTools: func() *tool.Manager {
				mgr := tool.NewManager(tool.Config{})
				schema, _ := json.Marshal(ai.Schema{Type: "object"})
				mgr.Register(&mockTool{
					name:   "failing_tool",
					schema: schema,
					executeFunc: func(ctx context.Context, args any) (any, error) {
						return nil, errors.New("tool execution failed")
					},
				})
				return mgr
			},
			expectedCount: 1,
			validateResult: func(t *testing.T, results []tool.ToolResult, names []string) {
				assert.Error(t, results[0].Error)
				assert.Contains(t, results[0].Content, "failed")
			},
		},
		{
			name: "complex arguments handling",
			toolCalls: []ai.ToolCall{
				{
					ID:   "call_complex",
					Name: "complex_tool",
					Args: ai.Arguments{
						"string_arg": json.RawMessage(`"hello"`),
						"number_arg": json.RawMessage(`42`),
						"bool_arg":   json.RawMessage(`true`),
						"array_arg":  json.RawMessage(`["a", "b", "c"]`),
						"object_arg": json.RawMessage(`{"key": "value"}`),
					},
				},
			},
			setupTools: func() *tool.Manager {
				mgr := tool.NewManager(tool.Config{})
				schema, _ := json.Marshal(ai.Schema{Type: "object"})
				mgr.Register(&mockTool{
					name:   "complex_tool",
					schema: schema,
					executeFunc: func(ctx context.Context, args any) (any, error) {
						// Verify arguments were parsed correctly
						argsMap := args.(map[string]any)
						assert.Equal(t, "hello", argsMap["string_arg"])
						assert.Equal(t, float64(42), argsMap["number_arg"])
						assert.Equal(t, true, argsMap["bool_arg"])
						assert.IsType(t, []any{}, argsMap["array_arg"])
						assert.IsType(t, map[string]any{}, argsMap["object_arg"])
						return "success", nil
					},
				})
				return mgr
			},
			expectedCount: 1,
			validateResult: func(t *testing.T, results []tool.ToolResult, names []string) {
				assert.Equal(t, "success", results[0].Content)
				assert.NoError(t, results[0].Error)
			},
		},
		{
			name: "MCP tool execution (dotted name)",
			toolCalls: []ai.ToolCall{
				{
					ID:   "call_mcp",
					Name: "server.tool_name",
					Args: ai.Arguments{},
				},
			},
			setupTools: func() *tool.Manager {
				mgr := tool.NewManager(tool.Config{})
				schema, _ := json.Marshal(ai.Schema{Type: "object"})
				mgr.Register(&mockTool{
					name:   "server.tool_name",
					schema: schema,
					executeFunc: func(ctx context.Context, args any) (any, error) {
						return "MCP tool result", nil
					},
				})
				return mgr
			},
			expectedCount: 1,
			validateResult: func(t *testing.T, results []tool.ToolResult, names []string) {
				assert.Equal(t, "MCP tool result", results[0].Content)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := &assistant{
				tools:  tt.setupTools(),
				logger: logger.NewConsoleLogger(),
			}

			results, names := a.executeTools(context.Background(), tt.toolCalls)

			assert.Len(t, results, tt.expectedCount)
			assert.Len(t, names, tt.expectedCount)

			if tt.validateResult != nil {
				tt.validateResult(t, results, names)
			}
		})
	}
}

func TestExecuteToolsAndGetFinalResponse(t *testing.T) {
	tests := []struct {
		name           string
		initialResp    *ai.Response
		setupAI        func() *mockAIService
		setupTools     func() *tool.Manager
		validateResult func(t *testing.T, resp *ai.Response, toolNames []string, err error)
	}{
		{
			name: "single tool execution with final response",
			initialResp: &ai.Response{
				Content: "Let me search for that.",
				ToolCalls: []ai.ToolCall{
					{
						ID:   "call_123",
						Name: "search",
						Args: ai.Arguments{"query": json.RawMessage(`"test"`)},
					},
				},
			},
			setupAI: func() *mockAIService {
				return &mockAIService{
					chatFunc: func(ctx context.Context, req *ai.Request) (*ai.Response, error) {
						// Verify tool results are in messages
						lastMsg := req.Messages[len(req.Messages)-1]
						assert.Contains(t, lastMsg.Content, "Tool Results")

						return &ai.Response{
							Content: "Based on the search results, here's what I found...",
							Usage:   ai.Usage{Input: 100, Output: 50},
						}, nil
					},
				}
			},
			setupTools: func() *tool.Manager {
				mgr := tool.NewManager(tool.Config{})
				schema, _ := json.Marshal(ai.Schema{Type: "object"})
				mgr.Register(&mockTool{
					name:   "search",
					schema: schema,
					executeFunc: func(ctx context.Context, args any) (any, error) {
						argsMap := args.(map[string]any)
						return "Search results for: " + argsMap["query"].(string), nil
					},
				})
				return mgr
			},
			validateResult: func(t *testing.T, resp *ai.Response, toolNames []string, err error) {
				require.NoError(t, err)
				assert.Contains(t, resp.Content, "Based on the search results")
				assert.Contains(t, toolNames, "search")
			},
		},
		{
			name: "recursive tool execution",
			initialResp: &ai.Response{
				Content: "First tool call",
				ToolCalls: []ai.ToolCall{
					{ID: "1", Name: "tool1", Args: ai.Arguments{}},
				},
			},
			setupAI: func() *mockAIService {
				callCount := 0
				return &mockAIService{
					chatFunc: func(ctx context.Context, req *ai.Request) (*ai.Response, error) {
						callCount++
						if callCount == 1 {
							// First response: trigger another tool
							return &ai.Response{
								Content: "Need more info",
								ToolCalls: []ai.ToolCall{
									{ID: "2", Name: "tool2", Args: ai.Arguments{}},
								},
							}, nil
						}
						// Final response
						return &ai.Response{
							Content: "All done!",
						}, nil
					},
				}
			},
			setupTools: func() *tool.Manager {
				mgr := tool.NewManager(tool.Config{})
				schema, _ := json.Marshal(ai.Schema{Type: "object"})
				mgr.Register(&mockTool{name: "tool1", schema: schema})
				mgr.Register(&mockTool{name: "tool2", schema: schema})
				return mgr
			},
			validateResult: func(t *testing.T, resp *ai.Response, toolNames []string, err error) {
				require.NoError(t, err)
				assert.Equal(t, "All done!", resp.Content)
				assert.Len(t, toolNames, 2)
			},
		},
		{
			name: "AI error during final response",
			initialResp: &ai.Response{
				Content: "Executing tool",
				ToolCalls: []ai.ToolCall{
					{ID: "1", Name: "tool", Args: ai.Arguments{}},
				},
			},
			setupAI: func() *mockAIService {
				return &mockAIService{
					chatFunc: func(ctx context.Context, req *ai.Request) (*ai.Response, error) {
						return nil, errors.New("AI service error")
					},
				}
			},
			setupTools: func() *tool.Manager {
				mgr := tool.NewManager(tool.Config{})
				schema, _ := json.Marshal(ai.Schema{Type: "object"})
				mgr.Register(&mockTool{name: "tool", schema: schema})
				return mgr
			},
			validateResult: func(t *testing.T, resp *ai.Response, toolNames []string, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "final response failed")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := &assistant{
				ai:     tt.setupAI(),
				tools:  tt.setupTools(),
				logger: logger.NewConsoleLogger(),
				config: DefaultConfig(),
			}

			messages := []ai.Message{{Role: ai.User, Content: "test"}}
			systemPrompt := "Be helpful"
			req := Request{Options: Options{Temperature: 0.7}}

			resp, toolNames, err := a.executeToolsAndGetFinalResponse(
				context.Background(),
				tt.initialResp,
				messages,
				systemPrompt,
				req,
			)

			tt.validateResult(t, resp, toolNames, err)
		})
	}
}

// Fuzzing test for tool argument parsing
func FuzzToolArgumentParsing(f *testing.F) {
	// Add seed corpus
	f.Add(`{"key": "value"}`)
	f.Add(`"simple string"`)
	f.Add(`123`)
	f.Add(`true`)
	f.Add(`null`)
	f.Add(`[1, 2, 3]`)
	f.Add(`{"nested": {"key": "value"}}`)

	f.Fuzz(func(t *testing.T, argJSON string) {
		a := &assistant{
			tools:  tool.NewManager(tool.Config{}),
			logger: logger.NewConsoleLogger(),
		}

		// Register a mock tool
		schema, _ := json.Marshal(ai.Schema{Type: "object"})
		a.tools.Register(&mockTool{
			name:   "test_tool",
			schema: schema,
			executeFunc: func(ctx context.Context, args any) (any, error) {
				// Just verify we received some arguments
				return "ok", nil
			},
		})

		// Create tool call with fuzzed argument
		toolCall := ai.ToolCall{
			ID:   "fuzz_call",
			Name: "test_tool",
			Args: ai.Arguments{"fuzzed": json.RawMessage(argJSON)},
		}

		// Should not panic
		results, _ := a.executeTools(context.Background(), []ai.ToolCall{toolCall})
		assert.Len(t, results, 1)
	})
}

// Benchmark tool execution
func BenchmarkExecuteTools(b *testing.B) {
	// Setup
	mgr := tool.NewManager(tool.Config{})
	schema, _ := json.Marshal(ai.Schema{Type: "object"})

	for i := 0; i < 10; i++ {
		mgr.Register(&mockTool{
			name:   fmt.Sprintf("tool_%d", i),
			schema: schema,
			executeFunc: func(ctx context.Context, args any) (any, error) {
				return map[string]any{
					"result": "processed",
					"data":   []int{1, 2, 3, 4, 5},
				}, nil
			},
		})
	}

	a := &assistant{
		tools:  mgr,
		logger: logger.NewConsoleLogger(),
	}

	// Create tool calls
	toolCalls := make([]ai.ToolCall, 5)
	for i := 0; i < 5; i++ {
		toolCalls[i] = ai.ToolCall{
			ID:   fmt.Sprintf("call_%d", i),
			Name: fmt.Sprintf("tool_%d", i),
			Args: ai.Arguments{
				"param1": json.RawMessage(`"value1"`),
				"param2": json.RawMessage(`42`),
			},
		}
	}

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = a.executeTools(ctx, toolCalls)
	}
}

// TestUIToolsIntegration tests basic UI tool integration with the assistant.
func TestUIToolsIntegration(t *testing.T) {
	// Create a mock tool that simulates a UI form
	mockFormTool := &mockUITool{
		name:        "ui_form",
		description: "Collect user information via form",
		result:      `{"name": "John Doe", "email": "<EMAIL>"}`,
	}

	// Create tool manager and register the mock tool
	toolMgr := tool.NewManager(tool.Config{})
	toolMgr.Register(mockFormTool)

	// Create mock AI client that will use the tool
	mockAI := &mockAIClientWithTools{
		initialResponse: ai.Response{
			Content: "I'll help you set up your profile.",
			ToolCalls: []ai.ToolCall{
				{
					ID:   "form_1",
					Name: "ui_form",
					Args: makeSimpleArgs(map[string]string{
						"title": "Profile Setup",
					}),
				},
			},
		},
		finalResponse: ai.Response{
			Content: "Great! I've saved your profile information.",
			Usage:   ai.Usage{Input: 50, Output: 30, Total: 80},
		},
	}

	// Create assistant
	cfg := config.Config{
		Owner: config.OwnerConfig{
			Name: "Test User",
		},
		AI: config.AIConfig{
			DefaultModel: "test-model",
		},
	}

	a := &assistant{
		ai:     mockAI,
		memory: nil,
		conv:   nil,
		tools:  toolMgr,
		config: cfg,
		logger: logger.NewNoOpLogger(),
	}

	// Process request
	ctx := context.Background()
	req := Request{
		Query: "Help me set up my profile",
		Options: Options{
			EnableTools: true,
		},
	}

	resp, err := a.Chat(ctx, req)
	require.NoError(t, err)

	// Validate response
	assert.Contains(t, resp.Content, "saved your profile")
	assert.True(t, resp.Metadata.ToolsUsed)
	assert.Contains(t, resp.Metadata.ToolNames, "ui_form")

	// Validate that tool was called
	assert.Equal(t, 1, mockFormTool.callCount)
}

// TestMultipleUITools tests using multiple UI tools in sequence.
func TestMultipleUITools(t *testing.T) {
	// Create mock tools
	listTool := &mockUITool{
		name:        "ui_list",
		description: "Show list of options",
		result:      `{"selected": ["option1"]}`,
	}
	confirmTool := &mockUITool{
		name:        "ui_confirm",
		description: "Confirm action",
		result:      `{"confirmed": true}`,
	}

	// Create tool manager
	toolMgr := tool.NewManager(tool.Config{})
	toolMgr.Register(listTool)
	toolMgr.Register(confirmTool)

	// Test that both tools can be used
	assert.Equal(t, 2, len(toolMgr.List()))
}

// mockUITool implements tool.Tool for testing
type mockUITool struct {
	name        string
	description string
	result      string
	callCount   int
}

func (m *mockUITool) Name() string {
	return m.name
}

func (m *mockUITool) Description() string {
	return m.description
}

func (m *mockUITool) Execute(ctx context.Context, input any) (any, error) {
	m.callCount++
	return m.result, nil
}

func (m *mockUITool) InputSchema() json.RawMessage {
	return json.RawMessage(`{
		"type": "object",
		"properties": {
			"title": {"type": "string"}
		}
	}`)
}

// mockAIClientWithTools implements ai.Client for testing with tool support
type mockAIClientWithTools struct {
	initialResponse  ai.Response
	finalResponse    ai.Response
	capturedRequests []*ai.Request
	callCount        int
}

func (m *mockAIClientWithTools) Chat(ctx context.Context, req *ai.Request) (*ai.Response, error) {
	m.capturedRequests = append(m.capturedRequests, req)
	m.callCount++

	// First call returns tool calls
	if m.callCount == 1 {
		return &m.initialResponse, nil
	}

	// Second call returns final response
	return &m.finalResponse, nil
}

func (m *mockAIClientWithTools) Stream(ctx context.Context, req *ai.Request) (<-chan ai.Stream, error) {
	ch := make(chan ai.Stream)
	go func() {
		defer close(ch)
		resp, _ := m.Chat(ctx, req)
		ch <- ai.Stream{
			Delta: resp.Content,
			Done:  true,
			Usage: &resp.Usage,
		}
	}()
	return ch, nil
}

func (m *mockAIClientWithTools) Provider() ai.Provider {
	return ai.Claude
}

func (m *mockAIClientWithTools) IsAvailable(ctx context.Context) bool {
	return true
}

func (m *mockAIClientWithTools) Close() error {
	return nil
}

// makeSimpleArgs creates Arguments from a simple string map
func makeSimpleArgs(m map[string]string) ai.Arguments {
	args := make(ai.Arguments)
	for k, v := range m {
		args[k] = json.RawMessage(`"` + v + `"`)
	}
	return args
}
