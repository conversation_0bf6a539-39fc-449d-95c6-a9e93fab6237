package assistant

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/koopa0/assistant-go/internal/conversation"
	"github.com/koopa0/assistant-go/internal/platform/config"
	"github.com/koopa0/assistant-go/internal/platform/logger"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// Mock conversation store
type mockConvStore struct {
	messages map[uuid.UUID][]conversation.Message
	convs    map[uuid.UUID]*conversation.Conversation
}

func newMockConvStore() *mockConvStore {
	return &mockConvStore{
		messages: make(map[uuid.UUID][]conversation.Message),
		convs:    make(map[uuid.UUID]*conversation.Conversation),
	}
}

func (m *mockConvStore) Create(ctx context.Context) (*conversation.Conversation, error) {
	conv := &conversation.Conversation{
		ID:        uuid.New(),
		Title:     "New Conversation",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	m.convs[conv.ID] = conv
	return conv, nil
}

func (m *mockConvStore) Get(ctx context.Context, id uuid.UUID) (*conversation.Conversation, error) {
	conv, ok := m.convs[id]
	if !ok {
		return nil, errors.New("conversation not found")
	}
	return conv, nil
}

func (m *mockConvStore) GetMessages(ctx context.Context, convID uuid.UUID, limit int) ([]*conversation.Message, error) {
	msgs, ok := m.messages[convID]
	if !ok {
		return []*conversation.Message{}, nil
	}

	// Apply limit if specified
	start := 0
	if limit > 0 && len(msgs) > limit {
		start = len(msgs) - limit
	}

	// Convert to pointers
	result := make([]*conversation.Message, 0, len(msgs)-start)
	for i := start; i < len(msgs); i++ {
		msg := msgs[i]
		result = append(result, &msg)
	}
	return result, nil
}

func (m *mockConvStore) AddMessage(ctx context.Context, convID uuid.UUID, msg conversation.Message) error {
	if msg.ID == uuid.Nil {
		msg.ID = uuid.New()
	}
	msg.ConversationID = convID
	msg.CreatedAt = time.Now()
	m.messages[convID] = append(m.messages[convID], msg)
	return nil
}

func (m *mockConvStore) UpdateTitle(ctx context.Context, convID uuid.UUID, title string) error {
	if conv, ok := m.convs[convID]; ok {
		conv.Title = title
		conv.UpdatedAt = time.Now()
	}
	return nil
}

func (m *mockConvStore) Delete(ctx context.Context, id uuid.UUID) error {
	delete(m.convs, id)
	delete(m.messages, id)
	return nil
}

func (m *mockConvStore) List(ctx context.Context, limit int) ([]*conversation.Conversation, error) {
	var result []*conversation.Conversation
	for _, conv := range m.convs {
		result = append(result, conv)
	}
	return result, nil
}

func (m *mockConvStore) Search(ctx context.Context, query string, limit int) ([]*conversation.SearchResult, error) {
	// Simple mock implementation that searches through conversations
	var results []*conversation.SearchResult
	queryLower := strings.ToLower(query)

	for _, conv := range m.convs {
		// Check if query matches title or messages
		titleMatch := strings.Contains(strings.ToLower(conv.Title), queryLower)

		// Check messages for this conversation
		messageMatch := false
		if messages, ok := m.messages[conv.ID]; ok {
			for _, msg := range messages {
				if strings.Contains(strings.ToLower(msg.Content), queryLower) {
					messageMatch = true
					break
				}
			}
		}

		if titleMatch || messageMatch {
			result := &conversation.SearchResult{
				Conversation:       conv,
				HighlightedTitle:   conv.Title,
				HighlightedSummary: "",
				Rank:               1.0,
			}

			// Simple case-insensitive highlighting
			if titleMatch {
				// Find all occurrences case-insensitively and replace them
				highlighted := conv.Title
				lowerTitle := strings.ToLower(conv.Title)
				idx := strings.Index(lowerTitle, queryLower)
				if idx >= 0 {
					// Get the actual substring from the original title
					actualMatch := conv.Title[idx : idx+len(query)]
					highlighted = strings.ReplaceAll(conv.Title, actualMatch, "<<"+actualMatch+">>")
				}
				result.HighlightedTitle = highlighted
			}

			results = append(results, result)

			if len(results) >= limit {
				break
			}
		}
	}

	return results, nil
}

func TestBuildContext(t *testing.T) {
	tests := []struct {
		name           string
		req            Request
		setupConv      func() (*conversation.Conversation, *mockConvStore)
		expectedMsgLen int
		validateSystem func(t *testing.T, system string)
	}{
		{
			name: "new conversation",
			req: Request{
				Query: "Hello, assistant!",
			},
			setupConv: func() (*conversation.Conversation, *mockConvStore) {
				return nil, nil
			},
			expectedMsgLen: 1,
			validateSystem: func(t *testing.T, system string) {
				assert.NotEmpty(t, system)
			},
		},
		{
			name: "existing conversation with history",
			req: Request{
				Query: "What did we discuss?",
			},
			setupConv: func() (*conversation.Conversation, *mockConvStore) {
				store := newMockConvStore()
				conv, _ := store.Create(context.Background())
				store.AddMessage(context.Background(), conv.ID, conversation.Message{
					Role:    ai.User,
					Content: "Tell me about Go",
				})
				store.AddMessage(context.Background(), conv.ID, conversation.Message{
					Role:    ai.Assistant,
					Content: "Go is a programming language...",
				})
				return conv, store
			},
			expectedMsgLen: 3, // 2 history + 1 current
			validateSystem: func(t *testing.T, system string) {
				assert.NotEmpty(t, system)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			conv, convStore := tt.setupConv()

			a := &assistant{
				conv:   convStore,
				logger: logger.NewConsoleLogger(),
				config: config.Config{},
			}

			messages, system, err := a.buildContext(context.Background(), tt.req, conv)
			require.NoError(t, err)

			assert.Len(t, messages, tt.expectedMsgLen)
			assert.Equal(t, tt.req.Query, messages[len(messages)-1].Content)
			assert.Equal(t, ai.User, messages[len(messages)-1].Role)

			tt.validateSystem(t, system)
		})
	}
}

func TestBuildSystemPrompt(t *testing.T) {
	// These tests focus on the assistant's system prompt building
	// Memory-specific testing should be done in the memory package
	tests := []struct {
		name     string
		req      Request
		validate func(t *testing.T, prompt string)
	}{
		{
			name: "basic system prompt",
			req: Request{
				Query: "Hello",
			},
			validate: func(t *testing.T, prompt string) {
				assert.NotEmpty(t, prompt)
				// System prompt should contain assistant identity
				assert.Contains(t, strings.ToLower(prompt), "assistant")
			},
		},
		{
			name: "with custom instructions",
			req: Request{
				Query: "Hello",
				Context: Context{
					SystemPrompt: "Always respond in haiku format",
				},
			},
			validate: func(t *testing.T, prompt string) {
				assert.Contains(t, prompt, "haiku")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := &assistant{
				memory: nil, // Memory tests should be in memory package
				logger: logger.NewConsoleLogger(),
				config: config.Config{},
			}

			prompt := a.buildSystemPrompt(context.Background(), tt.req)
			tt.validate(t, prompt)
		})
	}
}

func TestBuildMemoryContext(t *testing.T) {
	tests := []struct {
		name           string
		query          string
		hasMemory      bool
		expectNonEmpty bool
	}{
		{
			name:           "nil memory service",
			query:          "test",
			hasMemory:      false,
			expectNonEmpty: false,
		},
		{
			name:           "time query - tomorrow",
			query:          "明天有什麼安排",
			hasMemory:      false,
			expectNonEmpty: false,
		},
		{
			name:           "time query - schedule",
			query:          "我的行程是什麼",
			hasMemory:      false,
			expectNonEmpty: false,
		},
		{
			name:           "memory query - remember",
			query:          "你記得我喜歡什麼嗎",
			hasMemory:      false,
			expectNonEmpty: false,
		},
		{
			name:           "memory query - when",
			query:          "上次我們聊了什麼",
			hasMemory:      false,
			expectNonEmpty: false,
		},
		{
			name:           "general query",
			query:          "tell me about Go",
			hasMemory:      false,
			expectNonEmpty: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := &assistant{
				memory: nil,
				logger: logger.NewConsoleLogger(),
			}

			result := a.buildMemoryContext(context.Background(), Request{Query: tt.query})

			if tt.expectNonEmpty {
				assert.NotEmpty(t, result)
			} else {
				assert.Empty(t, result)
			}
		})
	}
}

// Fuzzing test for system prompt building
func FuzzBuildSystemPrompt(f *testing.F) {
	// Add seed corpus
	f.Add("Hello", "Be helpful")
	f.Add("", "")
	f.Add(strings.Repeat("test ", 1000), "Very long instructions...")
	f.Add("Special chars: 你好 🌍", "")

	f.Fuzz(func(t *testing.T, query string, instructions string) {
		a := &assistant{
			logger: logger.NewConsoleLogger(),
			config: config.Config{},
		}

		req := Request{
			Query: query,
			Context: Context{
				SystemPrompt: instructions,
			},
		}

		// Should never panic
		prompt := a.buildSystemPrompt(context.Background(), req)
		assert.NotEmpty(t, prompt) // System prompt should always have content
	})
}

// TestMockConvStoreSearch tests the Search method implementation
func TestMockConvStoreSearch(t *testing.T) {
	tests := []struct {
		name          string
		setupConv     func() *mockConvStore
		query         string
		limit         int
		expectedCount int
	}{
		{
			name: "search in title",
			setupConv: func() *mockConvStore {
				store := newMockConvStore()
				ctx := context.Background()

				// Create conversations with different titles
				conv1, _ := store.Create(ctx)
				store.UpdateTitle(ctx, conv1.ID, "Go Programming Tutorial")

				conv2, _ := store.Create(ctx)
				store.UpdateTitle(ctx, conv2.ID, "Python Basics")

				conv3, _ := store.Create(ctx)
				store.UpdateTitle(ctx, conv3.ID, "Advanced Go Patterns")

				return store
			},
			query:         "Go",
			limit:         10,
			expectedCount: 2, // Should match "Go Programming Tutorial" and "Advanced Go Patterns"
		},
		{
			name: "search in messages",
			setupConv: func() *mockConvStore {
				store := newMockConvStore()
				ctx := context.Background()

				conv1, _ := store.Create(ctx)
				store.AddMessage(ctx, conv1.ID, conversation.Message{
					Role:    ai.User,
					Content: "Tell me about concurrency in Go",
				})

				conv2, _ := store.Create(ctx)
				store.AddMessage(ctx, conv2.ID, conversation.Message{
					Role:    ai.User,
					Content: "What is Python?",
				})

				return store
			},
			query:         "concurrency",
			limit:         10,
			expectedCount: 1,
		},
		{
			name: "limit results",
			setupConv: func() *mockConvStore {
				store := newMockConvStore()
				ctx := context.Background()

				// Create multiple matching conversations
				for i := 0; i < 5; i++ {
					conv, _ := store.Create(ctx)
					store.UpdateTitle(ctx, conv.ID, fmt.Sprintf("Go Tutorial Part %d", i+1))
				}

				return store
			},
			query:         "Go",
			limit:         2,
			expectedCount: 2, // Should respect the limit
		},
		{
			name: "no matches",
			setupConv: func() *mockConvStore {
				store := newMockConvStore()
				ctx := context.Background()

				conv, _ := store.Create(ctx)
				store.UpdateTitle(ctx, conv.ID, "Python Tutorial")

				return store
			},
			query:         "JavaScript",
			limit:         10,
			expectedCount: 0,
		},
		{
			name: "case insensitive search",
			setupConv: func() *mockConvStore {
				store := newMockConvStore()
				ctx := context.Background()

				conv, _ := store.Create(ctx)
				store.UpdateTitle(ctx, conv.ID, "GO PROGRAMMING")

				return store
			},
			query:         "go",
			limit:         10,
			expectedCount: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := tt.setupConv()
			results, err := mgr.Search(context.Background(), tt.query, tt.limit)

			require.NoError(t, err)
			assert.Len(t, results, tt.expectedCount)

			// Verify all results are valid
			for _, result := range results {
				assert.NotNil(t, result.Conversation)
				assert.NotEmpty(t, result.HighlightedTitle) // Should always have a title
				assert.Equal(t, float32(1.0), result.Rank)  // Mock always returns rank 1.0
			}
		})
	}
}
