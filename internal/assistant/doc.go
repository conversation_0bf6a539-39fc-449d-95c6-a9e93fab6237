/*
Package assistant provides the core orchestration layer for the AI assistant.

It acts as the central nervous system, coordinating interactions between various components:
  - User input processing (from the CLI or other interfaces)
  - Conversation management (handling history and context)
  - Prompt construction (using the prompt package)
  - AI interaction (calling the appropriate AI client)
  - Tool execution (using the tool manager)
  - Memory integration (searching for and storing memories)

The primary goal of this package is to handle the logic of a single conversational turn, whether it's a simple chat, a streaming response, or a complex interaction involving multiple tool calls.
*/
package assistant
