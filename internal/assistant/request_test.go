package assistant_test

import (
	"encoding/json"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/koopa0/assistant-go/internal/assistant"
)

// TestRequestSerialization tests JSON serialization of Request
func TestRequestSerialization(t *testing.T) {
	convID := uuid.New()
	req := assistant.Request{
		Query:          "Test query",
		ConversationID: convID,
		Options: assistant.Options{
			Temperature:  0.8,
			MaxTokens:    1000,
			EnableTools:  true,
			EnableMemory: false,
		},
		Context: assistant.Context{
			SessionID:    "session-456",
			SystemPrompt: "Be helpful",
			Tags:         []string{"test", "example"},
			Metadata:     map[string]string{"key": "value"},
		},
	}

	// Marshal to JSON
	data, err := json.Marshal(req)
	require.NoError(t, err)

	// Unmarshal back
	var decoded assistant.Request
	err = json.Unmarshal(data, &decoded)
	require.NoError(t, err)

	// Verify fields
	assert.Equal(t, req.Query, decoded.Query)
	assert.Equal(t, req.ConversationID, decoded.ConversationID)
	assert.Equal(t, req.Options, decoded.Options)
	assert.Equal(t, req.Context, decoded.Context)

}

// TestRequestDefaults tests default values for Request
func TestRequestDefaults(t *testing.T) {
	req := assistant.Request{}

	// Test zero values
	assert.Equal(t, uuid.Nil, req.ConversationID)

	// Context defaults
	assert.Empty(t, req.Context.SessionID)
	assert.Empty(t, req.Context.SystemPrompt)
	assert.Nil(t, req.Context.Tags)
	assert.Nil(t, req.Context.Metadata)

	// Options defaults
	assert.Equal(t, float32(0), req.Options.Temperature)
	assert.Equal(t, 0, req.Options.MaxTokens)
	assert.False(t, req.Options.EnableTools)
	assert.False(t, req.Options.EnableMemory)
}

// TestContextFields tests Context functionality
func TestContextFields(t *testing.T) {
	t.Run("empty context", func(t *testing.T) {
		ctx := assistant.Context{}

		// Test JSON serialization of empty context
		data, err := json.Marshal(ctx)
		require.NoError(t, err)

		var decoded assistant.Context
		err = json.Unmarshal(data, &decoded)
		require.NoError(t, err)

		assert.Equal(t, ctx, decoded)
	})

	t.Run("full context", func(t *testing.T) {
		ctx := assistant.Context{
			SessionID:    "sess-123",
			SystemPrompt: "You are an expert in Go programming",
			Tags:         []string{"golang", "programming", "technical"},
			Metadata:     map[string]string{"lang": "go", "level": "expert"},
		}

		// Verify all fields are set
		assert.NotEmpty(t, ctx.SessionID)
		assert.Contains(t, ctx.SystemPrompt, "expert")
		assert.Contains(t, ctx.Tags, "golang")
		assert.Equal(t, "go", ctx.Metadata["lang"])
	})

	t.Run("context with nil slices", func(t *testing.T) {
		ctx := assistant.Context{
			SessionID: "test",
			// Tags and Metadata are nil
		}

		data, err := json.Marshal(ctx)
		require.NoError(t, err)

		// Verify nil slices are handled properly
		assert.Contains(t, string(data), `"session_id":"test"`)
	})
}

// TestOptionsValues tests Options functionality
func TestOptionsValues(t *testing.T) {
	tests := []struct {
		name    string
		options assistant.Options
		check   func(t *testing.T, opts assistant.Options)
	}{
		{
			name: "temperature bounds",
			options: assistant.Options{
				Temperature: 0.9,
			},
			check: func(t *testing.T, opts assistant.Options) {
				assert.GreaterOrEqual(t, opts.Temperature, float32(0))
				assert.LessOrEqual(t, opts.Temperature, float32(2.0))
			},
		},
		{
			name: "max tokens positive",
			options: assistant.Options{
				MaxTokens: 2048,
			},
			check: func(t *testing.T, opts assistant.Options) {
				assert.Greater(t, opts.MaxTokens, 0)
			},
		},
		{
			name: "all features enabled",
			options: assistant.Options{
				EnableTools:  true,
				EnableMemory: true,
			},
			check: func(t *testing.T, opts assistant.Options) {
				assert.True(t, opts.EnableTools)
				assert.True(t, opts.EnableMemory)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.check(t, tt.options)
		})
	}
}

// TestRequestValidation tests the request validation method
func TestRequestValidation(t *testing.T) {
	tests := []struct {
		name    string
		request assistant.Request
		wantErr bool
	}{
		{
			name: "valid request",
			request: assistant.Request{
				Query: "Hello",
			},
			wantErr: false,
		},
		{
			name:    "empty query",
			request: assistant.Request{},
			wantErr: true,
		},
		{
			name: "invalid temperature",
			request: assistant.Request{
				Query: "Hello",
				Options: assistant.Options{
					Temperature: 3.0, // > 2.0
				},
			},
			wantErr: true,
		},
		{
			name: "negative max tokens",
			request: assistant.Request{
				Query: "Hello",
				Options: assistant.Options{
					MaxTokens: -100,
				},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.request.Validate()
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestConversationID tests ConversationID handling
func TestConversationID(t *testing.T) {
	t.Run("zero value conversation ID", func(t *testing.T) {
		req := assistant.Request{}
		assert.Equal(t, uuid.Nil, req.ConversationID)
	})

	t.Run("valid conversation ID", func(t *testing.T) {
		id := uuid.New()
		req := assistant.Request{
			ConversationID: id,
		}

		assert.NotEqual(t, uuid.Nil, req.ConversationID)
		assert.Equal(t, id, req.ConversationID)
	})

	t.Run("nil UUID value", func(t *testing.T) {
		req := assistant.Request{
			ConversationID: uuid.Nil,
		}

		assert.Equal(t, uuid.Nil, req.ConversationID)
	})
}

// TestRequestValidationScenarios tests various request validation scenarios
func TestRequestValidationScenarios(t *testing.T) {
	tests := []struct {
		name        string
		request     assistant.Request
		shouldError bool
		errorMsg    string
	}{
		{
			name: "valid minimal request",
			request: assistant.Request{
				Query: "Hello",
			},
			shouldError: false,
		},
		{
			name: "empty query",
			request: assistant.Request{
				Query: "",
			},
			shouldError: true,
			errorMsg:    "query is required",
		},
		{
			name: "whitespace only query",
			request: assistant.Request{
				Query: "   \t\n   ",
			},
			shouldError: false, // Currently whitespace is allowed
		},
		{
			name: "very long query",
			request: assistant.Request{
				Query: string(make([]byte, 10000)), // 10KB query
			},
			shouldError: false, // No length limit currently
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Note: actual validation happens in coordinator.validateRequest
			// This test documents expected behavior
			if tt.request.Query == "" {
				assert.Empty(t, tt.request.Query)
			} else {
				assert.NotEmpty(t, tt.request.Query)
			}
		})
	}
}

// TestOptionsJSON tests JSON tags for Options
func TestOptionsJSON(t *testing.T) {
	opts := assistant.Options{
		Temperature:  0.5,
		MaxTokens:    1500,
		EnableTools:  true,
		EnableMemory: false,
	}

	data, err := json.Marshal(opts)
	require.NoError(t, err)

	// Check JSON field names
	jsonStr := string(data)
	assert.Contains(t, jsonStr, `"temperature":0.5`)
	assert.Contains(t, jsonStr, `"max_tokens":1500`)
	assert.Contains(t, jsonStr, `"enable_tools":true`)
	// enable_memory is omitted when false due to omitempty tag
}
