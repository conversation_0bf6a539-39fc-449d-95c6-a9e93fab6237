package assistant

import (
	"context"
	"encoding/json"
	"errors"
	"strings"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgxpool"

	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/koopa0/assistant-go/internal/conversation"
	"github.com/koopa0/assistant-go/internal/memory"
	"github.com/koopa0/assistant-go/internal/platform/config"
	"github.com/koopa0/assistant-go/internal/platform/logger"
	"github.com/koopa0/assistant-go/internal/tool"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNew(t *testing.T) {
	tests := []struct {
		name    string
		db      *pgxpool.Pool
		ai      ai.Client
		cfg     config.Config
		log     logger.Logger
		wantErr bool
		errMsg  string
	}{
		{
			name:    "valid configuration",
			db:      nil, // DB is optional
			ai:      &mockAIService{},
			cfg:     DefaultConfig(),
			log:     logger.NewConsoleLogger(),
			wantErr: false,
		},
		{
			name:    "nil AI client",
			db:      nil,
			ai:      nil,
			cfg:     DefaultConfig(),
			log:     logger.NewConsoleLogger(),
			wantErr: true,
			errMsg:  "ai client is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assistant, err := New(tt.db, tt.ai, tt.cfg, tt.log)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
				assert.Nil(t, assistant)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, assistant)
				// Clean up
				assert.NoError(t, assistant.Close())
			}
		})
	}
}

func TestAssistant_Chat(t *testing.T) {
	tests := []struct {
		name         string
		req          Request
		setupAI      func() *mockAIService
		setupConv    func() *mockConvStore
		setupMemory  func() *memory.Service
		validateResp func(t *testing.T, resp *Response, err error)
		wantErr      bool
	}{
		{
			name: "successful chat",
			req: Request{
				Query: "What is Go?",
			},
			setupAI: func() *mockAIService {
				return &mockAIService{
					chatFunc: func(ctx context.Context, req *ai.Request) (*ai.Response, error) {
						return &ai.Response{
							Content:  "Go is a programming language developed by Google.",
							Model:    "claude-3.5-sonnet",
							Provider: ai.Claude,
							Usage:    ai.Usage{Input: 10, Output: 20, Total: 30},
						}, nil
					},
				}
			},
			setupConv:   newMockConvStore,
			setupMemory: func() *memory.Service { return nil },
			validateResp: func(t *testing.T, resp *Response, err error) {
				require.NoError(t, err)
				require.NotNil(t, resp)
				assert.Equal(t, "Go is a programming language developed by Google.", resp.Content)
				assert.NotEqual(t, uuid.Nil, resp.ConversationID)
				assert.Equal(t, 10, resp.Usage.Input)
				assert.Equal(t, 20, resp.Usage.Output)
				assert.Equal(t, "claude-3.5-sonnet", resp.Metadata.Model)
				assert.Equal(t, "claude", resp.Metadata.Provider)
				assert.False(t, resp.Metadata.ToolsUsed)
			},
			wantErr: false,
		},
		{
			name: "empty query",
			req: Request{
				Query: "",
			},
			setupAI:     func() *mockAIService { return &mockAIService{} },
			setupConv:   newMockConvStore,
			setupMemory: func() *memory.Service { return nil },
			validateResp: func(t *testing.T, resp *Response, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "query is required")
				assert.Nil(t, resp)
			},
			wantErr: true,
		},
		{
			name: "with existing conversation",
			req: Request{
				Query:          "Tell me more",
				ConversationID: uuid.MustParse("123e4567-e89b-12d3-a456-************"),
			},
			setupAI: func() *mockAIService {
				return &mockAIService{
					chatFunc: func(ctx context.Context, req *ai.Request) (*ai.Response, error) {
						// Verify conversation history is included
						assert.Greater(t, len(req.Messages), 1)
						return &ai.Response{
							Content: "Go has many features...",
						}, nil
					},
				}
			},
			setupConv: func() *mockConvStore {
				store := newMockConvStore()
				// Pre-create conversation with history
				conv := &conversation.Conversation{
					ID:    uuid.MustParse("123e4567-e89b-12d3-a456-************"),
					Title: "Test Conv",
				}
				store.convs[conv.ID] = conv
				store.AddMessage(context.Background(), conv.ID, conversation.Message{
					Role:    ai.User,
					Content: "What is Go?",
				})
				store.AddMessage(context.Background(), conv.ID, conversation.Message{
					Role:    ai.Assistant,
					Content: "Go is a programming language.",
				})
				return store
			},
			setupMemory: func() *memory.Service { return nil },
			validateResp: func(t *testing.T, resp *Response, err error) {
				require.NoError(t, err)
				assert.Contains(t, resp.Content, "features")
			},
			wantErr: false,
		},
		{
			name: "with tools enabled",
			req: Request{
				Query: "Search for Go tutorials",
				Options: Options{
					EnableTools: true,
				},
			},
			setupAI: func() *mockAIService {
				callCount := 0
				return &mockAIService{
					chatFunc: func(ctx context.Context, req *ai.Request) (*ai.Response, error) {
						callCount++
						if callCount == 1 {
							// First call: return tool calls
							assert.NotEmpty(t, req.Tools)
							return &ai.Response{
								Content: "I'll search for tutorials.",
								ToolCalls: []ai.ToolCall{
									{
										ID:   "call_123",
										Name: "search",
										Args: ai.Arguments{"query": json.RawMessage(`"Go tutorials"`)},
									},
								},
							}, nil
						}
						// Second call: final response after tool execution
						return &ai.Response{
							Content: "Here are some great Go tutorials I found...",
							Usage:   ai.Usage{Input: 50, Output: 100},
						}, nil
					},
				}
			},
			setupConv:   newMockConvStore,
			setupMemory: func() *memory.Service { return nil },
			validateResp: func(t *testing.T, resp *Response, err error) {
				require.NoError(t, err)
				assert.Contains(t, resp.Content, "tutorials")
				assert.True(t, resp.Metadata.ToolsUsed)
				assert.Contains(t, resp.Metadata.ToolNames, "search")
			},
			wantErr: false,
		},
		{
			name: "AI service error",
			req: Request{
				Query: "Test query",
			},
			setupAI: func() *mockAIService {
				return &mockAIService{
					chatFunc: func(ctx context.Context, req *ai.Request) (*ai.Response, error) {
						return nil, errors.New("AI service unavailable")
					},
				}
			},
			setupConv:   newMockConvStore,
			setupMemory: func() *memory.Service { return nil },
			validateResp: func(t *testing.T, resp *Response, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "ai chat failed")
				assert.Nil(t, resp)
			},
			wantErr: true,
		},
		{
			name: "with custom instructions",
			req: Request{
				Query: "Hello",
				Context: Context{
					SystemPrompt: "Always respond in haiku format",
				},
			},
			setupAI: func() *mockAIService {
				return &mockAIService{
					chatFunc: func(ctx context.Context, req *ai.Request) (*ai.Response, error) {
						// Verify instructions are in system prompt
						assert.Contains(t, req.System, "haiku")
						return &ai.Response{
							Content: "Hello dear human\nHow may I assist you now?\nHere to help you smile",
						}, nil
					},
				}
			},
			setupConv:   newMockConvStore,
			setupMemory: func() *memory.Service { return nil },
			validateResp: func(t *testing.T, resp *Response, err error) {
				require.NoError(t, err)
				// Response should be in haiku format (3 lines)
				lines := len(strings.Split(strings.TrimSpace(resp.Content), "\n"))
				assert.Equal(t, 3, lines)
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx, cancel := context.WithCancel(context.Background())
			defer cancel()

			// Setup tools if needed for the test
			var tools *tool.Manager
			if tt.req.Options.EnableTools {
				tools = tool.NewManager(tool.Config{})
				// Register a mock search tool
				tools.Register(&mockTool{
					name:        "search",
					description: "Search for information",
					schema:      json.RawMessage(`{"type":"object","properties":{"query":{"type":"string"}},"required":["query"]}`),
				})
			}

			a := &assistant{
				ai:               tt.setupAI(),
				conv:             tt.setupConv(),
				memory:           tt.setupMemory(),
				tools:            tools,
				logger:           logger.NewConsoleLogger(),
				config:           DefaultConfig(),
				streamer:         newStreamer(tt.setupAI(), tools, logger.NewConsoleLogger()),
				ctx:              ctx,
				cancel:           cancel,
				toolHistory:      make([]ToolExecution, 100),
				toolHistoryIndex: 0,
			}

			resp, err := a.Chat(context.Background(), tt.req)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			if tt.validateResp != nil {
				tt.validateResp(t, resp, err)
			}
		})
	}
}

func TestAssistant_Stream(t *testing.T) {
	tests := []struct {
		name           string
		req            Request
		setupAI        func() *mockAIService
		expectedChunks int
		validateChunks func(t *testing.T, chunks []StreamChunk)
		wantErr        bool
	}{
		{
			name: "successful streaming",
			req: Request{
				Query: "Tell me a story",
			},
			setupAI: func() *mockAIService {
				return &mockAIService{
					streamFunc: func(ctx context.Context, req *ai.Request) (<-chan ai.Stream, error) {
						ch := make(chan ai.Stream, 4)
						go func() {
							defer close(ch)
							ch <- ai.Stream{Delta: "Once upon "}
							ch <- ai.Stream{Delta: "a time..."}
							ch <- ai.Stream{Done: true, Usage: &ai.Usage{Input: 5, Output: 10}}
						}()
						return ch, nil
					},
				}
			},
			expectedChunks: 3, // 2 content chunks + 1 done chunk
			validateChunks: func(t *testing.T, chunks []StreamChunk) {
				// First two chunks should have content
				assert.Equal(t, "Once upon ", chunks[0].Content)
				assert.Equal(t, "a time...", chunks[1].Content)

				// Last chunk should be done
				assert.True(t, chunks[2].Done)
				assert.NotNil(t, chunks[2].Usage)
			},
			wantErr: false,
		},
		{
			name: "empty query error",
			req: Request{
				Query: "",
			},
			setupAI:        func() *mockAIService { return &mockAIService{} },
			expectedChunks: 0,
			wantErr:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			aiService := tt.setupAI()
			a := &assistant{
				ai:       aiService,
				conv:     newMockConvStore(),
				memory:   nil,
				tools:    nil,
				logger:   logger.NewConsoleLogger(),
				config:   DefaultConfig(),
				streamer: newStreamer(aiService, nil, logger.NewConsoleLogger()),
			}

			ctx := context.Background()
			ch, err := a.Stream(ctx, tt.req)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, ch)
				return
			}

			require.NoError(t, err)
			require.NotNil(t, ch)

			// Collect chunks
			var chunks []StreamChunk
			for chunk := range ch {
				chunks = append(chunks, chunk)
			}

			assert.Len(t, chunks, tt.expectedChunks)
			if tt.validateChunks != nil {
				tt.validateChunks(t, chunks)
			}
		})
	}
}

func TestAssistant_Close(t *testing.T) {
	// Create assistant with background tasks
	a := &assistant{
		ai:       &mockAIService{},
		conv:     newMockConvStore(),
		memory:   nil,
		tools:    nil,
		logger:   logger.NewConsoleLogger(),
		config:   DefaultConfig(),
		streamer: newStreamer(&mockAIService{}, nil, logger.NewConsoleLogger()),
	}

	// Initialize context for background tasks
	ctx, cancel := context.WithCancel(context.Background())
	a.ctx = ctx
	a.cancel = cancel

	// Simulate background task
	a.wg.Add(1)
	go func() {
		defer a.wg.Done()
		<-a.ctx.Done()
	}()

	// Close should not hang
	done := make(chan bool)
	go func() {
		err := a.Close()
		assert.NoError(t, err)
		done <- true
	}()

	select {
	case <-done:
		// Success
	case <-time.After(1 * time.Second):
		t.Fatal("Close() timed out")
	}
}

func TestAssistant_getOrCreateConversation(t *testing.T) {
	tests := []struct {
		name      string
		req       Request
		setupConv func() conversation.Store
		validate  func(t *testing.T, conv *conversation.Conversation, err error)
	}{
		{
			name: "create new conversation",
			req: Request{
				Query: "Hello",
			},
			setupConv: func() conversation.Store {
				return newMockConvStore()
			},
			validate: func(t *testing.T, conv *conversation.Conversation, err error) {
				require.NoError(t, err)
				assert.NotNil(t, conv)
				assert.NotEqual(t, uuid.Nil, conv.ID)
			},
		},
		{
			name: "get existing conversation",
			req: Request{
				Query:          "Hello again",
				ConversationID: uuid.MustParse("123e4567-e89b-12d3-a456-************"),
			},
			setupConv: func() conversation.Store {
				store := newMockConvStore()
				// Pre-create conversation
				conv := &conversation.Conversation{
					ID:    uuid.MustParse("123e4567-e89b-12d3-a456-************"),
					Title: "Existing Conv",
				}
				store.convs[conv.ID] = conv
				return store
			},
			validate: func(t *testing.T, conv *conversation.Conversation, err error) {
				require.NoError(t, err)
				assert.Equal(t, uuid.MustParse("123e4567-e89b-12d3-a456-************"), conv.ID)
				assert.Equal(t, "Existing Conv", conv.Title)
			},
		},
		{
			name: "no database - temporary conversation",
			req: Request{
				Query: "Hello",
			},
			setupConv: func() conversation.Store {
				return nil // No conversation store
			},
			validate: func(t *testing.T, conv *conversation.Conversation, err error) {
				require.NoError(t, err)
				assert.NotNil(t, conv)
				assert.NotEqual(t, uuid.Nil, conv.ID)
				assert.Equal(t, "Temporary Conversation", conv.Title)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := &assistant{
				conv:   tt.setupConv(),
				logger: logger.NewConsoleLogger(),
			}

			conv, err := a.getOrCreateConversation(context.Background(), tt.req)
			tt.validate(t, conv, err)
		})
	}
}

func TestDefaultConfig(t *testing.T) {
	cfg := DefaultConfig()

	assert.Equal(t, config.DefaultOwnerName, cfg.Owner.Name)
	assert.Equal(t, "claude", cfg.AI.Provider)
	assert.Equal(t, ai.Claude35Sonnet, cfg.AI.DefaultModel)
}

func TestAssistant_extractMemoriesAsync(t *testing.T) {
	// Test that extractMemoriesAsync handles nil memory gracefully
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	a := &assistant{
		memory: nil, // Test with nil memory
		logger: logger.NewConsoleLogger(),
		ctx:    ctx,
	}

	// Test with a meaningful query
	query := "I'm going to the gym at 5pm today"
	response := "Great! I'll remember that you're going to the gym at 5pm today."

	// Call the async function - should not panic with nil memory
	a.extractMemoriesAsync(query, response)

	// Wait a bit for async processing
	time.Sleep(50 * time.Millisecond)

	// Test passes if no panic occurred
}

// saveConversation is now handled internally by the conversation manager
// This test is no longer needed as conversation saving is done through
// the manager's AddMessage method

// Benchmark tests
func BenchmarkAssistant_Chat(b *testing.B) {
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	a := &assistant{
		ai: &mockAIService{
			chatFunc: func(ctx context.Context, req *ai.Request) (*ai.Response, error) {
				return &ai.Response{
					Content: "Benchmark response",
					Usage:   ai.Usage{Input: 10, Output: 20},
				}, nil
			},
		},
		conv:     newMockConvStore(),
		memory:   nil,
		tools:    nil,
		logger:   logger.NewConsoleLogger(),
		config:   DefaultConfig(),
		streamer: newStreamer(&mockAIService{}, nil, logger.NewConsoleLogger()),
		ctx:      ctx,
	}

	req := Request{
		Query: "Benchmark query",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := a.Chat(context.Background(), req)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// TestExtractMemoriesAsync tests the async memory extraction
func TestExtractMemoriesAsync(t *testing.T) {
	tests := []struct {
		name       string
		query      string
		response   string
		hasMemory  bool
		expectCall bool
	}{
		{
			name:       "no memory service",
			query:      "Hello world",
			response:   "Hi there!",
			hasMemory:  false,
			expectCall: false,
		},
		{
			name:       "short query (should skip)",
			query:      "Hi",
			response:   "Hello!",
			hasMemory:  true,
			expectCall: false,
		},
		{
			name:       "meaningful query",
			query:      "I went to the gym today at 6pm",
			response:   "That's great! Regular exercise is important.",
			hasMemory:  true,
			expectCall: true,
		},
		{
			name:       "long query with context",
			query:      "Yesterday I had a meeting with John about the new project proposal",
			response:   "How did the meeting go?",
			hasMemory:  true,
			expectCall: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create context
			ctx, cancel := context.WithCancel(context.Background())
			defer cancel()

			// For testing memory interaction, we need to test through memory.Service
			// Since extractMemoriesAsync is tightly coupled with memory.Service,
			// we'll skip memory-specific tests here and focus on the nil case
			if !tt.hasMemory {
				a := &assistant{
					memory: nil,
					logger: logger.NewConsoleLogger(),
					ctx:    ctx,
				}

				// Call the method
				a.extractMemoriesAsync(tt.query, tt.response)

				// Wait a bit to ensure it completes
				time.Sleep(50 * time.Millisecond)
				a.wg.Wait()

				// No assertions needed for nil memory - just ensure it doesn't panic
				return
			}

			// Skip memory service tests - these should be tested in the memory package
			t.Skip("Memory service interaction tests should be in memory package")
		})
	}
}

// Test extractMemoriesAsync with context cancellation
func TestExtractMemoriesAsync_ContextCancellation(t *testing.T) {
	ctx, cancel := context.WithCancel(context.Background())

	a := &assistant{
		memory: nil, // No memory service
		logger: logger.NewConsoleLogger(),
		ctx:    ctx,
	}

	// Start extraction
	a.extractMemoriesAsync("This is a test message", "Test response")

	// Cancel context immediately
	cancel()

	// Should complete without hanging
	done := make(chan bool)
	go func() {
		a.wg.Wait()
		done <- true
	}()

	select {
	case <-done:
		// Success
	case <-time.After(1 * time.Second):
		t.Fatal("extractMemoriesAsync did not complete in time")
	}
}

// Fuzzing tests
func FuzzRequestValidation(f *testing.F) {
	// Add seed corpus
	f.Add("Hello, assistant", 0.7, 100)
	f.Add("", 0.0, 0)
	f.Add(strings.Repeat("x", 10000), 1.0, 4096)
	f.Add("Special chars: 你好 🌍", 0.5, 500)

	f.Fuzz(func(t *testing.T, query string, temp float64, maxTokens int) {
		req := Request{
			Query: query,
			Options: Options{
				Temperature: float32(temp),
				MaxTokens:   maxTokens,
			},
		}

		// Should not panic
		err := req.Validate()
		if query == "" {
			assert.Error(t, err)
		}
	})
}

// BenchmarkRequestSerialization benchmarks JSON serialization of requests
func BenchmarkRequestSerialization(b *testing.B) {
	req := assistant.Request{
		Query:          "What is the weather like today?",
		ConversationID: uuid.New(),
		Options: assistant.Options{
			Temperature:  0.7,
			MaxTokens:    1000,
			EnableTools:  true,
			EnableMemory: true,
		},
		Context: assistant.Context{
			SessionID:    "bench-session",
			SystemPrompt: "You are a helpful assistant",
			Tags:         []string{"benchmark", "test"},
			Metadata:     map[string]string{"env": "test"},
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		data, err := json.Marshal(req)
		if err != nil {
			b.Fatal(err)
		}
		_ = data
	}
}

// BenchmarkResponseSerialization benchmarks JSON serialization of responses
func BenchmarkResponseSerialization(b *testing.B) {
	resp := assistant.Response{
		Content:        "The weather today is sunny with a high of 75°F.",
		ConversationID: uuid.New(),
		Usage: ai.Usage{
			Input:  100,
			Output: 50,
			Total:  150,
		},
		Metadata: assistant.Metadata{
			Model:       "claude-3.5-sonnet",
			Provider:    "claude",
			Duration:    1500 * time.Millisecond,
			ToolsUsed:   true,
			ToolNames:   []string{"weather_api"},
			MemoryUsed:  true,
			MemoryCount: 3,
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		data, err := json.Marshal(resp)
		if err != nil {
			b.Fatal(err)
		}
		_ = data
	}
}

// BenchmarkRequestValidation benchmarks request validation
func BenchmarkRequestValidation(b *testing.B) {
	requests := []assistant.Request{
		{Query: "Valid query", Options: assistant.Options{Temperature: 0.7}},
		{Query: "", Options: assistant.Options{Temperature: 0.7}},     // Invalid
		{Query: "Test", Options: assistant.Options{Temperature: 3.0}}, // Invalid temp
		{Query: "Test", Options: assistant.Options{MaxTokens: -1}},    // Invalid tokens
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		req := requests[i%len(requests)]
		_ = req.Validate()
	}
}

// BenchmarkRequestWithDefaults benchmarks applying defaults to requests
func BenchmarkRequestWithDefaults(b *testing.B) {
	cfg := assistant.DefaultConfig()
	req := assistant.Request{
		Query: "Test query",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = req.WithDefaults(cfg)
	}
}

// BenchmarkStreamChunk benchmarks streaming chunk creation
func BenchmarkStreamChunk(b *testing.B) {
	chunks := []assistant.StreamChunk{
		{Content: "Hello"},
		{Content: " world"},
		{Content: "!", Done: true, Usage: &ai.Usage{Input: 10, Output: 5, Total: 15}},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		chunk := chunks[i%len(chunks)]
		data, err := json.Marshal(chunk)
		if err != nil {
			b.Fatal(err)
		}
		_ = data
	}
}

// BenchmarkContextBuilding simulates context building performance
func BenchmarkContextBuilding(b *testing.B) {
	ctx := context.Background()
	messages := []ai.Message{
		{Role: ai.User, Content: "Hello", Time: time.Now()},
		{Role: ai.Assistant, Content: "Hi there!", Time: time.Now()},
		{Role: ai.User, Content: "How are you?", Time: time.Now()},
		{Role: ai.Assistant, Content: "I'm doing well, thanks!", Time: time.Now()},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// Simulate building context with messages
		allMessages := make([]ai.Message, 0, len(messages)+1)
		allMessages = append(allMessages, messages...)
		allMessages = append(allMessages, ai.Message{
			Role:    ai.User,
			Content: "What's the weather?",
			Time:    time.Now(),
		})
		_ = allMessages
		_ = ctx
	}
}
