package assistant_test

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/google/uuid"

	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/koopa0/assistant-go/internal/assistant"
)

// BenchmarkRequestSerialization benchmarks JSON serialization of requests
func BenchmarkRequestSerialization(b *testing.B) {
	req := assistant.Request{
		Query:          "What is the weather like today?",
		ConversationID: uuid.New(),
		Options: assistant.Options{
			Temperature:  0.7,
			MaxTokens:    1000,
			EnableTools:  true,
			EnableMemory: true,
		},
		Context: assistant.Context{
			SessionID:    "bench-session",
			SystemPrompt: "You are a helpful assistant",
			Tags:         []string{"benchmark", "test"},
			Metadata:     map[string]string{"env": "test"},
		},
	}

	b.<PERSON>set<PERSON>ime<PERSON>()
	for i := 0; i < b.N; i++ {
		data, err := json.Marshal(req)
		if err != nil {
			b.<PERSON>al(err)
		}
		_ = data
	}
}

// BenchmarkResponseSerialization benchmarks JSON serialization of responses
func BenchmarkResponseSerialization(b *testing.B) {
	resp := assistant.Response{
		Content:        "The weather today is sunny with a high of 75°F.",
		ConversationID: uuid.New(),
		Usage: ai.Usage{
			Input:  100,
			Output: 50,
			Total:  150,
		},
		Metadata: assistant.Metadata{
			Model:       "claude-3.5-sonnet",
			Provider:    "claude",
			Duration:    1500 * time.Millisecond,
			ToolsUsed:   true,
			ToolNames:   []string{"weather_api"},
			MemoryUsed:  true,
			MemoryCount: 3,
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		data, err := json.Marshal(resp)
		if err != nil {
			b.Fatal(err)
		}
		_ = data
	}
}

// BenchmarkRequestValidation benchmarks request validation
func BenchmarkRequestValidation(b *testing.B) {
	requests := []assistant.Request{
		{Query: "Valid query", Options: assistant.Options{Temperature: 0.7}},
		{Query: "", Options: assistant.Options{Temperature: 0.7}},     // Invalid
		{Query: "Test", Options: assistant.Options{Temperature: 3.0}}, // Invalid temp
		{Query: "Test", Options: assistant.Options{MaxTokens: -1}},    // Invalid tokens
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		req := requests[i%len(requests)]
		_ = req.Validate()
	}
}

// BenchmarkRequestWithDefaults benchmarks applying defaults to requests
func BenchmarkRequestWithDefaults(b *testing.B) {
	cfg := assistant.DefaultConfig()
	req := assistant.Request{
		Query: "Test query",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = req.WithDefaults(cfg)
	}
}

// BenchmarkStreamChunk benchmarks streaming chunk creation
func BenchmarkStreamChunk(b *testing.B) {
	chunks := []assistant.StreamChunk{
		{Content: "Hello"},
		{Content: " world"},
		{Content: "!", Done: true, Usage: &ai.Usage{Input: 10, Output: 5, Total: 15}},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		chunk := chunks[i%len(chunks)]
		data, err := json.Marshal(chunk)
		if err != nil {
			b.Fatal(err)
		}
		_ = data
	}
}

// BenchmarkContextBuilding simulates context building performance
func BenchmarkContextBuilding(b *testing.B) {
	ctx := context.Background()
	messages := []ai.Message{
		{Role: ai.User, Content: "Hello", Time: time.Now()},
		{Role: ai.Assistant, Content: "Hi there!", Time: time.Now()},
		{Role: ai.User, Content: "How are you?", Time: time.Now()},
		{Role: ai.Assistant, Content: "I'm doing well, thanks!", Time: time.Now()},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// Simulate building context with messages
		allMessages := make([]ai.Message, 0, len(messages)+1)
		allMessages = append(allMessages, messages...)
		allMessages = append(allMessages, ai.Message{
			Role:    ai.User,
			Content: "What's the weather?",
			Time:    time.Now(),
		})
		_ = allMessages
		_ = ctx
	}
}
