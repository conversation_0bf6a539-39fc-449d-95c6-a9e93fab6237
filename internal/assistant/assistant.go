// Package assistant provides a conversational AI assistant with memory capabilities.
package assistant

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgxpool"

	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/koopa0/assistant-go/internal/conversation"
	"github.com/koopa0/assistant-go/internal/mcp"
	"github.com/koopa0/assistant-go/internal/memory"
	"github.com/koopa0/assistant-go/internal/platform/config"
	"github.com/koopa0/assistant-go/internal/platform/logger"
	"github.com/koopa0/assistant-go/internal/tool"
	memorytools "github.com/koopa0/assistant-go/internal/tool/memory"
)

// Assistant defines the core interface for the AI assistant.
// It provides both synchronous and streaming chat capabilities.
type Assistant interface {
	// Chat processes a request and returns a complete response.
	Chat(ctx context.Context, req Request) (*Response, error)

	// Stream processes a request and returns a channel of response chunks.
	// The channel is closed when streaming is complete or an error occurs.
	Stream(ctx context.Context, req Request) (<-chan StreamChunk, error)

	// GetToolManager returns the tool manager for external tool registration.
	GetToolManager() *tool.Manager

	// Close releases any resources held by the assistant.
	Close() error
}

// StreamChunk represents a single chunk in a streaming response.
type StreamChunk struct {
	// Content is the text content of this chunk
	Content string `json:"content"`

	// Done indicates if this is the final chunk
	Done bool `json:"done"`

	// Usage is only populated in the final chunk
	Usage *ai.Usage `json:"usage,omitempty"`

	// ToolCalls contains any tool calls in this chunk
	ToolCalls []ai.ToolCall `json:"tool_calls,omitempty"`

	// Error contains any error that occurred
	Error error `json:"error,omitempty"`
}

// New creates a new assistant instance with the provided dependencies.
func New(
	db *pgxpool.Pool,
	aiService ai.Client,
	cfg config.Config,
	log logger.Logger,
) (Assistant, error) {
	if aiService == nil {
		return nil, ErrNoAIClient
	}
	// log cannot be nil, will error if not provided

	// Database is optional - assistant can work without persistence
	if db == nil {
		log.Warn("Running without database - conversation history and memory features will be disabled")
	}

	return newAssistant(db, aiService, cfg, log)
}

// Error types for better error handling.
var (
	ErrInvalidRequest  = fmt.Errorf("invalid request")
	ErrNoAIClient      = fmt.Errorf("ai client is required")
	ErrNoDatabase      = fmt.Errorf("database connection is required")
	ErrEmptyQuery      = fmt.Errorf("query cannot be empty")
	ErrStreamingFailed = fmt.Errorf("streaming failed")
)

// ToolExecution records a tool execution for auditing
type ToolExecution struct {
	Timestamp time.Time
	Name      string
	Args      map[string]any
	Success   bool
	Error     string
	ResultLen int
	CallID    string
}

// DefaultConfig returns a configuration with sensible defaults.
func DefaultConfig() config.Config {
	return config.Config{
		Owner: config.OwnerConfig{
			Name: config.DefaultOwnerName,
		},
		AI: config.AIConfig{
			Provider:     "claude",
			DefaultModel: ai.Claude35Sonnet,
		},
		MCP: &config.MCPConfig{
			Enabled: false,
			Servers: []config.MCPServerConfig{},
		},
	}
}

// assistant implements the Assistant interface.
// It coordinates between various services to provide AI assistance.
type assistant struct {
	// Core services
	ai       ai.Client
	memory   *memory.Service
	conv     conversation.Store
	tools    *tool.Manager
	mcp      *mcp.Integration
	streamer *streamer

	// Configuration
	config config.Config
	logger logger.Logger

	// Background task management
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup

	// Tool execution history (circular buffer)
	toolHistory      []ToolExecution
	toolHistoryMutex sync.RWMutex
	toolHistoryIndex int
}

// newAssistant creates a new assistant implementation.
func newAssistant(
	db *pgxpool.Pool,
	aiClient ai.Client,
	cfg config.Config,
	log logger.Logger,
) (*assistant, error) {
	// Check if aiClient supports both chat and embeddings for memory integration
	var memSvc *memory.Service
	log.Debug("Checking AI client capabilities for memory integration")
	log.Info("Checking AI client capabilities for memory integration",
		"clientType", fmt.Sprintf("%T", aiClient),
		"hasDB", db != nil)

	if hybrid, ok := aiClient.(ai.Hybrid); ok && db != nil {
		// We have a hybrid client with both chat and embedding capabilities
		log.Debug("Creating memory service with hybrid client")
		log.Info("Creating memory service with hybrid client")
		var err error
		memSvc, err = memory.New(db, hybrid, log)
		if err != nil {
			log.Error("Failed to create memory service", "error", err)
		} else {
			log.Info("Memory service initialized successfully")
		}
	} else if db != nil {
		// Client doesn't support full service capabilities
		log.Warn("Memory service disabled - AI client doesn't support full service capabilities",
			"clientType", fmt.Sprintf("%T", aiClient))
	} else {
		log.Warn("Memory service disabled - no database")
	}

	// Create conversation store only if database is available
	var convStore conversation.Store
	if db != nil {
		convStore = conversation.NewStore(db, conversation.WithLogger(log))
	}

	// Create tool manager
	toolMgr := tool.NewManager(tool.Config{})

	// Register built-in tools
	if err := tool.RegisterBuiltinTools(toolMgr, &cfg, log); err != nil {
		return nil, fmt.Errorf("register built-in tools: %w", err)
	}

	// UI tools will be registered separately to avoid circular imports

	// Register memory tools if memory service is available
	if memSvc != nil {
		memTools := memorytools.GetMemoryTools(memSvc)
		for _, memTool := range memTools {
			if err := toolMgr.Register(memTool); err != nil {
				log.Warn("Failed to register memory tool",
					"tool", memTool.Name(),
					"error", err)
			}
		}
		if len(memTools) > 0 {
			log.Info("Memory tools registered", "count", len(memTools))
		}
	}

	// Initialize MCP if enabled
	var mcpIntegration *mcp.Integration
	if cfg.MCP != nil && cfg.MCP.Enabled {
		mcpCfg := mcp.IntegrationConfig{
			MCPConfig: mcp.Config{
				ClientName:    cfg.MCP.ClientName,
				ClientVersion: cfg.MCP.ClientVersion,
				Servers:       convertMCPServers(cfg.MCP.Servers),
				Timeout:       cfg.MCP.Timeout,
			},
			ToolManager:       toolMgr,
			AutoDiscover:      cfg.MCP.AutoDiscover,
			DiscoveryInterval: cfg.MCP.DiscoveryInterval,
		}

		var err error
		mcpIntegration, err = mcp.NewIntegration(mcpCfg, log)
		if err != nil {
			log.Warn("failed to initialize MCP integration", "error", err)
			// Continue without MCP - it's optional
		} else {
			// Start auto-discovery if enabled
			if cfg.MCP.AutoDiscover {
				mcpIntegration.StartAutoDiscovery(context.Background())
			}

			log.Info("MCP integration initialized",
				"servers", len(mcpIntegration.ListServers()),
				"tools", len(mcpIntegration.ListMCPTools()))
		}
	}

	// Create context for background tasks
	ctx, cancel := context.WithCancel(context.Background())

	a := &assistant{
		ai:       aiClient,
		memory:   memSvc,
		conv:     convStore,
		tools:    toolMgr,
		mcp:      mcpIntegration,
		streamer: newStreamer(aiClient, toolMgr, log),
		config:   cfg,
		logger:   log,
		ctx:      ctx,
		cancel:   cancel,
		// Initialize tool history with a circular buffer of 100 entries
		toolHistory:      make([]ToolExecution, 100),
		toolHistoryIndex: 0,
	}

	// Register system tools that need access to assistant
	// Note: Importing system package here would create a circular dependency
	// So we'll register this tool through a different mechanism

	return a, nil
}

// recordToolExecution records a tool execution in the history
func (a *assistant) recordToolExecution(exec ToolExecution) {
	a.toolHistoryMutex.Lock()
	defer a.toolHistoryMutex.Unlock()

	a.toolHistory[a.toolHistoryIndex] = exec
	a.toolHistoryIndex = (a.toolHistoryIndex + 1) % len(a.toolHistory)
}

// GetToolHistory returns recent tool executions (newest first)
func (a *assistant) GetToolHistory(limit int) []ToolExecution {
	a.toolHistoryMutex.RLock()
	defer a.toolHistoryMutex.RUnlock()

	if limit <= 0 || limit > len(a.toolHistory) {
		limit = len(a.toolHistory)
	}

	result := make([]ToolExecution, 0, limit)

	// Start from the most recent entry and work backwards
	for i := 0; i < limit; i++ {
		idx := (a.toolHistoryIndex - 1 - i + len(a.toolHistory)) % len(a.toolHistory)
		exec := a.toolHistory[idx]
		if exec.Timestamp.IsZero() {
			// Skip uninitialized entries
			continue
		}
		result = append(result, exec)
	}

	return result
}

// Chat implements the Assistant interface.
// It orchestrates a single conversational turn, following these steps:
// 1. Validate the request and apply default configurations.
// 2. Retrieve or create the conversation context.
// 3. Build the final prompt, including system instructions, conversation history, and retrieved memories.
// 4. Call the AI service with the prepared request.
// 5. If the AI response includes tool calls, execute them and send the results back to the AI for a final response.
// 6. Save the new user message and assistant response to the conversation history.
// 7. Asynchronously trigger the memory extraction pipeline to learn from the interaction.
func (a *assistant) Chat(ctx context.Context, req Request) (*Response, error) {
	start := time.Now()

	// Validate and prepare request
	if err := req.Validate(); err != nil {
		return nil, err
	}
	req = req.WithDefaults(a.config)

	// Processing request

	// Get or create conversation
	conv, err := a.getOrCreateConversation(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("conversation error: %w", err)
	}

	// Build context (messages + system prompt)
	messages, systemPrompt, err := a.buildContext(ctx, req, conv)
	if err != nil {
		return nil, fmt.Errorf("build context: %w", err)
	}

	// Process with AI

	aiReq := &ai.Request{
		Messages:    messages,
		System:      systemPrompt,
		Temperature: req.Options.Temperature,
		MaxTokens:   req.Options.MaxTokens,
		Model:       a.config.AI.DefaultModel,
	}

	// Add tools if enabled
	var toolsUsed bool
	var toolNames []string
	if req.Options.EnableTools {
		tools := a.getAvailableTools()
		if len(tools) > 0 {
			aiReq.Tools = tools
		}
	}

	// First AI call
	resp, err := a.ai.Chat(ctx, aiReq)
	if err != nil {
		return nil, fmt.Errorf("ai chat failed: %w", err)
	}

	// Handle tool calls if any
	if len(resp.ToolCalls) > 0 && req.Options.EnableTools {
		// Executing tools

		toolsUsed = true
		resp, toolNames, err = a.executeToolsAndGetFinalResponse(ctx, resp, messages, systemPrompt, req)
		if err != nil {
			return nil, fmt.Errorf("tool execution failed: %w", err)
		}
	}

	// Save conversation
	if a.conv != nil && conv.ID != uuid.Nil {
		// Add user message
		if err := a.conv.AddMessage(ctx, conv.ID, conversation.Message{
			Role:    conversation.RoleUser,
			Content: req.Query,
		}); err != nil {
			a.logger.Warn("failed to save user message", "error", err)
		}

		// Add assistant response
		if err := a.conv.AddMessage(ctx, conv.ID, conversation.Message{
			Role:    conversation.RoleAssistant,
			Content: resp.Content,
		}); err != nil {
			a.logger.Warn("failed to save assistant message", "error", err)
		}
	}

	// Extract memories asynchronously using new pipeline
	if a.memory != nil {
		a.logger.Info("Extracting memories from conversation",
			"query", req.Query,
			"responsePreview", truncateContent(resp.Content, 50))
		memoryMessages := []ai.Message{
			{Role: ai.User, Content: req.Query},
			{Role: ai.Assistant, Content: resp.Content},
		}
		// Process memories for personal assistant
		a.logger.Debug("Triggering memory extraction for conversation")
		a.logger.Debug("Sending messages to memory service", "message_count", len(memoryMessages))
		a.memory.ProcessAsync(ctx, memoryMessages)
		a.logger.Debug("Memory processing triggered (async)")
	} else {
		a.logger.Warn("Memory service is nil - memories will not be extracted")
		a.logger.Warn("Memory service is nil - memories will not be extracted")
	}

	// Complete

	return &Response{
		Content:        resp.Content,
		ConversationID: conv.ID,
		Usage:          resp.Usage,
		Metadata: Metadata{
			Model:       resp.Model,
			Provider:    string(resp.Provider),
			Duration:    time.Since(start),
			ToolsUsed:   toolsUsed,
			ToolNames:   toolNames,
			MemoryUsed:  len(messages) > 1,
			MemoryCount: len(messages) - 1,
		},
	}, nil
}

// Stream implements the Assistant interface.
func (a *assistant) Stream(ctx context.Context, req Request) (<-chan StreamChunk, error) {
	// Validate and prepare request
	if err := req.Validate(); err != nil {
		return nil, err
	}
	req = req.WithDefaults(a.config)
	req.Options.Stream = true

	// Use the internal streaming implementation
	return a.streamInternal(ctx, req)
}

// GetToolManager implements the Assistant interface.
func (a *assistant) GetToolManager() *tool.Manager {
	return a.tools
}

// Close implements the Assistant interface.
func (a *assistant) Close() error {
	// Cancel background context to signal all goroutines to stop
	if a.cancel != nil {
		a.cancel()
	}

	// Wait for all background goroutines to finish
	a.wg.Wait()

	// Close memory service
	if a.memory != nil {
		_ = a.memory.Close()
	}

	// Close MCP integration
	if a.mcp != nil {
		_ = a.mcp.Close()
	}
	return nil
}

// Helper methods

func (a *assistant) getOrCreateConversation(ctx context.Context, req Request) (*conversation.Conversation, error) {
	// If no conversation store (no database), return a dummy conversation
	if a.conv == nil {
		return &conversation.Conversation{
			ID:        uuid.New(),
			Title:     "Temporary Conversation",
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}, nil
	}

	if req.ConversationID != uuid.Nil {
		return a.conv.Get(ctx, req.ConversationID)
	}

	// Create new conversation
	return a.conv.Create(ctx)
}

func convertMCPServers(servers []config.MCPServerConfig) []mcp.ServerConfig {
	result := make([]mcp.ServerConfig, len(servers))
	for i, s := range servers {
		result[i] = mcp.ServerConfig{
			Name:    s.Name,
			Command: s.Command,
			Args:    s.Args,
			Env:     s.Env,
		}
	}
	return result
}

// extractMemoriesAsync processes conversations for memory extraction.
// This runs asynchronously to avoid blocking the main response flow.
func (a *assistant) extractMemoriesAsync(query, response string) {
	if a.memory == nil {
		return
	}

	// Convert to AI messages format for memory pipeline
	messages := []ai.Message{
		{Role: ai.User, Content: query},
		{Role: ai.Assistant, Content: response},
	}

	// Process asynchronously for personal assistant
	ctx := context.Background()
	a.memory.ProcessAsync(ctx, messages)
}

// truncateContent truncates content to maxLen characters
func truncateContent(content string, maxLen int) string {
	if len(content) <= maxLen {
		return content
	}
	return content[:maxLen] + "..."
}
