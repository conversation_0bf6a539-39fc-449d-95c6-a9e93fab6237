package assistant

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/koopa0/assistant-go/internal/conversation"
	"github.com/koopa0/assistant-go/internal/platform/logger"
	"github.com/koopa0/assistant-go/internal/tool"
)

// streamer handles streaming responses.
type streamer struct {
	ai     ai.Client
	tools  *tool.Manager
	logger logger.Logger
}

func newStreamer(ai ai.Client, tools *tool.Manager, logger logger.Logger) *streamer {
	return &streamer{
		ai:     ai,
		tools:  tools,
		logger: logger,
	}
}

// ChatUnified implements unified streaming chat that returns a complete response.
// It internally uses streaming for better user experience.
func (a *assistant) ChatUnified(ctx context.Context, req Request) (*Response, error) {
	start := time.Now()

	// Validate and prepare request
	if err := req.Validate(); err != nil {
		return nil, err
	}
	req = req.WithDefaults(a.config)

	// Use streaming internally
	ch, err := a.streamInternal(ctx, req)
	if err != nil {
		return nil, err
	}

	// Collect all chunks into a complete response
	var content strings.Builder
	var usage *ai.Usage
	var toolsUsed bool
	var toolNames []string
	var conversationID string
	memoryCount := 0

	for chunk := range ch {
		// Handle errors
		if chunk.Error != nil {
			return nil, chunk.Error
		}

		// Accumulate content
		if chunk.Content != "" {
			content.WriteString(chunk.Content)
		}

		// Capture final chunk data
		if chunk.Done {
			usage = chunk.Usage
		}
	}

	// Parse conversation ID
	convID, _ := uuid.Parse(conversationID)

	// Handle nil usage
	var finalUsage ai.Usage
	if usage != nil {
		finalUsage = *usage
	}

	return &Response{
		Content:        content.String(),
		ConversationID: convID,
		Usage:          finalUsage,
		Metadata: Metadata{
			Model:       a.config.AI.DefaultModel,
			Provider:    string(a.ai.Provider()),
			Duration:    time.Since(start),
			ToolsUsed:   toolsUsed,
			ToolNames:   toolNames,
			MemoryUsed:  memoryCount > 0,
			MemoryCount: memoryCount,
		},
	}, nil
}

// streamInternal handles the actual streaming logic.
// This is the unified implementation used by the Chat method.
func (a *assistant) streamInternal(ctx context.Context, req Request) (<-chan StreamChunk, error) {
	ch := make(chan StreamChunk, StreamChannelBufferSize)

	// Track this goroutine
	a.wg.Add(1)
	go func() {
		defer a.wg.Done()
		defer close(ch)

		// Processing request

		// Get or create conversation
		conv, err := a.getOrCreateConversation(ctx, req)
		if err != nil {
			ch <- StreamChunk{Error: fmt.Errorf("conversation error: %w", err)}
			return
		}

		// Build context
		messages, systemPrompt, err := a.buildContext(ctx, req, conv)
		if err != nil {
			ch <- StreamChunk{Error: fmt.Errorf("build context: %w", err)}
			return
		}

		// Generating response

		// Create AI request
		aiReq := &ai.Request{
			Messages:    messages,
			System:      systemPrompt,
			Temperature: req.Options.Temperature,
			MaxTokens:   req.Options.MaxTokens,
			Model:       a.config.AI.DefaultModel,
			Stream:      true, // Always stream
		}

		// Add tools if enabled
		if req.Options.EnableTools {
			tools := a.getAvailableTools()
			if len(tools) > 0 {
				aiReq.Tools = tools
			}
		}

		// Stream from AI
		stream, err := a.ai.Stream(ctx, aiReq)
		if err != nil {
			ch <- StreamChunk{Error: fmt.Errorf("ai stream failed: %w", err)}
			return
		}

		// Process stream and handle tool calls
		a.processStream(ctx, stream, ch, conv, req, messages, systemPrompt)
	}()

	return ch, nil
}

// processStream handles the stream processing including tool execution.
func (a *assistant) processStream(
	ctx context.Context,
	stream <-chan ai.Stream,
	ch chan<- StreamChunk,
	conv *conversation.Conversation,
	req Request,
	messages []ai.Message,
	systemPrompt string,
) {
	var fullContent strings.Builder
	var toolCalls []ai.ToolCall
	var usage *ai.Usage

	// Process initial stream
	for chunk := range stream {
		if chunk.Err != nil {
			ch <- StreamChunk{Error: chunk.Err}
			return
		}

		// Accumulate content
		if chunk.Delta != "" {
			fullContent.WriteString(chunk.Delta)
			ch <- StreamChunk{Content: chunk.Delta}
		}

		// Accumulate tool calls
		if len(chunk.ToolCalls) > 0 {
			toolCalls = append(toolCalls, chunk.ToolCalls...)
		}

		// Capture usage from final chunk
		if chunk.Usage != nil {
			usage = chunk.Usage
		}
	}

	// Handle tool calls if any
	if len(toolCalls) > 0 && req.Options.EnableTools {

		// Execute tools and stream the continuation
		a.executeToolsAndStreamContinuation(ctx, ch, toolCalls, messages, systemPrompt, req, fullContent.String())
		return
	}

	// Save conversation
	finalContent := fullContent.String()
	if a.conv != nil && conv.ID != uuid.Nil {
		// Add user message
		if err := a.conv.AddMessage(ctx, conv.ID, conversation.Message{
			Role:    conversation.RoleUser,
			Content: req.Query,
		}); err != nil {
			a.logger.Warn("failed to save user message", "error", err)
		}

		// Add assistant response
		if err := a.conv.AddMessage(ctx, conv.ID, conversation.Message{
			Role:    conversation.RoleAssistant,
			Content: finalContent,
		}); err != nil {
			a.logger.Warn("failed to save assistant message", "error", err)
		}
	}

	// Extract memories asynchronously
	a.extractMemoriesAsync(req.Query, finalContent)

	// Processing complete

	// Send final chunk
	ch <- StreamChunk{
		Done:  true,
		Usage: usage,
	}
}

// executeToolsAndStreamContinuation executes tools and streams the continuation response.
func (a *assistant) executeToolsAndStreamContinuation(
	ctx context.Context,
	ch chan<- StreamChunk,
	toolCalls []ai.ToolCall,
	messages []ai.Message,
	systemPrompt string,
	req Request,
	assistantContent string,
) {
	// Execute tools
	toolResults, _, _ := a.executeToolsWithRawResults(ctx, toolCalls)

	// Tool execution completed

	// Build messages with tool results
	updatedMessages := messages
	if assistantContent != "" {
		updatedMessages = append(updatedMessages, ai.Message{
			Role:    ai.Assistant,
			Content: assistantContent,
		})
	}

	// Add tool results
	if len(toolResults) > 0 {
		toolMsg := "Tool Results:\n"
		for _, result := range toolResults {
			toolMsg += fmt.Sprintf("%s: %s\n", result.ID, result.Content)
		}
		updatedMessages = append(updatedMessages, ai.Message{
			Role:    ai.User,
			Content: toolMsg,
		})
	}

	// Continue streaming with tool results

	// Create continuation request
	contReq := &ai.Request{
		Messages:    updatedMessages,
		System:      systemPrompt,
		Temperature: req.Options.Temperature,
		MaxTokens:   req.Options.MaxTokens,
		Model:       a.config.AI.DefaultModel,
		Stream:      true,
		Tools:       a.getAvailableTools(), // Include tools for potential follow-up
	}

	// Stream continuation
	stream, err := a.ai.Stream(ctx, contReq)
	if err != nil {
		ch <- StreamChunk{Error: fmt.Errorf("continuation stream failed: %w", err)}
		return
	}

	// Process continuation stream
	var contContent strings.Builder
	var contUsage *ai.Usage
	var moreCalls []ai.ToolCall

	for chunk := range stream {
		if chunk.Err != nil {
			ch <- StreamChunk{Error: chunk.Err}
			return
		}

		if chunk.Delta != "" {
			contContent.WriteString(chunk.Delta)
			ch <- StreamChunk{Content: chunk.Delta}
		}

		if len(chunk.ToolCalls) > 0 {
			moreCalls = append(moreCalls, chunk.ToolCalls...)
		}

		if chunk.Usage != nil {
			contUsage = chunk.Usage
		}
	}

	// Handle recursive tool calls if any
	if len(moreCalls) > 0 && req.Options.EnableTools {
		// Recursive tool execution
		allContent := assistantContent + contContent.String()
		a.executeToolsAndStreamContinuation(ctx, ch, moreCalls, updatedMessages, systemPrompt, req, allContent)
		return
	}

	// Save complete conversation
	finalContent := assistantContent + contContent.String()

	// Messages were already saved during initial processing
	if a.conv != nil {
		a.logger.Debug("conversation saved via store")
	}

	// Extract memories
	a.extractMemoriesAsync(req.Query, finalContent)

	// Processing complete

	// Send final chunk with tool information
	ch <- StreamChunk{
		Done:  true,
		Usage: contUsage,
	}
}
