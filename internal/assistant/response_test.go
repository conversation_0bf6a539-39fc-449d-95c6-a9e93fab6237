package assistant_test

import (
	"encoding/json"
	"strings"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/koopa0/assistant-go/internal/assistant"
)

// TestResponseSerialization tests JSON serialization of Response
func TestResponseSerialization(t *testing.T) {
	resp := assistant.Response{
		Content:        "This is the response content",
		ConversationID: uuid.New(),
		Metadata: assistant.Metadata{
			Model:       "claude-3.5-sonnet",
			Provider:    "claude",
			Duration:    1500 * time.Millisecond,
			ToolsUsed:   true,
			ToolNames:   []string{"search", "memory"},
			MemoryUsed:  true,
			MemoryCount: 3,
		},
		Usage: ai.Usage{
			Input:  100,
			Output: 50,
			Total:  150,
		},
	}

	// Marshal to JSON
	data, err := json.<PERSON>(resp)
	require.NoError(t, err)

	// Unmarshal back
	var decoded assistant.Response
	err = json.Unmarshal(data, &decoded)
	require.NoError(t, err)

	// Verify all fields
	assert.Equal(t, resp.Content, decoded.Content)
	assert.Equal(t, resp.ConversationID, decoded.ConversationID)
	assert.Equal(t, resp.Metadata, decoded.Metadata)
	assert.Equal(t, resp.Usage, decoded.Usage)
}

// TestMetadataValues tests Metadata functionality
func TestMetadataValues(t *testing.T) {
	tests := []struct {
		name     string
		metadata assistant.Metadata
		check    func(t *testing.T, m assistant.Metadata)
	}{
		{
			name: "basic metadata",
			metadata: assistant.Metadata{
				Model:    "claude-3.5-sonnet",
				Provider: "claude",
				Duration: 500 * time.Millisecond,
			},
			check: func(t *testing.T, m assistant.Metadata) {
				assert.Equal(t, "claude-3.5-sonnet", m.Model)
				assert.Equal(t, "claude", m.Provider)
				assert.Equal(t, 500*time.Millisecond, m.Duration)
			},
		},
		{
			name: "tools used",
			metadata: assistant.Metadata{
				ToolsUsed: true,
				ToolNames: []string{"search", "calculator", "memory"},
			},
			check: func(t *testing.T, m assistant.Metadata) {
				assert.True(t, m.ToolsUsed)
				assert.Len(t, m.ToolNames, 3)
				assert.Contains(t, m.ToolNames, "search")
			},
		},
		{
			name: "memory used",
			metadata: assistant.Metadata{
				MemoryUsed:  true,
				MemoryCount: 5,
			},
			check: func(t *testing.T, m assistant.Metadata) {
				assert.True(t, m.MemoryUsed)
				assert.Equal(t, 5, m.MemoryCount)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.check(t, tt.metadata)
		})
	}
}

// TestResponseDefaults tests default values for Response
func TestResponseDefaults(t *testing.T) {
	resp := assistant.Response{}

	// Test zero values
	assert.Empty(t, resp.Content)
	assert.Equal(t, uuid.Nil, resp.ConversationID)

	// Metadata defaults
	assert.Empty(t, resp.Metadata.Model)
	assert.Empty(t, resp.Metadata.Provider)
	assert.Equal(t, time.Duration(0), resp.Metadata.Duration)
	assert.False(t, resp.Metadata.ToolsUsed)
	assert.Nil(t, resp.Metadata.ToolNames)
	assert.False(t, resp.Metadata.MemoryUsed)
	assert.Equal(t, 0, resp.Metadata.MemoryCount)

	// Usage defaults
	assert.Equal(t, 0, resp.Usage.Input)
	assert.Equal(t, 0, resp.Usage.Output)
	assert.Equal(t, 0, resp.Usage.Total)
}

// TestUsageCalculation tests token usage tracking
func TestUsageCalculation(t *testing.T) {
	usage := ai.Usage{
		Input:  150,
		Output: 75,
		Total:  225,
	}

	assert.Equal(t, usage.Input+usage.Output, usage.Total)
}

// TestResponseWithLargeContent tests response with large content
func TestResponseWithLargeContent(t *testing.T) {
	// Create a large content string (1MB)
	largeContent := make([]byte, 1024*1024)
	for i := range largeContent {
		largeContent[i] = byte('a' + (i % 26))
	}

	resp := assistant.Response{
		Content:        string(largeContent),
		ConversationID: uuid.New(),
		Usage: ai.Usage{
			Input:  10000,
			Output: 5000,
			Total:  15000,
		},
	}

	// Test serialization with large content
	data, err := json.Marshal(resp)
	require.NoError(t, err)
	assert.Greater(t, len(data), 1024*1024)

	// Test deserialization
	var decoded assistant.Response
	err = json.Unmarshal(data, &decoded)
	require.NoError(t, err)
	assert.Equal(t, len(resp.Content), len(decoded.Content))
}

// TestMetadataJSON tests JSON field names for Metadata
func TestMetadataJSON(t *testing.T) {
	meta := assistant.Metadata{
		Model:       "claude-3.5-sonnet",
		Provider:    "claude",
		Duration:    1 * time.Second,
		ToolsUsed:   true,
		ToolNames:   []string{"search"},
		MemoryUsed:  true,
		MemoryCount: 2,
	}

	data, err := json.Marshal(meta)
	require.NoError(t, err)

	jsonStr := string(data)
	// Check JSON field names match the tags
	assert.Contains(t, jsonStr, `"model":"claude-3.5-sonnet"`)
	assert.Contains(t, jsonStr, `"provider":"claude"`)
	assert.Contains(t, jsonStr, `"duration":**********`)
	assert.Contains(t, jsonStr, `"tools_used":true`)
	assert.Contains(t, jsonStr, `"tool_names":["search"]`)
	assert.Contains(t, jsonStr, `"memory_used":true`)
	assert.Contains(t, jsonStr, `"memory_count":2`)
}

// TestResponseScenarios tests different response scenarios
func TestResponseScenarios(t *testing.T) {
	convID := uuid.New()

	scenarios := []struct {
		name     string
		response assistant.Response
	}{
		{
			name: "simple chat response",
			response: assistant.Response{
				Content:        "Hello! How can I help you today?",
				ConversationID: convID,
				Usage: ai.Usage{
					Input:  10,
					Output: 8,
					Total:  18,
				},
			},
		},
		{
			name: "tool-assisted response",
			response: assistant.Response{
				Content:        "The current weather in New York is 72°F and sunny.",
				ConversationID: convID,
				Metadata: assistant.Metadata{
					ToolsUsed: true,
				},
				Usage: ai.Usage{
					Input:  50,
					Output: 15,
					Total:  65,
				},
			},
		},
		{
			name: "analysis response",
			response: assistant.Response{
				Content:        "## Analysis Results\n\n### Sentiment\nPositive\n\n### Keywords\n- technology\n- innovation",
				ConversationID: convID,
				Metadata:       assistant.Metadata{
					// Could add fields relevant to analysis if needed
				},
			},
		},
		{
			name: "streamed response",
			response: assistant.Response{
				Content:        "This is a longer response that was streamed to the user in real-time...",
				ConversationID: convID,
				Metadata: assistant.Metadata{
					Duration: 3200 * time.Millisecond,
				},
			},
		},
		{
			name: "empty response",
			response: assistant.Response{
				Content:        "",
				ConversationID: convID,
			},
		},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.name, func(t *testing.T) {
			// Test each scenario can be serialized and deserialized
			data, err := json.Marshal(scenario.response)
			require.NoError(t, err)

			var decoded assistant.Response
			err = json.Unmarshal(data, &decoded)
			require.NoError(t, err)

			assert.Equal(t, scenario.response.Content, decoded.Content)
			assert.Equal(t, scenario.response.ConversationID, decoded.ConversationID)
		})
	}
}

// TestDurationValues tests duration values
func TestDurationValues(t *testing.T) {
	durations := []time.Duration{
		100 * time.Millisecond,
		1500 * time.Millisecond,
		2*time.Minute + 30*time.Second,
		1*time.Hour + 2*time.Minute + 3*time.Second,
	}

	for _, dur := range durations {
		meta := assistant.Metadata{
			Duration: dur,
		}
		_ = meta

		// Verify the duration value
		assert.Greater(t, dur, time.Duration(0))
	}
}

// TestResponseComparison tests comparing responses
func TestResponseComparison(t *testing.T) {
	convID := uuid.New()

	resp1 := assistant.Response{
		Content:        "Response 1",
		ConversationID: convID,
		Usage: ai.Usage{
			Total: 100,
		},
	}

	resp2 := assistant.Response{
		Content:        "Response 2",
		ConversationID: convID,
		Usage: ai.Usage{
			Total: 200,
		},
	}

	// Different content
	assert.NotEqual(t, resp1.Content, resp2.Content)

	// Same conversation
	assert.Equal(t, resp1.ConversationID, resp2.ConversationID)

	// Different token usage
	assert.Less(t, resp1.Usage.Total, resp2.Usage.Total)
}

// TestResponseValidation documents expected validation rules
func TestResponseValidation(t *testing.T) {
	tests := []struct {
		name     string
		response assistant.Response
		valid    bool
		reason   string
	}{
		{
			name: "valid response",
			response: assistant.Response{
				Content:        "Valid content",
				ConversationID: uuid.New(),
			},
			valid: true,
		},
		{
			name: "empty content allowed",
			response: assistant.Response{
				Content:        "",
				ConversationID: uuid.New(),
			},
			valid:  true,
			reason: "Empty content is valid for error cases",
		},
		{
			name: "nil conversation ID invalid",
			response: assistant.Response{
				Content:        "Content",
				ConversationID: uuid.Nil,
			},
			valid:  false,
			reason: "Conversation ID should not be nil",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if !tt.valid {
				assert.Equal(t, uuid.Nil, tt.response.ConversationID)
			} else {
				assert.NotEqual(t, uuid.Nil, tt.response.ConversationID)
			}
		})
	}
}

// TestResponseSummary tests the Summary method
func TestResponseSummary(t *testing.T) {
	tests := []struct {
		name     string
		content  string
		expected string
	}{
		{
			name:     "short content returned as-is",
			content:  "This is a short response",
			expected: "This is a short response",
		},
		{
			name:     "exactly 100 characters",
			content:  "This is exactly one hundred characters long string that should be returned completely without dots.",
			expected: "This is exactly one hundred characters long string that should be returned completely without dots.",
		},
		{
			name:     "long content truncated",
			content:  "This is a very long response that exceeds one hundred characters and should be truncated with ellipsis at the end of it",
			expected: "This is a very long response that exceeds one hundred characters and should be truncated with ell...",
		},
		{
			name:     "empty content",
			content:  "",
			expected: "",
		},
		{
			name:     "unicode content not truncated",
			content:  "這是一個很長的中文回覆，應該在第一百個字的地方被截断並添加省略的符號。還有更多更多更多更多字來確保他超過一百個字的限制 超多字超多字超多字超多字超多字",
			expected: "這是一個很長的中文回覆，應該在第一百個字的地方被截断並添加省略的符號。還有更多更多更多更多字來確保他超過一百個字的限制 超多字超多字超多字超多字超多字",
		},
		{
			name:     "unicode content truncated",
			content:  strings.Repeat("你好世界", 30),          // 120 characters
			expected: strings.Repeat("你好世界", 24) + "你...", // 97 chars + ...
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp := &assistant.Response{
				Content: tt.content,
			}
			summary := resp.Summary()
			assert.Equal(t, tt.expected, summary)

			// Ensure summary is never longer than 100 runes (characters)
			summaryRunes := []rune(summary)
			if len([]rune(tt.content)) > 100 {
				assert.LessOrEqual(t, len(summaryRunes), 100)
			}
		})
	}
}

// TestResponseIsEmpty tests the IsEmpty method
func TestResponseIsEmpty(t *testing.T) {
	tests := []struct {
		name     string
		content  string
		expected bool
	}{
		{
			name:     "empty string",
			content:  "",
			expected: true,
		},
		{
			name:     "whitespace only is not empty",
			content:  "   ",
			expected: false,
		},
		{
			name:     "newline is not empty",
			content:  "\n",
			expected: false,
		},
		{
			name:     "regular content",
			content:  "Hello, world!",
			expected: false,
		},
		{
			name:     "single character",
			content:  "x",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp := &assistant.Response{
				Content: tt.content,
			}
			assert.Equal(t, tt.expected, resp.IsEmpty())
		})
	}
}
