package assistant

import (
	"testing"
)

// TestMemoryContextInConversations tests that memory context is properly built for different query types.
func TestMemoryContextInConversations(t *testing.T) {
	t.Skip("Memory context tests need to be updated for the new memory system with embeddings")

	// TODO: These tests need to be rewritten to:
	// 1. Use the new memory.Service API with Save() instead of StoreInfo/StorePreference
	// 2. Mock or provide an AI service for embeddings
	// 3. Use sqlc.MemoryType constants for memory types
}

// TestMemoryContextXMLStructure tests that memory content is properly formatted in XML.
func TestMemoryContextXMLStructure(t *testing.T) {
	t.Skip("Memory XML structure tests need to be updated for the new memory system")

	// TODO: Rewrite to test the new XML structure with the updated memory API
}

// TestMemorySearchIntegration tests memory search functionality in the assistant.
func TestMemorySearchIntegration(t *testing.T) {
	t.Skip("Memory search integration tests need to be updated for the new memory system")

	// TODO: Rewrite to test search with embeddings and the new memory API
}

// TestMemoryPrioritization tests that memory sections are properly prioritized.
func TestMemoryPrioritization(t *testing.T) {
	t.Skip("Memory prioritization tests need to be updated for the new memory system")

	// TODO: Rewrite to test memory prioritization with the new system
}

// TestMemoryContextBuilding tests the memory context building process.
func TestMemoryContextBuilding(t *testing.T) {
	t.Skip("Memory context building tests need to be updated for the new memory system")

	// TODO: Rewrite to test context building with the new memory API
}
