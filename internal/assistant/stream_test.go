package assistant

import (
	"context"
	"strings"
	"testing"

	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/koopa0/assistant-go/internal/platform/logger"
	"github.com/stretchr/testify/assert"
)

// Mock AI service for testing streaming
type mockAIService struct {
	streamFunc func(ctx context.Context, req *ai.Request) (<-chan ai.Stream, error)
	chatFunc   func(ctx context.Context, req *ai.Request) (*ai.Response, error)
}

func (m *mockAIService) Chat(ctx context.Context, req *ai.Request) (*ai.Response, error) {
	if m.chatFunc != nil {
		return m.chatFunc(ctx, req)
	}
	return &ai.Response{Content: "mock response"}, nil
}

func (m *mockAIService) Stream(ctx context.Context, req *ai.Request) (<-chan ai.Stream, error) {
	if m.streamFunc != nil {
		return m.streamFunc(ctx, req)
	}

	// Default stream implementation
	ch := make(chan ai.Stream, 3)
	go func() {
		defer close(ch)
		ch <- ai.Stream{Delta: "Hello", Done: false}
		ch <- ai.Stream{Delta: " world", Done: false}
		ch <- ai.Stream{Delta: "", Done: true, Usage: &ai.Usage{Input: 10, Output: 2}}
	}()
	return ch, nil
}

func (m *mockAIService) Close() error {
	return nil
}

func (m *mockAIService) IsAvailable(ctx context.Context) bool {
	return true
}

func (m *mockAIService) Provider() ai.Provider {
	return ai.Claude
}

// formatToolResults is no longer used - tool results are formatted inline
/*
// TestFormatToolResults tests the formatToolResults function
func TestFormatToolResults(t *testing.T) {
	tests := []struct {
		name     string
		results  []ai.ToolResult
		expected []string // Check that these strings are present in output
	}{
		{
			name:    "empty results",
			results: []ai.ToolResult{},
			expected: []string{
				"Tool execution results:",
			},
		},
		{
			name: "single successful result",
			results: []ai.ToolResult{
				{
					ID:      "tool_123",
					Content: "Weather in NYC: 72°F, sunny",
				},
			},
			expected: []string{
				"Tool execution results:",
				"Tool ID: tool_123",
				"Result: Weather in NYC: 72°F, sunny",
			},
		},
		{
			name: "single error result",
			results: []ai.ToolResult{
				{
					ID:    "tool_456",
					Error: assert.AnError,
				},
			},
			expected: []string{
				"Tool execution results:",
				"Tool ID: tool_456",
				"Error: assert.AnError general error for testing",
			},
		},
		{
			name: "multiple mixed results",
			results: []ai.ToolResult{
				{
					ID:      "tool_001",
					Content: "First tool success",
				},
				{
					ID:    "tool_002",
					Error: assert.AnError,
				},
				{
					ID:      "tool_003",
					Content: "Third tool success",
				},
			},
			expected: []string{
				"Tool execution results:",
				"Tool ID: tool_001",
				"Result: First tool success",
				"Tool ID: tool_002",
				"Error: assert.AnError general error for testing",
				"Tool ID: tool_003",
				"Result: Third tool success",
			},
		},
		{
			name: "result with multiline content",
			results: []ai.ToolResult{
				{
					ID:      "tool_multi",
					Content: "Line 1\nLine 2\nLine 3",
				},
			},
			expected: []string{
				"Tool execution results:",
				"Tool ID: tool_multi",
				"Result: Line 1\nLine 2\nLine 3",
			},
		},
		{
			name: "result with special characters",
			results: []ai.ToolResult{
				{
					ID:      "tool_special",
					Content: "Special chars: 你好 🌍 <>&\"",
				},
			},
			expected: []string{
				"Tool execution results:",
				"Tool ID: tool_special",
				"Result: Special chars: 你好 🌍 <>&\"",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			formatted := formatToolResults(tt.results)

			// Check that all expected strings are present
			for _, expected := range tt.expected {
				assert.Contains(t, formatted, expected)
			}

			// Check formatting structure
			assert.True(t, strings.HasPrefix(formatted, "Tool execution results:\n\n"))

			// Verify each result ends with double newline
			for _, result := range tt.results {
				assert.Contains(t, formatted, result.ID)
			}
		})
	}
}
*/

// Fuzzing test for stream processing
func FuzzStreamProcessing(f *testing.F) {
	// Add seed corpus
	f.Add("Hello world", 10, 5)
	f.Add("", 0, 0)
	f.Add(strings.Repeat("test ", 1000), 1000, 100)
	f.Add("Special: 你好 🌍", 20, 10)

	f.Fuzz(func(t *testing.T, content string, inputTokens int, outputTokens int) {
		// Create mock AI service that streams the content
		aiService := &mockAIService{
			streamFunc: func(ctx context.Context, req *ai.Request) (<-chan ai.Stream, error) {
				ch := make(chan ai.Stream, 2)
				go func() {
					defer close(ch)
					if content != "" {
						ch <- ai.Stream{Delta: content, Done: false}
					}
					ch <- ai.Stream{
						Done: true,
						Usage: &ai.Usage{
							Input:  inputTokens,
							Output: outputTokens,
							Total:  inputTokens + outputTokens,
						},
					}
				}()
				return ch, nil
			},
		}

		// Create a simple test to verify streaming doesn't panic
		streamer := newStreamer(aiService, nil, logger.NewConsoleLogger())
		assert.NotNil(t, streamer)

		// Verify the mock AI service works
		ch, err := aiService.Stream(context.Background(), &ai.Request{
			Messages: []ai.Message{{Role: ai.User, Content: "test"}},
		})
		assert.NoError(t, err)

		// Collect streamed content
		var totalContent strings.Builder
		var finalUsage *ai.Usage
		for chunk := range ch {
			if chunk.Delta != "" {
				totalContent.WriteString(chunk.Delta)
			}
			if chunk.Done {
				finalUsage = chunk.Usage
			}
		}

		if content != "" {
			assert.Equal(t, content, totalContent.String())
		}
		if finalUsage != nil {
			assert.Equal(t, inputTokens, finalUsage.Input)
			assert.Equal(t, outputTokens, finalUsage.Output)
		}
	})
}
