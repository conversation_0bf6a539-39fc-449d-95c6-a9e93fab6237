package assistant

import (
	"time"

	"github.com/google/uuid"
	"github.com/koopa0/assistant-go/internal/ai"
)

// Response represents the assistant's response to a request.
type Response struct {
	// Content is the main response text
	Content string `json:"content"`

	// ConversationID links this response to a conversation
	ConversationID uuid.UUID `json:"conversation_id"`

	// Usage tracks token consumption
	Usage ai.Usage `json:"usage,omitempty"`

	// Metadata contains additional response information
	Metadata Metadata `json:"metadata,omitempty"`
}

// Metadata provides additional response information.
type Metadata struct {
	// Model used for generation
	Model string `json:"model,omitempty"`

	// Provider that generated the response
	Provider string `json:"provider,omitempty"`

	// Duration of processing
	Duration time.Duration `json:"duration,omitempty"`

	// ToolsUsed indicates if any tools were executed
	ToolsUsed bool `json:"tools_used,omitempty"`

	// ToolNames lists the names of tools that were used
	ToolNames []string `json:"tool_names,omitempty"`

	// MemoryUsed indicates if memory/context was retrieved
	MemoryUsed bool `json:"memory_used,omitempty"`

	// MemoryCount is the number of memories retrieved
	MemoryCount int `json:"memory_count,omitempty"`

	// Streamed indicates if response was streamed
	Streamed bool `json:"streamed,omitempty"`

	// Error contains any non-fatal errors or warnings
	Error string `json:"error,omitempty"`
}

// Summary provides a brief summary of the response.
func (r *Response) Summary() string {
	// Convert to runes to handle unicode properly
	runes := []rune(r.Content)
	if len(runes) <= SummaryMaxLength {
		return r.Content
	}
	return string(runes[:SummaryTruncateLength]) + "..."
}

// IsEmpty checks if the response has no content.
func (r *Response) IsEmpty() bool {
	return r.Content == ""
}
