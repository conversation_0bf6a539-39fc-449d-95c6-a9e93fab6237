package assistant

import (
	"fmt"

	"github.com/google/uuid"
	"github.com/koopa0/assistant-go/internal/platform/config"
)

// Request represents a user request to the assistant.
// It contains the query and configuration for processing.
type Request struct {
	// Query is the user's input text (required)
	Query string `json:"query"`

	// ConversationID links to an existing conversation
	// If empty, a new conversation will be created
	ConversationID uuid.UUID `json:"conversation_id,omitempty"`

	// Options configures how the request is processed
	Options Options `json:"options,omitempty"`

	// Context provides additional information for this request
	Context Context `json:"context,omitempty"`

	// Reserved for future use
	// StateCallback func(StateUpdate) `json:"-"`
}

// Options configures request processing behavior.
type Options struct {
	// Stream enables streaming response
	Stream bool `json:"stream,omitempty"`

	// Temperature controls response randomness (0.0-1.0)
	// Default: 0.7
	Temperature float32 `json:"temperature,omitempty"`

	// MaxTokens limits the response length
	// Default: 2000
	MaxTokens int `json:"max_tokens,omitempty"`

	// EnableTools allows the assistant to use configured tools
	// Default: true
	EnableTools bool `json:"enable_tools,omitempty"`

	// EnableMemory allows using memory/context retrieval
	// Default: true
	EnableMemory bool `json:"enable_memory,omitempty"`

	// EnableMCPResources includes MCP resources in context
	// Default: false (only enabled when explicitly requested)
	EnableMCPResources bool `json:"enable_mcp_resources,omitempty"`

	// EnableMCPContext adds MCP server capabilities to system prompt
	// Default: true when MCP is enabled
	EnableMCPContext bool `json:"enable_mcp_context,omitempty"`
}

// Context provides additional request-specific information.
type Context struct {
	// SessionID for tracking user sessions
	SessionID string `json:"session_id,omitempty"`

	// SystemPrompt overrides the default system prompt
	SystemPrompt string `json:"system_prompt,omitempty"`

	// Tags for categorizing requests
	Tags []string `json:"tags,omitempty"`

	// Metadata for any additional data
	Metadata map[string]string `json:"metadata,omitempty"`
}

// Validate checks if the request is valid.
func (r *Request) Validate() error {
	if r.Query == "" {
		return fmt.Errorf("%w: query is required", ErrInvalidRequest)
	}

	// Validate temperature
	if r.Options.Temperature < 0 || r.Options.Temperature > 2 {
		return fmt.Errorf("%w: temperature must be between 0 and 2", ErrInvalidRequest)
	}

	// Validate max tokens
	if r.Options.MaxTokens < 0 {
		return fmt.Errorf("%w: max tokens cannot be negative", ErrInvalidRequest)
	}

	return nil
}

// WithDefaults returns a copy of the request with default values applied.
func (r Request) WithDefaults(cfg config.Config) Request {
	// Apply default options
	if r.Options.Temperature == 0 {
		r.Options.Temperature = 0.7
	}
	if r.Options.MaxTokens == 0 {
		r.Options.MaxTokens = 2000
	}

	// Enable tools and memory by default
	if !r.Options.Stream { // Only set defaults for non-streaming
		r.Options.EnableTools = true
		r.Options.EnableMemory = true
	}

	// Enable MCP context by default if MCP is configured
	if cfg.MCP != nil && cfg.MCP.Enabled {
		r.Options.EnableMCPContext = true
		// MCP resources are opt-in due to potential performance impact
		// r.Options.EnableMCPResources = false
	}

	return r
}
