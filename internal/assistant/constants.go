package assistant

import "time"

// Channel and buffer sizes
const (
	// StreamChannelBufferSize is the buffer size for streaming response channels
	StreamChannelBufferSize = 10

	// ConversationHistoryLimit is the maximum number of messages to load for context
	ConversationHistoryLimit = 100
)

// AI model parameters
const (
	// DefaultTemperature is the default randomness for AI responses
	DefaultTemperature float32 = 0.7

	// MaxTemperature is the maximum allowed temperature
	MaxTemperature float32 = 2.0

	// MinTemperature is the minimum allowed temperature
	MinTemperature float32 = 0.0

	// DefaultMaxTokens is the default maximum response length
	DefaultMaxTokens = 2000
)

// Content processing
const (
	// SummaryMaxLength is the maximum length for content summaries
	SummaryMaxLength = 100

	// SummaryTruncateLength is where to truncate for ellipsis
	SummaryTruncateLength = 97

	// MinMessageLengthForMemory is the minimum message length worth storing
	MinMessageLengthForMemory = 10
)

// Timeouts
const (
	// MemoryExtractionTimeout is the timeout for async memory extraction
	MemoryExtractionTimeout = 5 * time.Second

	// ConversationCloseTimeout is the timeout for closing conversation manager
	ConversationCloseTimeout = 5 * time.Second
)
