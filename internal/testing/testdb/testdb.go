// Package testdb provides test database utilities for integration tests.
package testdb

import (
	"context"
	"fmt"
	"os"
	"testing"

	"github.com/jackc/pgx/v5/pgxpool"
)

// GetTestDB returns a test database connection pool.
// It skips the test if no database is available.
func GetTestDB(t *testing.T) *pgxpool.Pool {
	t.Helper()

	// Check for test database URL
	dbURL := os.Getenv("TEST_DATABASE_URL")
	if dbURL == "" {
		// Try to construct from individual env vars
		host := os.Getenv("TEST_DB_HOST")
		if host == "" {
			host = "localhost"
		}
		port := os.Getenv("TEST_DB_PORT")
		if port == "" {
			port = "5432"
		}
		user := os.Getenv("TEST_DB_USER")
		if user == "" {
			user = "postgres"
		}
		pass := os.Getenv("TEST_DB_PASS")
		if pass == "" {
			pass = "postgres"
		}
		dbname := os.Getenv("TEST_DB_NAME")
		if dbname == "" {
			dbname = "assistant_test"
		}

		// Only construct URL if we have minimum required info
		if os.Getenv("CI") != "" || os.Getenv("TEST_DATABASE") != "" {
			dbURL = fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=disable",
				user, pass, host, port, dbname)
		}
	}

	if dbURL == "" {
		t.Skip("No test database configured. Set TEST_DATABASE_URL or TEST_DATABASE=1")
		return nil
	}

	// Create connection pool
	config, err := pgxpool.ParseConfig(dbURL)
	if err != nil {
		t.Skipf("Invalid database URL: %v", err)
		return nil
	}

	// Set reasonable test pool limits
	config.MaxConns = 5
	config.MinConns = 1

	ctx := context.Background()
	pool, err := pgxpool.NewWithConfig(ctx, config)
	if err != nil {
		t.Skipf("Failed to create connection pool: %v", err)
		return nil
	}

	// Test the connection
	if err := pool.Ping(ctx); err != nil {
		pool.Close()
		t.Skipf("Failed to ping database: %v", err)
		return nil
	}

	// Clean up on test completion
	t.Cleanup(func() {
		pool.Close()
	})

	return pool
}
