// Package prompt provides structured prompt management for AI interactions.
//
// WHY: Prompt engineering is critical for AI application quality. Templates
// enable reusable, testable, and maintainable prompt patterns while avoiding
// string concatenation bugs and ensuring consistent formatting.
package prompt

import (
	"bytes"
	"fmt"
	"strings"
	"text/template"
)

// Template represents a reusable prompt template.
// WHY: Interface allows different template implementations while
// keeping the API surface small and focused.
type Template interface {
	// Format applies variables to the template and returns the result.
	Format(variables *TemplateVariables) (string, error)

	// Variables returns the expected variable names for validation.
	Variables() []string
}

type SimpleTemplate struct {
	name     string
	template string
	vars     []string
	tmpl     *template.Template
}

// NewSimpleTemplate creates a new simple template.
func NewSimpleTemplate(name, templateStr string) (*SimpleTemplate, error) {
	// Parse template to validate syntax
	tmpl, err := template.New(name).Parse(templateStr)
	if err != nil {
		return nil, fmt.Errorf("invalid template syntax: %w", err)
	}

	// Extract variable names for validation
	vars := extractVariables(templateStr)

	return &SimpleTemplate{
		name:     name,
		template: templateStr,
		vars:     vars,
		tmpl:     tmpl,
	}, nil
}

// Format applies variables to the template.
func (t *SimpleTemplate) Format(variables *TemplateVariables) (string, error) {
	var buf bytes.Buffer

	// Convert to map for template execution
	varsMap := variables.ToMap()

	if err := t.tmpl.Execute(&buf, varsMap); err != nil {
		return "", fmt.Errorf("template execution failed: %w", err)
	}

	return buf.String(), nil
}

// Variables returns expected variable names.
func (t *SimpleTemplate) Variables() []string {
	return t.vars
}

// extractVariables extracts variable names from template.
// Simple implementation - could be enhanced with proper parsing.
func extractVariables(tmpl string) []string {
	var vars []string
	seen := make(map[string]bool)

	// Look for {{.VarName}} patterns
	parts := strings.Split(tmpl, "{{")
	for _, part := range parts[1:] {
		if idx := strings.Index(part, "}}"); idx > 0 {
			varExpr := strings.TrimSpace(part[:idx])
			if strings.HasPrefix(varExpr, ".") {
				varName := strings.TrimPrefix(varExpr, ".")
				// Handle nested access like .User.Name
				if dotIdx := strings.Index(varName, "."); dotIdx > 0 {
					varName = varName[:dotIdx]
				}
				if !seen[varName] {
					vars = append(vars, varName)
					seen[varName] = true
				}
			}
		}
	}

	return vars
}

// ChatTemplate structures prompts for chat-based interactions.
// WHY: Chat-based AI models require specific message formatting.
// This provides a reusable structure for common patterns.
type ChatTemplate struct {
	name           string
	systemTemplate Template
	userTemplate   Template
}

// NewChatTemplate creates a new chat template.
func NewChatTemplate(name, systemPrompt, userPrompt string) (*ChatTemplate, error) {
	systemTmpl, err := NewSimpleTemplate(name+"_system", systemPrompt)
	if err != nil {
		return nil, fmt.Errorf("invalid system template: %w", err)
	}

	userTmpl, err := NewSimpleTemplate(name+"_user", userPrompt)
	if err != nil {
		return nil, fmt.Errorf("invalid user template: %w", err)
	}

	return &ChatTemplate{
		name:           name,
		systemTemplate: systemTmpl,
		userTemplate:   userTmpl,
	}, nil
}

// FormatSystem formats the system message.
func (c *ChatTemplate) FormatSystem(variables *TemplateVariables) (string, error) {
	return c.systemTemplate.Format(variables)
}

// FormatUser formats the user message.
func (c *ChatTemplate) FormatUser(variables *TemplateVariables) (string, error) {
	return c.userTemplate.Format(variables)
}

// Variables returns all variables from both templates.
func (c *ChatTemplate) Variables() []string {
	vars := make(map[string]bool)
	allVars := []string{}

	// Combine variables from both templates
	for _, v := range c.systemTemplate.Variables() {
		if !vars[v] {
			vars[v] = true
			allVars = append(allVars, v)
		}
	}

	for _, v := range c.userTemplate.Variables() {
		if !vars[v] {
			vars[v] = true
			allVars = append(allVars, v)
		}
	}

	return allVars
}

// PromptBuilder provides fluent API for building prompts.
// WHY: Builder pattern enables step-by-step prompt construction
// with validation at each step.
type PromptBuilder struct {
	sections []string
	vars     *TemplateVariables
}

// NewPromptBuilder creates a new prompt builder.
func NewPromptBuilder() *PromptBuilder {
	return &PromptBuilder{
		sections: []string{},
		vars:     NewTemplateVariables(),
	}
}

// AddSection adds a section to the prompt.
func (b *PromptBuilder) AddSection(section string) *PromptBuilder {
	b.sections = append(b.sections, section)
	return b
}

// WithUserContext sets user context variables.
func (b *PromptBuilder) WithUserContext(userID, userName, userRole string) *PromptBuilder {
	b.vars.UserID = userID
	b.vars.UserName = userName
	b.vars.UserRole = userRole
	return b
}

// WithQuery sets the query variable.
func (b *PromptBuilder) WithQuery(query string) *PromptBuilder {
	b.vars.Query = query
	return b
}

// WithContent sets content-related variables.
func (b *PromptBuilder) WithContent(content, context, instructions string) *PromptBuilder {
	b.vars.Content = content
	b.vars.Context = context
	b.vars.Instructions = instructions
	return b
}

// WithTask sets task-related variables.
func (b *PromptBuilder) WithTask(taskType, description string, parameters []string) *PromptBuilder {
	b.vars.TaskType = taskType
	b.vars.TaskDescription = description
	b.vars.TaskParameters = parameters
	return b
}

// WithConfiguration sets configuration variables.
func (b *PromptBuilder) WithConfiguration(maxTokens int, temperature float64, responseFormat string) *PromptBuilder {
	b.vars.MaxTokens = maxTokens
	b.vars.Temperature = temperature
	b.vars.ResponseFormat = responseFormat
	return b
}

// WithCustomString adds a custom string variable.
func (b *PromptBuilder) WithCustomString(key, value string) *PromptBuilder {
	b.vars.CustomStrings[key] = value
	return b
}

// WithCustomNumber adds a custom numeric variable.
func (b *PromptBuilder) WithCustomNumber(key string, value float64) *PromptBuilder {
	b.vars.CustomNumbers[key] = value
	return b
}

// WithCustomBoolean adds a custom boolean variable.
func (b *PromptBuilder) WithCustomBoolean(key string, value bool) *PromptBuilder {
	b.vars.CustomBooleans[key] = value
	return b
}

// Build constructs the final prompt.
func (b *PromptBuilder) Build() (string, error) {
	if len(b.sections) == 0 {
		return "", fmt.Errorf("no sections added to prompt")
	}

	// Join sections with double newline
	fullPrompt := strings.Join(b.sections, "\n\n")

	// Apply variable substitution if any variables are set
	// Check if any variables have been set
	hasVars := false
	varsMap := b.vars.ToMap()
	if len(varsMap) > 0 {
		hasVars = true
	}

	if hasVars {
		tmpl, err := NewSimpleTemplate("builder", fullPrompt)
		if err != nil {
			return "", err
		}
		return tmpl.Format(b.vars)
	}

	return fullPrompt, nil
}
