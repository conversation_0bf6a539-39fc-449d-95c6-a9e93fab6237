// Package prompt provides dynamic prompt building capabilities.
package prompt

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/koopa0/assistant-go/internal/tool"
)

// <PERSON>uild<PERSON> constructs dynamic prompts by combining static templates with runtime information.
type Builder struct {
	userInfo     UserInfo
	toolManager  *tool.Manager
	baseTemplate string
}

// NewBuilder creates a new prompt builder.
func NewBuilder(userInfo UserInfo, toolManager *tool.Manager) *Builder {
	return &Builder{
		userInfo:     userInfo,
		toolManager:  toolManager,
		baseTemplate: SystemPrompt,
	}
}

// BuildSystemPrompt creates a complete system prompt with dynamic tool descriptions.
func (b *Builder) BuildSystemPrompt(additionalContext string) string {
	// Get the base prompt with user information
	basePrompt := b.formatBasePrompt()

	// Generate dynamic tool descriptions
	toolSection := b.generateToolSection()

	// Replace the static tool section with dynamic content
	finalPrompt := b.replaceToolSection(basePrompt, toolSection)

	// Add additional context if provided
	if additionalContext != "" {
		finalPrompt = finalPrompt + "\n\n" + additionalContext
	}

	return finalPrompt
}

// formatBasePrompt applies user information to the base template.
func (b *Builder) formatBasePrompt() string {
	return fmt.Sprintf(b.baseTemplate,
		b.userInfo.Name,                                                                               // identity
		b.userInfo.Language,                                                                           // communication language preference
		b.userInfo.Name,                                                                               // critical_rules
		b.userInfo.Name, b.userInfo.Email, b.userInfo.Language, b.userInfo.Timezone, b.userInfo.Theme, // user_context
		b.userInfo.Name, // memory capability
		b.userInfo.Name, // final reminder
	)
}

// generateToolSection creates dynamic tool descriptions from registered tools.
func (b *Builder) generateToolSection() string {
	if b.toolManager == nil {
		return ""
	}

	tools := b.toolManager.List()
	if len(tools) == 0 {
		return "<tools>\n<no_tools>No tools are currently available</no_tools>\n</tools>"
	}

	var builder strings.Builder
	builder.WriteString("<tools>\n")

	// Generate tool descriptions
	for _, t := range tools {
		builder.WriteString(b.formatToolDescription(t))
		builder.WriteString("\n")
	}

	builder.WriteString("</tools>")
	return builder.String()
}

// formatToolDescription formats a single tool for the prompt.
func (b *Builder) formatToolDescription(t tool.Tool) string {
	var builder strings.Builder

	builder.WriteString(fmt.Sprintf(`<tool name="%s">`, t.Name()))
	builder.WriteString("\n")

	// Purpose/Description
	builder.WriteString(fmt.Sprintf("<purpose>%s</purpose>\n", t.Description()))

	// Parameters
	if schema := t.InputSchema(); len(schema) > 0 {
		// Parse the schema to extract parameters
		var schemaObj map[string]interface{}
		if err := json.Unmarshal(schema, &schemaObj); err == nil {
			if parameters, ok := schemaObj["parameters"].(map[string]interface{}); ok {
				builder.WriteString("<parameters>")
				builder.WriteString(b.formatParameters(parameters))
				builder.WriteString("</parameters>\n")
			}
		}
	}

	// Usage hints (if available in description)
	if strings.Contains(t.Description(), "when") || strings.Contains(t.Description(), "use") {
		builder.WriteString(fmt.Sprintf("<usage>%s</usage>\n", t.Description()))
	}

	builder.WriteString("</tool>")
	return builder.String()
}

// formatParameters formats tool parameters for the prompt.
func (b *Builder) formatParameters(params map[string]interface{}) string {
	var parts []string

	// Extract properties
	if props, ok := params["properties"].(map[string]interface{}); ok {
		// Extract required fields
		required := make(map[string]bool)
		if reqList, ok := params["required"].([]interface{}); ok {
			for _, r := range reqList {
				if name, ok := r.(string); ok {
					required[name] = true
				}
			}
		}

		// Format each parameter
		for name, propDef := range props {
			if prop, ok := propDef.(map[string]interface{}); ok {
				paramDesc := name
				if required[name] {
					paramDesc += " (required)"
				} else {
					paramDesc += " (optional)"
				}

				// Add type if available
				if propType, ok := prop["type"].(string); ok {
					paramDesc += fmt.Sprintf(", type: %s", propType)
				}

				// Add description if available
				if desc, ok := prop["description"].(string); ok {
					paramDesc += fmt.Sprintf(" - %s", desc)
				}

				parts = append(parts, paramDesc)
			}
		}
	}

	return strings.Join(parts, "; ")
}

// replaceToolSection replaces the static tool section with dynamic content.
func (b *Builder) replaceToolSection(prompt, newToolSection string) string {
	// Find the start and end of the tools section
	startTag := "<tools>"
	endTag := "</tools>"

	startIdx := strings.Index(prompt, startTag)
	if startIdx == -1 {
		// No tools section found, append at the end
		return prompt + "\n\n" + newToolSection
	}

	endIdx := strings.Index(prompt[startIdx:], endTag)
	if endIdx == -1 {
		// Malformed prompt, just append
		return prompt + "\n\n" + newToolSection
	}

	// Calculate actual end index
	endIdx = startIdx + endIdx + len(endTag)

	// Replace the section
	return prompt[:startIdx] + newToolSection + prompt[endIdx:]
}

// UpdateUserInfo updates the user information.
func (b *Builder) UpdateUserInfo(userInfo UserInfo) {
	b.userInfo = userInfo
}

// UpdateToolManager updates the tool manager reference.
func (b *Builder) UpdateToolManager(toolManager *tool.Manager) {
	b.toolManager = toolManager
}

// SetBaseTemplate allows customizing the base template.
func (b *Builder) SetBaseTemplate(template string) {
	b.baseTemplate = template
}
