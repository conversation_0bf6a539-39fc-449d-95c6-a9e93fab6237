package prompt

import (
	"fmt"
	"strings"
	"time"
)

// MemoryInstructions provides enhanced instructions for memory understanding
const MemoryInstructions = `
## Memory and Schedule Understanding Instructions

When processing user queries and memories, follow these principles:

### 1. Temporal Awareness
- Always consider the current time and date when interpreting queries
- Understand relative time expressions (tomorrow, day after tomorrow, next week, last month)
- Match weekday references to actual dates
- Consider timezone for all time calculations

### 2. Schedule Recognition
- Identify recurring patterns from phrases like:
  - "every week", "weekly" → Weekly recurring event
  - "usually", "often", "always" → Regular activity
  - "sometimes", "occasionally" → Irregular activity
- Extract time information even from casual mentions
- Understand schedule exceptions and updates

### 3. Context Integration
- When user asks about future dates (e.g., "what's happening the day after tomorrow"):
  1. Calculate the actual date
  2. Check what day of week it is
  3. Look for recurring schedules for that weekday
  4. Consider any specific plans mentioned for that date
  5. Account for exceptions or changes mentioned

### 4. Memory Priority
- More recent information overrides older information
- Explicit statements override implicit patterns
- Specific dates override recurring patterns
- Always explain your reasoning when uncertain

### 5. Memory Conflict Resolution
IMPORTANT: When you detect conflicting memories (e.g., "喜歡綠茶" vs "不喝綠茶"):
- Silently handle conflicts using the available tools:
  - delete_memory: Delete a specific outdated or incorrect memory
  - resolve_memory_conflicts: Resolve multiple conflicting memories at once
- NEVER mention to the user that you are:
  - "Updating memories", "resolving conflicts", or "processing memories"
  - "Deleting old memories" or any internal operations
  - Any technical details about memory management
- Simply provide responses based on the most recent/accurate information
- Example workflow:
  1. If you see memories with IDs in the format: <memory id="uuid" type="preference">content</memory>
  2. Identify conflicting memories internally
  3. Use resolve_memory_conflicts tool silently
  4. Respond naturally based on the updated information

### 6. Natural Response Patterns
- Don't just list facts, integrate them naturally
- For schedule queries, mention both recurring and specific events
- Handle memory conflicts silently without mentioning the process
- Express uncertainty when information is ambiguous
- Suggest clarification when needed

### Example Responses:
- "The day after tomorrow is Wednesday, you have Thai boxing class (every Tuesday and Thursday at 7 PM)"
- "Next Tuesday you mentioned you might skip Thai boxing because of overtime work"
- "You usually have coffee in the morning, recently switched to Americano"
- "Based on what you've told me, you don't drink green tea anymore"
`

// EnhanceSystemPromptWithMemoryInstructions adds memory-specific instructions
func EnhanceSystemPromptWithMemoryInstructions(basePrompt string) string {
	return basePrompt + "\n\n" + MemoryInstructions
}

// GenerateTemporalContext creates enhanced temporal context
func GenerateTemporalContext(now time.Time, timezone string) string {
	loc, err := time.LoadLocation(timezone)
	if err != nil {
		loc = time.UTC
	}

	localTime := now.In(loc)

	// Calculate useful temporal markers
	tomorrow := localTime.AddDate(0, 0, 1)
	dayAfterTomorrow := localTime.AddDate(0, 0, 2)
	nextWeek := localTime.AddDate(0, 0, 7)

	context := fmt.Sprintf(`<temporal_context>
<now>%s</now>
<date>%s</date>
<time>%s</time>
<weekday>%s</weekday>
<hour>%d</hour>
<timezone>%s</timezone>
<period>%s</period>
<relative_dates>
	<tomorrow date="%s" weekday="%s"/>
	<day_after_tomorrow date="%s" weekday="%s"/>
	<next_week date="%s"/>
</relative_dates>
</temporal_context>`,
		localTime.Format("2006-01-02 15:04:05"),
		localTime.Format("2006-01-02"),
		localTime.Format("15:04"),
		localTime.Weekday().String(),
		localTime.Hour(),
		timezone,
		getTimePeriod(localTime.Hour()),
		tomorrow.Format("2006-01-02"),
		tomorrow.Weekday().String(),
		dayAfterTomorrow.Format("2006-01-02"),
		dayAfterTomorrow.Weekday().String(),
		nextWeek.Format("2006-01-02"))

	return context
}

// getTimePeriod returns the period of day
func getTimePeriod(hour int) string {
	switch {
	case hour >= 5 && hour < 9:
		return "early_morning"
	case hour >= 9 && hour < 12:
		return "morning"
	case hour >= 12 && hour < 14:
		return "noon"
	case hour >= 14 && hour < 18:
		return "afternoon"
	case hour >= 18 && hour < 22:
		return "evening"
	default:
		return "night"
	}
}

// QueryAnalysisPrompt helps analyze user queries for better memory retrieval
func QueryAnalysisPrompt(query string) string {
	return fmt.Sprintf(`<query_analysis>
Analyze this query to understand the user's intent:
Query: "%s"

Consider:
1. Is this asking about a specific time? (when)
2. Is this asking about activities or schedules? (what)
3. Is this asking about preferences or habits? (patterns)
4. What temporal context is needed? (past/present/future)
5. What memory types should be searched? (facts/schedules/preferences)
</query_analysis>`, query)
}

// ScheduleInterpretationPrompt helps interpret schedule information
func ScheduleInterpretationPrompt(schedules []string) string {
	if len(schedules) == 0 {
		return ""
	}

	return fmt.Sprintf(`<schedule_interpretation>
Based on the user's schedules:
%s

Remember to:
- Consider current day and time
- Check for schedule conflicts or exceptions
- Mention both recurring and one-time events
- Express appropriate uncertainty for ambiguous cases
</schedule_interpretation>`, strings.Join(schedules, "\n"))
}

// MemoryConflictResolutionPrompt helps resolve conflicting information
func MemoryConflictResolutionPrompt() string {
	return `<conflict_resolution>
When memories contain conflicting information:
1. Prioritize more recent statements
2. Consider context (casual mention vs explicit statement)
3. Look for updates or corrections
4. Express uncertainty when appropriate
5. Ask for clarification if needed
</conflict_resolution>`
}

// GetMemoryExtractionPrompt returns the prompt for extracting structured facts
func GetMemoryExtractionPrompt() string {
	return `You are a memory extraction system that converts conversations into structured data.

Extract information as structured facts with entities, attributes, and context.

Memory Types:
- fact: Static information about the world
- preference: User likes, dislikes, choices
- schedule: Time-based events, appointments
- relationship: Connections between people/entities
- goal: Objectives, plans, aspirations
- skill: Abilities, knowledge, expertise

Entity Types:
- person: People mentioned
- place: Locations
- activity: Actions, events
- time: Temporal references
- thing: Objects
- concept: Abstract ideas

Entity Roles:
- subject: Main actor
- object: Target of action
- location: Where something happens
- time: When something happens
- with: Accompanying entity

Focus on:
1. Atomic facts (one main idea per fact)
2. Complete information with all relevant details
3. Temporal context (when things happen)
4. Relationships between entities
5. Attributes that can be updated later

CRITICAL for corrections:
- Extract ONLY the corrected information
- "不是X而是Y", "actually Y not X", "correct to Y" → Extract Y
- Time/date corrections are common

Always preserve the original language of the conversation.`
}

// GetMemoryUpdatePrompt returns the prompt for deciding memory update actions
// This follows mem0's UPDATE_MEMORY_PROMPT pattern
func GetMemoryUpdatePrompt() string {
	return `You must analyze memory updates and respond with a JSON decision.

FIRST, output your decision as JSON: {"action": "ACTION", "target": "mem_X"}

Operations:
- UPDATE: When new fact corrects/changes existing memory (MOST COMMON for corrections)
- ADD: Only when fact is completely new, unrelated to existing memories
- DELETE: Rare, only for direct contradictions
- NONE: If fact already exists

Key rules for UPDATE:
1. Same topic, different detail = UPDATE (e.g., time 8PM→7:30PM)
2. Corrections with "not X but Y", "actually", "should be" = UPDATE
3. Any modification of existing info = UPDATE

Example decisions:
- "Favorite color is blue" vs mem_1:"Favorite color is red" → {"action": "UPDATE", "target": "mem_1"}
- "Meeting at 7/16" vs mem_1:"Meeting at 7/17" → {"action": "UPDATE", "target": "mem_1"}
- "Thai boxing at 7:30PM" vs mem_1:"Thai boxing at 8:00PM" → {"action": "UPDATE", "target": "mem_1"}

Start your response with the JSON decision.`
}

// GetStructuredExtractionExamples provides examples for structured extraction
func GetStructuredExtractionExamples() string {
	return `Example extractions:

1. "我的泰拳課是星期三晚上7點30分到8點30分"
{
  "facts": [{
    "content": "泰拳課是星期三晚上7點30分到8點30分",
    "type": "schedule",
    "entities": [
      {"name": "泰拳", "type": "activity", "role": "object"},
      {"name": "星期三", "type": "time", "role": "time"},
      {"name": "晚上7點30分到8點30分", "type": "time", "role": "time"}
    ],
    "attributes": {
      "weekday": "Wednesday",
      "start_time": "19:30",
      "end_time": "20:30"
    },
    "context": {
      "recurrence": {"pattern": "weekly", "days": ["wednesday"]}
    },
    "confidence": 0.95
  }]
}

2. "I love Italian food, especially pasta"
{
  "facts": [{
    "content": "I love Italian food, especially pasta",
    "type": "preference",
    "entities": [
      {"name": "Italian food", "type": "thing", "role": "object"},
      {"name": "pasta", "type": "thing", "role": "object"}
    ],
    "attributes": {
      "preference_level": "love",
      "favorite_type": "pasta"
    },
    "context": {},
    "confidence": 0.9
  }]
}`
}

// GetCustomMemoryExtractionPrompt returns a customizable extraction prompt
// This allows users to focus on specific types of information
func GetCustomMemoryExtractionPrompt(focusAreas []string, examples []Example) string {
	basePrompt := `You are a Personal Information Organizer specializing in extracting specific information.

Focus on extracting:`

	for _, area := range focusAreas {
		basePrompt += fmt.Sprintf("\n- %s", area)
	}

	basePrompt += `

Extract ONLY relevant facts from the conversation.
Return facts in the user's original language.

Return a JSON object with a "facts" array:
{"facts": ["fact 1", "fact 2", ...]}

If there are no relevant facts, return:
{"facts": []}`

	if len(examples) > 0 {
		template := NewFewShotTemplate(basePrompt, examples)
		return template.Format("")
	}

	return basePrompt
}

// MemoryCategories defines the categories for automatic memory classification
var MemoryCategories = []string{
	"personal_info",
	"preferences",
	"schedule",
	"relationships",
	"work",
	"health",
	"goals",
	"skills",
	"location",
	"contact",
}

// GetMemoryCategoryPrompt returns a prompt for categorizing a memory
func GetMemoryCategoryPrompt(memory string) string {
	return fmt.Sprintf(`Categorize this memory into one of these categories:
%s

Memory: "%s"

Return only the category name, nothing else.`, strings.Join(MemoryCategories, ", "), memory)
}

// GetEntityExtractionPrompt returns a prompt for extracting entities
func GetEntityExtractionPrompt() string {
	return `Extract entities from the text with their types and roles.

Entity types:
- person: Names of people
- place: Locations, venues
- activity: Actions, events, classes
- time: Dates, times, days of week
- thing: Objects, items
- concept: Abstract ideas

Entity roles:
- subject: Who is doing something
- object: What is being done or talked about
- location: Where something happens
- time: When something happens
- with: Who/what accompanies

Return entities with clear types and roles.`
}

// GetTimePatterns returns common time patterns for extraction
func GetTimePatterns() []string {
	return []string{
		// Chinese patterns
		"星期[\u4e00\u4e8c\u4e09\u56db\u4e94\u516d\u65e5]", // 星期一 to 星期日
		"週[\u4e00\u4e8c\u4e09\u56db\u4e94\u516d\u65e5]",  // 週一 to 週日
		"(\\d{1,2})[點:：](\\d{2})?",                       // 7點, 7:30, 7：30
		"(早上|上午|中午|下午|晚上|夜晚)",                            // Time periods
		"(\\d{1,2})[月/](\\d{1,2})[日]?",                   // 7月8日, 7/8
		// English patterns
		"(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday)",
		"(\\d{1,2}):(\\d{2})\\s*(AM|PM|am|pm)?",
		"(morning|afternoon|evening|night)",
		"(\\d{1,2})/(\\d{1,2})",
	}
}
