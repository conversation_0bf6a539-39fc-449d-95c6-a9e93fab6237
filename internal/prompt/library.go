package prompt

import (
	"fmt"
	"strings"
)

// Library provides a collection of reusable prompt templates.
// WHY: Common prompt patterns should be standardized and tested
// rather than recreated each time. This improves consistency.
type Library struct {
	templates map[string]Template
}

// NewLibrary creates a new prompt library.
func NewLibrary() *Library {
	lib := &Library{
		templates: make(map[string]Template),
	}

	// Load default templates
	lib.loadDefaults()

	return lib
}

// loadDefaults loads standard prompt templates.
func (l *Library) loadDefaults() {
	// Summarization template
	summarize, err := NewSimpleTemplate("summarize",
		`Summarize the following text in {{.MaxSentences}} sentences:

{{.Text}}`)
	if err != nil {
		panic(fmt.Sprintf("failed to create summarize template: %v", err))
	}
	l.Register("summarize", summarize)

	// Question answering template
	qa, err := NewSimpleTemplate("question_answer",
		`Based on the following context, answer the question.

Context:
{{.Context}}

Question: {{.Question}}

Answer:`)
	if err != nil {
		panic(fmt.Sprintf("failed to create qa template: %v", err))
	}
	l.Register("qa", qa)

	// Translation template
	translate, _ := NewSimpleTemplate("translate",
		`Translate the following text from {{.SourceLang}} to {{.TargetLang}}:

{{.Text}}`)
	l.Register("translate", translate)

	// Code explanation template
	codeExplain, _ := NewSimpleTemplate("code_explain",
		"Explain the following {{.Language}} code in simple terms:\n\n```{{.Language}}\n{{.Code}}\n```")
	l.Register("code_explain", codeExplain)

	// Entity extraction template
	entities, _ := NewSimpleTemplate("extract_entities",
		`Extract all {{.EntityType}} entities from the following text.
Return as a comma-separated list.

Text: {{.Text}}`)
	l.Register("extract_entities", entities)
}

// Register adds a template to the library.
func (l *Library) Register(name string, template Template) {
	l.templates[name] = template
}

// Get retrieves a template by name.
func (l *Library) Get(name string) (Template, bool) {
	tmpl, ok := l.templates[name]
	return tmpl, ok
}

// List returns all template names.
func (l *Library) List() []string {
	names := make([]string, 0, len(l.templates))
	for name := range l.templates {
		names = append(names, name)
	}
	return names
}

// CommonPrompts provides pre-built prompts for common tasks.
// WHY: These patterns are used frequently and benefit from
// standardization and optimization.
var CommonPrompts = struct {
	// Summarization creates a summarization prompt
	Summarization func(text string, maxSentences int) string

	// QuestionAnswer creates a QA prompt
	QuestionAnswer func(context, question string) string

	// Translation creates a translation prompt
	Translation func(text, sourceLang, targetLang string) string

	// Sentiment creates a sentiment analysis prompt
	Sentiment func(text string) string

	// Keywords creates a keyword extraction prompt
	Keywords func(text string, count int) string
}{
	Summarization: func(text string, maxSentences int) string {
		prompt, _ := NewPromptBuilder().
			AddSection("You are a professional summarizer.").
			AddSection("Summarize the following text in {{.MaxSentences}} sentences:").
			AddSection("{{.Text}}").
			WithCustomNumber("MaxSentences", float64(maxSentences)).
			WithCustomString("Text", text).
			Build()
		return prompt
	},

	QuestionAnswer: func(context, question string) string {
		prompt, _ := NewPromptBuilder().
			AddSection("Answer the question based solely on the provided context.").
			AddSection("If the answer cannot be found in the context, say so.").
			AddSection("Context:\n{{.Context}}").
			AddSection("Question: {{.Question}}").
			WithCustomString("Context", context).
			WithCustomString("Question", question).
			Build()
		return prompt
	},

	Translation: func(text, sourceLang, targetLang string) string {
		prompt, _ := NewPromptBuilder().
			AddSection("You are a professional translator.").
			AddSection("Translate from {{.SourceLang}} to {{.TargetLang}}.").
			AddSection("Preserve the original meaning and tone.").
			AddSection("Text to translate:\n{{.Text}}").
			WithCustomString("SourceLang", sourceLang).
			WithCustomString("TargetLang", targetLang).
			WithCustomString("Text", text).
			Build()
		return prompt
	},

	Sentiment: func(text string) string {
		return "Analyze the sentiment of the following text. " +
			"Return one of: positive, negative, neutral, mixed.\n" +
			"Also provide a confidence score (0-1).\n\n" +
			"Text: " + text
	},

	Keywords: func(text string, count int) string {
		prompt, _ := NewPromptBuilder().
			AddSection("Extract the {{.Count}} most important keywords from the text.").
			AddSection("Return as a comma-separated list.").
			AddSection("Text:\n{{.Text}}").
			WithCustomNumber("Count", float64(count)).
			WithCustomString("Text", text).
			Build()
		return prompt
	},
}

// FewShotTemplate supports few-shot learning with examples.
// WHY: Few-shot prompting significantly improves AI performance
// on specific tasks by providing examples.
type FewShotTemplate struct {
	instruction string
	examples    []Example
	// TODO: template field reserved for future template engine integration
	// template    Template
}

// Example represents an input-output example.
type Example struct {
	Input  string
	Output string
}

// NewFewShotTemplate creates a new few-shot template.
func NewFewShotTemplate(instruction string, examples []Example) *FewShotTemplate {
	return &FewShotTemplate{
		instruction: instruction,
		examples:    examples,
	}
}

// Format creates the few-shot prompt.
func (f *FewShotTemplate) Format(input string) string {
	var builder strings.Builder

	// Add instruction
	builder.WriteString(f.instruction)
	builder.WriteString("\n\n")

	// Add examples
	for i, example := range f.examples {
		builder.WriteString(fmt.Sprintf("Example %d:\n", i+1))
		builder.WriteString("Input: ")
		builder.WriteString(example.Input)
		builder.WriteString("\n")
		builder.WriteString("Output: ")
		builder.WriteString(example.Output)
		builder.WriteString("\n\n")
	}

	// Add actual input
	builder.WriteString("Input: ")
	builder.WriteString(input)
	builder.WriteString("\n")
	builder.WriteString("Output:")

	return builder.String()
}
