package prompt

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestNewTemplateVariables tests TemplateVariables creation
func TestNewTemplateVariables(t *testing.T) {
	vars := NewTemplateVariables()

	assert.NotNil(t, vars)
	assert.NotNil(t, vars.UserPreferences)
	assert.NotNil(t, vars.CustomStrings)
	assert.NotNil(t, vars.CustomNumbers)
	assert.NotNil(t, vars.CustomBooleans)
	assert.NotZero(t, vars.CurrentTime)
	assert.Equal(t, "en", vars.Language)
	assert.Equal(t, "2006-01-02 15:04:05", vars.DateFormat)
}

// TestTemplateVariables_ToMap tests conversion to map
func TestTemplateVariables_ToMap(t *testing.T) {
	vars := &TemplateVariables{
		// User context
		UserID:      "user123",
		UserName:    "<PERSON>",
		UserRole:    "admin",
		UserContext: "working on project",

		// Conversation context
		ConversationID:    "conv456",
		MessageCount:      10,
		LastMessageTime:   time.Date(2024, 1, 15, 10, 30, 0, 0, time.UTC),
		ConversationTopic: "project planning",

		// Content variables
		Query:           "How to implement feature X?",
		Content:         "Feature X specification",
		Context:         "Development context",
		Instructions:    "Be detailed",
		Examples:        []string{"Example 1", "Example 2"},
		AdditionalNotes: "Consider performance",

		// Task-specific variables
		TaskType:        "implementation",
		TaskDescription: "Implement feature X",
		TaskParameters:  []string{"param1", "param2"},
		ExpectedOutput:  "Working code",

		// Configuration
		MaxTokens:      1000,
		Temperature:    0.7,
		ResponseFormat: "json",
		Language:       "English",

		// Time context
		CurrentTime: time.Date(2024, 1, 15, 11, 0, 0, 0, time.UTC),
		Timezone:    "UTC",
		DateFormat:  "2006-01-02 15:04:05",

		// Memory/knowledge context
		RelevantMemories: []string{"Previous discussion about feature X"},
		UserPreferences:  map[string]string{"theme": "dark", "editor": "vim"},
		FactualContext:   []string{"Fact 1", "Fact 2"},

		// Tool context
		AvailableTools: []string{"code_search", "web_search"},
		ToolResults:    []string{"Found 5 matches"},

		// Custom fields
		CustomStrings:  map[string]string{"project": "assistant-go"},
		CustomNumbers:  map[string]float64{"version": 1.0},
		CustomBooleans: map[string]bool{"debug": true},
	}

	m := vars.ToMap()

	// Check user context fields
	assert.Equal(t, "user123", m["UserID"])
	assert.Equal(t, "John Doe", m["UserName"])
	assert.Equal(t, "admin", m["UserRole"])
	assert.Equal(t, "working on project", m["UserContext"])

	// Check conversation fields
	assert.Equal(t, "conv456", m["ConversationID"])
	assert.Equal(t, 10, m["MessageCount"])
	assert.Equal(t, "project planning", m["ConversationTopic"])

	// Check content fields
	assert.Equal(t, "How to implement feature X?", m["Query"])
	assert.Equal(t, "Feature X specification", m["Content"])
	assert.Equal(t, "Development context", m["Context"])
	assert.Equal(t, "Be detailed", m["Instructions"])

	// Check task fields
	assert.Equal(t, "implementation", m["TaskType"])
	assert.Equal(t, "Implement feature X", m["TaskDescription"])
	assert.Equal(t, "Working code", m["ExpectedOutput"])

	// Check configuration fields
	assert.Equal(t, 1000, m["MaxTokens"])
	assert.Equal(t, 0.7, m["Temperature"])
	assert.Equal(t, "json", m["ResponseFormat"])

	// Check custom fields
	assert.Equal(t, "assistant-go", m["project"])
	assert.Equal(t, 1.0, m["version"])
	assert.Equal(t, true, m["debug"])
}

// TestTemplateVariables_FromMap tests creation from map
func TestTemplateVariables_FromMap(t *testing.T) {
	m := map[string]any{
		"UserID":          "user456",
		"UserName":        "Jane Smith",
		"UserRole":        "developer",
		"Query":           "test query",
		"Context":         "test context",
		"Language":        "Japanese",
		"MessageCount":    5,
		"MaxTokens":       500,
		"Temperature":     0.5,
		"CurrentTime":     "2024-01-15 10:30:00",
		"Examples":        []any{"ex1", "ex2"},
		"TaskParameters":  []any{"p1", "p2"},
		"UserPreferences": map[string]any{"color": "blue"},
		"custom_field":    "custom value",
		"custom_number":   42.5,
		"custom_bool":     false,
	}

	vars := FromMap(m)

	// Check basic fields
	assert.Equal(t, "user456", vars.UserID)
	assert.Equal(t, "Jane Smith", vars.UserName)
	assert.Equal(t, "developer", vars.UserRole)
	assert.Equal(t, "test query", vars.Query)
	assert.Equal(t, "test context", vars.Context)
	assert.Equal(t, "Japanese", vars.Language)

	// Check numeric fields
	assert.Equal(t, 5, vars.MessageCount)
	assert.Equal(t, 500, vars.MaxTokens)
	assert.Equal(t, 0.5, vars.Temperature)

	// Check array fields
	assert.Equal(t, []string{"ex1", "ex2"}, vars.Examples)
	assert.Equal(t, []string{"p1", "p2"}, vars.TaskParameters)

	// Check map fields
	assert.Equal(t, "blue", vars.UserPreferences["color"])

	// Check custom fields
	assert.Equal(t, "custom value", vars.CustomStrings["custom_field"])
	assert.Equal(t, 42.5, vars.CustomNumbers["custom_number"])
	assert.Equal(t, false, vars.CustomBooleans["custom_bool"])
}

// TestTemplateVariables_EdgeCases tests edge cases in conversion
func TestTemplateVariables_EdgeCases(t *testing.T) {
	t.Run("minimal values", func(t *testing.T) {
		vars := &TemplateVariables{
			UserID:         "test",
			CustomStrings:  make(map[string]string),
			CustomNumbers:  make(map[string]float64),
			CustomBooleans: make(map[string]bool),
		}

		m := vars.ToMap()
		assert.NotNil(t, m)
		assert.Equal(t, "test", m["UserID"])
		// Empty fields should not be included
		_, hasQuery := m["Query"]
		assert.False(t, hasQuery)
	})

	t.Run("zero time values", func(t *testing.T) {
		vars := &TemplateVariables{
			LastMessageTime: time.Time{}, // Zero time
			CurrentTime:     time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
			DateFormat:      "2006-01-02",
		}
		m := vars.ToMap()

		// Zero time should not be included
		_, hasLastMessage := m["LastMessageTime"]
		assert.False(t, hasLastMessage)

		// Non-zero time should be included
		assert.Equal(t, "2024-01-01", m["CurrentTime"])
	})

	t.Run("from map with type conversions", func(t *testing.T) {
		m := map[string]any{
			"MessageCount": "10",         // String that should convert to int
			"Temperature":  "0.7",        // String that should convert to float
			"MaxTokens":    float64(100), // Float that should convert to int
		}

		vars := FromMap(m)
		assert.Equal(t, 10, vars.MessageCount)
		assert.Equal(t, 0.7, vars.Temperature)
		assert.Equal(t, 100, vars.MaxTokens)
	})
}

// TestIsKnownField tests field recognition
func TestIsKnownField(t *testing.T) {
	knownFields := []string{
		"UserID", "UserName", "UserRole", "UserContext",
		"ConversationID", "Query", "Content", "Context",
		"MaxTokens", "Temperature", "Language",
	}

	for _, field := range knownFields {
		assert.True(t, isKnownField(field), "Field %s should be known", field)
	}

	unknownFields := []string{
		"unknown_field", "custom_value", "random_data",
	}

	for _, field := range unknownFields {
		assert.False(t, isKnownField(field), "Field %s should be unknown", field)
	}
}

// TestTypeConversionHelpers tests helper functions
func TestTypeConversionHelpers(t *testing.T) {
	t.Run("getString", func(t *testing.T) {
		s, ok := getString(map[string]any{"key": "hello"}, "key")
		assert.True(t, ok)
		assert.Equal(t, "hello", s)

		s, ok = getString(map[string]any{}, "key")
		assert.False(t, ok)
		assert.Equal(t, "", s)

		s, ok = getString(map[string]any{"key": 123}, "key")
		assert.False(t, ok)
		assert.Equal(t, "", s)
	})

	t.Run("getInt", func(t *testing.T) {
		n, ok := getInt(map[string]any{"key": 42}, "key")
		assert.True(t, ok)
		assert.Equal(t, 42, n)

		n, ok = getInt(map[string]any{"key": float64(42)}, "key")
		assert.True(t, ok)
		assert.Equal(t, 42, n)

		n, ok = getInt(map[string]any{"key": "42"}, "key")
		assert.True(t, ok)
		assert.Equal(t, 42, n)

		n, ok = getInt(map[string]any{}, "key")
		assert.False(t, ok)
		assert.Equal(t, 0, n)
	})

	t.Run("getFloat", func(t *testing.T) {
		f, ok := getFloat(map[string]any{"key": 3.14}, "key")
		assert.True(t, ok)
		assert.Equal(t, 3.14, f)

		f, ok = getFloat(map[string]any{"key": 42}, "key")
		assert.True(t, ok)
		assert.Equal(t, 42.0, f)

		f, ok = getFloat(map[string]any{"key": "3.14"}, "key")
		assert.True(t, ok)
		assert.Equal(t, 3.14, f)

		f, ok = getFloat(map[string]any{}, "key")
		assert.False(t, ok)
		assert.Equal(t, 0.0, f)
	})

	t.Run("getTime", func(t *testing.T) {
		now := time.Now()

		// Test with time.Time
		result, ok := getTime(map[string]any{"key": now}, "key")
		assert.True(t, ok)
		assert.Equal(t, now, result)

		// Test with RFC3339 string
		timeStr := "2024-01-15T10:30:00Z"
		result, ok = getTime(map[string]any{"key": timeStr}, "key")
		assert.True(t, ok)
		assert.NotZero(t, result)

		// Test with missing key
		result, ok = getTime(map[string]any{}, "key")
		assert.False(t, ok)
		assert.Zero(t, result)
	})

	t.Run("getStringArray", func(t *testing.T) {
		// Test with []string
		arr := []string{"a", "b", "c"}
		result, ok := getStringArray(map[string]any{"key": arr}, "key")
		assert.True(t, ok)
		assert.Equal(t, arr, result)

		// Test with []any
		arr2 := []any{"a", "b", "c"}
		result, ok = getStringArray(map[string]any{"key": arr2}, "key")
		assert.True(t, ok)
		assert.Equal(t, []string{"a", "b", "c"}, result)

		// Test with missing key
		result, ok = getStringArray(map[string]any{}, "key")
		assert.False(t, ok)
		assert.Nil(t, result)
	})

	t.Run("getStringMap", func(t *testing.T) {
		// Test with map[string]string
		m := map[string]string{"a": "1", "b": "2"}
		result, ok := getStringMap(map[string]any{"key": m}, "key")
		assert.True(t, ok)
		assert.Equal(t, m, result)

		// Test with map[string]any
		m2 := map[string]any{"a": "1", "b": "2"}
		result, ok = getStringMap(map[string]any{"key": m2}, "key")
		assert.True(t, ok)
		assert.Equal(t, map[string]string{"a": "1", "b": "2"}, result)

		// Test with missing key
		result, ok = getStringMap(map[string]any{}, "key")
		assert.False(t, ok)
		assert.Nil(t, result)
	})
}

// TestVariablesRoundTrip tests that variables survive a round trip
func TestVariablesRoundTrip(t *testing.T) {
	original := &TemplateVariables{
		UserID:          "user123",
		UserName:        "Test User",
		Query:           "test query",
		Context:         "test context",
		Language:        "English",
		MessageCount:    5,
		MaxTokens:       1000,
		Temperature:     0.7,
		Examples:        []string{"ex1", "ex2"},
		TaskParameters:  []string{"p1", "p2"},
		UserPreferences: map[string]string{"key": "value"},
		CustomStrings:   map[string]string{"custom1": "value1"},
		CustomNumbers:   map[string]float64{"custom2": 42.5},
		CustomBooleans:  map[string]bool{"custom3": true},
	}

	// Convert to map and back
	m := original.ToMap()
	restored := FromMap(m)

	// Check that values are preserved
	assert.Equal(t, original.UserID, restored.UserID)
	assert.Equal(t, original.UserName, restored.UserName)
	assert.Equal(t, original.Query, restored.Query)
	assert.Equal(t, original.Context, restored.Context)
	assert.Equal(t, original.Language, restored.Language)
	assert.Equal(t, original.MessageCount, restored.MessageCount)
	assert.Equal(t, original.MaxTokens, restored.MaxTokens)
	assert.Equal(t, original.Temperature, restored.Temperature)
	assert.Equal(t, original.Examples, restored.Examples)
	assert.Equal(t, original.TaskParameters, restored.TaskParameters)
	assert.Equal(t, original.UserPreferences["key"], restored.UserPreferences["key"])
	assert.Equal(t, original.CustomStrings["custom1"], restored.CustomStrings["custom1"])
	assert.Equal(t, original.CustomNumbers["custom2"], restored.CustomNumbers["custom2"])
	assert.Equal(t, original.CustomBooleans["custom3"], restored.CustomBooleans["custom3"])
}

// TestTemplateVariables_ComplexScenarios tests complex usage scenarios
func TestTemplateVariables_ComplexScenarios(t *testing.T) {
	t.Run("all fields populated", func(t *testing.T) {
		vars := NewTemplateVariables()

		// Populate all fields
		vars.UserID = "user123"
		vars.UserName = "John Doe"
		vars.UserRole = "admin"
		vars.UserContext = "working"
		vars.ConversationID = "conv123"
		vars.MessageCount = 10
		vars.LastMessageTime = time.Now()
		vars.ConversationTopic = "topic"
		vars.Query = "query"
		vars.Content = "content"
		vars.Context = "context"
		vars.Instructions = "instructions"
		vars.Examples = []string{"ex1"}
		vars.AdditionalNotes = "notes"
		vars.TaskType = "type"
		vars.TaskDescription = "desc"
		vars.TaskParameters = []string{"p1"}
		vars.ExpectedOutput = "output"
		vars.MaxTokens = 1000
		vars.Temperature = 0.7
		vars.ResponseFormat = "json"
		vars.Language = "en"
		vars.CurrentTime = time.Now()
		vars.Timezone = "UTC"
		vars.DateFormat = "2006-01-02"
		vars.RelevantMemories = []string{"mem1"}
		vars.UserPreferences["pref"] = "value"
		vars.FactualContext = []string{"fact1"}
		vars.AvailableTools = []string{"tool1"}
		vars.ToolResults = []string{"result1"}
		vars.CustomStrings["cs"] = "value"
		vars.CustomNumbers["cn"] = 42.0
		vars.CustomBooleans["cb"] = true

		m := vars.ToMap()

		// Verify all fields are present in the map
		assert.Greater(t, len(m), 25) // Should have many fields

		// Spot check some fields
		assert.Equal(t, "user123", m["UserID"])
		assert.Equal(t, "query", m["Query"])
		assert.Equal(t, 1000, m["MaxTokens"])
		assert.Equal(t, "value", m["cs"])
	})

	t.Run("template variable usage", func(t *testing.T) {
		// Create a template that uses various variable types
		tmpl, err := NewSimpleTemplate("test",
			`User: {{.UserName}} ({{.UserID}})
Query: {{.Query}}
Max Tokens: {{.MaxTokens}}
Temperature: {{.Temperature}}
Custom: {{.project}}`)
		require.NoError(t, err)

		vars := NewTemplateVariables()
		vars.UserName = "Alice"
		vars.UserID = "alice123"
		vars.Query = "How to use templates?"
		vars.MaxTokens = 500
		vars.Temperature = 0.8
		vars.CustomStrings["project"] = "assistant-go"

		result, err := tmpl.Format(vars)
		require.NoError(t, err)

		assert.Contains(t, result, "User: Alice (alice123)")
		assert.Contains(t, result, "Query: How to use templates?")
		assert.Contains(t, result, "Max Tokens: 500")
		assert.Contains(t, result, "Temperature: 0.8")
		assert.Contains(t, result, "Custom: assistant-go")
	})
}
