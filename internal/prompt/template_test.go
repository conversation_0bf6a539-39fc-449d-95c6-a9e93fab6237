package prompt

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewSimpleTemplate(t *testing.T) {
	tests := []struct {
		name        string
		templateStr string
		wantErr     bool
		errMsg      string
		wantVars    []string
	}{
		{
			name:        "valid template with variables",
			templateStr: "Hello {{.UserName}}, welcome to {{.Place}}!",
			wantErr:     false,
			wantVars:    []string{"UserName", "Place"},
		},
		{
			name:        "template without variables",
			templateStr: "Hello, world!",
			wantErr:     false,
			wantVars:    []string{},
		},
		{
			name:        "invalid template syntax",
			templateStr: "Hello {{.Name",
			wantErr:     true,
			errMsg:      "invalid template syntax",
		},
		{
			name:        "complex template",
			templateStr: "User: {{.UserName}}\nRole: {{.UserRole}}\n{{if .IsAdmin}}Admin Access{{end}}",
			wantErr:     false,
			wantVars:    []string{"UserName", "UserRole"}, // IsAdmin is inside if, not extracted by simple parser
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tmpl, err := NewSimpleTemplate(tt.name, tt.templateStr)
			if tt.wantErr {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
				assert.Nil(t, tmpl)
			} else {
				require.NoError(t, err)
				require.NotNil(t, tmpl)
				assert.Equal(t, tt.name, tmpl.name)
				assert.Equal(t, tt.templateStr, tmpl.template)
				assert.ElementsMatch(t, tt.wantVars, tmpl.vars)
			}
		})
	}
}

func TestSimpleTemplate_Format(t *testing.T) {
	tests := []struct {
		name        string
		templateStr string
		variables   *TemplateVariables
		expected    string
		wantErr     bool
	}{
		{
			name:        "basic formatting with struct fields",
			templateStr: "Hello {{.UserName}}!",
			variables: &TemplateVariables{
				UserName: "Alice",
			},
			expected: "Hello Alice!",
			wantErr:  false,
		},
		{
			name:        "multiple variables",
			templateStr: "{{.UserName}} is working on {{.TaskDescription}}.",
			variables: &TemplateVariables{
				UserName:        "Bob",
				TaskDescription: "testing",
			},
			expected: "Bob is working on testing.",
			wantErr:  false,
		},
		{
			name:        "missing variable shows empty",
			templateStr: "Hello {{.UserName}}!",
			variables:   &TemplateVariables{},
			expected:    "Hello <no value>!", // Go template default behavior
			wantErr:     false,
		},
		{
			name:        "conditional rendering",
			templateStr: "{{if .Query}}Query: {{.Query}}{{else}}No query{{end}}",
			variables: &TemplateVariables{
				Query: "What is Go?",
			},
			expected: "Query: What is Go?",
			wantErr:  false,
		},
		{
			name:        "with empty variables",
			templateStr: "Hello world!",
			variables:   &TemplateVariables{},
			expected:    "Hello world!",
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tmpl, err := NewSimpleTemplate("test", tt.templateStr)
			require.NoError(t, err)

			result, err := tmpl.Format(tt.variables)
			if tt.wantErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestSimpleTemplate_Variables(t *testing.T) {
	tmpl, err := NewSimpleTemplate("test", "Hello {{.UserName}}, you have {{.MessageCount}} messages!")
	require.NoError(t, err)

	vars := tmpl.Variables()
	assert.ElementsMatch(t, []string{"UserName", "MessageCount"}, vars)
}

func TestExtractVariables(t *testing.T) {
	tests := []struct {
		name     string
		template string
		expected []string
	}{
		{
			name:     "single variable",
			template: "Hello {{.Name}}",
			expected: []string{"Name"},
		},
		{
			name:     "multiple variables",
			template: "{{.First}} {{.Last}}, age {{.Age}}",
			expected: []string{"First", "Last", "Age"},
		},
		{
			name:     "duplicate variables",
			template: "{{.Name}} is {{.Name}}'s name",
			expected: []string{"Name"},
		},
		{
			name:     "nested in conditionals",
			template: "{{if .Show}}{{.Name}}{{end}}",
			expected: []string{"Name"}, // Simple parser only finds direct {{.X}} patterns
		},
		{
			name:     "no variables",
			template: "Hello, world!",
			expected: []string{},
		},
		{
			name:     "struct field access",
			template: "{{.UserName}} has {{.MessageCount}} messages",
			expected: []string{"UserName", "MessageCount"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := extractVariables(tt.template)
			assert.ElementsMatch(t, tt.expected, result)
		})
	}
}

// Fuzzing test for template parsing
func FuzzTemplateFormat(f *testing.F) {
	// Add seed corpus
	f.Add("Hello {{.UserName}}", "Alice")
	f.Add("{{.Query}} {{.Content}}!", "Hi")
	f.Add("No variables here", "")
	f.Add("{{if .Query}}yes{{else}}no{{end}}", "test")
	f.Add(strings.Repeat("{{.Content}} ", 100), "test")

	f.Fuzz(func(t *testing.T, templateStr string, value string) {
		// Try to create template - might fail with invalid syntax
		tmpl, err := NewSimpleTemplate("fuzz", templateStr)
		if err != nil {
			// Invalid template syntax is ok
			return
		}

		// Create variables with common fields populated
		vars := &TemplateVariables{
			UserName: value,
			Query:    value,
			Content:  value,
		}

		// Format should never panic
		result, err := tmpl.Format(vars)
		if err == nil {
			assert.NotNil(t, result) // Result exists even if empty
		}
	})
}

// Test ChatTemplate
func TestChatTemplate(t *testing.T) {
	t.Run("NewChatTemplate", func(t *testing.T) {
		system := "You are a helpful assistant."
		user := "User: {{.Query}}"

		tmpl, err := NewChatTemplate("test", system, user)
		require.NoError(t, err)
		assert.NotNil(t, tmpl)
		assert.Equal(t, "test", tmpl.name)
	})

	t.Run("FormatSystem", func(t *testing.T) {
		tmpl, err := NewChatTemplate("test",
			"You are a helpful assistant for {{.UserName}}.",
			"User query")
		require.NoError(t, err)

		vars := &TemplateVariables{
			UserName: "Alice",
		}

		result, err := tmpl.FormatSystem(vars)
		require.NoError(t, err)
		assert.Equal(t, "You are a helpful assistant for Alice.", result)
	})

	t.Run("FormatUser", func(t *testing.T) {
		tmpl, err := NewChatTemplate("test",
			"System prompt",
			"Question: {{.Query}}")
		require.NoError(t, err)

		vars := &TemplateVariables{
			Query: "What is Go?",
		}

		result, err := tmpl.FormatUser(vars)
		require.NoError(t, err)
		assert.Equal(t, "Question: What is Go?", result)
	})
}

// Fuzzing test for variable extraction
func FuzzExtractVariables(f *testing.F) {
	// Add seed corpus
	f.Add("{{.Simple}}")
	f.Add("{{.CamelCase}} {{.snake_case}} {{.number123}}")
	f.Add("No variables")
	f.Add("{{.}}")
	f.Add("{{.")
	f.Add(strings.Repeat("{{.Var}}", 100))

	f.Fuzz(func(t *testing.T, input string) {
		// Should never panic
		vars := extractVariables(input)

		// Validate extracted variables
		for _, v := range vars {
			// Skip empty variables (edge case in parser)
			if v == "" {
				continue
			}
			// Variables should not contain dots or braces
			assert.NotContains(t, v, ".")
			assert.NotContains(t, v, "{")
			assert.NotContains(t, v, "}")
		}

		// No duplicates
		seen := make(map[string]bool)
		for _, v := range vars {
			assert.False(t, seen[v], "Duplicate variable: %s", v)
			seen[v] = true
		}
	})
}
