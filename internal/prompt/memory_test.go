package prompt

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// TestEnhanceSystemPromptWithMemoryInstructions tests memory enhancement
func TestEnhanceSystemPromptWithMemoryInstructions(t *testing.T) {
	basePrompt := "You are a helpful assistant."

	enhanced := EnhanceSystemPromptWithMemoryInstructions(basePrompt)

	// Should contain original prompt
	assert.Contains(t, enhanced, basePrompt)

	// Should contain memory instructions
	assert.Contains(t, enhanced, "Memory and Schedule Understanding Instructions")
	assert.Contains(t, enhanced, "Temporal Awareness")
	assert.Contains(t, enhanced, "Schedule Recognition")
}

// TestGenerateTemporalContext tests temporal context generation
func TestGenerateTemporalContext(t *testing.T) {
	tests := []struct {
		name        string
		currentTime time.Time
		wantPeriod  string
		wantDay     string
	}{
		{
			name:        "morning time",
			currentTime: time.Date(2024, 1, 15, 8, 30, 0, 0, time.UTC),
			wantPeriod:  "morning",
			wantDay:     "Monday",
		},
		{
			name:        "afternoon time",
			currentTime: time.Date(2024, 1, 15, 14, 0, 0, 0, time.UTC),
			wantPeriod:  "afternoon",
			wantDay:     "Monday",
		},
		{
			name:        "evening time",
			currentTime: time.Date(2024, 1, 15, 19, 0, 0, 0, time.UTC),
			wantPeriod:  "evening",
			wantDay:     "Monday",
		},
		{
			name:        "night time",
			currentTime: time.Date(2024, 1, 15, 23, 0, 0, 0, time.UTC),
			wantPeriod:  "night",
			wantDay:     "Monday",
		},
		{
			name:        "late night",
			currentTime: time.Date(2024, 1, 15, 2, 0, 0, 0, time.UTC),
			wantPeriod:  "night",
			wantDay:     "Monday",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := GenerateTemporalContext(tt.currentTime, "UTC")

			// Should contain temporal_context tag
			assert.Contains(t, ctx, "<temporal_context>")

			// Should contain time period
			assert.Contains(t, ctx, tt.wantPeriod)

			// Should contain day of week
			assert.Contains(t, ctx, tt.wantDay)

			// Should contain date in ISO format
			assert.Contains(t, ctx, "2024-01-15")

			// Should contain relative dates
			assert.Contains(t, ctx, "<tomorrow")
			assert.Contains(t, ctx, "<day_after_tomorrow")
		})
	}
}

// TestGetTimePeriod tests time period determination
func TestGetTimePeriod(t *testing.T) {
	tests := []struct {
		hour     int
		expected string
	}{
		{0, "night"},
		{3, "night"},
		{5, "early_morning"},
		{6, "early_morning"},
		{8, "early_morning"},
		{9, "morning"},
		{11, "morning"},
		{12, "noon"},
		{14, "afternoon"},
		{16, "afternoon"},
		{18, "evening"},
		{20, "evening"},
		{22, "night"},
		{23, "night"},
	}

	for _, tt := range tests {
		t.Run(tt.expected, func(t *testing.T) {
			result := getTimePeriod(tt.hour)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestQueryAnalysisPrompt tests query analysis prompt generation
func TestQueryAnalysisPrompt(t *testing.T) {
	prompt := QueryAnalysisPrompt("What am I doing tomorrow?")

	// Should contain query_analysis tag
	assert.Contains(t, prompt, "<query_analysis>")

	// Should contain the query
	assert.Contains(t, prompt, "What am I doing tomorrow?")

	// Should contain analysis instructions
	assert.Contains(t, prompt, "Analyze this query")
	assert.Contains(t, prompt, "Consider:")
	assert.Contains(t, prompt, "temporal context")
}

// TestScheduleInterpretationPrompt tests schedule interpretation prompt
func TestScheduleInterpretationPrompt(t *testing.T) {
	tests := []struct {
		name            string
		includeExamples bool
		expectExamples  bool
	}{
		{
			name:            "with examples",
			includeExamples: true,
			expectExamples:  true,
		},
		{
			name:            "without examples",
			includeExamples: false,
			expectExamples:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var schedules []string
			if tt.includeExamples {
				schedules = []string{
					"Every Monday at 9am: Team meeting",
					"Tuesday and Thursday: Thai boxing at 7 PM",
				}
			}
			prompt := ScheduleInterpretationPrompt(schedules)

			if tt.expectExamples {
				// Should contain schedule_interpretation tag
				assert.Contains(t, prompt, "<schedule_interpretation>")
				assert.Contains(t, prompt, "Based on the user's schedules:")
				assert.Contains(t, prompt, "Every Monday at 9am")
				assert.Contains(t, prompt, "Thai boxing")
			} else {
				// Should be empty for no schedules
				assert.Empty(t, prompt)
			}
		})
	}
}

// TestMemoryConflictResolutionPrompt tests conflict resolution prompt
func TestMemoryConflictResolutionPrompt(t *testing.T) {
	prompt := MemoryConflictResolutionPrompt()

	// Should contain conflict resolution tag
	assert.Contains(t, prompt, "<conflict_resolution>")

	// Should contain key resolution strategies
	assert.Contains(t, prompt, "conflicting information")
	assert.Contains(t, prompt, "Prioritize more recent")
	assert.Contains(t, prompt, "Consider context")
	assert.Contains(t, prompt, "Express uncertainty")
}

// TestMemoryPromptStructure tests that memory prompts are well-structured
func TestMemoryPromptStructure(t *testing.T) {
	// Test each memory-related prompt for basic structure
	prompts := []struct {
		name   string
		prompt string
	}{
		{"QueryAnalysis", QueryAnalysisPrompt("test query")},
		{"ScheduleInterpretation", ScheduleInterpretationPrompt([]string{"test schedule"})},
		{"ConflictResolution", MemoryConflictResolutionPrompt()},
	}

	for _, p := range prompts {
		t.Run(p.name, func(t *testing.T) {
			// Should not be empty
			assert.NotEmpty(t, p.prompt)

			// Should be reasonably sized (not too short, not too long)
			assert.Greater(t, len(p.prompt), 50)
			assert.Less(t, len(p.prompt), 5000)

			// Should not have template variables (unless intentional)
			assert.NotContains(t, p.prompt, "{{")
			assert.NotContains(t, p.prompt, "}}")
		})
	}
}

// TestMemoryEnhancementIdempotent tests that enhancement is idempotent
func TestMemoryEnhancementIdempotent(t *testing.T) {
	basePrompt := "You are a helpful assistant."

	// Enhance once
	enhanced1 := EnhanceSystemPromptWithMemoryInstructions(basePrompt)

	// Enhance the already enhanced prompt
	enhanced2 := EnhanceSystemPromptWithMemoryInstructions(enhanced1)

	// Should not double-add instructions
	// (This assumes the function checks for existing instructions)
	// For now, just verify they're different sizes
	assert.Greater(t, len(enhanced1), len(basePrompt))
	assert.Equal(t, len(enhanced2), len(enhanced1)+len("\n\n")+len(MemoryInstructions))
}
