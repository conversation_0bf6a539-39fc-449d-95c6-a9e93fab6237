package prompt

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestNewLibrary tests Library creation
func TestNewLibrary(t *testing.T) {
	lib := NewLibrary()

	assert.NotNil(t, lib)
	assert.NotNil(t, lib.templates)

	// Should have default templates loaded
	assert.NotEmpty(t, lib.templates)
}

// TestLibrary_Register tests template registration
func TestLibrary_Register(t *testing.T) {
	lib := NewLibrary()

	// Register a new template
	tmpl, err := NewSimpleTemplate("test-template", "Test content: {{.Variable}}")
	require.NoError(t, err)

	lib.Register("test-template", tmpl)

	// Verify it was registered
	retrieved, exists := lib.Get("test-template")
	assert.True(t, exists)
	assert.Equal(t, tmpl, retrieved)
}

// TestLibrary_Get tests template retrieval
func TestLibrary_Get(t *testing.T) {
	lib := NewLibrary()

	// Test getting existing template (should have defaults)
	tmpl, exists := lib.Get("summarize")
	assert.True(t, exists)
	assert.NotNil(t, tmpl)

	// Test getting non-existent template
	tmpl, exists = lib.Get("non-existent")
	assert.False(t, exists)
	assert.Nil(t, tmpl)
}

// TestLibrary_List tests template listing
func TestLibrary_List(t *testing.T) {
	lib := NewLibrary()

	templates := lib.List()
	assert.NotEmpty(t, templates)

	// Should contain default templates
	assert.Contains(t, templates, "summarize")
	assert.Contains(t, templates, "qa")
	assert.Contains(t, templates, "translate")
	assert.Contains(t, templates, "code_explain")
	assert.Contains(t, templates, "extract_entities")
}

// TestLibrary_DefaultTemplates tests that default templates are loaded correctly
func TestLibrary_DefaultTemplates(t *testing.T) {
	lib := NewLibrary()

	// Test each default template
	defaultTemplates := []struct {
		name     string
		hasVars  bool
		varNames []string
	}{
		{
			name:     "summarize",
			hasVars:  true,
			varNames: []string{"MaxSentences", "Text"},
		},
		{
			name:     "qa",
			hasVars:  true,
			varNames: []string{"Context", "Question"},
		},
		{
			name:     "translate",
			hasVars:  true,
			varNames: []string{"SourceLang", "TargetLang", "Text"},
		},
		{
			name:     "code_explain",
			hasVars:  true,
			varNames: []string{"Language", "Code"},
		},
		{
			name:     "extract_entities",
			hasVars:  true,
			varNames: []string{"EntityType", "Text"},
		},
	}

	for _, tt := range defaultTemplates {
		t.Run(tt.name, func(t *testing.T) {
			tmpl, exists := lib.Get(tt.name)
			require.True(t, exists, "Template %s should exist", tt.name)
			require.NotNil(t, tmpl)

			vars := tmpl.Variables()
			if tt.hasVars {
				assert.NotEmpty(t, vars)
				for _, varName := range tt.varNames {
					assert.Contains(t, vars, varName)
				}
			} else {
				assert.Empty(t, vars)
			}
		})
	}
}

// TestNewFewShotTemplate tests FewShotTemplate creation
func TestNewFewShotTemplate(t *testing.T) {
	examples := []Example{
		{Input: "What's 2+2?", Output: "4"},
		{Input: "What's 3+3?", Output: "6"},
	}

	tmpl := NewFewShotTemplate("Calculate simple math", examples)

	assert.NotNil(t, tmpl)
	// Check that template was created (fields are private)
}

// TestFewShotTemplate_Format tests FewShotTemplate formatting
func TestFewShotTemplate_Format(t *testing.T) {
	examples := []Example{
		{Input: "Hello", Output: "Hi there!"},
		{Input: "Goodbye", Output: "See you later!"},
	}

	tmpl := NewFewShotTemplate("Greeting responses", examples)

	// Format with input
	result := tmpl.Format("How are you?")

	// Should contain instruction
	assert.Contains(t, result, "Greeting responses")

	// Should contain examples
	assert.Contains(t, result, "Input: Hello")
	assert.Contains(t, result, "Output: Hi there!")
	assert.Contains(t, result, "Input: Goodbye")
	assert.Contains(t, result, "Output: See you later!")

	// Should contain the actual input
	assert.Contains(t, result, "Input: How are you?")

	// Should have proper formatting
	assert.Contains(t, result, "Example 1:")
	assert.Contains(t, result, "Example 2:")
}

// TestFewShotTemplate_EmptyExamples tests FewShotTemplate with no examples
func TestFewShotTemplate_EmptyExamples(t *testing.T) {
	tmpl := NewFewShotTemplate("Task with no examples", nil)

	result := tmpl.Format("Test input")

	// Should still contain instruction
	assert.Contains(t, result, "Task with no examples")

	// Should contain the input
	assert.Contains(t, result, "Input: Test input")

	// Should not contain examples
	assert.NotContains(t, result, "Example")
}

// TestTemplateTypes tests that different template types implement the Template interface
func TestTemplateTypes(t *testing.T) {
	// Test that different types implement Template interface
	var tmpl Template

	// SimpleTemplate
	simple, err := NewSimpleTemplate("test", "test content")
	require.NoError(t, err)
	tmpl = simple
	assert.NotNil(t, tmpl)

	// ChatTemplate
	chat, err := NewChatTemplate("test-chat", "system prompt", "user prompt")
	require.NoError(t, err)
	// Note: ChatTemplate doesn't implement Template interface directly
	// It has FormatSystem and FormatUser methods instead
	assert.NotNil(t, chat)

	// Note: FewShotTemplate doesn't implement Template interface, it has its own Format method
}
