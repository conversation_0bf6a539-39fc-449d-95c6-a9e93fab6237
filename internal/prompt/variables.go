// Package prompt provides type-safe variable handling for templates.
package prompt

import (
	"strconv"
	"time"
)

// TemplateVariables represents type-safe variables for prompt templates.
// This replaces the generic map[string]any with concrete types.
type TemplateVariables struct {
	// User context
	UserID      string `json:"user_id,omitempty"`
	UserName    string `json:"user_name,omitempty"`
	UserRole    string `json:"user_role,omitempty"`
	UserContext string `json:"user_context,omitempty"`

	// Conversation context
	ConversationID    string    `json:"conversation_id,omitempty"`
	MessageCount      int       `json:"message_count,omitempty"`
	LastMessageTime   time.Time `json:"last_message_time,omitempty"`
	ConversationTopic string    `json:"conversation_topic,omitempty"`

	// Content variables
	Query           string   `json:"query,omitempty"`
	Content         string   `json:"content,omitempty"`
	Context         string   `json:"context,omitempty"`
	Instructions    string   `json:"instructions,omitempty"`
	Examples        []string `json:"examples,omitempty"`
	AdditionalNotes string   `json:"additional_notes,omitempty"`

	// Task-specific variables
	TaskType        string   `json:"task_type,omitempty"`
	TaskDescription string   `json:"task_description,omitempty"`
	TaskParameters  []string `json:"task_parameters,omitempty"`
	ExpectedOutput  string   `json:"expected_output,omitempty"`

	// Configuration
	MaxTokens      int     `json:"max_tokens,omitempty"`
	Temperature    float64 `json:"temperature,omitempty"`
	ResponseFormat string  `json:"response_format,omitempty"`
	Language       string  `json:"language,omitempty"`

	// Time context
	CurrentTime time.Time `json:"current_time,omitempty"`
	Timezone    string    `json:"timezone,omitempty"`
	DateFormat  string    `json:"date_format,omitempty"`

	// Memory/knowledge context
	RelevantMemories []string          `json:"relevant_memories,omitempty"`
	UserPreferences  map[string]string `json:"user_preferences,omitempty"`
	FactualContext   []string          `json:"factual_context,omitempty"`

	// Tool context
	AvailableTools []string `json:"available_tools,omitempty"`
	ToolResults    []string `json:"tool_results,omitempty"`

	// Custom fields for extensibility (limited set)
	CustomStrings  map[string]string  `json:"custom_strings,omitempty"`
	CustomNumbers  map[string]float64 `json:"custom_numbers,omitempty"`
	CustomBooleans map[string]bool    `json:"custom_booleans,omitempty"`
}

// NewTemplateVariables creates a new template variables instance.
func NewTemplateVariables() *TemplateVariables {
	return &TemplateVariables{
		UserPreferences: make(map[string]string),
		CustomStrings:   make(map[string]string),
		CustomNumbers:   make(map[string]float64),
		CustomBooleans:  make(map[string]bool),
		CurrentTime:     time.Now(),
		Language:        "en",
		DateFormat:      "2006-01-02 15:04:05",
	}
}

// ToMap converts TemplateVariables to a map for template execution.
// This is needed for compatibility with text/template.
func (v *TemplateVariables) ToMap() map[string]any {
	m := make(map[string]any)

	// Process all fields through helper functions
	v.addStringFields(m)
	v.addNumericFields(m)
	v.addTimeFields(m)
	v.addArrayFields(m)
	v.addMapFields(m)
	v.addCustomFields(m)

	return m
}

// addStringFields adds non-empty string fields to the map
func (v *TemplateVariables) addStringFields(m map[string]any) {
	stringFields := map[string]string{
		"UserID":            v.UserID,
		"UserName":          v.UserName,
		"UserRole":          v.UserRole,
		"UserContext":       v.UserContext,
		"ConversationID":    v.ConversationID,
		"ConversationTopic": v.ConversationTopic,
		"Query":             v.Query,
		"Content":           v.Content,
		"Context":           v.Context,
		"Instructions":      v.Instructions,
		"TaskType":          v.TaskType,
		"TaskDescription":   v.TaskDescription,
		"ExpectedOutput":    v.ExpectedOutput,
		"ResponseFormat":    v.ResponseFormat,
		"Language":          v.Language,
		"Timezone":          v.Timezone,
		"DateFormat":        v.DateFormat,
		"AdditionalNotes":   v.AdditionalNotes,
	}

	for key, value := range stringFields {
		if value != "" {
			m[key] = value
		}
	}
}

// addNumericFields adds non-zero numeric fields to the map
func (v *TemplateVariables) addNumericFields(m map[string]any) {
	if v.MessageCount > 0 {
		m["MessageCount"] = v.MessageCount
	}
	if v.MaxTokens > 0 {
		m["MaxTokens"] = v.MaxTokens
	}
	if v.Temperature > 0 {
		m["Temperature"] = v.Temperature
	}
}

// addTimeFields adds non-zero time fields to the map
func (v *TemplateVariables) addTimeFields(m map[string]any) {
	if !v.LastMessageTime.IsZero() {
		m["LastMessageTime"] = v.LastMessageTime.Format(v.DateFormat)
	}
	if !v.CurrentTime.IsZero() {
		m["CurrentTime"] = v.CurrentTime.Format(v.DateFormat)
		m["CurrentTimeRaw"] = v.CurrentTime
	}
}

// addArrayFields adds non-empty array fields to the map
func (v *TemplateVariables) addArrayFields(m map[string]any) {
	arrayFields := map[string][]string{
		"Examples":         v.Examples,
		"TaskParameters":   v.TaskParameters,
		"RelevantMemories": v.RelevantMemories,
		"FactualContext":   v.FactualContext,
		"AvailableTools":   v.AvailableTools,
		"ToolResults":      v.ToolResults,
	}

	for key, value := range arrayFields {
		if len(value) > 0 {
			m[key] = value
		}
	}
}

// addMapFields adds non-empty map fields to the map
func (v *TemplateVariables) addMapFields(m map[string]any) {
	if len(v.UserPreferences) > 0 {
		m["UserPreferences"] = v.UserPreferences
	}
}

// addCustomFields adds custom fields to the map
func (v *TemplateVariables) addCustomFields(m map[string]any) {
	for k, v := range v.CustomStrings {
		m[k] = v
	}
	for k, v := range v.CustomNumbers {
		m[k] = v
	}
	for k, v := range v.CustomBooleans {
		m[k] = v
	}
}

// FromMap creates TemplateVariables from a map (for migration).
func FromMap(data map[string]any) *TemplateVariables {
	v := NewTemplateVariables()

	// Process all fields through helper functions
	extractStringFields(data, v)
	extractNumericFields(data, v)
	extractTimeFields(data, v)
	extractArrayFields(data, v)
	extractMapFields(data, v)
	extractCustomFields(data, v)

	return v
}

// extractStringFields extracts string fields from the map
func extractStringFields(data map[string]any, v *TemplateVariables) {
	// Map of field names to their corresponding pointers in the struct
	stringFieldMap := map[string]*string{
		"UserID":            &v.UserID,
		"UserName":          &v.UserName,
		"UserRole":          &v.UserRole,
		"UserContext":       &v.UserContext,
		"ConversationID":    &v.ConversationID,
		"ConversationTopic": &v.ConversationTopic,
		"Query":             &v.Query,
		"Content":           &v.Content,
		"Context":           &v.Context,
		"Instructions":      &v.Instructions,
		"TaskType":          &v.TaskType,
		"TaskDescription":   &v.TaskDescription,
		"ExpectedOutput":    &v.ExpectedOutput,
		"ResponseFormat":    &v.ResponseFormat,
		"Language":          &v.Language,
		"Timezone":          &v.Timezone,
		"DateFormat":        &v.DateFormat,
		"AdditionalNotes":   &v.AdditionalNotes,
	}

	for fieldName, fieldPtr := range stringFieldMap {
		if s, ok := getString(data, fieldName); ok {
			*fieldPtr = s
		}
	}
}

// extractNumericFields extracts numeric fields from the map
func extractNumericFields(data map[string]any, v *TemplateVariables) {
	if n, ok := getInt(data, "MessageCount"); ok {
		v.MessageCount = n
	}
	if n, ok := getInt(data, "MaxTokens"); ok {
		v.MaxTokens = n
	}
	if f, ok := getFloat(data, "Temperature"); ok {
		v.Temperature = f
	}
}

// extractTimeFields extracts time fields from the map
func extractTimeFields(data map[string]any, v *TemplateVariables) {
	if t, ok := getTime(data, "LastMessageTime"); ok {
		v.LastMessageTime = t
	}
	if t, ok := getTime(data, "CurrentTime"); ok {
		v.CurrentTime = t
	}
}

// extractArrayFields extracts array fields from the map
func extractArrayFields(data map[string]any, v *TemplateVariables) {
	// Map of field names to their corresponding slice pointers
	arrayFieldMap := map[string]*[]string{
		"Examples":         &v.Examples,
		"TaskParameters":   &v.TaskParameters,
		"RelevantMemories": &v.RelevantMemories,
		"FactualContext":   &v.FactualContext,
		"AvailableTools":   &v.AvailableTools,
		"ToolResults":      &v.ToolResults,
	}

	for fieldName, fieldPtr := range arrayFieldMap {
		if arr, ok := getStringArray(data, fieldName); ok {
			*fieldPtr = arr
		}
	}
}

// extractMapFields extracts map fields from the map
func extractMapFields(data map[string]any, v *TemplateVariables) {
	if m, ok := getStringMap(data, "UserPreferences"); ok {
		v.UserPreferences = m
	}
}

// extractCustomFields extracts remaining fields as custom fields
func extractCustomFields(data map[string]any, v *TemplateVariables) {
	for k, val := range data {
		// Skip known fields
		if isKnownField(k) {
			continue
		}

		// Add to appropriate custom map based on type
		switch vt := val.(type) {
		case string:
			v.CustomStrings[k] = vt
		case float64:
			v.CustomNumbers[k] = vt
		case int:
			v.CustomNumbers[k] = float64(vt)
		case bool:
			v.CustomBooleans[k] = vt
		}
	}
}

// Helper functions for type conversion

func getString(m map[string]any, key string) (string, bool) {
	if v, ok := m[key]; ok {
		if s, ok := v.(string); ok {
			return s, true
		}
	}
	return "", false
}

func getInt(m map[string]any, key string) (int, bool) {
	if v, ok := m[key]; ok {
		switch n := v.(type) {
		case int:
			return n, true
		case float64:
			return int(n), true
		case string:
			if i, err := strconv.Atoi(n); err == nil {
				return i, true
			}
		}
	}
	return 0, false
}

func getFloat(m map[string]any, key string) (float64, bool) {
	if v, ok := m[key]; ok {
		switch n := v.(type) {
		case float64:
			return n, true
		case int:
			return float64(n), true
		case string:
			if f, err := strconv.ParseFloat(n, 64); err == nil {
				return f, true
			}
		}
	}
	return 0, false
}

func getTime(m map[string]any, key string) (time.Time, bool) {
	if v, ok := m[key]; ok {
		switch t := v.(type) {
		case time.Time:
			return t, true
		case string:
			// Try common time formats
			formats := []string{
				time.RFC3339,
				"2006-01-02 15:04:05",
				"2006-01-02",
			}
			for _, format := range formats {
				if parsed, err := time.Parse(format, t); err == nil {
					return parsed, true
				}
			}
		}
	}
	return time.Time{}, false
}

func getStringArray(m map[string]any, key string) ([]string, bool) {
	if v, ok := m[key]; ok {
		switch arr := v.(type) {
		case []string:
			return arr, true
		case []any:
			result := make([]string, 0, len(arr))
			for _, item := range arr {
				if s, ok := item.(string); ok {
					result = append(result, s)
				}
			}
			return result, true
		}
	}
	return nil, false
}

func getStringMap(m map[string]any, key string) (map[string]string, bool) {
	if v, ok := m[key]; ok {
		switch m := v.(type) {
		case map[string]string:
			return m, true
		case map[string]any:
			result := make(map[string]string)
			for k, v := range m {
				if s, ok := v.(string); ok {
					result[k] = s
				}
			}
			return result, true
		}
	}
	return nil, false
}

func isKnownField(field string) bool {
	knownFields := map[string]bool{
		"UserID": true, "UserName": true, "UserRole": true, "UserContext": true,
		"ConversationID": true, "ConversationTopic": true, "MessageCount": true,
		"LastMessageTime": true, "Query": true, "Content": true, "Context": true,
		"Instructions": true, "Examples": true, "TaskType": true,
		"TaskDescription": true, "TaskParameters": true, "ExpectedOutput": true,
		"MaxTokens": true, "Temperature": true, "ResponseFormat": true,
		"Language": true, "CurrentTime": true, "Timezone": true, "DateFormat": true,
		"RelevantMemories": true, "UserPreferences": true, "FactualContext": true,
		"AvailableTools": true, "ToolResults": true, "AdditionalNotes": true,
	}
	return knownFields[field]
}
