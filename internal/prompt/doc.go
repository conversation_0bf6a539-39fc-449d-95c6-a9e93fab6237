// Package prompt manages system prompts and instructions for the AI assistant.
//
// This package centralizes all system prompts, making them easy to maintain,
// version, and customize. It provides a clean separation between the AI's
// instructions and the application logic.
//
// # Design Principles
//
//   - Single source of truth for all prompts
//   - Clear, maintainable prompt templates
//   - Support for dynamic prompt generation
//   - Separation of concerns between prompts and code
//
// # Prompt Categories
//
// System prompts are organized by purpose:
//
//   - Core system prompt: Base personality and capabilities
//   - Memory extraction: Instructions for identifying memorable content
//   - Tool usage: Guidelines for tool selection and execution
//   - Conversation management: Multi-turn interaction handling
//   - Error handling: Graceful failure responses
//
// # Usage
//
//	// Get the base system prompt
//	systemPrompt := prompt.GetSystemPrompt(config.OwnerConfig{
//	    Name:     "Alice",
//	    Location: "San Francisco",
//	})
//
//	// Get memory extraction prompt
//	extractPrompt := prompt.GetMemoryExtractionPrompt()
//
//	// Get tool-specific instructions
//	toolPrompt := prompt.GetToolInstructions(enabledTools)
//
// # Customization
//
// Prompts can be customized through:
//
//   - Template variables for personalization
//   - Conditional sections based on features
//   - External prompt files for easy editing
//   - Version control for prompt evolution
//
// # Best Practices
//
// When modifying prompts:
//
//  1. Keep instructions clear and specific
//  2. Use consistent terminology
//  3. Test changes thoroughly
//  4. Document the reasoning for changes
//  5. Version significant modifications
package prompt
