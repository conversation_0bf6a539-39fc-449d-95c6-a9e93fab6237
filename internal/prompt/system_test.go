package prompt

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestGetSystemPrompt tests system prompt generation
func TestGetSystemPrompt(t *testing.T) {
	tests := []struct {
		name              string
		user              UserInfo
		additionalContext string
		expectUserInfo    bool
		expectAddContext  bool
	}{
		{
			name: "basic user info",
			user: UserInfo{
				Name:     "Alice",
				Email:    "<EMAIL>",
				Language: "English",
				Timezone: "UTC",
				Theme:    "dark",
			},
			additionalContext: "",
			expectUserInfo:    true,
			expectAddContext:  false,
		},
		{
			name: "with additional context",
			user: UserInfo{
				Name:     "<PERSON>",
				Email:    "<EMAIL>",
				Language: "Japanese",
				Timezone: "Asia/Tokyo",
				Theme:    "light",
			},
			additionalContext: "Additional context for testing",
			expectUserInfo:    true,
			expectAddContext:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			prompt := GetSystemPrompt(tt.user, tt.additionalContext)

			// Should not be empty
			assert.NotEmpty(t, prompt)

			// Check for user info
			if tt.expectUserInfo {
				assert.Contains(t, prompt, tt.user.Name)
				assert.Contains(t, prompt, tt.user.Email)
				assert.Contains(t, prompt, tt.user.Language)
				assert.Contains(t, prompt, tt.user.Timezone)
				assert.Contains(t, prompt, tt.user.Theme)
			}

			// Check for additional context
			if tt.expectAddContext {
				assert.Contains(t, prompt, tt.additionalContext)
			} else {
				assert.NotContains(t, prompt, "Additional context for testing")
			}
		})
	}
}

// TestSystemPromptFormatting tests proper formatting of system prompt
func TestSystemPromptFormatting(t *testing.T) {
	user := UserInfo{
		Name:     "TestUser",
		Email:    "<EMAIL>",
		Language: "English",
		Timezone: "UTC",
		Theme:    "auto",
	}

	prompt := GetSystemPrompt(user, "")

	// Should have reasonable length
	assert.Greater(t, len(prompt), 100)
	assert.Less(t, len(prompt), 10000)

	// Should contain system tags
	assert.Contains(t, prompt, "<system>")
	assert.Contains(t, prompt, "</system>")

	// Should contain identity section
	assert.Contains(t, prompt, "<identity>")
	assert.Contains(t, prompt, "</identity>")

	// Should contain user context
	assert.Contains(t, prompt, "<user_context>")
	assert.Contains(t, prompt, "</user_context>")

	// Should contain capabilities
	assert.Contains(t, prompt, "<capabilities>")
	assert.Contains(t, prompt, "</capabilities>")

	// Should contain tools
	assert.Contains(t, prompt, "<tools>")
	assert.Contains(t, prompt, "</tools>")
}

// TestSystemPromptConsistency tests that system prompts are consistent
func TestSystemPromptConsistency(t *testing.T) {
	user := UserInfo{
		Name:     "ConsistentUser",
		Email:    "<EMAIL>",
		Language: "English",
		Timezone: "UTC",
		Theme:    "dark",
	}

	// Same inputs should produce same outputs
	prompt1 := GetSystemPrompt(user, "test context")
	prompt2 := GetSystemPrompt(user, "test context")

	assert.Equal(t, prompt1, prompt2)

	// Different additional context should produce different outputs
	promptWithContext := GetSystemPrompt(user, "some context")
	promptWithoutContext := GetSystemPrompt(user, "")

	assert.NotEqual(t, promptWithContext, promptWithoutContext)
}

// TestSystemPromptWithMemoryInstructions tests memory enhancement integration
func TestSystemPromptWithMemoryInstructions(t *testing.T) {
	user := UserInfo{
		Name:     "MemoryUser",
		Email:    "<EMAIL>",
		Language: "English",
		Timezone: "UTC",
		Theme:    "dark",
	}

	// Get base prompt
	basePrompt := GetSystemPrompt(user, "")

	// Enhance with memory instructions
	enhancedPrompt := EnhanceSystemPromptWithMemoryInstructions(basePrompt)

	// Enhanced should be longer
	assert.Greater(t, len(enhancedPrompt), len(basePrompt))

	// Enhanced should contain memory instructions
	assert.Contains(t, enhancedPrompt, "Memory and Schedule Understanding Instructions")
	assert.Contains(t, enhancedPrompt, "Temporal Awareness")
	assert.Contains(t, enhancedPrompt, "Schedule Recognition")
}

// TestSystemPromptStructure tests the structure of system prompts
func TestSystemPromptStructure(t *testing.T) {
	user := UserInfo{
		Name:     "StructureTest",
		Email:    "<EMAIL>",
		Language: "English",
		Timezone: "UTC",
		Theme:    "light",
	}

	prompt := GetSystemPrompt(user, "")

	// Should contain XML-like structure
	assert.Contains(t, prompt, "<")
	assert.Contains(t, prompt, ">")

	// Should contain the formatted user name multiple times
	// Count occurrences of user name
	count := 0
	for i := 0; i < len(prompt)-len(user.Name); i++ {
		if prompt[i:i+len(user.Name)] == user.Name {
			count++
		}
	}
	assert.GreaterOrEqual(t, count, 5, "User name should appear multiple times in the prompt")
}
