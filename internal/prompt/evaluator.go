// Package prompt provides prompt evaluation capabilities.
package prompt

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/koopa0/assistant-go/internal/platform/logger"
)

// TestCase represents a single test case for prompt evaluation.
type TestCase struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Input       string                 `json:"input"`
	Expected    ExpectedBehavior       `json:"expected"`
	Tags        []string               `json:"tags"`
	Context     map[string]interface{} `json:"context,omitempty"`
}

// ExpectedBehavior defines what we expect from the AI for a test case.
type ExpectedBehavior struct {
	// ToolCalls expected to be made
	ToolCalls []ExpectedToolCall `json:"tool_calls,omitempty"`

	// Response patterns to match
	ResponsePatterns []string `json:"response_patterns,omitempty"`

	// Response patterns that should NOT appear
	ForbiddenPatterns []string `json:"forbidden_patterns,omitempty"`

	// Whether the response should include search results
	ShouldSearch bool `json:"should_search"`

	// Whether current_time should be called first
	ShouldCheckTime bool `json:"should_check_time"`
}

// ExpectedToolCall represents an expected tool invocation.
type ExpectedToolCall struct {
	Name       string                 `json:"name"`
	Parameters map[string]interface{} `json:"parameters,omitempty"`
	Order      int                    `json:"order,omitempty"` // 0 means any order
}

// TestResult captures the result of running a test case.
type TestResult struct {
	TestCase    TestCase      `json:"test_case"`
	Passed      bool          `json:"passed"`
	Errors      []string      `json:"errors"`
	ActualCalls []ai.ToolCall `json:"actual_calls"`
	Response    string        `json:"response"`
	Duration    time.Duration `json:"duration"`
	Timestamp   time.Time     `json:"timestamp"`
}

// Evaluator runs prompt evaluation test suites.
type Evaluator struct {
	aiClient ai.Client
	logger   logger.Logger
	testDir  string
}

// NewEvaluator creates a new prompt evaluator.
func NewEvaluator(aiClient ai.Client, logger logger.Logger, testDir string) *Evaluator {
	return &Evaluator{
		aiClient: aiClient,
		logger:   logger,
		testDir:  testDir,
	}
}

// LoadTestSuite loads test cases from a JSON file.
func (e *Evaluator) LoadTestSuite(filename string) ([]TestCase, error) {
	// Validate filename to prevent directory traversal
	if strings.Contains(filename, "..") || strings.ContainsAny(filename, `/\`) {
		return nil, fmt.Errorf("invalid filename: %s", filename)
	}
	path := filepath.Join(e.testDir, filename)
	data, err := os.ReadFile(path) // #nosec G304 - Path is sanitized above
	if err != nil {
		return nil, fmt.Errorf("read test file: %w", err)
	}

	var suite struct {
		Version   string     `json:"version"`
		TestCases []TestCase `json:"test_cases"`
	}

	if err := json.Unmarshal(data, &suite); err != nil {
		return nil, fmt.Errorf("parse test file: %w", err)
	}

	return suite.TestCases, nil
}

// RunTestCase executes a single test case with a given system prompt.
func (e *Evaluator) RunTestCase(ctx context.Context, testCase TestCase, systemPrompt string) TestResult {
	start := time.Now()
	result := TestResult{
		TestCase:  testCase,
		Timestamp: start,
		Errors:    []string{},
	}

	// Create the AI request
	req := &ai.Request{
		Messages: []ai.Message{
			{Role: ai.User, Content: testCase.Input},
		},
		System:      systemPrompt,
		Temperature: 0.0, // Deterministic for testing
		// Enable tools based on test expectations
		Tools: e.getToolsForTest(testCase),
	}

	// Execute the request
	resp, err := e.aiClient.Chat(ctx, req)
	if err != nil {
		result.Errors = append(result.Errors, fmt.Sprintf("AI request failed: %v", err))
		result.Duration = time.Since(start)
		return result
	}

	result.Response = resp.Content
	result.ActualCalls = resp.ToolCalls
	result.Duration = time.Since(start)

	// Evaluate the result
	e.evaluateResult(&result)

	return result
}

// RunTestSuite runs all test cases in a suite.
func (e *Evaluator) RunTestSuite(ctx context.Context, testCases []TestCase, systemPrompt string) []TestResult {
	results := make([]TestResult, 0, len(testCases))

	for i, tc := range testCases {
		e.logger.Info("Running test case",
			"number", i+1,
			"total", len(testCases),
			"id", tc.ID,
			"name", tc.Name)

		result := e.RunTestCase(ctx, tc, systemPrompt)
		results = append(results, result)

		if result.Passed {
			e.logger.Info("Test passed", "id", tc.ID)
		} else {
			e.logger.Warn("Test failed",
				"id", tc.ID,
				"errors", result.Errors)
		}
	}

	return results
}

// ComparePrompts runs the same test suite against multiple prompts.
func (e *Evaluator) ComparePrompts(ctx context.Context, testCases []TestCase, prompts map[string]string) map[string][]TestResult {
	results := make(map[string][]TestResult)

	for name, prompt := range prompts {
		e.logger.Info("Testing prompt version", "name", name)
		results[name] = e.RunTestSuite(ctx, testCases, prompt)
	}

	return results
}

// GenerateReport creates a comparison report.
func (e *Evaluator) GenerateReport(results map[string][]TestResult) string {
	// TODO: Implement detailed report generation
	// For now, return a simple summary
	var report strings.Builder

	report.WriteString("# Prompt Evaluation Report\n\n")
	report.WriteString(fmt.Sprintf("Generated at: %s\n\n", time.Now().Format(time.RFC3339)))

	for promptName, testResults := range results {
		passed := 0
		for _, r := range testResults {
			if r.Passed {
				passed++
			}
		}

		report.WriteString(fmt.Sprintf("## %s\n", promptName))
		report.WriteString(fmt.Sprintf("- Total tests: %d\n", len(testResults)))
		report.WriteString(fmt.Sprintf("- Passed: %d\n", passed))
		report.WriteString(fmt.Sprintf("- Failed: %d\n", len(testResults)-passed))
		report.WriteString(fmt.Sprintf("- Success rate: %.1f%%\n\n", float64(passed)/float64(len(testResults))*100))
	}

	return report.String()
}

// Private helper methods

func (e *Evaluator) getToolsForTest(tc TestCase) []ai.Tool {
	// TODO: Return appropriate tool definitions based on test expectations
	// For now, return empty to test without tools
	return []ai.Tool{}
}

func (e *Evaluator) evaluateResult(result *TestResult) {
	expected := result.TestCase.Expected
	result.Passed = true

	// Check tool calls
	if expected.ShouldCheckTime {
		if !e.hasToolCall(result.ActualCalls, "current_time", 0) {
			result.Errors = append(result.Errors, "Expected current_time to be called first")
			result.Passed = false
		}
	}

	if expected.ShouldSearch {
		if !e.hasToolCall(result.ActualCalls, "web_search", -1) {
			result.Errors = append(result.Errors, "Expected web_search to be called")
			result.Passed = false
		}
	}

	// Check expected tool calls
	for _, expectedCall := range expected.ToolCalls {
		if !e.hasToolCall(result.ActualCalls, expectedCall.Name, expectedCall.Order) {
			result.Errors = append(result.Errors,
				fmt.Sprintf("Expected tool call '%s' not found", expectedCall.Name))
			result.Passed = false
		}
	}

	// Check response patterns
	for _, pattern := range expected.ResponsePatterns {
		if !strings.Contains(result.Response, pattern) {
			result.Errors = append(result.Errors,
				fmt.Sprintf("Expected pattern '%s' not found in response", pattern))
			result.Passed = false
		}
	}

	// Check forbidden patterns
	for _, pattern := range expected.ForbiddenPatterns {
		if strings.Contains(result.Response, pattern) {
			result.Errors = append(result.Errors,
				fmt.Sprintf("Forbidden pattern '%s' found in response", pattern))
			result.Passed = false
		}
	}
}

func (e *Evaluator) hasToolCall(calls []ai.ToolCall, name string, order int) bool {
	for i, call := range calls {
		if call.Name == name {
			if order == -1 || order == i {
				return true
			}
		}
	}
	return false
}
