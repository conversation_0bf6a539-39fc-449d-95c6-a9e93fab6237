package prompt

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestNewPromptBuilder tests PromptBuilder creation
func TestNewPromptBuilder(t *testing.T) {
	builder := NewPromptBuilder()

	assert.NotNil(t, builder)
	assert.NotNil(t, builder.sections)
	assert.NotNil(t, builder.vars)
	assert.Empty(t, builder.sections)
}

// TestPromptBuilder_AddSection tests adding sections
func TestPromptBuilder_AddSection(t *testing.T) {
	builder := NewPromptBuilder()

	builder.AddSection("This is the introduction section")
	builder.AddSection("This is the body section")

	assert.Len(t, builder.sections, 2)
	assert.Equal(t, "This is the introduction section", builder.sections[0])
	assert.Equal(t, "This is the body section", builder.sections[1])
}

// TestPromptBuilder_WithMethods tests builder methods
func TestPromptBuilder_WithMethods(t *testing.T) {
	builder := NewPromptBuilder()

	// Test method chaining
	result := builder.
		WithUserContext("user123", "<PERSON>", "developer").
		WithQuery("How do I use this API?").
		WithContent("API documentation", "Here are the endpoints...", "Follow the examples").
		WithTask("API Integration", "Integrate the payment API", []string{"param1", "param2"}).
		WithConfiguration(1000, 0.7, "json").
		WithCustomString("api_key", "test-key").
		WithCustomNumber("retry_count", 3).
		WithCustomBoolean("debug_mode", true)

	// Should return same builder for chaining
	assert.Equal(t, builder, result)

	// Check variables were set
	assert.Equal(t, "user123", builder.vars.UserID)
	assert.Equal(t, "John Doe", builder.vars.UserName)
	assert.Equal(t, "developer", builder.vars.UserRole)
	assert.Equal(t, "How do I use this API?", builder.vars.Query)
	assert.Equal(t, "API documentation", builder.vars.Content)
	assert.Equal(t, "Here are the endpoints...", builder.vars.Context)
	assert.Equal(t, "Follow the examples", builder.vars.Instructions)
	assert.Equal(t, "API Integration", builder.vars.TaskType)
	assert.Equal(t, "Integrate the payment API", builder.vars.TaskDescription)
	assert.Equal(t, []string{"param1", "param2"}, builder.vars.TaskParameters)
	assert.Equal(t, 1000, builder.vars.MaxTokens)
	assert.Equal(t, 0.7, builder.vars.Temperature)
	assert.Equal(t, "json", builder.vars.ResponseFormat)
	assert.Equal(t, "test-key", builder.vars.CustomStrings["api_key"])
	assert.Equal(t, float64(3), builder.vars.CustomNumbers["retry_count"])
	assert.Equal(t, true, builder.vars.CustomBooleans["debug_mode"])
}

// TestPromptBuilder_Build tests building the final prompt
func TestPromptBuilder_Build(t *testing.T) {
	builder := NewPromptBuilder()

	builder.
		AddSection("Context: You are helping with code review").
		WithTask("Code Review", "Review the pull request", []string{"check style", "check logic"}).
		WithQuery("What issues do you see?")

	prompt, err := builder.Build()
	require.NoError(t, err)

	// Should contain sections
	assert.Contains(t, prompt, "Context: You are helping with code review")

	// Should be structured properly
	assert.NotEmpty(t, prompt)
}

// TestPromptBuilder_BuildEmpty tests building with no content
func TestPromptBuilder_BuildEmpty(t *testing.T) {
	builder := NewPromptBuilder()

	prompt, err := builder.Build()
	require.Error(t, err)
	assert.Equal(t, "no sections added to prompt", err.Error())

	// Should return empty string for empty builder
	assert.Empty(t, prompt)
}

// TestPromptBuilder_ComplexPrompt tests building a complex prompt
func TestPromptBuilder_ComplexPrompt(t *testing.T) {
	builder := NewPromptBuilder()

	builder.
		AddSection("System: You are a technical assistant.").
		AddSection("Context: Working on e-commerce project.").
		AddSection("Task: Your task is to review the architecture.").
		WithUserContext("user123", "Alice", "architect").
		WithQuery("Should we use Kubernetes?").
		WithConfiguration(2000, 0.3, "markdown")

	prompt, err := builder.Build()
	require.NoError(t, err)

	// Verify content is present
	assert.Contains(t, prompt, "System: You are a technical assistant")
	assert.Contains(t, prompt, "Context: Working on e-commerce project")
	assert.Contains(t, prompt, "Task: Your task is to review the architecture")
	assert.NotEmpty(t, prompt)
}

// TestPromptBuilder_MultipleBuilds tests building multiple times
func TestPromptBuilder_MultipleBuilds(t *testing.T) {
	builder := NewPromptBuilder()

	builder.
		AddSection("First section").
		WithCustomString("key", "value")

	// Build once
	prompt1, err := builder.Build()
	require.NoError(t, err)

	// Add more and build again
	builder.AddSection("Second section")
	prompt2, err := builder.Build()
	require.NoError(t, err)

	// Second prompt should have both sections
	assert.Contains(t, prompt2, "First section")
	assert.Contains(t, prompt2, "Second section")
	assert.Greater(t, len(prompt2), len(prompt1))
}

// TestPromptBuilder_CustomVariables tests custom variable methods
func TestPromptBuilder_CustomVariables(t *testing.T) {
	builder := NewPromptBuilder()

	// Ensure custom maps are initialized
	if builder.vars.CustomStrings == nil {
		builder.vars.CustomStrings = make(map[string]string)
	}
	if builder.vars.CustomNumbers == nil {
		builder.vars.CustomNumbers = make(map[string]float64)
	}
	if builder.vars.CustomBooleans == nil {
		builder.vars.CustomBooleans = make(map[string]bool)
	}

	builder.
		WithCustomString("env", "production").
		WithCustomNumber("timeout", 30.5).
		WithCustomBoolean("debug", false)

	assert.Equal(t, "production", builder.vars.CustomStrings["env"])
	assert.Equal(t, 30.5, builder.vars.CustomNumbers["timeout"])
	assert.Equal(t, false, builder.vars.CustomBooleans["debug"])
}

// TestPromptBuilder_SectionOrder tests that sections maintain order
func TestPromptBuilder_SectionOrder(t *testing.T) {
	builder := NewPromptBuilder()

	sections := []string{"First", "Second", "Third", "Fourth", "Fifth"}
	for _, section := range sections {
		builder.AddSection(section + " content")
	}

	prompt, err := builder.Build()
	require.NoError(t, err)

	// Verify sections appear in order
	for i := 1; i < len(sections); i++ {
		idx1 := strings.Index(prompt, sections[i-1]+" content")
		idx2 := strings.Index(prompt, sections[i]+" content")
		assert.Less(t, idx1, idx2, "Sections should appear in order")
	}
}
