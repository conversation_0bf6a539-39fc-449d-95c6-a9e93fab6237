package prompt

// DetailedMemoryInstructions provides detailed instructions for the LLM on how to use the memory context.
const DetailedMemoryInstructions = `
## Memory Context Usage Guide

When you receive a <retrieved_memory> block, it contains structured information from your memory system:

### 1. Direct Matches
These are memories directly relevant to the current query:
- High confidence scores (>0.8) indicate very relevant memories
- Pay special attention to the memory type (fact, preference, schedule, etc.)
- Keywords help understand the main topics

### 2. Related Memories
These are memories found through relationship expansion:
- The "relation" field explains how they connect to the query
- The "from" field shows which direct match led to this memory
- These provide broader context and may reveal patterns

### 3. Conflicts
When conflicting information exists:
- The "status" field indicates which information is more recent
- "current" status means it's the latest known fact
- "outdated" means it has been superseded
- Always prefer "current" information unless explicitly discussing history

### 4. Timeline
Chronological view of related events:
- Helps understand the evolution of preferences or situations
- Useful for detecting patterns or changes over time

### 5. Entities
Important people, places, or things mentioned across memories:
- High occurrence counts indicate significance
- Contexts show different ways the entity appears
- Useful for understanding relationships and connections

### Best Practices:
1. **Prioritize Recent Information**: More recent memories typically reflect current state
2. **Consider Confidence Scores**: Higher confidence memories are more reliable
3. **Look for Patterns**: Multiple related memories often reveal deeper insights
4. **Respect Contradictions**: If memories conflict, acknowledge the change rather than ignoring it
5. **Use Context Appropriately**: Not all memories need to be mentioned in your response

### Example Usage:
- For preferences: Check both direct matches and conflicts to ensure you have the latest preference
- For schedules: Look at recurring patterns in timeline and direct matches
- For facts: Prioritize high-confidence direct matches
- For relationships: Examine entity occurrences and related memories

Remember: The memory system helps you provide personalized, consistent responses while adapting to changes over time.
`

// EnhanceWithDetailedInstructions adds detailed memory usage instructions to the system prompt.
func EnhanceWithDetailedInstructions(systemPrompt string) string {
	return systemPrompt + "\n\n" + DetailedMemoryInstructions
}
