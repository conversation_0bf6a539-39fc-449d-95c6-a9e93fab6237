// Package styles provides shared UI styles and theming for the CLI interface.
// It defines color schemes, text styles, and formatting helpers used across
// all UI components to ensure consistent visual presentation.
package styles

import (
	"fmt"

	"github.com/charmbracelet/lipgloss"
)

// Color styles
var (
	errorStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("196")).
			Bold(true)

	warningStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("214"))

	successStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("42")).
			Bold(true)

	titleStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("86")).
			Bold(true).
			Underline(true)

	blueStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("39")).
			Bold(true)

	dimStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("241"))

	greenStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("42")).
			Bold(true)
)

// Styles contains all the styles for the UI components.
type Styles struct {
	Title     lipgloss.Style
	Subtitle  lipgloss.Style
	User      lipgloss.Style
	Assistant lipgloss.Style
	Tool      lipgloss.Style
	Error     lipgloss.Style
	Warning   lipgloss.Style
	Success   lipgloss.Style
	Info      lipgloss.Style
	Timestamp lipgloss.Style
	Code      lipgloss.Style
}

// DefaultStyles returns the default set of styles.
func DefaultStyles() *Styles {
	return &Styles{
		Title:     greenStyle,
		Subtitle:  blueStyle,
		User:      lipgloss.NewStyle().Foreground(lipgloss.Color("33")).Bold(true),
		Assistant: lipgloss.NewStyle().Foreground(lipgloss.Color("205")).Bold(true),
		Tool:      lipgloss.NewStyle().Foreground(lipgloss.Color("99")),
		Error:     errorStyle,
		Warning:   warningStyle,
		Success:   successStyle,
		Info:      blueStyle,
		Timestamp: dimStyle,
		Code:      lipgloss.NewStyle().Foreground(lipgloss.Color("213")),
	}
}

// PrintError prints an error message
func PrintError(format string, args ...any) {
	msg := fmt.Sprintf(format, args...)
	fmt.Println(errorStyle.Render("✗ " + msg))
}

// PrintWarning prints a warning message
func PrintWarning(format string, args ...any) {
	msg := fmt.Sprintf(format, args...)
	fmt.Println(warningStyle.Render("⚠ " + msg))
}

// PrintSuccess prints a success message
func PrintSuccess(format string, args ...any) {
	msg := fmt.Sprintf(format, args...)
	fmt.Println(successStyle.Render("✓ " + msg))
}

// FormatTitle formats a title string
func FormatTitle(title string) string {
	return titleStyle.Render(title)
}

// Blue returns blue colored text
func Blue(text string) string {
	return blueStyle.Render(text)
}

// Dim returns dimmed text
func Dim(format string, args ...any) string {
	text := fmt.Sprintf(format, args...)
	return dimStyle.Render(text)
}
