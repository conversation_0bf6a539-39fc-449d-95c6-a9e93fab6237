// Package chat provides markdown rendering for chat messages
package chat

import (
	"strings"

	"github.com/charmbracelet/glamour"
	"github.com/charmbracelet/lipgloss"
)

// MarkdownRenderer handles markdown rendering with syntax highlighting
type MarkdownRenderer struct {
	renderer *glamour.TermRenderer
	width    int
}

// NewMarkdownRenderer creates a new markdown renderer
func NewMarkdownRenderer(width int) (*MarkdownRenderer, error) {
	// Create a custom style for the terminal
	style := glamour.WithStylePath("dark")
	if width > 0 {
		style = glamour.WithWordWrap(width)
	}

	renderer, err := glamour.NewTermRenderer(
		style,
		glamour.WithEmoji(),
	)
	if err != nil {
		return nil, err
	}

	return &MarkdownRenderer{
		renderer: renderer,
		width:    width,
	}, nil
}

// Render renders markdown content
func (r *MarkdownRenderer) Render(content string) (string, error) {
	rendered, err := r.renderer.Render(content)
	if err != nil {
		return content, err // Fallback to original content
	}

	// Remove extra newlines that glamour might add
	rendered = strings.TrimSpace(rendered)
	return rendered, nil
}

// RenderCodeBlock renders a code block with syntax highlighting
func (r *MarkdownRenderer) RenderCodeBlock(code, language string) (string, error) {
	// Wrap code in markdown code block
	markdown := "```" + language + "\n" + code + "\n```"
	return r.Render(markdown)
}

// DetectAndRenderCode detects code blocks in content and renders them
func (r *MarkdownRenderer) DetectAndRenderCode(content string) (string, error) {
	// Simple detection - if content has ``` markers, it likely contains code
	if strings.Contains(content, "```") {
		return r.Render(content)
	}

	// Otherwise return as-is
	return content, nil
}

// CodeBlockStyle returns a style for inline code
func CodeBlockStyle() lipgloss.Style {
	return lipgloss.NewStyle().
		Background(lipgloss.Color("235")).
		Foreground(lipgloss.Color("213")).
		Padding(0, 1)
}

// InlineCodeStyle returns a style for inline code
func InlineCodeStyle() lipgloss.Style {
	return lipgloss.NewStyle().
		Background(lipgloss.Color("237")).
		Foreground(lipgloss.Color("213"))
}
