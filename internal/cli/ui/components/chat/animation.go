// Package chat provides chat UI animations
package chat

import (
	"sync"
	"time"
)

// SpinnerAnimation provides animated spinner characters
type SpinnerAnimation struct {
	frames  []string
	current int
	mu      sync.Mutex
}

// NewSpinnerAnimation creates a new spinner animation
func NewSpinnerAnimation() *SpinnerAnimation {
	return &SpinnerAnimation{
		frames: []string{
			"⣾", "⣽", "⣻", "⢿", "⡿", "⣟", "⣯", "⣷",
		},
		current: 0,
	}
}

// Next returns the next frame in the animation
func (s *SpinnerAnimation) Next() string {
	s.mu.Lock()
	defer s.mu.Unlock()

	frame := s.frames[s.current]
	s.current = (s.current + 1) % len(s.frames)
	return frame
}

// ThinkingAnimation provides a thinking indicator with spinner
type ThinkingAnimation struct {
	spinner *SpinnerAnimation
	ticker  *time.Ticker
	done    chan bool
}

// NewThinkingAnimation creates a new thinking animation
func NewThinkingAnimation() *ThinkingAnimation {
	return &ThinkingAnimation{
		spinner: NewSpinnerAnimation(),
		done:    make(chan bool),
	}
}

// Start begins the animation loop
func (t *ThinkingAnimation) Start(render func(string)) {
	t.ticker = time.NewTicker(100 * time.Millisecond)

	go func() {
		for {
			select {
			case <-t.ticker.C:
				frame := t.spinner.Next()
				render(frame + " Thinking...")
			case <-t.done:
				return
			}
		}
	}()
}

// Stop stops the animation
func (t *ThinkingAnimation) Stop() {
	if t.ticker != nil {
		t.ticker.Stop()
	}
	close(t.done)
}

// PulsingDots provides animated dots for loading states
type PulsingDots struct {
	dots    int
	maxDots int
}

// NewPulsingDots creates a new pulsing dots animation
func NewPulsingDots(maxDots int) *PulsingDots {
	return &PulsingDots{
		dots:    0,
		maxDots: maxDots,
	}
}

// Next returns the next state of dots
func (p *PulsingDots) Next() string {
	p.dots = (p.dots + 1) % (p.maxDots + 1)

	result := ""
	for i := 0; i < p.maxDots; i++ {
		if i < p.dots {
			result += "•"
		} else {
			result += " "
		}
	}

	return result
}
