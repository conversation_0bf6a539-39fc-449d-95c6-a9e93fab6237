// Package chat provides chat UI components
package chat

import (
	"fmt"
	"strings"
	"unicode/utf8"

	"github.com/charmbracelet/lipgloss"
	"github.com/koopa0/assistant-go/internal/cli/ui/components/styles"
)

// MessageType represents the type of message
type MessageType int

// Message type constants
const (
	UserMessage      MessageType = iota // UserMessage represents a message from the user
	AssistantMessage                    // AssistantMessage represents a message from the assistant
	SystemMessage                       // SystemMessage represents a system message
	ErrorMessage                        // ErrorMessage represents an error message
)

// Message represents a chat message with styling
type Message struct {
	Type    MessageType
	Content string
	Tool    *ToolInfo // Optional tool execution info
}

// ToolInfo contains information about tool execution
type ToolInfo struct {
	Name     string
	Status   string // "executing", "success", "error"
	Duration string
}

// ToolDisplayMode defines how tool executions are displayed
type ToolDisplayMode int

// Tool display mode constants
const (
	ToolDisplaySilent ToolDisplayMode = iota // ToolDisplaySilent doesn't show tool executions
	ToolDisplayIcon                          // ToolDisplayIcon shows only icon
	ToolDisplayDetail                        // ToolDisplayDetail shows full details
)

// MessageRenderer handles message rendering with modern chat UI
type MessageRenderer struct {
	styles          *styles.Styles
	width           int
	boxStyles       map[MessageType]lipgloss.Style
	markdown        *MarkdownRenderer
	toolDisplayMode ToolDisplayMode
	spacing         lipgloss.Style
}

// NewMessageRenderer creates a new message renderer
func NewMessageRenderer(styles *styles.Styles, width int) *MessageRenderer {
	// Create markdown renderer
	markdownRenderer, _ := NewMarkdownRenderer(int(float64(width) * 0.7))

	r := &MessageRenderer{
		styles:          styles,
		width:           width,
		boxStyles:       make(map[MessageType]lipgloss.Style),
		markdown:        markdownRenderer,
		toolDisplayMode: ToolDisplaySilent, // Default to silent
		spacing:         lipgloss.NewStyle().MarginBottom(1),
	}

	// Define message styles
	r.createMessageStyles()

	return r
}

// EnhancedMessageRenderer is an alias for backward compatibility
type EnhancedMessageRenderer = MessageRenderer

// NewEnhancedMessageRenderer creates a new enhanced renderer (alias for compatibility)
func NewEnhancedMessageRenderer(styles *styles.Styles, width int) *MessageRenderer {
	return NewMessageRenderer(styles, width)
}

func (r *MessageRenderer) createMessageStyles() {
	// Base bubble style
	baseBubble := lipgloss.NewStyle().
		Padding(1, 2).
		BorderStyle(lipgloss.RoundedBorder())

	// User message - right aligned, blue theme
	userStyle := baseBubble
	r.boxStyles[UserMessage] = userStyle.
		BorderForeground(lipgloss.Color("39")).
		Background(lipgloss.Color("237")).
		MaxWidth(int(float64(r.width) * 0.7))

	// Assistant message - left aligned, subtle gray
	assistantStyle := baseBubble
	r.boxStyles[AssistantMessage] = assistantStyle.
		BorderForeground(lipgloss.Color("241")).
		MaxWidth(int(float64(r.width) * 0.8))

	// System message - center aligned, no border, dimmed
	r.boxStyles[SystemMessage] = lipgloss.NewStyle().
		Align(lipgloss.Center).
		Foreground(lipgloss.Color("243")).
		Italic(true).
		Width(r.width).
		Padding(0, 1)

	// Error message - red accent
	errorStyle := baseBubble
	r.boxStyles[ErrorMessage] = errorStyle.
		BorderForeground(lipgloss.Color("196")).
		Foreground(lipgloss.Color("196")).
		Width(int(float64(r.width) * 0.8))
}

// Render renders a message with modern styling
func (r *MessageRenderer) Render(msg Message) string {
	switch msg.Type {
	case SystemMessage:
		return r.renderSystemMessage(msg)
	case ErrorMessage:
		return r.renderErrorMessage(msg)
	case UserMessage:
		return r.renderUserMessage(msg)
	case AssistantMessage:
		return r.renderAssistantMessage(msg)
	default:
		return msg.Content
	}
}

func (r *MessageRenderer) renderSystemMessage(msg Message) string {
	// Center-aligned system messages with decorative lines
	content := fmt.Sprintf("── %s ──", msg.Content)
	return r.spacing.Render(r.boxStyles[SystemMessage].Render(content))
}

func (r *MessageRenderer) renderErrorMessage(msg Message) string {
	// Error with icon
	content := fmt.Sprintf("⚠️  %s", msg.Content)
	styled := r.boxStyles[ErrorMessage].Render(content)
	return r.spacing.Render(r.centerAlign(styled))
}

func (r *MessageRenderer) renderUserMessage(msg Message) string {
	// Right-aligned user bubble
	bubble := r.boxStyles[UserMessage].Render(msg.Content)
	return r.spacing.Render(r.rightAlign(bubble))
}

func (r *MessageRenderer) renderAssistantMessage(msg Message) string {
	content := msg.Content

	// Apply markdown rendering for code blocks
	if r.markdown != nil {
		if rendered, err := r.markdown.DetectAndRenderCode(content); err == nil {
			content = rendered
		}
	}

	// Build the message
	var parts []string

	// Add content
	bubble := r.boxStyles[AssistantMessage].Render(content)
	parts = append(parts, bubble)

	// Add tool indicator if needed
	if msg.Tool != nil && r.toolDisplayMode != ToolDisplaySilent {
		toolInfo := r.renderToolIndicator(msg.Tool)
		if toolInfo != "" {
			parts = append(parts, toolInfo)
		}
	}

	return r.spacing.Render(strings.Join(parts, "\n"))
}

// RenderToolInfo renders tool execution information (legacy method)
func (r *MessageRenderer) RenderToolInfo(tool *ToolInfo) string {
	return r.renderToolIndicator(tool)
}

func (r *MessageRenderer) renderToolIndicator(tool *ToolInfo) string {
	switch r.toolDisplayMode {
	case ToolDisplayIcon:
		// Just show a small indicator
		return r.styles.Tool.Render(" 🛠")
	case ToolDisplayDetail:
		// Show name and duration
		info := fmt.Sprintf(" 🛠 %s", tool.Name)
		if tool.Duration != "" {
			info += fmt.Sprintf(" (%s)", tool.Duration)
		}
		return r.styles.Tool.Render(info)
	default:
		return ""
	}
}

// RenderThinking renders a thinking/loading animation
func (r *MessageRenderer) RenderThinking() string {
	// Use the spinner animation
	spinner := NewSpinnerAnimation()
	frame := spinner.Next()
	thinking := r.styles.Tool.Render(frame + " Thinking...")
	return thinking
}

// SetToolDisplayMode changes how tools are displayed
func (r *MessageRenderer) SetToolDisplayMode(mode ToolDisplayMode) {
	r.toolDisplayMode = mode
}

// UpdateWidth updates the renderer width for responsive design
func (r *MessageRenderer) UpdateWidth(width int) {
	r.width = width
	// Recreate styles with new width
	r.createMessageStyles()
}

// Helper functions for alignment

func (r *MessageRenderer) rightAlign(content string) string {
	lines := strings.Split(content, "\n")
	maxWidth := 0

	// Find the visual width of the widest line
	for _, line := range lines {
		width := visualWidth(line)
		if width > maxWidth {
			maxWidth = width
		}
	}

	// Right align each line
	var aligned []string
	for _, line := range lines {
		lineWidth := visualWidth(line)
		padding := r.width - lineWidth - 2 // 2 for margins
		if padding > 0 {
			aligned = append(aligned, strings.Repeat(" ", padding)+line)
		} else {
			aligned = append(aligned, line)
		}
	}

	return strings.Join(aligned, "\n")
}

func (r *MessageRenderer) centerAlign(content string) string {
	lines := strings.Split(content, "\n")
	var centered []string

	for _, line := range lines {
		width := visualWidth(line)
		padding := (r.width - width) / 2
		if padding > 0 {
			centered = append(centered, strings.Repeat(" ", padding)+line)
		} else {
			centered = append(centered, line)
		}
	}

	return strings.Join(centered, "\n")
}

// visualWidth calculates the visual width of a string, accounting for ANSI codes
func visualWidth(s string) int {
	// Strip ANSI codes for width calculation
	stripped := stripANSI(s)
	return utf8.RuneCountInString(stripped)
}

// stripANSI removes ANSI escape codes from a string
func stripANSI(s string) string {
	// Simple implementation - in production, use a proper ANSI stripping library
	var result strings.Builder
	inCode := false

	for _, ch := range s {
		if ch == '\033' {
			inCode = true
		} else if inCode && ch == 'm' {
			inCode = false
		} else if !inCode {
			result.WriteRune(ch)
		}
	}

	return result.String()
}

// GetMarkdown returns the markdown renderer
func (r *MessageRenderer) GetMarkdown() *MarkdownRenderer {
	return r.markdown
}
