// Package render provides form input UI component
package render

import (
	"encoding/json"
	"fmt"
	"io"
	"strconv"
	"strings"
	"time"

	"github.com/charmbracelet/bubbles/textinput"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

// FormModel is a Bubble Tea model for form input.
type FormModel struct {
	title       string
	description string
	fields      []FormField
	inputs      []textinput.Model
	values      map[string]any
	cursor      int
	submitLabel string
	cancelLabel string
	showHelp    bool
	allowCancel bool
	width       int
	height      int
	done        bool
	canceled    bool
	submitted   bool
	styles      *FormStyles
}

// FormStyles contains styles for form rendering.
type FormStyles struct {
	Title        lipgloss.Style
	Description  lipgloss.Style
	Label        lipgloss.Style
	Input        lipgloss.Style
	Error        lipgloss.Style
	Help         lipgloss.Style
	Button       lipgloss.Style
	ActiveButton lipgloss.Style
}

// DefaultFormStyles returns default styles for form.
func DefaultFormStyles() *FormStyles {
	return &FormStyles{
		Title:        lipgloss.NewStyle().Bold(true).Foreground(lipgloss.Color("205")),
		Description:  lipgloss.NewStyle().Foreground(lipgloss.Color("243")),
		Label:        lipgloss.NewStyle().Foreground(lipgloss.Color("241")),
		Input:        lipgloss.NewStyle().BorderStyle(lipgloss.RoundedBorder()).BorderForeground(lipgloss.Color("238")),
		Error:        lipgloss.NewStyle().Foreground(lipgloss.Color("196")),
		Help:         lipgloss.NewStyle().Foreground(lipgloss.Color("241")),
		Button:       lipgloss.NewStyle().Background(lipgloss.Color("238")).Foreground(lipgloss.Color("255")).Padding(0, 2),
		ActiveButton: lipgloss.NewStyle().Background(lipgloss.Color("205")).Foreground(lipgloss.Color("255")).Padding(0, 2),
	}
}

// NewFormModel creates a new form input model.
func NewFormModel(req UIRequest) (*FormModel, error) {
	var data FormData
	if err := json.Unmarshal(req.Data, &data); err != nil {
		return nil, fmt.Errorf("parse form data: %w", err)
	}

	m := &FormModel{
		title:       req.Title,
		description: req.Description,
		fields:      data.Fields,
		inputs:      make([]textinput.Model, len(data.Fields)),
		values:      make(map[string]any),
		submitLabel: req.Options.SubmitLabel,
		cancelLabel: req.Options.CancelLabel,
		showHelp:    req.Options.ShowHelp,
		allowCancel: req.Options.AllowCancel,
		width:       req.Options.Width,
		height:      req.Options.Height,
		styles:      DefaultFormStyles(),
	}

	if m.submitLabel == "" {
		m.submitLabel = "Submit"
	}
	if m.cancelLabel == "" {
		m.cancelLabel = "Cancel"
	}

	// Initialize text inputs
	for i, field := range data.Fields {
		ti := textinput.New()
		ti.Placeholder = field.Placeholder

		// Set input type specific properties
		switch field.Type {
		case "password":
			ti.EchoMode = textinput.EchoPassword
			ti.EchoCharacter = '•'
		case "number":
			ti.CharLimit = 20
		default:
			ti.CharLimit = 256
		}

		// Set default value
		if field.Default != nil {
			ti.SetValue(fmt.Sprintf("%v", field.Default))
		}

		if i == 0 {
			ti.Focus()
		}

		m.inputs[i] = ti
	}

	return m, nil
}

// Init initializes the model.
func (m *FormModel) Init() tea.Cmd {
	return textinput.Blink
}

// Update handles messages.
func (m *FormModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.Type {
		case tea.KeyCtrlC:
			m.canceled = true
			m.done = true
			return m, tea.Quit

		case tea.KeyEsc:
			if m.allowCancel {
				m.canceled = true
				m.done = true
				return m, tea.Quit
			}

		case tea.KeyTab, tea.KeyShiftTab:
			// Move to next/previous field
			if msg.Type == tea.KeyTab {
				m.cursor = (m.cursor + 1) % (len(m.fields) + 2) // +2 for submit/cancel buttons
			} else {
				m.cursor--
				if m.cursor < 0 {
					m.cursor = len(m.fields) + 1
				}
			}

			// Update focus
			for i := range m.inputs {
				if i == m.cursor {
					cmds = append(cmds, m.inputs[i].Focus())
				} else {
					m.inputs[i].Blur()
				}
			}

		case tea.KeyEnter:
			if m.cursor == len(m.fields) { // Submit button
				if m.validateForm() {
					m.collectValues()
					m.submitted = true
					m.done = true
					return m, tea.Quit
				}
			} else if m.cursor == len(m.fields)+1 && m.allowCancel { // Cancel button
				m.canceled = true
				m.done = true
				return m, tea.Quit
			}
		}

	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height
	}

	// Update current input if focused on a field
	if m.cursor < len(m.inputs) {
		m.inputs[m.cursor], cmds[m.cursor] = m.inputs[m.cursor].Update(msg)
	}

	return m, tea.Batch(cmds...)
}

// View renders the form.
func (m *FormModel) View() string {
	if m.done {
		return ""
	}

	var b strings.Builder

	// Title
	if m.title != "" {
		b.WriteString(m.styles.Title.Render(m.title))
		b.WriteString("\n")
	}

	// Description
	if m.description != "" {
		b.WriteString(m.styles.Description.Render(m.description))
		b.WriteString("\n")
	}

	if m.title != "" || m.description != "" {
		b.WriteString("\n")
	}

	// Fields
	for i, field := range m.fields {
		label := field.Label
		if field.Required {
			label += " *"
		}
		b.WriteString(m.styles.Label.Render(label))
		b.WriteString("\n")

		// Render input
		inputView := m.inputs[i].View()
		if i == m.cursor {
			inputView = m.styles.Input.BorderForeground(lipgloss.Color("205")).Render(inputView)
		} else {
			inputView = m.styles.Input.Render(inputView)
		}
		b.WriteString(inputView)
		b.WriteString("\n\n")
	}

	// Buttons
	submitStyle := m.styles.Button
	cancelStyle := m.styles.Button

	if m.cursor == len(m.fields) {
		submitStyle = m.styles.ActiveButton
	} else if m.cursor == len(m.fields)+1 {
		cancelStyle = m.styles.ActiveButton
	}

	buttons := submitStyle.Render(m.submitLabel)
	if m.allowCancel {
		buttons += "  " + cancelStyle.Render(m.cancelLabel)
	}
	b.WriteString(buttons)

	// Help
	if m.showHelp {
		b.WriteString("\n\n")
		helpText := "tab: next field • shift+tab: previous field • enter: submit"
		if m.allowCancel {
			helpText += " • esc: cancel"
		}
		b.WriteString(m.styles.Help.Render(helpText))
	}

	return b.String()
}

// validateForm validates all form fields.
func (m *FormModel) validateForm() bool {
	for i, field := range m.fields {
		value := m.inputs[i].Value()

		// Required field check
		if field.Required && strings.TrimSpace(value) == "" {
			return false
		}

		// Type-specific validation
		switch field.Type {
		case "number":
			if value != "" {
				_, err := strconv.ParseFloat(value, 64)
				if err != nil {
					return false
				}
			}
		}
	}
	return true
}

// collectValues collects form values into the values map.
func (m *FormModel) collectValues() {
	for i, field := range m.fields {
		value := m.inputs[i].Value()

		switch field.Type {
		case "number":
			if value != "" {
				if f, err := strconv.ParseFloat(value, 64); err == nil {
					m.values[field.ID] = f
				}
			}
		case "checkbox":
			// For checkbox, we'd need a different input type
			m.values[field.ID] = value == "true" || value == "yes"
		default:
			m.values[field.ID] = value
		}
	}
}

// Result returns the form result.
func (m *FormModel) Result() UIResponse {
	if m.canceled {
		return UIResponse{
			Type:     "form",
			Canceled: true,
		}
	}

	if m.submitted {
		return UIResponse{
			Type:  "form",
			Input: m.values,
		}
	}

	return UIResponse{
		Type: "form",
	}
}

// RunForm runs a form UI and returns the result.
func RunForm(req UIRequest, output io.Writer) (UIResponse, error) {
	model, err := NewFormModel(req)
	if err != nil {
		return UIResponse{}, err
	}

	p := tea.NewProgram(model, tea.WithOutput(output))
	start := time.Now()

	if _, err := p.Run(); err != nil {
		return UIResponse{}, fmt.Errorf("run form UI: %w", err)
	}

	result := model.Result()
	result.Duration = time.Since(start)
	return result, nil
}
