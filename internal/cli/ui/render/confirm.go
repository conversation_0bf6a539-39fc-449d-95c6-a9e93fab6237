// Package render provides confirmation dialog UI component
package render

import (
	"encoding/json"
	"fmt"
	"io"
	"strings"
	"time"

	"github.com/charmbracelet/bubbles/key"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

// ConfirmModel is a Bubble Tea model for confirmation dialogs.
type ConfirmModel struct {
	title       string
	description string
	message     string
	defaultYes  bool
	cursor      bool // true = yes, false = no
	done        bool
	confirmed   bool
	styles      *ConfirmStyles
}

// ConfirmStyles contains styles for confirmation dialog.
type ConfirmStyles struct {
	Title        lipgloss.Style
	Description  lipgloss.Style
	Message      lipgloss.Style
	Button       lipgloss.Style
	ActiveButton lipgloss.Style
	Help         lipgloss.Style
}

// DefaultConfirmStyles returns default styles for confirmation.
func DefaultConfirmStyles() *ConfirmStyles {
	return &ConfirmStyles{
		Title:        lipgloss.NewStyle().Bold(true).Foreground(lipgloss.Color("205")),
		Description:  lipgloss.NewStyle().Foreground(lipgloss.Color("243")),
		Message:      lipgloss.NewStyle().Foreground(lipgloss.Color("255")),
		Button:       lipgloss.NewStyle().Background(lipgloss.Color("238")).Foreground(lipgloss.Color("255")).Padding(0, 3),
		ActiveButton: lipgloss.NewStyle().Background(lipgloss.Color("205")).Foreground(lipgloss.Color("255")).Padding(0, 3),
		Help:         lipgloss.NewStyle().Foreground(lipgloss.Color("241")),
	}
}

// NewConfirmModel creates a new confirmation dialog model.
func NewConfirmModel(req UIRequest) (*ConfirmModel, error) {
	var data ConfirmData
	if err := json.Unmarshal(req.Data, &data); err != nil {
		return nil, fmt.Errorf("parse confirm data: %w", err)
	}

	m := &ConfirmModel{
		title:       req.Title,
		description: req.Description,
		message:     data.Message,
		defaultYes:  data.Default,
		cursor:      data.Default,
		styles:      DefaultConfirmStyles(),
	}

	return m, nil
}

// Init initializes the model.
func (m *ConfirmModel) Init() tea.Cmd {
	return nil
}

// Update handles messages.
func (m *ConfirmModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch {
		case key.Matches(msg, key.NewBinding(key.WithKeys("left", "h", "tab"))):
			m.cursor = true // Move to Yes

		case key.Matches(msg, key.NewBinding(key.WithKeys("right", "l"))):
			m.cursor = false // Move to No

		case key.Matches(msg, key.NewBinding(key.WithKeys("y", "Y"))):
			m.confirmed = true
			m.done = true
			return m, tea.Quit

		case key.Matches(msg, key.NewBinding(key.WithKeys("n", "N"))):
			m.confirmed = false
			m.done = true
			return m, tea.Quit

		case key.Matches(msg, key.NewBinding(key.WithKeys("enter", "space"))):
			m.confirmed = m.cursor
			m.done = true
			return m, tea.Quit

		case key.Matches(msg, key.NewBinding(key.WithKeys("esc", "q", "ctrl+c"))):
			m.confirmed = false
			m.done = true
			return m, tea.Quit
		}
	}

	return m, nil
}

// View renders the confirmation dialog.
func (m *ConfirmModel) View() string {
	if m.done {
		return ""
	}

	var b strings.Builder

	// Title
	if m.title != "" {
		b.WriteString(m.styles.Title.Render(m.title))
		b.WriteString("\n")
	}

	// Description
	if m.description != "" {
		b.WriteString(m.styles.Description.Render(m.description))
		b.WriteString("\n")
	}

	if m.title != "" || m.description != "" {
		b.WriteString("\n")
	}

	// Message
	b.WriteString(m.styles.Message.Render(m.message))
	b.WriteString("\n\n")

	// Buttons
	yesStyle := m.styles.Button
	noStyle := m.styles.Button

	if m.cursor {
		yesStyle = m.styles.ActiveButton
	} else {
		noStyle = m.styles.ActiveButton
	}

	buttons := yesStyle.Render("Yes") + "  " + noStyle.Render("No")
	b.WriteString(buttons)

	// Help
	b.WriteString("\n\n")
	b.WriteString(m.styles.Help.Render("←/→: select • enter: confirm • y/n: quick answer • esc: cancel"))

	return b.String()
}

// Result returns the confirmation result.
func (m *ConfirmModel) Result() UIResponse {
	return UIResponse{
		Type:     "confirm",
		Selected: m.confirmed,
	}
}

// RunConfirm runs a confirmation dialog and returns the result.
func RunConfirm(req UIRequest, output io.Writer) (UIResponse, error) {
	model, err := NewConfirmModel(req)
	if err != nil {
		return UIResponse{}, err
	}

	p := tea.NewProgram(model, tea.WithOutput(output))
	start := time.Now()

	if _, err := p.Run(); err != nil {
		return UIResponse{}, fmt.Errorf("run confirm UI: %w", err)
	}

	result := model.Result()
	result.Duration = time.Since(start)
	return result, nil
}

// QuickConfirm provides a simple confirmation dialog.
func QuickConfirm(message string, defaultYes bool, output io.Writer) (bool, error) {
	data, _ := json.Marshal(ConfirmData{
		Message: message,
		Default: defaultYes,
	})

	req := UIRequest{
		Type: "confirm",
		Data: data,
		Options: UIOptions{
			ShowHelp: true,
		},
	}

	resp, err := RunConfirm(req, output)
	if err != nil {
		return false, err
	}

	if confirmed, ok := resp.Selected.(bool); ok {
		return confirmed, nil
	}

	return false, nil
}
