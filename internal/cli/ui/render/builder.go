// Package render provides UI component builders for dynamic rendering
package render

import (
	"encoding/json"
	"fmt"
)

// Builder provides methods to construct UI components programmatically.
type Builder struct {
	request UIRequest
}

// NewBuilder creates a new UI builder.
func NewBuilder(uiType string) *Builder {
	return &Builder{
		request: UIRequest{
			Type:    uiType,
			Options: UIOptions{},
		},
	}
}

// WithTitle sets the title.
func (b *Builder) WithTitle(title string) *Builder {
	b.request.Title = title
	return b
}

// WithDescription sets the description.
func (b *Builder) WithDescription(desc string) *Builder {
	b.request.Description = desc
	return b
}

// WithOptions sets UI options.
func (b *Builder) WithOptions(opts UIOptions) *Builder {
	b.request.Options = opts
	return b
}

// Build finalizes and returns the UI request.
func (b *Builder) Build() UIRequest {
	return b.request
}

// ListBuilder provides fluent API for building list selections.
type ListBuilder struct {
	*Builder
	items []ListItem
}

// NewListBuilder creates a new list builder.
func NewListBuilder() *ListBuilder {
	return &ListBuilder{
		Builder: NewBuilder("list"),
		items:   []ListItem{},
	}
}

// AddItem adds an item to the list.
func (b *ListBuilder) AddItem(id, title, description string, value interface{}) *ListBuilder {
	b.items = append(b.items, ListItem{
		ID:          id,
		Title:       title,
		Description: description,
		Value:       value,
	})
	return b
}

// WithTitle sets the title for list.
func (b *ListBuilder) WithTitle(title string) *ListBuilder {
	b.Builder.WithTitle(title)
	return b
}

// MultiSelect enables multi-selection mode.
func (b *ListBuilder) MultiSelect(enabled bool) *ListBuilder {
	b.request.Options.MultiSelect = enabled
	return b
}

// Build finalizes the list request.
func (b *ListBuilder) Build() UIRequest {
	data, _ := json.Marshal(ListData{Items: b.items})
	b.request.Data = data
	return b.request
}

// FormBuilder provides fluent API for building forms.
type FormBuilder struct {
	*Builder
	fields []FormField
}

// NewFormBuilder creates a new form builder.
func NewFormBuilder() *FormBuilder {
	return &FormBuilder{
		Builder: NewBuilder("form"),
		fields:  []FormField{},
	}
}

// AddTextField adds a text field.
func (b *FormBuilder) AddTextField(id, label, placeholder string, required bool) *FormBuilder {
	b.fields = append(b.fields, FormField{
		ID:          id,
		Label:       label,
		Type:        "text",
		Required:    required,
		Placeholder: placeholder,
	})
	return b
}

// AddPasswordField adds a password field.
func (b *FormBuilder) AddPasswordField(id, label, placeholder string, required bool) *FormBuilder {
	b.fields = append(b.fields, FormField{
		ID:          id,
		Label:       label,
		Type:        "password",
		Required:    required,
		Placeholder: placeholder,
	})
	return b
}

// AddNumberField adds a number field.
func (b *FormBuilder) AddNumberField(id, label string, min, max float64, required bool) *FormBuilder {
	b.fields = append(b.fields, FormField{
		ID:       id,
		Label:    label,
		Type:     "number",
		Required: required,
		Min:      min,
		Max:      max,
	})
	return b
}

// AddSelectField adds a select field.
func (b *FormBuilder) AddSelectField(id, label string, options []string, defaultValue string, required bool) *FormBuilder {
	b.fields = append(b.fields, FormField{
		ID:       id,
		Label:    label,
		Type:     "select",
		Required: required,
		Options:  options,
		Default:  defaultValue,
	})
	return b
}

// WithTitle sets the title for form.
func (b *FormBuilder) WithTitle(title string) *FormBuilder {
	b.Builder.WithTitle(title)
	return b
}

// WithSubmitLabel sets the submit button label.
func (b *FormBuilder) WithSubmitLabel(label string) *FormBuilder {
	b.request.Options.SubmitLabel = label
	return b
}

// WithCancelLabel sets the cancel button label.
func (b *FormBuilder) WithCancelLabel(label string) *FormBuilder {
	b.request.Options.CancelLabel = label
	return b
}

// Build finalizes the form request.
func (b *FormBuilder) Build() UIRequest {
	data, _ := json.Marshal(FormData{Fields: b.fields})
	b.request.Data = data
	return b.request
}

// TableBuilder provides fluent API for building tables.
type TableBuilder struct {
	*Builder
	columns []TableColumn
	rows    []TableRow
}

// NewTableBuilder creates a new table builder.
func NewTableBuilder() *TableBuilder {
	return &TableBuilder{
		Builder: NewBuilder("table"),
		columns: []TableColumn{},
		rows:    []TableRow{},
	}
}

// AddColumn adds a column definition.
func (b *TableBuilder) AddColumn(id, title string, width int, align string) *TableBuilder {
	b.columns = append(b.columns, TableColumn{
		ID:    id,
		Title: title,
		Width: width,
		Align: align,
	})
	return b
}

// AddRow adds a row to the table.
func (b *TableBuilder) AddRow(id string, values map[string]string) *TableBuilder {
	b.rows = append(b.rows, TableRow{
		ID:     id,
		Values: values,
	})
	return b
}

// WithTitle sets the title for table.
func (b *TableBuilder) WithTitle(title string) *TableBuilder {
	b.Builder.WithTitle(title)
	return b
}

// ShowHeaders enables header display.
func (b *TableBuilder) ShowHeaders(show bool) *TableBuilder {
	b.request.Options.ShowHeaders = show
	return b
}

// Build finalizes the table request.
func (b *TableBuilder) Build() UIRequest {
	data, _ := json.Marshal(TableData{
		Columns: b.columns,
		Rows:    b.rows,
	})
	b.request.Data = data
	return b.request
}

// ConfirmBuilder provides fluent API for building confirmations.
type ConfirmBuilder struct {
	*Builder
	message    string
	defaultYes bool
}

// NewConfirmBuilder creates a new confirmation builder.
func NewConfirmBuilder(message string) *ConfirmBuilder {
	return &ConfirmBuilder{
		Builder: NewBuilder("confirm"),
		message: message,
	}
}

// WithTitle sets the title for confirmation.
func (b *ConfirmBuilder) WithTitle(title string) *ConfirmBuilder {
	b.Builder.WithTitle(title)
	return b
}

// DefaultYes sets the default selection to Yes.
func (b *ConfirmBuilder) DefaultYes() *ConfirmBuilder {
	b.defaultYes = true
	return b
}

// DefaultNo sets the default selection to No.
func (b *ConfirmBuilder) DefaultNo() *ConfirmBuilder {
	b.defaultYes = false
	return b
}

// Build finalizes the confirmation request.
func (b *ConfirmBuilder) Build() UIRequest {
	data, _ := json.Marshal(ConfirmData{
		Message: b.message,
		Default: b.defaultYes,
	})
	b.request.Data = data
	return b.request
}

// Quick builders for common use cases

// QuickList creates a simple list selection.
func QuickList(title string, items map[string]string) UIRequest {
	builder := NewListBuilder().WithTitle(title)

	for id, label := range items {
		builder.AddItem(id, label, "", id)
	}

	return builder.Build()
}

// QuickForm creates a simple form with text fields.
func QuickForm(title string, fields map[string]string) UIRequest {
	builder := NewFormBuilder().WithTitle(title)

	for id, label := range fields {
		builder.AddTextField(id, label, fmt.Sprintf("Enter %s", label), true)
	}

	return builder.Build()
}

// QuickTable creates a simple table from data.
func QuickTable(title string, headers []string, rows [][]string) UIRequest {
	builder := NewTableBuilder().WithTitle(title).ShowHeaders(true)

	// Add columns
	for i, header := range headers {
		colID := fmt.Sprintf("col%d", i)
		builder.AddColumn(colID, header, 0, "left")
	}

	// Add rows
	for rowIdx, row := range rows {
		values := make(map[string]string)
		for colIdx, value := range row {
			colID := fmt.Sprintf("col%d", colIdx)
			values[colID] = value
		}
		builder.AddRow(fmt.Sprintf("row%d", rowIdx), values)
	}

	return builder.Build()
}
