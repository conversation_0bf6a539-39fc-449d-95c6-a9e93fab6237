// Package render provides list selection UI component
package render

import (
	"encoding/json"
	"fmt"
	"io"
	"strings"
	"time"

	"github.com/charmbracelet/bubbles/key"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

// ListModel is a Bubble Tea model for list selection.
type ListModel struct {
	title       string
	description string
	items       []ListItem
	cursor      int
	selected    map[int]bool
	multiSelect bool
	showIndices bool
	showHelp    bool
	allowCancel bool
	width       int
	height      int
	done        bool
	canceled    bool
	styles      *ListStyles
}

// ListStyles contains styles for list rendering.
type ListStyles struct {
	Title       lipgloss.Style
	Description lipgloss.Style
	Item        lipgloss.Style
	Selected    lipgloss.Style
	Cursor      lipgloss.Style
	Help        lipgloss.Style
}

// DefaultListStyles returns default styles for list.
func DefaultListStyles() *ListStyles {
	return &ListStyles{
		Title:       lipgloss.NewStyle().Bold(true).Foreground(lipgloss.Color("205")),
		Description: lipgloss.NewStyle().Foreground(lipgloss.Color("243")),
		Item:        lipgloss.NewStyle().PaddingLeft(2),
		Selected:    lipgloss.NewStyle().Foreground(lipgloss.Color("77")).Bold(true),
		Cursor:      lipgloss.NewStyle().Foreground(lipgloss.Color("205")),
		Help:        lipgloss.NewStyle().Foreground(lipgloss.Color("241")),
	}
}

// NewListModel creates a new list selection model.
func NewListModel(req UIRequest) (*ListModel, error) {
	var data ListData
	if err := json.Unmarshal(req.Data, &data); err != nil {
		return nil, fmt.Errorf("parse list data: %w", err)
	}

	m := &ListModel{
		title:       req.Title,
		description: req.Description,
		items:       data.Items,
		cursor:      data.Default,
		selected:    make(map[int]bool),
		multiSelect: req.Options.MultiSelect,
		showIndices: req.Options.ShowIndices,
		showHelp:    req.Options.ShowHelp,
		allowCancel: req.Options.AllowCancel,
		width:       req.Options.Width,
		height:      req.Options.Height,
		styles:      DefaultListStyles(),
	}

	// Pre-select items marked as selected
	for i, item := range data.Items {
		if item.Selected {
			m.selected[i] = true
		}
	}

	// Ensure cursor is in bounds
	if m.cursor >= len(m.items) {
		m.cursor = len(m.items) - 1
	}
	if m.cursor < 0 {
		m.cursor = 0
	}

	return m, nil
}

// Init initializes the model.
func (m *ListModel) Init() tea.Cmd {
	return nil
}

// Update handles messages.
func (m *ListModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch {
		case key.Matches(msg, key.NewBinding(key.WithKeys("up", "k"))):
			if m.cursor > 0 {
				m.cursor--
			}

		case key.Matches(msg, key.NewBinding(key.WithKeys("down", "j"))):
			if m.cursor < len(m.items)-1 {
				m.cursor++
			}

		case key.Matches(msg, key.NewBinding(key.WithKeys("space"))):
			if m.multiSelect {
				if m.selected[m.cursor] {
					delete(m.selected, m.cursor)
				} else {
					m.selected[m.cursor] = true
				}
			}

		case key.Matches(msg, key.NewBinding(key.WithKeys("enter"))):
			if !m.multiSelect {
				m.selected = map[int]bool{m.cursor: true}
			}
			m.done = true
			return m, tea.Quit

		case key.Matches(msg, key.NewBinding(key.WithKeys("esc", "q"))):
			if m.allowCancel {
				m.canceled = true
				m.done = true
				return m, tea.Quit
			}

		case key.Matches(msg, key.NewBinding(key.WithKeys("ctrl+c"))):
			m.canceled = true
			m.done = true
			return m, tea.Quit
		}

	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height
	}

	return m, nil
}

// View renders the list.
func (m *ListModel) View() string {
	if m.done {
		return ""
	}

	var b strings.Builder

	// Title
	if m.title != "" {
		b.WriteString(m.styles.Title.Render(m.title))
		b.WriteString("\n")
	}

	// Description
	if m.description != "" {
		b.WriteString(m.styles.Description.Render(m.description))
		b.WriteString("\n")
	}

	if m.title != "" || m.description != "" {
		b.WriteString("\n")
	}

	// Items
	for i, item := range m.items {
		cursor := "  "
		if i == m.cursor {
			cursor = m.styles.Cursor.Render("> ")
		}

		var text string
		if m.showIndices {
			text = fmt.Sprintf("%d. %s", i+1, item.Title)
		} else {
			text = item.Title
		}

		if item.Description != "" {
			text += "\n    " + m.styles.Description.Render(item.Description)
		}

		if m.selected[i] {
			if m.multiSelect {
				text = "[✓] " + text
			}
			text = m.styles.Selected.Render(text)
		} else if m.multiSelect {
			text = "[ ] " + text
		}

		b.WriteString(cursor)
		b.WriteString(m.styles.Item.Render(text))
		b.WriteString("\n")
	}

	// Help
	if m.showHelp {
		b.WriteString("\n")
		helpText := "↑/↓: navigate"
		if m.multiSelect {
			helpText += " • space: select • enter: confirm"
		} else {
			helpText += " • enter: select"
		}
		if m.allowCancel {
			helpText += " • esc: cancel"
		}
		b.WriteString(m.styles.Help.Render(helpText))
	}

	return b.String()
}

// Result returns the selection result.
func (m *ListModel) Result() UIResponse {
	if m.canceled {
		return UIResponse{
			Type:     "list",
			Canceled: true,
		}
	}

	if m.multiSelect {
		var selected []interface{}
		for i := range m.selected {
			if i < len(m.items) {
				selected = append(selected, m.items[i].Value)
			}
		}
		return UIResponse{
			Type:     "list",
			Selected: selected,
		}
	}

	// Single select
	for i := range m.selected {
		if i < len(m.items) {
			return UIResponse{
				Type:     "list",
				Selected: m.items[i].Value,
			}
		}
	}

	return UIResponse{
		Type: "list",
	}
}

// RunList runs a list selection UI and returns the result.
func RunList(req UIRequest, output io.Writer) (UIResponse, error) {
	model, err := NewListModel(req)
	if err != nil {
		return UIResponse{}, err
	}

	p := tea.NewProgram(model, tea.WithOutput(output))
	start := time.Now()

	if _, err := p.Run(); err != nil {
		return UIResponse{}, fmt.Errorf("run list UI: %w", err)
	}

	result := model.Result()
	result.Duration = time.Since(start)
	return result, nil
}
