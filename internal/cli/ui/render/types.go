// Package render provides interactive UI tools for the assistant.
package render

import (
	"encoding/json"
	"time"
)

// UIRequest represents a request to display a UI component.
type UIRequest struct {
	// Common fields
	Title       string          `json:"title,omitempty"`
	Description string          `json:"description,omitempty"`
	Type        string          `json:"type"` // "list", "form", "table", "confirm"
	Data        json.RawMessage `json:"data"`
	Options     UIOptions       `json:"options,omitempty"`
}

// UIOptions provides display options for UI components.
type UIOptions struct {
	// Common options
	Width       int  `json:"width,omitempty"`
	Height      int  `json:"height,omitempty"`
	ShowHelp    bool `json:"show_help,omitempty"`
	AllowCancel bool `json:"allow_cancel,omitempty"`

	// List options
	MultiSelect bool `json:"multi_select,omitempty"`
	ShowIndices bool `json:"show_indices,omitempty"`

	// Form options
	SubmitLabel string `json:"submit_label,omitempty"`
	CancelLabel string `json:"cancel_label,omitempty"`

	// Table options
	ShowHeaders bool `json:"show_headers,omitempty"`
	Sortable    bool `json:"sortable,omitempty"`
	Filterable  bool `json:"filterable,omitempty"`
}

// UIResponse represents the result of a UI interaction.
type UIResponse struct {
	Type     string          `json:"type"`
	Selected interface{}     `json:"selected,omitempty"`
	Input    interface{}     `json:"input,omitempty"`
	Canceled bool            `json:"canceled"`
	Duration time.Duration   `json:"duration"`
	Data     json.RawMessage `json:"data,omitempty"`
}

// ListItem represents an item in a list selection.
type ListItem struct {
	ID          string      `json:"id"`
	Title       string      `json:"title"`
	Description string      `json:"description,omitempty"`
	Value       interface{} `json:"value,omitempty"`
	Selected    bool        `json:"selected,omitempty"`
}

// ListData contains data for list selection UI.
type ListData struct {
	Items   []ListItem `json:"items"`
	Default int        `json:"default,omitempty"`
}

// FormField represents a field in a form.
type FormField struct {
	ID          string      `json:"id"`
	Label       string      `json:"label"`
	Type        string      `json:"type"` // "text", "password", "number", "select", "checkbox"
	Required    bool        `json:"required,omitempty"`
	Default     interface{} `json:"default,omitempty"`
	Placeholder string      `json:"placeholder,omitempty"`
	Options     []string    `json:"options,omitempty"` // For select type
	Min         float64     `json:"min,omitempty"`     // For number type
	Max         float64     `json:"max,omitempty"`     // For number type
	Pattern     string      `json:"pattern,omitempty"` // Regex validation
}

// FormData contains data for form UI.
type FormData struct {
	Fields []FormField `json:"fields"`
}

// TableColumn represents a column in a table.
type TableColumn struct {
	ID    string `json:"id"`
	Title string `json:"title"`
	Width int    `json:"width,omitempty"`
	Align string `json:"align,omitempty"` // "left", "center", "right"
}

// TableRow represents a row in a table.
type TableRow struct {
	ID     string            `json:"id"`
	Values map[string]string `json:"values"`
}

// TableData contains data for table display.
type TableData struct {
	Columns []TableColumn `json:"columns"`
	Rows    []TableRow    `json:"rows"`
}

// ConfirmData contains data for confirmation dialog.
type ConfirmData struct {
	Message string `json:"message"`
	Default bool   `json:"default,omitempty"`
}
