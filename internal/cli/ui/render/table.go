// Package render provides table display UI component
package render

import (
	"encoding/json"
	"fmt"
	"io"
	"strings"
	"time"

	"github.com/charmbracelet/bubbles/key"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

// TableModel is a Bubble Tea model for table display.
type TableModel struct {
	title       string
	description string
	columns     []TableColumn
	rows        []TableRow
	cursor      int
	offset      int
	selected    map[int]bool
	showHeaders bool
	showHelp    bool
	allowCancel bool
	width       int
	height      int
	done        bool
	canceled    bool
	styles      *TableStyles
	viewHeight  int // Visible rows
}

// TableStyles contains styles for table rendering.
type TableStyles struct {
	Title       lipgloss.Style
	Description lipgloss.Style
	Header      lipgloss.Style
	Row         lipgloss.Style
	Selected    lipgloss.Style
	Cursor      lipgloss.Style
	Border      lipgloss.Style
	Help        lipgloss.Style
}

// DefaultTableStyles returns default styles for table.
func DefaultTableStyles() *TableStyles {
	return &TableStyles{
		Title:       lipgloss.NewStyle().Bold(true).Foreground(lipgloss.Color("205")),
		Description: lipgloss.NewStyle().Foreground(lipgloss.Color("243")),
		Header:      lipgloss.NewStyle().Bold(true).Foreground(lipgloss.Color("255")).Background(lipgloss.Color("237")),
		Row:         lipgloss.NewStyle(),
		Selected:    lipgloss.NewStyle().Foreground(lipgloss.Color("77")).Bold(true),
		Cursor:      lipgloss.NewStyle().Foreground(lipgloss.Color("205")),
		Border:      lipgloss.NewStyle().Foreground(lipgloss.Color("238")),
		Help:        lipgloss.NewStyle().Foreground(lipgloss.Color("241")),
	}
}

// NewTableModel creates a new table display model.
func NewTableModel(req UIRequest) (*TableModel, error) {
	var data TableData
	if err := json.Unmarshal(req.Data, &data); err != nil {
		return nil, fmt.Errorf("parse table data: %w", err)
	}

	m := &TableModel{
		title:       req.Title,
		description: req.Description,
		columns:     data.Columns,
		rows:        data.Rows,
		cursor:      0,
		offset:      0,
		selected:    make(map[int]bool),
		showHeaders: req.Options.ShowHeaders,
		showHelp:    req.Options.ShowHelp,
		allowCancel: req.Options.AllowCancel,
		width:       req.Options.Width,
		height:      req.Options.Height,
		styles:      DefaultTableStyles(),
		viewHeight:  10, // Default visible rows
	}

	// Default to showing headers
	if !req.Options.ShowHeaders {
		m.showHeaders = true
	}

	return m, nil
}

// Init initializes the model.
func (m *TableModel) Init() tea.Cmd {
	return nil
}

// Update handles messages.
func (m *TableModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch {
		case key.Matches(msg, key.NewBinding(key.WithKeys("up", "k"))):
			if m.cursor > 0 {
				m.cursor--
				if m.cursor < m.offset {
					m.offset = m.cursor
				}
			}

		case key.Matches(msg, key.NewBinding(key.WithKeys("down", "j"))):
			if m.cursor < len(m.rows)-1 {
				m.cursor++
				if m.cursor >= m.offset+m.viewHeight {
					m.offset = m.cursor - m.viewHeight + 1
				}
			}

		case key.Matches(msg, key.NewBinding(key.WithKeys("pgup"))):
			m.cursor -= m.viewHeight
			if m.cursor < 0 {
				m.cursor = 0
			}
			m.offset = m.cursor

		case key.Matches(msg, key.NewBinding(key.WithKeys("pgdown"))):
			m.cursor += m.viewHeight
			if m.cursor >= len(m.rows) {
				m.cursor = len(m.rows) - 1
			}
			if m.cursor >= m.offset+m.viewHeight {
				m.offset = m.cursor - m.viewHeight + 1
			}

		case key.Matches(msg, key.NewBinding(key.WithKeys("home"))):
			m.cursor = 0
			m.offset = 0

		case key.Matches(msg, key.NewBinding(key.WithKeys("end"))):
			m.cursor = len(m.rows) - 1
			if m.cursor >= m.viewHeight {
				m.offset = m.cursor - m.viewHeight + 1
			}

		case key.Matches(msg, key.NewBinding(key.WithKeys("enter", "space"))):
			if m.selected[m.cursor] {
				delete(m.selected, m.cursor)
			} else {
				m.selected[m.cursor] = true
			}

		case key.Matches(msg, key.NewBinding(key.WithKeys("esc", "q"))):
			if m.allowCancel {
				m.canceled = true
				m.done = true
				return m, tea.Quit
			}

		case key.Matches(msg, key.NewBinding(key.WithKeys("ctrl+c"))):
			m.canceled = true
			m.done = true
			return m, tea.Quit
		}

	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height
		// Calculate view height based on window size
		m.viewHeight = msg.Height - 10 // Leave space for title, headers, help
		if m.viewHeight < 5 {
			m.viewHeight = 5
		}
	}

	return m, nil
}

// View renders the table.
func (m *TableModel) View() string {
	if m.done {
		return ""
	}

	var b strings.Builder

	// Title
	if m.title != "" {
		b.WriteString(m.styles.Title.Render(m.title))
		b.WriteString("\n")
	}

	// Description
	if m.description != "" {
		b.WriteString(m.styles.Description.Render(m.description))
		b.WriteString("\n")
	}

	if m.title != "" || m.description != "" {
		b.WriteString("\n")
	}

	// Calculate column widths
	colWidths := m.calculateColumnWidths()

	// Headers
	if m.showHeaders && len(m.columns) > 0 {
		var headers []string
		for i, col := range m.columns {
			header := m.padOrTruncate(col.Title, colWidths[i])
			headers = append(headers, header)
		}
		b.WriteString(m.styles.Header.Render(strings.Join(headers, " │ ")))
		b.WriteString("\n")

		// Separator
		var sep []string
		for _, w := range colWidths {
			sep = append(sep, strings.Repeat("─", w))
		}
		b.WriteString(m.styles.Border.Render(strings.Join(sep, "─┼─")))
		b.WriteString("\n")
	}

	// Rows
	endRow := m.offset + m.viewHeight
	if endRow > len(m.rows) {
		endRow = len(m.rows)
	}

	for i := m.offset; i < endRow; i++ {
		row := m.rows[i]
		var cells []string

		for j, col := range m.columns {
			value := row.Values[col.ID]
			cell := m.padOrTruncate(value, colWidths[j])
			cells = append(cells, cell)
		}

		rowStr := strings.Join(cells, " │ ")

		// Apply cursor and selection styling
		if i == m.cursor {
			rowStr = m.styles.Cursor.Render("▶ ") + rowStr
		} else {
			rowStr = "  " + rowStr
		}

		if m.selected[i] {
			rowStr = m.styles.Selected.Render(rowStr)
		}

		b.WriteString(rowStr)
		b.WriteString("\n")
	}

	// Scroll indicator
	if len(m.rows) > m.viewHeight {
		scrollInfo := fmt.Sprintf("Row %d/%d", m.cursor+1, len(m.rows))
		b.WriteString("\n")
		b.WriteString(m.styles.Help.Render(scrollInfo))
	}

	// Help
	if m.showHelp {
		b.WriteString("\n")
		helpText := "↑/↓: navigate • space: select • pgup/pgdn: page • home/end: first/last"
		if m.allowCancel {
			helpText += " • esc: close"
		}
		b.WriteString(m.styles.Help.Render(helpText))
	}

	return b.String()
}

// calculateColumnWidths calculates optimal column widths.
func (m *TableModel) calculateColumnWidths() []int {
	widths := make([]int, len(m.columns))

	// Start with column header widths
	for i, col := range m.columns {
		if col.Width > 0 {
			widths[i] = col.Width
		} else {
			widths[i] = len(col.Title)
		}
	}

	// Check all visible rows
	endRow := m.offset + m.viewHeight
	if endRow > len(m.rows) {
		endRow = len(m.rows)
	}

	for i := m.offset; i < endRow; i++ {
		row := m.rows[i]
		for j, col := range m.columns {
			if col.Width == 0 { // Only adjust if width not fixed
				valueLen := len(row.Values[col.ID])
				if valueLen > widths[j] {
					widths[j] = valueLen
				}
			}
		}
	}

	// Apply maximum widths to fit screen
	totalWidth := 0
	for _, w := range widths {
		totalWidth += w
	}
	totalWidth += len(widths) * 3 // For separators

	if totalWidth > m.width-4 && m.width > 0 {
		// Scale down proportionally
		scale := float64(m.width-4) / float64(totalWidth)
		for i := range widths {
			widths[i] = int(float64(widths[i]) * scale)
			if widths[i] < 5 {
				widths[i] = 5
			}
		}
	}

	return widths
}

// padOrTruncate pads or truncates a string to the given width.
func (m *TableModel) padOrTruncate(s string, width int) string {
	if len(s) > width {
		if width > 3 {
			return s[:width-3] + "..."
		}
		return s[:width]
	}
	return s + strings.Repeat(" ", width-len(s))
}

// Result returns the table interaction result.
func (m *TableModel) Result() UIResponse {
	if m.canceled {
		return UIResponse{
			Type:     "table",
			Canceled: true,
		}
	}

	// Return selected rows
	var selected []interface{}
	for i := range m.selected {
		if i < len(m.rows) {
			selected = append(selected, m.rows[i])
		}
	}

	return UIResponse{
		Type:     "table",
		Selected: selected,
	}
}

// RunTable runs a table UI and returns the result.
func RunTable(req UIRequest, output io.Writer) (UIResponse, error) {
	model, err := NewTableModel(req)
	if err != nil {
		return UIResponse{}, err
	}

	p := tea.NewProgram(model, tea.WithOutput(output))
	start := time.Now()

	if _, err := p.Run(); err != nil {
		return UIResponse{}, fmt.Errorf("run table UI: %w", err)
	}

	result := model.Result()
	result.Duration = time.Since(start)
	return result, nil
}
