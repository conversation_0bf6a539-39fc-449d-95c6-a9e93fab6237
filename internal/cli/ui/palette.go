// Package ui 提供 Bubble Tea UI 組件
package ui

import (
	"fmt"
	"strings"

	"github.com/charmbracelet/bubbles/textinput"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

// Command 代表一個可執行的命令
type Command struct {
	Name        string
	Description string
	Shortcut    string
	Category    string
	Action      tea.Cmd
}

// CommandPalette 命令面板組件
type CommandPalette struct {
	// 基本屬性
	visible bool
	width   int
	height  int

	// 輸入和搜索
	input       textinput.Model
	commands    []Command
	filtered    []Command
	selectedIdx int

	// 樣式
	overlayStyle   lipgloss.Style
	containerStyle lipgloss.Style
	inputStyle     lipgloss.Style
	itemStyle      lipgloss.Style
	selectedStyle  lipgloss.Style
	descStyle      lipgloss.Style
	shortcutStyle  lipgloss.Style
	categoryStyle  lipgloss.Style
}

// NewCommandPalette 創建新的命令面板
func NewCommandPalette() CommandPalette {
	input := textinput.New()
	input.Placeholder = "輸入命令或描述..."
	input.CharLimit = 100
	input.Width = 50

	return CommandPalette{
		input:       input,
		commands:    []Command{},
		filtered:    []Command{},
		selectedIdx: 0,

		overlayStyle: lipgloss.NewStyle().
			Background(lipgloss.Color("0")),

		containerStyle: lipgloss.NewStyle().
			Background(lipgloss.Color("235")).
			Border(lipgloss.RoundedBorder()).
			BorderForeground(lipgloss.Color("99")).
			Padding(1, 2).
			Width(60).
			MaxHeight(20),

		inputStyle: lipgloss.NewStyle().
			Foreground(lipgloss.Color("230")).
			Background(lipgloss.Color("237")).
			Padding(0, 1).
			Width(56),

		itemStyle: lipgloss.NewStyle().
			Padding(0, 1),

		selectedStyle: lipgloss.NewStyle().
			Background(lipgloss.Color("62")).
			Foreground(lipgloss.Color("230")).
			Padding(0, 1),

		descStyle: lipgloss.NewStyle().
			Foreground(lipgloss.Color("244")).
			Italic(true),

		shortcutStyle: lipgloss.NewStyle().
			Foreground(lipgloss.Color("99")).
			Bold(true),

		categoryStyle: lipgloss.NewStyle().
			Foreground(lipgloss.Color("105")).
			Faint(true),
	}
}

// SetCommands 設置可用命令
func (cp *CommandPalette) SetCommands(commands []Command) {
	cp.commands = commands
	cp.filter()
}

// Show 顯示命令面板
func (cp *CommandPalette) Show() {
	cp.visible = true
	cp.input.Focus()
	cp.input.SetValue("")
	cp.filter()
}

// Hide 隱藏命令面板
func (cp *CommandPalette) Hide() {
	cp.visible = false
	cp.input.Blur()
	cp.selectedIdx = 0
}

// Toggle 切換顯示狀態
func (cp *CommandPalette) Toggle() {
	if cp.visible {
		cp.Hide()
	} else {
		cp.Show()
	}
}

// Update 處理消息
func (cp CommandPalette) Update(msg tea.Msg) (CommandPalette, tea.Cmd) {
	if !cp.visible {
		return cp, nil
	}

	var cmd tea.Cmd

	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+p", "esc":
			cp.Hide()
			return cp, nil

		case "enter":
			if cp.selectedIdx >= 0 && cp.selectedIdx < len(cp.filtered) {
				selected := cp.filtered[cp.selectedIdx]
				cp.Hide()
				return cp, selected.Action
			}

		case "up", "ctrl+k":
			if cp.selectedIdx > 0 {
				cp.selectedIdx--
			}

		case "down", "ctrl+j":
			if cp.selectedIdx < len(cp.filtered)-1 {
				cp.selectedIdx++
			}

		case "home", "ctrl+a":
			cp.selectedIdx = 0

		case "end", "ctrl+e":
			if len(cp.filtered) > 0 {
				cp.selectedIdx = len(cp.filtered) - 1
			}

		default:
			// 處理輸入
			var inputCmd tea.Cmd
			cp.input, inputCmd = cp.input.Update(msg)
			cp.filter()
			return cp, inputCmd
		}

	case tea.WindowSizeMsg:
		cp.width = msg.Width
		cp.height = msg.Height
	}

	cp.input, cmd = cp.input.Update(msg)
	return cp, cmd
}

// View 渲染命令面板
func (cp CommandPalette) View() string {
	if !cp.visible {
		return ""
	}

	// 構建命令列表
	var items []string
	maxVisible := 10 // 最多顯示10個項目

	start := 0
	if cp.selectedIdx >= maxVisible {
		start = cp.selectedIdx - maxVisible + 1
	}

	end := start + maxVisible
	if end > len(cp.filtered) {
		end = len(cp.filtered)
	}

	for i := start; i < end; i++ {
		cmd := cp.filtered[i]

		// 構建命令顯示
		var item strings.Builder

		// 命令名稱
		name := cmd.Name
		if i == cp.selectedIdx {
			item.WriteString(cp.selectedStyle.Render(name))
		} else {
			item.WriteString(cp.itemStyle.Render(name))
		}

		// 快捷鍵
		if cmd.Shortcut != "" {
			shortcut := fmt.Sprintf(" %s", cmd.Shortcut)
			item.WriteString(cp.shortcutStyle.Render(shortcut))
		}

		// 分類
		if cmd.Category != "" {
			category := fmt.Sprintf(" [%s]", cmd.Category)
			item.WriteString(cp.categoryStyle.Render(category))
		}

		// 描述（只在選中時顯示）
		if i == cp.selectedIdx && cmd.Description != "" {
			desc := fmt.Sprintf("\n  %s", cmd.Description)
			item.WriteString(cp.descStyle.Render(desc))
		}

		items = append(items, item.String())
	}

	// 構建完整視圖
	content := lipgloss.JoinVertical(
		lipgloss.Left,
		cp.inputStyle.Render(cp.input.View()),
		"",
		strings.Join(items, "\n"),
	)

	// 顯示結果統計
	if len(cp.filtered) > maxVisible {
		stats := fmt.Sprintf("\n\n顯示 %d/%d 個結果", maxVisible, len(cp.filtered))
		content += cp.descStyle.Render(stats)
	}

	// 將內容放在容器中
	panel := cp.containerStyle.Render(content)

	// 創建覆蓋層並定位面板
	return lipgloss.Place(
		cp.width, cp.height,
		lipgloss.Center, lipgloss.Center,
		panel,
		lipgloss.WithWhitespaceChars(" "),
		lipgloss.WithWhitespaceForeground(lipgloss.NoColor{}),
	)
}

// filter 根據輸入過濾命令
func (cp *CommandPalette) filter() {
	query := cp.input.Value()

	if query == "" {
		cp.filtered = cp.commands
		return
	}

	// 使用簡單的字串包含搜尋
	queryLower := strings.ToLower(query)
	cp.filtered = make([]Command, 0)

	for _, cmd := range cp.commands {
		// 組合名稱、描述和分類進行搜索
		searchStr := strings.ToLower(fmt.Sprintf("%s %s %s", cmd.Name, cmd.Description, cmd.Category))
		if strings.Contains(searchStr, queryLower) {
			cp.filtered = append(cp.filtered, cmd)
		}
	}

	// 重置選擇索引
	cp.selectedIdx = 0
}

// IsVisible 檢查是否可見
func (cp CommandPalette) IsVisible() bool {
	return cp.visible
}
