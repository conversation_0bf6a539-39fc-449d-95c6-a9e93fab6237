// Package ui provides user interface related functionality
package ui

import (
	"fmt"
	"strings"

	"github.com/charmbracelet/lipgloss"
)

// OutputFormatter formats AI output
type OutputFormatter struct {
	codeStyle    lipgloss.Style
	tableStyle   lipgloss.Style
	listStyle    lipgloss.Style
	headerStyle  lipgloss.Style
	warningStyle lipgloss.Style
}

// NewOutputFormatter creates new output formatter
func NewOutputFormatter() *OutputFormatter {
	return &OutputFormatter{
		codeStyle: lipgloss.NewStyle().
			Background(lipgloss.Color("235")).
			Foreground(lipgloss.Color("252")).
			Padding(1).
			MarginLeft(2),
		tableStyle: lipgloss.NewStyle().
			BorderStyle(lipgloss.NormalBorder()).
			BorderForeground(lipgloss.Color("240")),
		listStyle: lipgloss.NewStyle().
			MarginLeft(2),
		headerStyle: lipgloss.NewStyle().
			Bold(true).
			Foreground(lipgloss.Color("86")).
			MarginTop(1).
			MarginBottom(1),
		warningStyle: lipgloss.NewStyle().
			Foreground(lipgloss.Color("214")),
	}
}

// FormatOutput formats output content
func (f *OutputFormatter) FormatOutput(content string) string {
	// Remove all markdown formatting and return plain text
	// WHY: User requested no markdown formatting in CLI output

	// Remove markdown symbols but preserve content structure
	lines := strings.Split(content, "\n")
	var result []string
	inCodeBlock := false

	for _, line := range lines {
		trimmed := strings.TrimSpace(line)

		// Skip code block markers
		if strings.HasPrefix(trimmed, "```") {
			inCodeBlock = !inCodeBlock
			continue
		}

		// For code blocks, just add the line as-is
		if inCodeBlock {
			result = append(result, line)
			continue
		}

		// Remove headers markers but keep text
		if strings.HasPrefix(trimmed, "#") {
			text := strings.TrimLeft(trimmed, "# ")
			result = append(result, text)
			continue
		}

		// Convert markdown lists to simple lists
		if strings.HasPrefix(trimmed, "- ") || strings.HasPrefix(trimmed, "* ") {
			text := strings.TrimLeft(trimmed, "- *")
			// Preserve indentation
			indent := len(line) - len(strings.TrimLeft(line, " \t"))
			indentStr := strings.Repeat(" ", indent)
			result = append(result, indentStr+"• "+text)
			continue
		}

		// Remove bold markers
		cleanLine := line
		cleanLine = strings.ReplaceAll(cleanLine, "**", "")
		cleanLine = strings.ReplaceAll(cleanLine, "__", "")

		// Remove inline code markers
		cleanLine = strings.ReplaceAll(cleanLine, "`", "")

		result = append(result, cleanLine)
	}

	return strings.Join(result, "\n")
}

// FormatTable 格式化表格
func (f *OutputFormatter) FormatTable(headers []string, rows [][]string) string {
	// 計算每列的最大寬度
	colWidths := make([]int, len(headers))
	for i, header := range headers {
		colWidths[i] = len(header)
	}

	for _, row := range rows {
		for i, cell := range row {
			if i < len(colWidths) && len(cell) > colWidths[i] {
				colWidths[i] = len(cell)
			}
		}
	}

	// 構建表格
	var lines []string

	// 標題行
	headerCells := make([]string, len(headers))
	for i, header := range headers {
		headerCells[i] = fmt.Sprintf("%-*s", colWidths[i], header)
	}
	headerRow := "│ " + strings.Join(headerCells, " │ ") + " │"

	// 分隔線
	separatorCells := make([]string, len(headers))
	for i := range headers {
		separatorCells[i] = strings.Repeat("─", colWidths[i])
	}
	separator := "├─" + strings.Join(separatorCells, "─┼─") + "─┤"
	topBorder := "┌─" + strings.Join(separatorCells, "─┬─") + "─┐"
	bottomBorder := "└─" + strings.Join(separatorCells, "─┴─") + "─┘"

	lines = append(lines, topBorder)
	lines = append(lines, headerRow)
	lines = append(lines, separator)

	// 數據行
	for _, row := range rows {
		cells := make([]string, len(headers))
		for i, cell := range row {
			if i < len(cells) {
				cells[i] = fmt.Sprintf("%-*s", colWidths[i], cell)
			}
		}
		dataRow := "│ " + strings.Join(cells, " │ ") + " │"
		lines = append(lines, dataRow)
	}

	lines = append(lines, bottomBorder)

	return f.tableStyle.Render(strings.Join(lines, "\n"))
}

// FormatJSON 格式化 JSON 輸出
func (f *OutputFormatter) FormatJSON(content string) string {
	// Return JSON content with code styling
	return f.codeStyle.Render(content)
}

// FormatWarning 格式化警告訊息
func (f *OutputFormatter) FormatWarning(message string) string {
	return f.warningStyle.Render("⚠️  " + message)
}
