package tree

// This entire file is commented out because the knowledge package has been removed
// and this code depends on types from that package (knowledge.Node, knowledge.Graph, etc.)

/*
package tree

import (
	"fmt"
	"strings"
	"time"

	"github.com/charmbracelet/bubbles/textinput"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/koopa0/assistant-go/internal/knowledge"
)

// Search handles search functionality for the tree view
type Search struct {
	input       textinput.Model
	active      bool
	results     []*Node
	resultIndex int
	treeView    *ViewModel
}

// NewSearch creates a new tree search component
func NewSearch(treeView *ViewModel) *Search {
	ti := textinput.New()
	ti.Placeholder = "Search in tree..."
	ti.CharLimit = 50
	ti.Width = 30

	return &Search{
		input:    ti,
		treeView: treeView,
	}
}

// Update handles messages for tree search
func (ts *Search) Update(msg tea.Msg) (*Search, tea.Cmd) {
	if !ts.active {
		return ts, nil
	}

	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "enter":
			// Perform search
			query := ts.input.Value()
			if query != "" {
				// Search the tree starting from the root
				if ts.treeView != nil && ts.treeView.root != nil {
					ts.results = SearchTree(ts.treeView.root, query)
				}
				ts.resultIndex = 0

				// Navigate to first result
				// TODO: Implement ExpandToNode to expand tree to show the node
				// if len(ts.results) > 0 {
				//     ts.treeView.ExpandToNode(ts.results[0])
				// }
			}

		case "ctrl+n", "tab":
			// Next result
			if len(ts.results) > 0 {
				ts.resultIndex = (ts.resultIndex + 1) % len(ts.results)
				// TODO: Implement ExpandToNode
				// ts.treeView.ExpandToNode(ts.results[ts.resultIndex])
			}

		case "ctrl+p", "shift+tab":
			// Previous result
			if len(ts.results) > 0 {
				ts.resultIndex--
				if ts.resultIndex < 0 {
					ts.resultIndex = len(ts.results) - 1
				}
				// TODO: Implement ExpandToNode
				// ts.treeView.ExpandToNode(ts.results[ts.resultIndex])
			}

		case "esc":
			// Close search
			ts.active = false
			ts.input.Blur()
			ts.results = nil
			ts.resultIndex = 0
			return ts, nil

		case "ctrl+c":
			// Clear search
			ts.input.SetValue("")
			ts.results = nil
			ts.resultIndex = 0
		}
	}

	// Update input
	var cmd tea.Cmd
	ts.input, cmd = ts.input.Update(msg)
	cmds = append(cmds, cmd)

	return ts, tea.Batch(cmds...)
}

// View renders the search interface
func (ts *Search) View() string {
	if !ts.active {
		return ""
	}

	searchStyle := lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(lipgloss.Color("62")).
		Padding(0, 1)

	resultStyle := lipgloss.NewStyle().
		Foreground(lipgloss.Color("241"))

	var content strings.Builder
	content.WriteString(ts.input.View())

	if len(ts.results) > 0 {
		resultText := fmt.Sprintf("\n%d results found", len(ts.results))
		if len(ts.results) > 1 {
			resultText += fmt.Sprintf(" (%d/%d)", ts.resultIndex+1, len(ts.results))
		}
		content.WriteString(resultStyle.Render(resultText))
	} else if ts.input.Value() != "" && len(ts.results) == 0 {
		content.WriteString(resultStyle.Render("\nNo results found"))
	}

	content.WriteString("\n")
	content.WriteString(resultStyle.Render("Enter: search • Tab/Ctrl+N: next • Shift+Tab/Ctrl+P: prev • Esc: close"))

	return searchStyle.Render(content.String())
}

// Activate activates the search interface
func (ts *Search) Activate() tea.Cmd {
	ts.active = true
	ts.input.Focus()
	ts.input.SetValue("")
	ts.results = nil
	ts.resultIndex = 0
	return textinput.Blink
}

// IsActive returns whether search is active
func (ts *Search) IsActive() bool {
	return ts.active
}

// Filter provides filtering capabilities for the tree
type Filter struct {
	nodeTypes     map[knowledge.NodeType]bool
	minImportance float64
	dateRange     *DateRange
}

// DateRange represents a date range for filtering
type DateRange struct {
	Start time.Time
	End   time.Time
}

// NewFilter creates a new tree filter
func NewFilter() *Filter {
	return &Filter{
		nodeTypes:     make(map[knowledge.NodeType]bool),
		minImportance: 0,
	}
}

// ToggleNodeType toggles filtering for a specific node type
func (tf *Filter) ToggleNodeType(nodeType knowledge.NodeType) {
	if tf.nodeTypes[nodeType] {
		delete(tf.nodeTypes, nodeType)
	} else {
		tf.nodeTypes[nodeType] = true
	}
}

// SetImportanceThreshold sets the minimum importance threshold
func (tf *Filter) SetImportanceThreshold(threshold float64) {
	tf.minImportance = threshold
}

// SetDateRange sets the date range filter
func (tf *Filter) SetDateRange(start, end time.Time) {
	tf.dateRange = &DateRange{
		Start: start,
		End:   end,
	}
}

// ShouldShow determines if a node should be shown based on filters
func (tf *Filter) ShouldShow(node *knowledge.Node) bool {
	// Check node type filter
	if len(tf.nodeTypes) > 0 && !tf.nodeTypes[node.Type] {
		return false
	}

	// Check importance filter
	if node.Importance < tf.minImportance {
		return false
	}

	// Check date range filter
	if tf.dateRange != nil {
		if node.CreatedAt.Before(tf.dateRange.Start) || node.CreatedAt.After(tf.dateRange.End) {
			return false
		}
	}

	return true
}

// ApplyToTree applies filters to a tree, hiding filtered nodes
func (tf *Filter) ApplyToTree(root *Node) *Node {
	// Create a filtered copy of the tree
	filteredRoot := &Node{
		Node:     root.Node,
		Expanded: root.Expanded,
		Level:    root.Level,
	}

	tf.filterNode(root, filteredRoot)
	return filteredRoot
}

// filterNode recursively filters nodes
func (tf *Filter) filterNode(source, target *Node) {
	for _, child := range source.Children {
		if child.Node.ID.String() == "00000000-0000-0000-0000-000000000000" {
			// Always include category nodes
			newChild := &Node{
				Node:     child.Node,
				Expanded: child.Expanded,
				Level:    child.Level,
				Parent:   target,
			}
			target.Children = append(target.Children, newChild)
			tf.filterNode(child, newChild)
		} else if tf.ShouldShow(child.Node) {
			newChild := &Node{
				Node:     child.Node,
				Expanded: child.Expanded,
				Level:    child.Level,
				Parent:   target,
			}
			target.Children = append(target.Children, newChild)
			tf.filterNode(child, newChild)
		}
	}
}
*/
