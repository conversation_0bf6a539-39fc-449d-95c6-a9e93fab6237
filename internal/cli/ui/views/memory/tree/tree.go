// Package tree provides tree visualization for the knowledge graph
package tree

// This entire file is commented out because the knowledge package has been removed
// and this code depends on types from that package (knowledge.Node, knowledge.Graph, etc.)

/*
import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/google/uuid"

	"github.com/koopa0/assistant-go/internal/knowledge"
)

// Node represents a node in the tree structure
type Node struct {
	Node     *knowledge.Node
	Children []*Node
	Parent   *Node
	Expanded bool
	Level    int
	IsLast   bool // For pretty printing
}

// Builder builds a tree structure from the knowledge graph
type Builder struct {
	graph      *knowledge.Graph
	visitedIDs map[uuid.UUID]bool
	maxDepth   int
}

// NewBuilder creates a new tree builder
func NewBuilder(graph *knowledge.Graph) *Builder {
	return &Builder{
		graph:      graph,
		visitedIDs: make(map[uuid.UUID]bool),
		maxDepth:   5, // Default max depth to prevent deep recursion
	}
}

// BuildTree builds a tree structure organized by node types
func (tb *Builder) BuildTree(ctx context.Context) (*Node, error) {
	// Create root node
	root := &Node{
		Node: &knowledge.Node{
			Name: "Knowledge Graph",
			Type: "root",
		},
		Expanded: true,
		Level:    0,
	}

	// Get all nodes from the graph
	nodes, err := tb.graph.GetAllNodes(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get nodes: %w", err)
	}

	// Group nodes by type
	nodesByType := make(map[knowledge.NodeType][]*knowledge.Node)
	for _, node := range nodes {
		nodesByType[node.Type] = append(nodesByType[node.Type], node)
	}

	// Create type category nodes
	typeOrder := []knowledge.NodeType{
		knowledge.TypeFact,
		knowledge.TypePreference,
		knowledge.TypeGoal,
		knowledge.TypeTask,
		knowledge.TypeEvent,
		knowledge.TypeConcept,
		knowledge.TypePerson,
		knowledge.TypeRelation,
	}

	for i, nodeType := range typeOrder {
		nodes := nodesByType[nodeType]
		if len(nodes) == 0 {
			continue
		}

		// Create category node
		categoryNode := &Node{
			Node: &knowledge.Node{
				Name: string(nodeType) + "s",
				Type: "category",
			},
			Parent:   root,
			Expanded: false,
			Level:    1,
			IsLast:   i == len(typeOrder)-1,
		}
		root.Children = append(root.Children, categoryNode)

		// Sort nodes by importance (descending) and then by name
		sort.Slice(nodes, func(i, j int) bool {
			if nodes[i].Importance != nodes[j].Importance {
				return nodes[i].Importance > nodes[j].Importance
			}
			return nodes[i].Name < nodes[j].Name
		})

		// Add nodes to category
		for j, node := range nodes {
			treeNode := &Node{
				Node:   node,
				Parent: categoryNode,
				Level:  2,
				IsLast: j == len(nodes)-1,
			}
			categoryNode.Children = append(categoryNode.Children, treeNode)

			// Add relations as children
			tb.visitedIDs[node.ID] = true
			if err := tb.addRelations(ctx, treeNode, 3); err != nil {
				return nil, err
			}
		}
	}

	return root, nil
}

// addRelations adds related nodes as children
func (tb *Builder) addRelations(ctx context.Context, parent *Node, level int) error {
	if level > tb.maxDepth {
		return nil
	}

	relations, err := tb.graph.GetRelations(ctx, parent.Node.ID)
	if err != nil {
		return fmt.Errorf("failed to get relations: %w", err)
	}

	for i, relation := range relations {
		// Skip if we've already visited this node
		if tb.visitedIDs[relation.TargetID] {
			continue
		}

		targetNode, err := tb.graph.GetNode(ctx, relation.TargetID)
		if err != nil {
			continue // Skip if node not found
		}

		relNode := &Node{
			Node: &knowledge.Node{
				ID:         relation.ID,
				Name:       fmt.Sprintf("%s → %s", relation.Type, targetNode.Name),
				Type:       "relation",
				Content:    targetNode.Content,
				Importance: relation.Strength,
			},
			Parent: parent,
			Level:  level,
			IsLast: i == len(relations)-1,
		}
		parent.Children = append(parent.Children, relNode)

		tb.visitedIDs[relation.TargetID] = true
	}

	return nil
}

// SearchTree searches for nodes matching the query
func (tb *Builder) SearchTree(ctx context.Context, query string) ([]*Node, error) {
	nodes, err := tb.graph.SearchNodes(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to search nodes: %w", err)
	}

	var results []*Node
	for _, node := range nodes {
		results = append(results, &Node{
			Node:     node,
			Expanded: false,
			Level:    0,
		})
	}

	return results, nil
}

// GetNodePath returns the path from root to a specific node
func (tb *Builder) GetNodePath(ctx context.Context, nodeID uuid.UUID) ([]*Node, error) {
	node, err := tb.graph.GetNode(ctx, nodeID)
	if err != nil {
		return nil, fmt.Errorf("failed to get node: %w", err)
	}

	// For now, just return the node itself
	// In a more complex implementation, we'd trace back through relations
	return []*Node{{
		Node:     node,
		Expanded: false,
		Level:    0,
	}}, nil
}

// FormatTree formats the tree for display
func FormatTree(node *Node, indent string, isLast bool) []string {
	var lines []string

	// Prepare the connector
	connector := "├── "
	if isLast {
		connector = "└── "
	}

	// Format current node
	nodeStr := fmt.Sprintf("%s%s%s", indent, connector, formatNode(node.Node))
	lines = append(lines, nodeStr)

	// Format children if expanded
	if node.Expanded && len(node.Children) > 0 {
		childIndent := indent
		if isLast {
			childIndent += "    "
		} else {
			childIndent += "│   "
		}

		for i, child := range node.Children {
			childLines := FormatTree(child, childIndent, i == len(node.Children)-1)
			lines = append(lines, childLines...)
		}
	}

	return lines
}

// formatNode formats a single node for display
func formatNode(node *knowledge.Node) string {
	if node == nil {
		return "[nil]"
	}

	// Format based on node type
	switch node.Type {
	case "root":
		return fmt.Sprintf("🌐 %s", node.Name)
	case "category":
		return fmt.Sprintf("📁 %s", node.Name)
	case "relation":
		return fmt.Sprintf("🔗 %s (%.2f)", node.Name, node.Importance)
	default:
		icon := getNodeIcon(node.Type)
		importance := ""
		if node.Importance > 0 {
			importance = fmt.Sprintf(" [%.2f]", node.Importance)
		}
		return fmt.Sprintf("%s %s%s", icon, node.Name, importance)
	}
}

// getNodeIcon returns an icon for a node type
func getNodeIcon(nodeType knowledge.NodeType) string {
	icons := map[knowledge.NodeType]string{
		knowledge.TypeFact:       "📌",
		knowledge.TypePreference: "⭐",
		knowledge.TypeGoal:       "🎯",
		knowledge.TypeTask:       "✅",
		knowledge.TypeEvent:      "📅",
		knowledge.TypeConcept:    "💡",
		knowledge.TypePerson:     "👤",
		knowledge.TypeRelation:   "🔗",
	}

	if icon, ok := icons[nodeType]; ok {
		return icon
	}
	return "•"
}

// ExpandPath expands all nodes in the path
func ExpandPath(root *Node, path []uuid.UUID) {
	// Implementation would traverse the tree and expand nodes in the path
	// For now, this is a placeholder
}

// FindNode finds a node by ID in the tree
func FindNode(root *Node, id uuid.UUID) *Node {
	if root.Node != nil && root.Node.ID == id {
		return root
	}

	for _, child := range root.Children {
		if found := FindNode(child, id); found != nil {
			return found
		}
	}

	return nil
}
*/
