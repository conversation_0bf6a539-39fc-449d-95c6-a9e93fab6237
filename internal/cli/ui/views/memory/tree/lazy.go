package tree

// This entire file is commented out because the knowledge package has been removed
// and this code depends on types from that package (knowledge.Node, knowledge.Graph, etc.)

/*
package tree

import (
	"context"
	"sync"

	"github.com/google/uuid"
	"github.com/koopa0/assistant-go/internal/knowledge"
)

// LazyNode represents a node that loads children on demand
type LazyNode struct {
	*Node
	childrenLoaded bool
	loadError      error
	mu             sync.Mutex
}

// LazyBuilder builds trees with lazy loading
type LazyBuilder struct {
	*Builder
	loadedNodes sync.Map // map[uuid.UUID]*LazyNode
	graph       *knowledge.Graph
	memService  interface{} // Add memory service if needed
	logger      interface{} // Add logger if needed
}

// NewLazyBuilder creates a lazy tree builder
func NewLazyBuilder(tb *Builder) *LazyBuilder {
	return &LazyBuilder{
		Builder: tb,
		graph:   tb.graph,
	}
}

// BuildLazyTree builds a tree with lazy loading
func (ltb *LazyBuilder) BuildLazyTree(ctx context.Context) (*LazyNode, error) {
	// Create root node
	root := &LazyNode{
		Node: &Node{
			Node: &knowledge.Node{
				ID:   uuid.Nil,
				Name: "Memory Graph",
				Type: knowledge.TypeConcept,
			},
			Expanded: true,
			Level:    0,
		},
		childrenLoaded: false,
	}

	// Load only first level (categories)
	if err := ltb.loadChildren(ctx, root); err != nil {
		return nil, err
	}

	return root, nil
}

// loadChildren loads children for a node
func (ltb *LazyBuilder) loadChildren(ctx context.Context, node *LazyNode) error {
	node.mu.Lock()
	defer node.mu.Unlock()

	if node.childrenLoaded {
		return nil
	}

	// For root node, create categories
	if node.Node.Node.ID == uuid.Nil {
		categories := ltb.createCategoryNodes()
		for _, cat := range categories {
			lazyChild := &LazyNode{
				Node:           cat,
				childrenLoaded: false,
			}
			lazyChild.Parent = node.Node
			node.Children = append(node.Children, lazyChild.Node)
			ltb.loadedNodes.Store(cat.Node.ID, lazyChild)
		}
	} else if node.Node.Node.ID.String() != "00000000-0000-0000-0000-000000000000" {
		// For regular nodes, load related nodes
		edges, err := ltb.graph.GetNodeEdges(ctx, node.Node.Node.ID)
		if err != nil {
			node.loadError = err
			return err
		}

		// Add child nodes
		for _, edge := range edges {
			relatedID := edge.ToID
			if edge.ToID == node.Node.Node.ID {
				relatedID = edge.FromID
			}

			// Check if already loaded
			if _, exists := ltb.loadedNodes.Load(relatedID); exists {
				continue
			}

			// Load the related node
			relatedNode, err := ltb.graph.GetNode(ctx, relatedID)
			if err != nil {
				// Skip nodes that can't be loaded
				continue
			}

			childNode := &Node{
				Node:   relatedNode,
				Level:  node.Level + 1,
				Parent: node.Node,
			}

			lazyChild := &LazyNode{
				Node:           childNode,
				childrenLoaded: false,
			}

			node.Children = append(node.Children, childNode)
			ltb.loadedNodes.Store(relatedID, lazyChild)
		}
	} else {
		// For category nodes, load nodes of that type
		nodeType := node.Node.Node.Type
		nodes, err := ltb.graph.SearchNodes(ctx, "", &nodeType, 100)
		if err != nil {
			node.loadError = err
			return err
		}

		for _, n := range nodes {
			childNode := &Node{
				Node:   n,
				Level:  node.Level + 1,
				Parent: node.Node,
			}

			lazyChild := &LazyNode{
				Node:           childNode,
				childrenLoaded: false,
			}

			node.Children = append(node.Children, childNode)
			ltb.loadedNodes.Store(n.ID, lazyChild)
		}
	}

	node.childrenLoaded = true
	ltb.updateIsLastFlags(node.Node)
	return nil
}

// ExpandNode expands a node by loading its children
func (ltb *LazyBuilder) ExpandNode(ctx context.Context, nodeID uuid.UUID) error {
	if val, ok := ltb.loadedNodes.Load(nodeID); ok {
		if lazyNode, ok := val.(*LazyNode); ok {
			return ltb.loadChildren(ctx, lazyNode)
		}
	}
	return nil
}

// GetLazyNode retrieves a lazy node by ID
func (ltb *LazyBuilder) GetLazyNode(nodeID uuid.UUID) *LazyNode {
	if val, ok := ltb.loadedNodes.Load(nodeID); ok {
		if lazyNode, ok := val.(*LazyNode); ok {
			return lazyNode
		}
	}
	return nil
}

// createCategoryNodes creates category nodes for organizing by type
func (ltb *LazyBuilder) createCategoryNodes() []*Node {
	typeOrder := []knowledge.NodeType{
		knowledge.TypePerson,
		knowledge.TypeEvent,
		knowledge.TypeTask,
		knowledge.TypeFact,
		knowledge.TypePreference,
		knowledge.TypeSkill,
		knowledge.TypeLocation,
		knowledge.TypeConcept,
		knowledge.TypeNote,
	}

	var categories []*Node
	for _, nodeType := range typeOrder {
		cat := &Node{
			Node: &knowledge.Node{
				ID:   uuid.New(), // Generate unique ID for category
				Name: string(nodeType),
				Type: nodeType,
			},
			Level:    1,
			Expanded: false,
		}
		categories = append(categories, cat)
	}
	return categories
}

// updateIsLastFlags updates the IsLast flag for proper tree rendering
func (ltb *LazyBuilder) updateIsLastFlags(node *Node) {
	if node == nil || len(node.Children) == 0 {
		return
	}

	// Mark all children as not last except the actual last one
	for i, child := range node.Children {
		child.IsLast = i == len(node.Children)-1
		// Recursively update children
		ltb.updateIsLastFlags(child)
	}
}

// Cache provides caching for tree operations
type Cache struct {
	nodeCache    sync.Map // map[uuid.UUID]*knowledge.Node
	edgeCache    sync.Map // map[uuid.UUID][]*knowledge.Edge
	searchCache  sync.Map // map[string][]*knowledge.Node
	mu           sync.RWMutex
	maxCacheSize int
	currentSize  int
}

// NewCache creates a new tree cache
func NewCache(maxSize int) *Cache {
	return &Cache{
		maxCacheSize: maxSize,
	}
}

// GetNode retrieves a node from cache
func (tc *Cache) GetNode(id uuid.UUID) (*knowledge.Node, bool) {
	if val, ok := tc.nodeCache.Load(id); ok {
		if node, ok := val.(*knowledge.Node); ok {
			return node, true
		}
	}
	return nil, false
}

// PutNode stores a node in cache
func (tc *Cache) PutNode(node *knowledge.Node) {
	tc.mu.Lock()
	defer tc.mu.Unlock()

	// Simple size check (could be improved with LRU)
	if tc.currentSize >= tc.maxCacheSize {
		tc.evictOldest()
	}

	tc.nodeCache.Store(node.ID, node)
	tc.currentSize++
}

// GetEdges retrieves edges from cache
func (tc *Cache) GetEdges(nodeID uuid.UUID) ([]*knowledge.Edge, bool) {
	if val, ok := tc.edgeCache.Load(nodeID); ok {
		if edges, ok := val.([]*knowledge.Edge); ok {
			return edges, true
		}
	}
	return nil, false
}

// PutEdges stores edges in cache
func (tc *Cache) PutEdges(nodeID uuid.UUID, edges []*knowledge.Edge) {
	tc.edgeCache.Store(nodeID, edges)
}

// evictOldest removes oldest entries (simplified implementation)
func (tc *Cache) evictOldest() {
	// In a real implementation, this would use LRU or similar
	// For now, just clear a portion of the cache
	count := 0
	tc.nodeCache.Range(func(key, value interface{}) bool {
		if count < tc.maxCacheSize/10 {
			tc.nodeCache.Delete(key)
			count++
			return true
		}
		return false
	})
	tc.currentSize -= count
}

// Clear clears all caches
func (tc *Cache) Clear() {
	tc.nodeCache = sync.Map{}
	tc.edgeCache = sync.Map{}
	tc.searchCache = sync.Map{}
	tc.currentSize = 0
}
*/
