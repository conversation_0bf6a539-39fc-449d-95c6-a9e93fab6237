package tree

// This entire file is commented out because the knowledge package has been removed
// and this code depends on types from that package (knowledge.Node, knowledge.Graph, etc.)

/*
// Package memory provides tree view UI for the knowledge graph
package tree

import (
	"context"
	"fmt"
	"strings"

	"github.com/charmbracelet/bubbles/key"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"

	"github.com/koopa0/assistant-go/internal/knowledge"
	"github.com/koopa0/assistant-go/internal/platform/logger"
)

// ViewModel represents the tree view state
type ViewModel struct {
	// Dependencies
	graph  *knowledge.Graph
	logger logger.Logger

	// Tree data
	root           *Node
	flattenedNodes []*Node
	cursor         int

	// UI state
	width  int
	height int
	offset int // Vertical scroll offset

	// Selected node details
	selectedNode *knowledge.Node

	// Key bindings
	keyMap treeKeyMap

	// Search
	searchMode  bool
	searchQuery string

	// Error handling
	err error
}

// treeKeyMap defines key bindings for the tree view
type treeKeyMap struct {
	Up           key.Binding
	Down         key.Binding
	Left         key.Binding
	Right        key.Binding
	ExpandToggle key.Binding
	Select       key.Binding
	Search       key.Binding
	Quit         key.Binding
	Help         key.Binding
}

// defaultTreeKeyMap returns default key bindings
func defaultTreeKeyMap() treeKeyMap {
	return treeKeyMap{
		Up: key.NewBinding(
			key.WithKeys("up", "k"),
			key.WithHelp("↑/k", "up"),
		),
		Down: key.NewBinding(
			key.WithKeys("down", "j"),
			key.WithHelp("↓/j", "down"),
		),
		Left: key.NewBinding(
			key.WithKeys("left", "h"),
			key.WithHelp("←/h", "collapse"),
		),
		Right: key.NewBinding(
			key.WithKeys("right", "l"),
			key.WithHelp("→/l", "expand"),
		),
		ExpandToggle: key.NewBinding(
			key.WithKeys("enter", " "),
			key.WithHelp("enter/space", "expand/collapse"),
		),
		Select: key.NewBinding(
			key.WithKeys("tab"),
			key.WithHelp("tab", "view details"),
		),
		Search: key.NewBinding(
			key.WithKeys("/"),
			key.WithHelp("/", "search"),
		),
		Quit: key.NewBinding(
			key.WithKeys("q", "esc"),
			key.WithHelp("q/esc", "quit"),
		),
		Help: key.NewBinding(
			key.WithKeys("?"),
			key.WithHelp("?", "help"),
		),
	}
}

// NewViewModel creates a new tree view model
func NewViewModel(graph *knowledge.Graph, logger logger.Logger) *ViewModel {
	return &ViewModel{
		graph:  graph,
		logger: logger,
		keyMap: defaultTreeKeyMap(),
	}
}

// Init initializes the tree view
func (m *ViewModel) Init() tea.Cmd {
	return m.loadTree
}

// Update handles messages
func (m *ViewModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height
		return m, nil

	case treeLoadedMsg:
		if msg.err != nil {
			m.err = msg.err
			return m, nil
		}
		m.root = msg.root
		m.flattenedNodes = FlattenTree(m.root)
		return m, nil

	case tea.KeyMsg:
		if m.searchMode {
			return m.handleSearchInput(msg)
		}
		return m.handleNavigation(msg)
	}

	return m, nil
}

// View renders the tree view
func (m *ViewModel) View() string {
	if m.err != nil {
		return m.renderError()
	}

	if m.root == nil {
		return m.renderLoading()
	}

	if m.searchMode {
		return m.renderSearch()
	}

	return m.renderTree()
}

// handleNavigation handles keyboard navigation
func (m *ViewModel) handleNavigation(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	switch {
	case key.Matches(msg, m.keyMap.Up):
		if m.cursor > 0 {
			m.cursor--
			m.ensureVisible()
		}

	case key.Matches(msg, m.keyMap.Down):
		if m.cursor < len(m.flattenedNodes)-1 {
			m.cursor++
			m.ensureVisible()
		}

	case key.Matches(msg, m.keyMap.Left):
		if m.cursor < len(m.flattenedNodes) {
			node := m.flattenedNodes[m.cursor]
			if node.Expanded && len(node.Children) > 0 {
				ExpandCollapseNode(node)
				m.flattenedNodes = FlattenTree(m.root)
			} else if node.Parent != nil {
				// Move to parent
				for i, n := range m.flattenedNodes {
					if n == node.Parent {
						m.cursor = i
						break
					}
				}
			}
		}

	case key.Matches(msg, m.keyMap.Right):
		if m.cursor < len(m.flattenedNodes) {
			node := m.flattenedNodes[m.cursor]
			if !node.Expanded && len(node.Children) > 0 {
				ExpandCollapseNode(node)
				m.flattenedNodes = FlattenTree(m.root)
			}
		}

	case key.Matches(msg, m.keyMap.ExpandToggle):
		if m.cursor < len(m.flattenedNodes) {
			node := m.flattenedNodes[m.cursor]
			if len(node.Children) > 0 {
				ExpandCollapseNode(node)
				m.flattenedNodes = FlattenTree(m.root)
			}
		}

	case key.Matches(msg, m.keyMap.Select):
		if m.cursor < len(m.flattenedNodes) {
			node := m.flattenedNodes[m.cursor]
			if node.Node != nil && node.Node.Type != "root" && node.Node.Type != "category" {
				m.selectedNode = node.Node
			}
		}

	case key.Matches(msg, m.keyMap.Search):
		m.searchMode = true
		m.searchQuery = ""

	case key.Matches(msg, m.keyMap.Quit):
		return m, tea.Quit
	}

	return m, nil
}

// handleSearchInput handles search mode input
func (m *ViewModel) handleSearchInput(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	switch msg.Type {
	case tea.KeyEscape:
		m.searchMode = false
		m.searchQuery = ""
		return m, nil

	case tea.KeyEnter:
		if m.searchQuery != "" {
			results := SearchTree(m.root, m.searchQuery)
			if len(results) > 0 {
				// Expand path to first result
				m.expandPathToNode(results[0])
				m.flattenedNodes = FlattenTree(m.root)

				// Move cursor to first result
				for i, node := range m.flattenedNodes {
					if node == results[0] {
						m.cursor = i
						m.ensureVisible()
						break
					}
				}
			}
		}
		m.searchMode = false
		return m, nil

	case tea.KeyBackspace:
		if len(m.searchQuery) > 0 {
			m.searchQuery = m.searchQuery[:len(m.searchQuery)-1]
		}

	case tea.KeyRunes:
		m.searchQuery += string(msg.Runes)
	}

	return m, nil
}

// expandPathToNode expands all nodes in the path to the target
func (m *ViewModel) expandPathToNode(target *Node) {
	path := []*Node{}
	current := target
	for current != nil {
		path = append([]*Node{current}, path...)
		current = current.Parent
	}

	for _, node := range path {
		if !node.Expanded && len(node.Children) > 0 {
			node.Expanded = true
		}
	}
}

// ensureVisible ensures the cursor is visible
func (m *ViewModel) ensureVisible() {
	visibleHeight := m.height - 5 // Leave room for header and footer

	if m.cursor < m.offset {
		m.offset = m.cursor
	} else if m.cursor >= m.offset+visibleHeight {
		m.offset = m.cursor - visibleHeight + 1
	}
}

// renderTree renders the main tree view
func (m *ViewModel) renderTree() string {
	var b strings.Builder

	// Header
	headerStyle := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("86")).
		MarginBottom(1)

	stats := GetTreeStats(m.root)
	header := fmt.Sprintf("🌳 Knowledge Graph Tree View - %d nodes, %d expanded",
		stats.TotalNodes-1, // Exclude root
		stats.ExpandedNodes-1)
	b.WriteString(headerStyle.Render(header))
	b.WriteString("\n\n")

	// Tree content
	visibleHeight := m.height - 5
	treeStyle := lipgloss.NewStyle().PaddingLeft(2)

	for i := m.offset; i < len(m.flattenedNodes) && i < m.offset+visibleHeight; i++ {
		node := m.flattenedNodes[i]
		line := m.renderNode(node, i == m.cursor)
		b.WriteString(treeStyle.Render(line))
		b.WriteString("\n")
	}

	// Footer with help
	footerStyle := lipgloss.NewStyle().
		Foreground(lipgloss.Color("241")).
		MarginTop(1)
	footer := "↑↓ Navigate • ←→ Collapse/Expand • Enter Toggle • / Search • Tab Details • q Quit"
	b.WriteString(footerStyle.Render(footer))

	return b.String()
}

// renderNode renders a single tree node
func (m *ViewModel) renderNode(node *Node, selected bool) string {
	var prefix string

	// Tree structure
	prefix = GetTreeLine(node)

	// Expansion indicator
	if len(node.Children) > 0 {
		if node.Expanded {
			prefix += "▼ "
		} else {
			prefix += "▶ "
		}
	} else if node.Level > 0 {
		prefix += "  "
	}

	// Node content
	content := FormatNodeForDisplay(node)

	// Apply styles
	style := lipgloss.NewStyle()

	if selected {
		style = style.
			Background(lipgloss.Color("237")).
			Foreground(lipgloss.Color("255"))
	} else if node.Node != nil {
		switch node.Node.Type {
		case "root":
			style = style.Bold(true).Foreground(lipgloss.Color("86"))
		case "category":
			style = style.Bold(true).Foreground(lipgloss.Color("33"))
		case "person":
			style = style.Foreground(lipgloss.Color("214"))
		case "event":
			style = style.Foreground(lipgloss.Color("141"))
		case "task":
			style = style.Foreground(lipgloss.Color("203"))
		case "fact":
			style = style.Foreground(lipgloss.Color("108"))
		default:
			style = style.Foreground(lipgloss.Color("252"))
		}
	}

	// Highlight search matches
	if m.searchQuery != "" && strings.Contains(strings.ToLower(content), strings.ToLower(m.searchQuery)) {
		highlightStyle := lipgloss.NewStyle().
			Background(lipgloss.Color("226")).
			Foreground(lipgloss.Color("232"))

		lowerContent := strings.ToLower(content)
		lowerQuery := strings.ToLower(m.searchQuery)
		idx := strings.Index(lowerContent, lowerQuery)
		if idx >= 0 {
			before := content[:idx]
			match := content[idx : idx+len(m.searchQuery)]
			after := content[idx+len(m.searchQuery):]
			content = before + highlightStyle.Render(match) + after
		}
	}

	return prefix + style.Render(content)
}

// renderLoading renders loading state
func (m *ViewModel) renderLoading() string {
	return lipgloss.NewStyle().
		Foreground(lipgloss.Color("243")).
		Italic(true).
		Render("Loading knowledge graph...")
}

// renderError renders error state
func (m *ViewModel) renderError() string {
	return lipgloss.NewStyle().
		Foreground(lipgloss.Color("196")).
		Bold(true).
		Render(fmt.Sprintf("Error: %v", m.err))
}

// renderSearch renders search mode
func (m *ViewModel) renderSearch() string {
	searchStyle := lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(lipgloss.Color("86")).
		Padding(1, 2)

	content := fmt.Sprintf("Search: %s_", m.searchQuery)
	return searchStyle.Render(content)
}

// loadTree loads the tree data
func (m *ViewModel) loadTree() tea.Msg {
	ctx := context.Background()
	builder := NewBuilder(m.graph)
	root, err := builder.BuildTree(ctx)
	return treeLoadedMsg{root: root, err: err}
}

// GetSelectedNode returns the currently selected node
func (m *ViewModel) GetSelectedNode() *knowledge.Node {
	return m.selectedNode
}

// Messages

type treeLoadedMsg struct {
	root *Node
	err  error
}
*/
