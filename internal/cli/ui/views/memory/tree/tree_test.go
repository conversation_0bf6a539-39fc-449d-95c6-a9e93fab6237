package tree

// This entire file is commented out because the knowledge package has been removed
// and this code depends on types from that package (knowledge.Node, knowledge.Graph, etc.)

/*
package tree

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/koopa0/assistant-go/internal/knowledge"
	"github.com/koopa0/assistant-go/internal/memory"
	"github.com/koopa0/assistant-go/internal/test"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestBuilder(t *testing.T) {
	// Setup test context with database
	tc := test.NewTestContext(t).WithDatabase()

	ctx := context.Background()
	memService := memory.New(tc.DB, tc.Logger)

	t.Run("BuildTree creates root with categories", func(t *testing.T) {
		// Create a knowledge graph from memory service
		graph := memService.Graph()

		builder := NewBuilder(graph)
		root, err := builder.BuildTree(ctx)
		require.NoError(t, err)
		assert.NotNil(t, root)
		assert.Equal(t, "Knowledge Graph", root.Node.Name)
		assert.True(t, root.Expanded)

		// Should have category nodes if there are any nodes in the graph
		// Note: root.Children may be empty if there are no nodes in the graph
	})

	t.Run("BuildTree with nodes creates categories", func(t *testing.T) {
		// Create some test nodes
		nodes := []*knowledge.Node{
			{
				Name:       "Alice Smith",
				Type:       knowledge.TypePerson,
				Content:    "Software engineer",
				Importance: 0.8,
			},
			{
				Name:       "Meeting Notes",
				Type:       knowledge.TypeFact,
				Content:    "Project kickoff meeting",
				Importance: 0.6,
			},
		}

		graph := memService.Graph()
		for _, n := range nodes {
			_, err := graph.CreateNode(ctx, n)
			require.NoError(t, err)
		}

		builder := NewBuilder(graph)
		root, err := builder.BuildTree(ctx)
		require.NoError(t, err)
		assert.NotNil(t, root)

		// Should have children representing node type categories
		assert.NotEmpty(t, root.Children)
	})

	t.Run("SearchTree finds nodes", func(t *testing.T) {
		// Create test nodes
		nodes := []*knowledge.Node{
			{
				Name:       "Alice Smith",
				Type:       knowledge.TypePerson,
				Content:    "Works at tech company",
				Importance: 0.7,
			},
			{
				Name:       "Bob Johnson",
				Type:       knowledge.TypePerson,
				Content:    "Neighbor",
				Importance: 0.5,
			},
			{
				Name:       "Coffee preferences",
				Type:       knowledge.TypePreference,
				Content:    "Likes espresso",
				Importance: 0.6,
			},
		}

		graph := memService.Graph()
		for _, n := range nodes {
			_, err := graph.CreateNode(ctx, n)
			require.NoError(t, err)
		}

		builder := NewBuilder(graph)
		root, err := builder.BuildTree(ctx)
		require.NoError(t, err)

		// Search for "Alice"
		results := SearchTree(root, "Alice")
		assert.NotEmpty(t, results)
		found := false
		for _, r := range results {
			if r.Node.Name == "Alice Smith" {
				found = true
				break
			}
		}
		assert.True(t, found, "Should find Alice Smith")

		// Search for "preferences"
		results = SearchTree(root, "preferences")
		assert.NotEmpty(t, results)
		found = false
		for _, r := range results {
			if r.Node.Name == "Coffee preferences" {
				found = true
				break
			}
		}
		assert.True(t, found, "Should find Coffee preferences")
	})

	t.Run("GetTreeStats provides statistics", func(t *testing.T) {
		graph := memService.Graph()
		builder := NewBuilder(graph)
		root, err := builder.BuildTree(ctx)
		require.NoError(t, err)

		stats := GetTreeStats(root)
		assert.GreaterOrEqual(t, stats.TotalNodes, 0)
		assert.NotNil(t, stats.NodesByType)
		assert.GreaterOrEqual(t, stats.MaxDepth, 0)
	})
}

func TestNode(t *testing.T) {
	t.Run("Node structure", func(t *testing.T) {
		node := &Node{
			Node: &knowledge.Node{
				ID:   uuid.New(),
				Name: "Test",
				Type: knowledge.TypeFact,
			},
			Level:    1,
			Expanded: false,
			IsLast:   true,
		}

		assert.Equal(t, "Test", node.Node.Name)
		assert.Equal(t, 1, node.Level)
		assert.False(t, node.Expanded)
		assert.True(t, node.IsLast)
	})

	t.Run("ExpandCollapseNode toggles expansion", func(t *testing.T) {
		node := &Node{
			Node: &knowledge.Node{
				Name: "Test",
			},
			Expanded: false,
		}

		ExpandCollapseNode(node)
		assert.True(t, node.Expanded)

		ExpandCollapseNode(node)
		assert.False(t, node.Expanded)
	})
}

func TestLazyBuilder(t *testing.T) {
	// Setup test context with database
	tc := test.NewTestContext(t).WithDatabase()

	ctx := context.Background()
	memService := memory.New(tc.DB, tc.Logger)

	t.Run("LazyBuilder creates lazy tree", func(t *testing.T) {
		graph := memService.Graph()
		builder := NewBuilder(graph)
		lazyBuilder := NewLazyBuilder(builder)

		root, err := lazyBuilder.BuildLazyTree(ctx)
		require.NoError(t, err)
		assert.NotNil(t, root)
		assert.Equal(t, "Knowledge Graph", root.Node.Node.Name)
		assert.True(t, root.Node.Expanded)
	})
}

func TestUtilityFunctions(t *testing.T) {
	t.Run("GetTreeLine formats node display", func(t *testing.T) {
		node := &Node{
			Node: &knowledge.Node{
				Name: "Test Node",
			},
			Level:  2,
			IsLast: true,
		}

		line := GetTreeLine(node)
		// GetTreeLine only returns the tree prefix, not the node name
		assert.Contains(t, line, "└")
	})

	t.Run("FlattenTree returns expanded nodes", func(t *testing.T) {
		root := &Node{
			Node:     &knowledge.Node{Name: "Root"},
			Expanded: true, // Must be expanded to include children
			Children: []*Node{
				{
					Node:     &knowledge.Node{Name: "Child1"},
					Expanded: true, // Must be expanded to include its children
					Children: []*Node{
						{
							Node:     &knowledge.Node{Name: "Grandchild1"},
							Expanded: false,
						},
					},
				},
				{
					Node:     &knowledge.Node{Name: "Child2"},
					Expanded: false,
				},
			},
		}

		flat := FlattenTree(root)
		assert.Len(t, flat, 4) // Root + 2 children + 1 grandchild (all are included because parents are expanded)

		names := make([]string, len(flat))
		for i, n := range flat {
			names[i] = n.Node.Name
		}
		assert.Contains(t, names, "Root")
		assert.Contains(t, names, "Child1")
		assert.Contains(t, names, "Child2")
		assert.Contains(t, names, "Grandchild1")
	})
}
*/
