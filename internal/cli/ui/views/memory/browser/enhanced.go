package browser

// This entire file is commented out because the knowledge package has been removed
// and this code depends on types from that package (knowledge.Node, knowledge.Graph, etc.)

/*
// Package browser provides an enhanced browser with tabbed views
package browser

import (
	"context"
	"strings"

	"github.com/charmbracelet/bubbles/key"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/jackc/pgx/v5/pgxpool"

	"github.com/koopa0/assistant-go/internal/cli/ui/views/memory/tree"
	"github.com/koopa0/assistant-go/internal/knowledge"
	"github.com/koopa0/assistant-go/internal/memory"
	"github.com/koopa0/assistant-go/internal/platform/logger"
)

// ViewMode represents different view modes
type ViewMode int

const (
	ViewModeList ViewMode = iota
	ViewModeTree
)

// EnhancedBrowser combines list and tree views with tabs
type EnhancedBrowser struct {
	// Dependencies
	deps BrowserDependencies

	// View models
	listBrowser *Browser
	treeView    *tree.ViewModel

	// State
	activeView ViewMode
	width      int
	height     int

	// Shared components
	graph      *knowledge.Graph
	memService *memory.Service

	// Key bindings
	keyMap enhancedKeyMap
}

// enhancedKeyMap defines key bindings for the enhanced browser
type enhancedKeyMap struct {
	Tab  key.Binding
	Quit key.Binding
}

// defaultEnhancedKeyMap returns default key bindings
func defaultEnhancedKeyMap() enhancedKeyMap {
	return enhancedKeyMap{
		Tab: key.NewBinding(
			key.WithKeys("tab"),
			key.WithHelp("tab", "switch view"),
		),
		Quit: key.NewBinding(
			key.WithKeys("ctrl+c", "ctrl+q"),
			key.WithHelp("ctrl+c", "quit"),
		),
	}
}

// NewEnhancedBrowser creates a new enhanced browser with tabs
func NewEnhancedBrowser(ctx context.Context, deps BrowserDependencies, initialQuery string) *EnhancedBrowser {
	// Create memory service
	memService := memory.CreateMemoryService(ctx, deps.DB, deps.Logger)

	// Create knowledge graph
	graph := knowledge.NewGraph(deps.DB, deps.Logger)

	// Create sub-views
	listBrowser := NewBrowser(deps, initialQuery)
	treeView := tree.NewViewModel(graph, deps.Logger)

	return &EnhancedBrowser{
		deps:        deps,
		listBrowser: listBrowser,
		treeView:    treeView,
		activeView:  ViewModeList,
		graph:       graph,
		memService:  memService,
		keyMap:      defaultEnhancedKeyMap(),
	}
}

// Init initializes the enhanced browser
func (m *EnhancedBrowser) Init() tea.Cmd {
	return tea.Batch(
		m.listBrowser.Init(),
		m.treeView.Init(),
	)
}

// Update handles messages
func (m *EnhancedBrowser) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height - 3 // Leave room for tabs

		// Update sub-views
		resizedMsg := tea.WindowSizeMsg{
			Width:  msg.Width,
			Height: m.height,
		}

		listModel, listCmd := m.listBrowser.Update(resizedMsg)
		if browser, ok := listModel.(*Browser); ok {
			m.listBrowser = browser
		}
		cmds = append(cmds, listCmd)

		treeModel, treeCmd := m.treeView.Update(resizedMsg)
		if treeVM, ok := treeModel.(*tree.ViewModel); ok {
			m.treeView = treeVM
		}
		cmds = append(cmds, treeCmd)

		return m, tea.Batch(cmds...)

	case tea.KeyMsg:
		// Global key handling
		switch {
		case key.Matches(msg, m.keyMap.Tab):
			// Switch view
			if m.activeView == ViewModeList {
				m.activeView = ViewModeTree
			} else {
				m.activeView = ViewModeList
			}
			return m, nil

		case key.Matches(msg, m.keyMap.Quit):
			return m, tea.Quit
		}
	}

	// Route to active view
	switch m.activeView {
	case ViewModeList:
		listModel, cmd := m.listBrowser.Update(msg)
		if browser, ok := listModel.(*Browser); ok {
			m.listBrowser = browser
		}
		return m, cmd

	case ViewModeTree:
		treeModel, cmd := m.treeView.Update(msg)
		if treeVM, ok := treeModel.(*tree.ViewModel); ok {
			m.treeView = treeVM
		}
		return m, cmd
	}

	return m, nil
}

// View renders the enhanced browser
func (m *EnhancedBrowser) View() string {
	if m.width == 0 || m.height == 0 {
		return "Loading..."
	}

	var b strings.Builder

	// Render tabs
	b.WriteString(m.renderTabs())
	b.WriteString("\n")

	// Render active view
	switch m.activeView {
	case ViewModeList:
		b.WriteString(m.listBrowser.View())
	case ViewModeTree:
		b.WriteString(m.treeView.View())
	}

	return b.String()
}

// renderTabs renders the tab bar
func (m *EnhancedBrowser) renderTabs() string {
	tabStyle := lipgloss.NewStyle().
		Border(lipgloss.NormalBorder(), false, false, true, false).
		BorderForeground(lipgloss.Color("240"))

	activeTabStyle := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("86")).
		Background(lipgloss.Color("235")).
		Padding(0, 2)

	inactiveTabStyle := lipgloss.NewStyle().
		Foreground(lipgloss.Color("243")).
		Padding(0, 2)

	var tabs []string

	// List tab
	listTab := " 📋 List View "
	if m.activeView == ViewModeList {
		listTab = activeTabStyle.Render(listTab)
	} else {
		listTab = inactiveTabStyle.Render(listTab)
	}
	tabs = append(tabs, listTab)

	// Tree tab
	treeTab := " 🌳 Tree View "
	if m.activeView == ViewModeTree {
		treeTab = activeTabStyle.Render(treeTab)
	} else {
		treeTab = inactiveTabStyle.Render(treeTab)
	}
	tabs = append(tabs, treeTab)

	// Join tabs
	tabBar := lipgloss.JoinHorizontal(lipgloss.Top, tabs...)

	// Add help text
	helpStyle := lipgloss.NewStyle().
		Foreground(lipgloss.Color("241"))
	help := helpStyle.Render("Tab: Switch View • Ctrl+C: Quit")

	// Create spacer to push help to the right
	spacerWidth := m.width - lipgloss.Width(tabBar) - lipgloss.Width(help) - 4
	if spacerWidth < 0 {
		spacerWidth = 0
	}
	spacer := lipgloss.NewStyle().Width(spacerWidth).Render(" ")

	// Combine tabs, spacer, and help
	fullBar := lipgloss.JoinHorizontal(lipgloss.Top, tabBar, spacer, help)
	return tabStyle.Render(fullBar)
}

// GetSelectedNode returns the currently selected node from either view
func (m *EnhancedBrowser) GetSelectedNode() *knowledge.Node {
	switch m.activeView {
	case ViewModeList:
		return m.listBrowser.currentNode
	case ViewModeTree:
		return m.treeView.GetSelectedNode()
	}
	return nil
}

// Run starts the enhanced browser
func (m *EnhancedBrowser) Run() error {
	p := tea.NewProgram(m, tea.WithAltScreen())
	_, err := p.Run()
	return err
}

// RunEnhancedBrowser is a convenience function to run the enhanced browser
func RunEnhancedBrowser(ctx context.Context, db *pgxpool.Pool, logger logger.Logger, initialQuery string) error {
	deps := BrowserDependencies{
		DB:     db,
		Logger: logger,
	}

	browser := NewEnhancedBrowser(ctx, deps, initialQuery)
	p := tea.NewProgram(browser, tea.WithAltScreen())

	_, err := p.Run()
	return err
}
*/
