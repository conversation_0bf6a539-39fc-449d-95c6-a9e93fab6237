package browser

// This entire file is commented out because the knowledge package has been removed
// and this code depends on types from that package (knowledge.Node, knowledge.Graph, etc.)

/*
package browser

import (
	"testing"

	"github.com/koopa0/assistant-go/internal/knowledge"
	"github.com/koopa0/assistant-go/internal/test"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestBrowser(t *testing.T) {
	// Setup test context with database
	tc := test.NewTestContext(t).WithDatabase()

	// Create browser dependencies
	deps := BrowserDependencies{
		DB:     tc.DB,
		Logger: tc.Logger,
	}

	// Create browser
	browser := NewBrowser(deps, "")
	require.NotNil(t, browser)

	// Test initialization
	assert.Equal(t, stateList, browser.state)
	assert.NotNil(t, browser.memService)
	assert.NotNil(t, browser.list)
	assert.NotNil(t, browser.searchInput)
	assert.NotNil(t, browser.viewport)
}

func TestMemoryItem(t *testing.T) {
	node := &knowledge.Node{
		Name:    "Test Memory",
		Type:    knowledge.TypeFact,
		Content: "This is a test memory content that is quite long and should be truncated in the description",
	}

	item := memoryItem{node: node}

	// Test title
	assert.Equal(t, "Test Memory", item.Title())

	// Test description truncation
	desc := item.Description()
	assert.Contains(t, desc, "[fact]")
	assert.Contains(t, desc, "...")
	assert.True(t, len(desc) < 100)

	// Test filter value
	filter := item.FilterValue()
	assert.Contains(t, filter, "Test Memory")
	assert.Contains(t, filter, "fact")
	assert.Contains(t, filter, "test memory content")
}

func TestFormatMetadata(t *testing.T) {
	tests := []struct {
		name     string
		metadata knowledge.NodeMetadata
		expected []string
	}{
		{
			name: "FactMetadata",
			metadata: knowledge.FactMetadata{
				Source:      "test",
				Category:    "general",
				Confidence:  0.9,
				LearnedFrom: "unit test",
			},
			expected: []string{
				"Source: test",
				"Category: general",
				"Confidence: 0.90",
				"Learned From: unit test",
			},
		},
		{
			name: "PreferenceMetadata",
			metadata: knowledge.PreferenceMetadata{
				Category:   "food",
				Strength:   "strong",
				Confidence: 0.95,
			},
			expected: []string{
				"Category: food",
				"Strength: strong",
				"Confidence: 0.95",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := formatMetadata(tt.metadata)
			for _, exp := range tt.expected {
				assert.Contains(t, result, exp)
			}
		})
	}
}
*/
