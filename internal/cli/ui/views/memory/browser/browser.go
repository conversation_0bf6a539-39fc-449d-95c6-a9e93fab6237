package browser

// This entire file is commented out because the knowledge package has been removed
// and this code depends on types from that package (knowledge.Node, knowledge.Graph, etc.)

/*
package browser

import (
	"context"
	"fmt"
	"strings"

	"github.com/charmbracelet/bubbles/help"
	"github.com/charmbracelet/bubbles/key"
	"github.com/charmbracelet/bubbles/list"
	"github.com/charmbracelet/bubbles/textinput"
	"github.com/charmbracelet/bubbles/viewport"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/koopa0/assistant-go/internal/knowledge"
	"github.com/koopa0/assistant-go/internal/memory"
	"github.com/koopa0/assistant-go/internal/platform/logger"
)

// Browser state constants
const (
	stateList = iota
	stateDetail
	stateSearch
)

// memoryItem implements list.Item interface
type memoryItem struct {
	node *knowledge.Node
}

func (i memoryItem) Title() string {
	return i.node.Name
}

func (i memoryItem) Description() string {
	// Format type and timestamp
	typeStr := string(i.node.Type)
	timeStr := i.node.CreatedAt.Format("2006-01-02 15:04")

	// Truncate content for preview
	content := strings.TrimSpace(i.node.Content)
	if len(content) > 60 {
		content = content[:57] + "..."
	}

	return fmt.Sprintf("[%s] %s | %s", typeStr, timeStr, content)
}

func (i memoryItem) FilterValue() string {
	// Allow filtering by name, type, and content
	return fmt.Sprintf("%s %s %s", i.node.Name, i.node.Type, i.node.Content)
}

// BrowserDependencies provides required dependencies for the memory browser
type BrowserDependencies struct {
	DB     *pgxpool.Pool
	Logger logger.Logger
}

// Browser is the main model for the memory browser UI
type Browser struct {
	memService *memory.Service
	logger     logger.Logger

	// UI state
	state  int
	width  int
	height int

	// List view
	list     list.Model
	items    []list.Item
	allNodes []*knowledge.Node

	// Detail view
	viewport    viewport.Model
	currentNode *knowledge.Node
	edges       []*knowledge.Edge

	// Search
	searchInput textinput.Model
	searchQuery string

	// Help
	help help.Model
	keys keyMap

	// Loading state
	loading bool
	err     error
}

// keyMap defines key bindings
type keyMap struct {
	Up      key.Binding
	Down    key.Binding
	Enter   key.Binding
	Back    key.Binding
	Search  key.Binding
	Refresh key.Binding
	Delete  key.Binding
	Help    key.Binding
	Quit    key.Binding
}

// ShortHelp returns key bindings for the short help view
func (k keyMap) ShortHelp() []key.Binding {
	return []key.Binding{k.Help, k.Quit}
}

// FullHelp returns key bindings for the full help view
func (k keyMap) FullHelp() [][]key.Binding {
	return [][]key.Binding{
		{k.Up, k.Down, k.Enter},
		{k.Search, k.Refresh, k.Back},
		{k.Help, k.Quit},
	}
}

var defaultKeyMap = keyMap{
	Up: key.NewBinding(
		key.WithKeys("up", "k"),
		key.WithHelp("↑/k", "up"),
	),
	Down: key.NewBinding(
		key.WithKeys("down", "j"),
		key.WithHelp("↓/j", "down"),
	),
	Enter: key.NewBinding(
		key.WithKeys("enter"),
		key.WithHelp("enter", "view details"),
	),
	Back: key.NewBinding(
		key.WithKeys("esc", "b"),
		key.WithHelp("esc/b", "back"),
	),
	Search: key.NewBinding(
		key.WithKeys("/", "s"),
		key.WithHelp("//s", "search"),
	),
	Refresh: key.NewBinding(
		key.WithKeys("r"),
		key.WithHelp("r", "refresh"),
	),
	Delete: key.NewBinding(
		key.WithKeys("d"),
		key.WithHelp("d", "delete"),
	),
	Help: key.NewBinding(
		key.WithKeys("?"),
		key.WithHelp("?", "toggle help"),
	),
	Quit: key.NewBinding(
		key.WithKeys("q", "ctrl+c"),
		key.WithHelp("q", "quit"),
	),
}

// New creates a new memory browser instance
func NewBrowser(deps BrowserDependencies, initialQuery string) *Browser {
	// Create memory service
	memSvc := memory.New(deps.DB, deps.Logger)

	// Setup list
	items := []list.Item{}
	delegate := list.NewDefaultDelegate()
	delegate.Styles.SelectedTitle = delegate.Styles.SelectedTitle.
		Foreground(lipgloss.Color("170")).
		BorderForeground(lipgloss.Color("170"))

	l := list.New(items, delegate, 0, 0)
	l.Title = "Memory Browser"
	l.SetStatusBarItemName("memory", "memories")
	l.SetFilteringEnabled(true)
	l.Styles.Title = l.Styles.Title.
		Background(lipgloss.Color("62")).
		Foreground(lipgloss.Color("230"))

	// Setup search input
	ti := textinput.New()
	ti.Placeholder = "Search memories..."
	ti.CharLimit = 100
	ti.Width = 50
	if initialQuery != "" {
		ti.SetValue(initialQuery)
	}

	// Setup viewport for detail view
	vp := viewport.New(0, 0)

	return &Browser{
		memService:  memSvc,
		logger:      deps.Logger,
		state:       stateList,
		list:        l,
		items:       items,
		searchInput: ti,
		searchQuery: initialQuery,
		viewport:    vp,
		help:        help.New(),
		keys:        defaultKeyMap,
	}
}

// Init implements tea.Model
func (m *Browser) Init() tea.Cmd {
	// Load initial data
	return m.loadMemories()
}

// Update implements tea.Model
func (m *Browser) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height
		m.list.SetSize(msg.Width, msg.Height-2)
		m.viewport.Width = msg.Width
		m.viewport.Height = msg.Height - 4

	case tea.KeyMsg:
		// Global keys work in any state
		switch {
		case key.Matches(msg, m.keys.Quit):
			return m, tea.Quit

		case key.Matches(msg, m.keys.Help):
			m.help.ShowAll = !m.help.ShowAll
			return m, nil
		}

		// State-specific keys
		switch m.state {
		case stateList:
			switch {
			case key.Matches(msg, m.keys.Search):
				m.state = stateSearch
				m.searchInput.Focus()
				return m, textinput.Blink

			case key.Matches(msg, m.keys.Enter):
				if item, ok := m.list.SelectedItem().(memoryItem); ok {
					m.currentNode = item.node
					cmds = append(cmds, m.loadNodeDetails())
					m.state = stateDetail
				}

			case key.Matches(msg, m.keys.Refresh):
				cmds = append(cmds, m.loadMemories())

			case key.Matches(msg, m.keys.Delete):
				// TODO: Implement delete functionality
				m.err = fmt.Errorf("delete not implemented yet")
			}

		case stateDetail:
			switch {
			case key.Matches(msg, m.keys.Back):
				m.state = stateList
				m.currentNode = nil
				m.edges = nil
			}

		case stateSearch:
			switch msg.String() {
			case "enter":
				m.searchQuery = m.searchInput.Value()
				m.state = stateList
				cmds = append(cmds, m.loadMemories())

			case "esc":
				m.state = stateList
				m.searchInput.Blur()
			}
		}

	case memoriesLoadedMsg:
		m.loading = false
		m.allNodes = msg.nodes
		m.updateList()

	case nodeDetailsLoadedMsg:
		m.edges = msg.edges
		m.updateDetailView()

	case errMsg:
		m.err = msg.err
		m.loading = false
	}

	// Update components
	var cmd tea.Cmd
	switch m.state {
	case stateList:
		m.list, cmd = m.list.Update(msg)
		cmds = append(cmds, cmd)

	case stateDetail:
		m.viewport, cmd = m.viewport.Update(msg)
		cmds = append(cmds, cmd)

	case stateSearch:
		m.searchInput, cmd = m.searchInput.Update(msg)
		cmds = append(cmds, cmd)
	}

	return m, tea.Batch(cmds...)
}

// View implements tea.Model
func (m *Browser) View() string {
	if m.err != nil {
		return fmt.Sprintf("\n  Error: %v\n\n  Press 'q' to quit.", m.err)
	}

	if m.loading {
		return "\n  Loading memories..."
	}

	switch m.state {
	case stateSearch:
		return m.searchView()
	case stateDetail:
		return m.detailView()
	default:
		return m.listView()
	}
}

// listView renders the list of memories
func (m *Browser) listView() string {
	return fmt.Sprintf("%s\n%s", m.list.View(), m.helpView())
}

// searchView renders the search input
func (m *Browser) searchView() string {
	return fmt.Sprintf(
		"\n  Search memories:\n\n  %s\n\n  Press enter to search, esc to cancel",
		m.searchInput.View(),
	)
}

// detailView renders the detail view for a node
func (m *Browser) detailView() string {
	if m.currentNode == nil {
		return "No node selected"
	}

	titleStyle := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("170")).
		MarginBottom(1)

	title := titleStyle.Render(m.currentNode.Name)

	return fmt.Sprintf("%s\n%s\n%s", title, m.viewport.View(), m.helpView())
}

// helpView renders the help text
func (m *Browser) helpView() string {
	return "\n" + m.help.View(m.keys)
}

// updateList updates the list items based on current nodes
func (m *Browser) updateList() {
	items := make([]list.Item, len(m.allNodes))
	for i, node := range m.allNodes {
		items[i] = memoryItem{node: node}
	}
	m.items = items
	m.list.SetItems(items)
}

// updateDetailView updates the viewport with node details
func (m *Browser) updateDetailView() {
	if m.currentNode == nil {
		return
	}

	// Build detail content
	var b strings.Builder

	// Basic info
	infoStyle := lipgloss.NewStyle().Foreground(lipgloss.Color("241"))
	labelStyle := lipgloss.NewStyle().Bold(true).Foreground(lipgloss.Color("33"))

	b.WriteString(labelStyle.Render("Type: "))
	b.WriteString(infoStyle.Render(string(m.currentNode.Type)))
	b.WriteString("\n")

	b.WriteString(labelStyle.Render("ID: "))
	b.WriteString(infoStyle.Render(m.currentNode.ID.String()))
	b.WriteString("\n")

	b.WriteString(labelStyle.Render("Created: "))
	b.WriteString(infoStyle.Render(m.currentNode.CreatedAt.Format("2006-01-02 15:04:05")))
	b.WriteString("\n")

	b.WriteString(labelStyle.Render("Updated: "))
	b.WriteString(infoStyle.Render(m.currentNode.UpdatedAt.Format("2006-01-02 15:04:05")))
	b.WriteString("\n")

	b.WriteString(labelStyle.Render("Importance: "))
	b.WriteString(infoStyle.Render(fmt.Sprintf("%.2f", m.currentNode.Importance)))
	b.WriteString("\n")

	b.WriteString(labelStyle.Render("Access Count: "))
	b.WriteString(infoStyle.Render(fmt.Sprintf("%d", m.currentNode.AccessCount)))
	b.WriteString("\n\n")

	// Content
	if m.currentNode.Content != "" {
		b.WriteString(labelStyle.Render("Content:"))
		b.WriteString("\n")
		b.WriteString(m.currentNode.Content)
		b.WriteString("\n\n")
	}

	// Observations
	if len(m.currentNode.Observations) > 0 {
		b.WriteString(labelStyle.Render("Observations:"))
		b.WriteString("\n")
		for _, obs := range m.currentNode.Observations {
			b.WriteString("  • ")
			b.WriteString(obs)
			b.WriteString("\n")
		}
		b.WriteString("\n")
	}

	// Metadata
	b.WriteString(labelStyle.Render("Metadata:"))
	b.WriteString("\n")
	b.WriteString(formatMetadata(m.currentNode.Metadata))
	b.WriteString("\n")

	// Edges
	if len(m.edges) > 0 {
		b.WriteString(labelStyle.Render("Relationships:"))
		b.WriteString("\n")
		for _, edge := range m.edges {
			direction := "→"
			relatedID := edge.ToID
			if edge.ToID == m.currentNode.ID {
				direction = "←"
				relatedID = edge.FromID
			}
			b.WriteString(fmt.Sprintf("  %s %s (%s, weight: %.2f)\n",
				direction, relatedID, edge.Type, edge.Weight))
		}
	}

	m.viewport.SetContent(b.String())
}

// formatMetadata formats node metadata for display
func formatMetadata(metadata knowledge.NodeMetadata) string {
	var b strings.Builder

	switch meta := metadata.(type) {
	case knowledge.FactMetadata:
		b.WriteString(fmt.Sprintf("  Source: %s\n", meta.Source))
		b.WriteString(fmt.Sprintf("  Category: %s\n", meta.Category))
		b.WriteString(fmt.Sprintf("  Confidence: %.2f\n", meta.Confidence))
		b.WriteString(fmt.Sprintf("  Learned From: %s\n", meta.LearnedFrom))
		if meta.ValidUntil != nil {
			b.WriteString(fmt.Sprintf("  Valid Until: %s\n", meta.ValidUntil.Format("2006-01-02")))
		}
		if meta.Context != "" {
			b.WriteString(fmt.Sprintf("  Context: %s\n", meta.Context))
		}

	case knowledge.PreferenceMetadata:
		b.WriteString(fmt.Sprintf("  Category: %s\n", meta.Category))
		b.WriteString(fmt.Sprintf("  Strength: %s\n", meta.Strength))
		b.WriteString(fmt.Sprintf("  Confidence: %.2f\n", meta.Confidence))
		b.WriteString(fmt.Sprintf("  Updated: %s\n", meta.UpdatedAt.Format("2006-01-02 15:04:05")))

	case knowledge.PersonMetadata:
		b.WriteString(fmt.Sprintf("  Relationship: %s\n", meta.Relationship))
		if meta.FirstMet != nil {
			b.WriteString(fmt.Sprintf("  First Met: %s\n", meta.FirstMet.Format("2006-01-02")))
		}
		if meta.LastContact != nil {
			b.WriteString(fmt.Sprintf("  Last Contact: %s\n", meta.LastContact.Format("2006-01-02")))
		}

	case knowledge.EventMetadata:
		b.WriteString(fmt.Sprintf("  Start Time: %s\n", meta.StartTime.Format("2006-01-02 15:04")))
		if meta.EndTime != nil {
			b.WriteString(fmt.Sprintf("  End Time: %s\n", meta.EndTime.Format("2006-01-02 15:04")))
		}
		if meta.Location != "" {
			b.WriteString(fmt.Sprintf("  Location: %s\n", meta.Location))
		}
		if len(meta.Attendees) > 0 {
			b.WriteString(fmt.Sprintf("  Attendees: %s\n", strings.Join(meta.Attendees, ", ")))
		}
		if meta.EventType != "" {
			b.WriteString(fmt.Sprintf("  Type: %s\n", meta.EventType))
		}
		if meta.Status != "" {
			b.WriteString(fmt.Sprintf("  Status: %s\n", meta.Status))
		}

	case knowledge.TaskMetadata:
		b.WriteString(fmt.Sprintf("  Status: %s\n", meta.Status))
		b.WriteString(fmt.Sprintf("  Priority: %s\n", meta.Priority))
		if meta.DueDate != nil {
			b.WriteString(fmt.Sprintf("  Due: %s\n", meta.DueDate.Format("2006-01-02")))
		}
		if meta.CompletedAt != nil {
			b.WriteString(fmt.Sprintf("  Completed: %s\n", meta.CompletedAt.Format("2006-01-02")))
		}

	default:
		b.WriteString("  (raw metadata not displayed)\n")
	}

	return b.String()
}

// Run starts the memory browser
func (m *Browser) Run() error {
	p := tea.NewProgram(m, tea.WithAltScreen())
	_, err := p.Run()
	return err
}

// Commands and messages

type memoriesLoadedMsg struct {
	nodes []*knowledge.Node
}

type nodeDetailsLoadedMsg struct {
	edges []*knowledge.Edge
}

type errMsg struct {
	err error
}

// loadMemories loads memories based on current search query
func (m *Browser) loadMemories() tea.Cmd {
	return func() tea.Msg {
		m.loading = true
		ctx := context.Background()

		var nodes []*knowledge.Node
		var err error

		if m.searchQuery != "" {
			// Search with query
			nodes, err = m.memService.SearchMemories(ctx, m.searchQuery)
		} else {
			// Get all nodes (up to 100)
			nodes, err = m.memService.Graph().SearchNodes(ctx, "", nil, 100)
		}

		if err != nil {
			return errMsg{err: err}
		}

		return memoriesLoadedMsg{nodes: nodes}
	}
}

// loadNodeDetails loads edges for the current node
func (m *Browser) loadNodeDetails() tea.Cmd {
	return func() tea.Msg {
		if m.currentNode == nil {
			return nil
		}

		ctx := context.Background()
		edges, err := m.memService.Graph().GetNodeEdges(ctx, m.currentNode.ID)
		if err != nil {
			return errMsg{err: err}
		}

		return nodeDetailsLoadedMsg{edges: edges}
	}
}
*/
