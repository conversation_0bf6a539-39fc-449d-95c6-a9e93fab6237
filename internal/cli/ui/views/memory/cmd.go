package memory

// This entire file is commented out because it depends on the browser package
// which has been commented out due to the removal of the knowledge package

/*
import (
	"context"

	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/koopa0/assistant-go/internal/cli/ui/views/memory/browser"
	"github.com/koopa0/assistant-go/internal/platform/logger"
)

// RunBrows<PERSON> starts the memory browser with default settings
func RunBrowser(ctx context.Context, db *pgxpool.Pool, log logger.Logger, query string) error {
	deps := browser.BrowserDependencies{
		DB:     db,
		Logger: log,
	}

	b := browser.NewBrowser(deps, query)
	return b.Run()
}

// BrowserOptions configures the browser behavior
type BrowserOptions struct {
	// UseEnhanced uses the enhanced browser with tree view
	UseEnhanced bool

	// InitialQuery sets the initial search query
	InitialQuery string

	// StartInTreeView starts in tree view instead of list view
	StartInTreeView bool

	// MaxTreeDepth sets the maximum depth for tree expansion
	MaxTreeDepth int
}

// DefaultBrowserOptions returns default browser options
func DefaultBrowserOptions() BrowserOptions {
	return BrowserOptions{
		UseEnhanced:     true, // Use enhanced browser by default
		InitialQuery:    "",
		StartInTreeView: false,
		MaxTreeDepth:    5,
	}
}

// RunBrowserWithOptions starts the browser with custom options
func RunBrowserWithOptions(ctx context.Context, db *pgxpool.Pool, log logger.Logger, opts BrowserOptions) error {
	deps := browser.BrowserDependencies{
		DB:     db,
		Logger: log,
	}

	if opts.UseEnhanced {
		// Use the enhanced browser from browser package
		return browser.RunEnhancedBrowser(ctx, db, log, opts.InitialQuery)
	}

	// Use basic browser
	b := browser.NewBrowser(deps, opts.InitialQuery)
	return b.Run()
}
*/
