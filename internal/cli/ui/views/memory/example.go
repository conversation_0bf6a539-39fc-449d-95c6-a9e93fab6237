// Package memory provides memory browser UI components
// This file shows example usage of the tree visualization
package memory

/*
Example usage in a CLI command:

```go
package cmd

import (
    "context"

    "github.com/spf13/cobra"
    "github.com/koopa0/assistant-go/internal/cli/ui/views/memory"
    "github.com/koopa0/assistant-go/internal/storage/database"
)

var memoryBrowseCmd = &cobra.Command{
    Use:   "browse",
    Short: "Browse memory in tree view",
    RunE: func(cmd *cobra.Command, args []string) error {
        // Get database connection
        db, err := database.Connect(cfg.Database.URL)
        if err != nil {
            return err
        }
        defer db.Close()

        // Parse flags
        useTree, _ := cmd.Flags().GetBool("tree")
        query, _ := cmd.Flags().GetString("query")

        // Configure options
        opts := memory.BrowserOptions{
            UseEnhanced:     true,
            InitialQuery:    query,
            StartInTreeView: useTree,
            MaxTreeDepth:    5,
        }

        // Run browser
        return memory.RunBrowserWithOptions(context.Background(), db, logger, opts)
    },
}

func init() {
    memoryCmd.AddCommand(memoryBrowseCmd)
    memoryBrowseCmd.Flags().BoolP("tree", "t", false, "Start in tree view")
    memoryBrowseCmd.Flags().StringP("query", "q", "", "Initial search query")
}
```

Key features of the tree visualization:

1. **Hierarchical Organization**:
   - Nodes are grouped by type (People, Events, Tasks, etc.)
   - Related nodes are shown as children
   - Automatic cycle detection prevents infinite recursion

2. **Lazy Loading**:
   - Only loads visible nodes initially
   - Children are loaded on demand when expanding
   - Improves performance for large graphs

3. **Navigation**:
   - Arrow keys or hjkl for movement
   - Enter/Space to expand/collapse nodes
   - Tab to switch between list and tree views
   - / to search within the tree

4. **Search**:
   - Search across all nodes in the tree
   - Automatically expands path to search results
   - Navigate between multiple results

5. **Filtering**:
   - Filter by node type
   - Filter by importance threshold
   - Filter by date range

6. **Performance Optimizations**:
   - Caching of loaded nodes and edges
   - Flattened tree structure for efficient rendering
   - Limited depth to prevent excessive recursion

The tree view provides an intuitive way to explore the knowledge graph,
making it easy to understand relationships between different pieces of
information stored in memory.
*/
