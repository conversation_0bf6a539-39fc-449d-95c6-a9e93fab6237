package memory

// This entire file is commented out because the knowledge package has been removed
// and this code depends on types from that package (knowledge.Node, knowledge.Graph, etc.)

/*
package memory

import (
	"context"

	"github.com/google/uuid"
	"github.com/koopa0/assistant-go/internal/knowledge"
)

// GraphReader defines read-only operations needed by the UI.
// This interface allows the UI to browse the knowledge graph without modification capabilities.
type GraphReader interface {
	SearchNodes(ctx context.Context, query string, nodeType *knowledge.NodeType, limit int) ([]*knowledge.Node, error)
	GetNode(ctx context.Context, id uuid.UUID) (*knowledge.Node, error)
	GetNodeEdges(ctx context.Context, nodeID uuid.UUID) ([]*knowledge.Edge, error)
}
*/
