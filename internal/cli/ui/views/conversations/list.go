// Package conversations provides UI components for conversation management
package conversations

import (
	"fmt"
	"io"
	"strings"
	"time"

	"github.com/charmbracelet/bubbles/key"
	"github.com/charmbracelet/bubbles/list"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"

	"github.com/koopa0/assistant-go/internal/conversation"
)

// conversationItem adapts conversation.Conversation for the list component
type conversationItem struct {
	conv *conversation.Conversation
}

func (i conversationItem) Title() string {
	title := i.conv.Title
	if title == "" {
		title = "Untitled Conversation"
	}
	return title
}

func (i conversationItem) Description() string {
	var parts []string

	// Time info
	if i.conv.LastMessageAt != nil {
		parts = append(parts, formatRelativeTime(*i.conv.LastMessageAt))
	} else {
		parts = append(parts, formatRelativeTime(i.conv.CreatedAt))
	}

	// Message count
	if i.conv.Metadata.MessageCount > 0 {
		parts = append(parts, fmt.Sprintf("%d messages", i.conv.Metadata.MessageCount))
	}

	// Summary preview
	if i.conv.Summary != "" {
		summary := i.conv.Summary
		if len(summary) > 50 {
			summary = summary[:47] + "..."
		}
		parts = append(parts, summary)
	}

	return strings.Join(parts, " • ")
}

func (i conversationItem) FilterValue() string {
	return i.conv.Title + " " + i.conv.Summary
}

// keyMap defines keyboard shortcuts
type keyMap struct {
	Up       key.Binding
	Down     key.Binding
	Select   key.Binding
	Quit     key.Binding
	PageUp   key.Binding
	PageDown key.Binding
	Search   key.Binding
}

var keys = keyMap{
	Up: key.NewBinding(
		key.WithKeys("up", "k"),
		key.WithHelp("↑/k", "up"),
	),
	Down: key.NewBinding(
		key.WithKeys("down", "j"),
		key.WithHelp("↓/j", "down"),
	),
	Select: key.NewBinding(
		key.WithKeys("enter"),
		key.WithHelp("enter", "select"),
	),
	Quit: key.NewBinding(
		key.WithKeys("q", "esc"),
		key.WithHelp("q/esc", "quit"),
	),
	PageUp: key.NewBinding(
		key.WithKeys("pgup"),
		key.WithHelp("pgup", "page up"),
	),
	PageDown: key.NewBinding(
		key.WithKeys("pgdown"),
		key.WithHelp("pgdown", "page down"),
	),
	Search: key.NewBinding(
		key.WithKeys("/"),
		key.WithHelp("/", "search"),
	),
}

// Model represents the conversation list view
type Model struct {
	list          list.Model
	conversations []*conversation.Conversation
	selected      *conversation.Conversation
	width         int
	height        int
	loading       bool
	err           error
}

// customDelegate is a custom item delegate that adds numbering
type customDelegate struct {
	list.DefaultDelegate
}

func (d customDelegate) Render(w io.Writer, m list.Model, index int, item list.Item) {
	// Add number prefix
	if index < 9 { // Show numbers 1-9 for quick access
		fmt.Fprintf(w, "%s ", lipgloss.NewStyle().
			Foreground(lipgloss.Color("241")).
			Render(fmt.Sprintf("[%d]", index+1)))
	} else {
		fmt.Fprintf(w, "    ")
	}
	// Call the default render
	d.DefaultDelegate.Render(w, m, index, item)
}

// NewModel creates a new conversation list model
func NewModel(conversations []*conversation.Conversation) Model {
	// Create list items with numbers
	items := make([]list.Item, len(conversations))
	for i, conv := range conversations {
		items[i] = conversationItem{conv: conv}
	}

	// Create custom item delegate with numbering
	baseDelegate := list.NewDefaultDelegate()
	baseDelegate.SetHeight(3)

	delegate := customDelegate{DefaultDelegate: baseDelegate}

	delegate.Styles.SelectedTitle = delegate.Styles.SelectedTitle.
		Foreground(lipgloss.Color("39")).
		BorderForeground(lipgloss.Color("39"))
	delegate.Styles.SelectedDesc = delegate.Styles.SelectedDesc.
		Foreground(lipgloss.Color("86"))

	l := list.New(items, delegate, 0, 0)
	l.Title = "💬 Conversation History"
	l.SetShowStatusBar(true)
	l.SetFilteringEnabled(true)
	l.SetShowPagination(true)
	l.SetShowHelp(true)

	// Custom styles
	l.Styles.Title = lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("39")).
		Padding(0, 0, 1, 0)

	// Add help text
	l.AdditionalShortHelpKeys = func() []key.Binding {
		return []key.Binding{
			key.NewBinding(
				key.WithKeys("1-9"),
				key.WithHelp("1-9", "quick select"),
			),
		}
	}

	return Model{
		list:          l,
		conversations: conversations,
	}
}

// Init initializes the model
func (m Model) Init() tea.Cmd {
	return nil
}

// Update handles messages
func (m Model) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height
		m.list.SetWidth(msg.Width)
		m.list.SetHeight(msg.Height - 2) // Leave room for help

	case tea.KeyMsg:
		// Handle number keys for quick selection
		if len(msg.String()) == 1 && msg.String() >= "1" && msg.String() <= "9" {
			index := int(msg.String()[0] - '1')
			if index < len(m.list.Items()) {
				m.list.Select(index)
				if i, ok := m.list.SelectedItem().(conversationItem); ok {
					m.selected = i.conv
					return m, tea.Quit
				}
			}
		}

		// Handle selection
		if key.Matches(msg, keys.Select) {
			if i, ok := m.list.SelectedItem().(conversationItem); ok {
				m.selected = i.conv
				return m, tea.Quit
			}
		}

		// Handle quit
		if key.Matches(msg, keys.Quit) {
			return m, tea.Quit
		}
	}

	// Update list
	var cmd tea.Cmd
	m.list, cmd = m.list.Update(msg)
	return m, cmd
}

// View renders the view
func (m Model) View() string {
	if m.loading {
		return lipgloss.NewStyle().
			Padding(2, 4).
			Render("Loading conversations...")
	}

	if m.err != nil {
		return lipgloss.NewStyle().
			Foreground(lipgloss.Color("196")).
			Padding(2, 4).
			Render(fmt.Sprintf("Error: %v", m.err))
	}

	return m.list.View()
}

// SelectedConversation returns the selected conversation
func (m Model) SelectedConversation() *conversation.Conversation {
	return m.selected
}

// formatRelativeTime formats time in a human-friendly way
func formatRelativeTime(t time.Time) string {
	now := time.Now()
	diff := now.Sub(t)

	switch {
	case diff < time.Minute:
		return "just now"
	case diff < time.Hour:
		mins := int(diff.Minutes())
		if mins == 1 {
			return "1 minute ago"
		}
		return fmt.Sprintf("%d minutes ago", mins)
	case diff < 24*time.Hour:
		hours := int(diff.Hours())
		if hours == 1 {
			return "1 hour ago"
		}
		return fmt.Sprintf("%d hours ago", hours)
	case diff < 7*24*time.Hour:
		days := int(diff.Hours() / 24)
		if days == 1 {
			return "yesterday"
		}
		return fmt.Sprintf("%d days ago", days)
	default:
		return t.Format("Jan 2, 2006")
	}
}
