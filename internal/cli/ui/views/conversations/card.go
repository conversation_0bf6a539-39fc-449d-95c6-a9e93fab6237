package conversations

import (
	"fmt"
	"strings"

	"github.com/charmbracelet/lipgloss"
	"github.com/koopa0/assistant-go/internal/conversation"
)

// CardStyle defines the visual style for conversation cards
type CardStyle struct {
	Border       lipgloss.Style
	Title        lipgloss.Style
	Time         lipgloss.Style
	Summary      lipgloss.Style
	Tags         lipgloss.Style
	MessageCount lipgloss.Style
	Selected     lipgloss.Style
}

// DefaultCardStyle returns the default card styling
func DefaultCardStyle() CardStyle {
	return CardStyle{
		Border: lipgloss.NewStyle().
			Border(lipgloss.RoundedBorder()).
			BorderForeground(lipgloss.Color("238")).
			Padding(1, 2),
		Title: lipgloss.NewStyle().
			Bold(true).
			Foreground(lipgloss.Color("39")),
		Time: lipgloss.NewStyle().
			Foreground(lipgloss.Color("241")).
			Italic(true),
		Summary: lipgloss.NewStyle().
			Foreground(lipgloss.Color("250")),
		Tags: lipgloss.NewStyle().
			Foreground(lipgloss.Color("86")).
			Background(lipgloss.Color("236")).
			Padding(0, 1),
		MessageCount: lipgloss.NewStyle().
			Foreground(lipgloss.Color("214")),
		Selected: lipgloss.NewStyle().
			Border(lipgloss.RoundedBorder()).
			BorderForeground(lipgloss.Color("39")).
			Padding(1, 2),
	}
}

// Card represents a conversation card component
type Card struct {
	conversation *conversation.Conversation
	style        CardStyle
	width        int
	selected     bool
}

// NewCard creates a new conversation card
func NewCard(conv *conversation.Conversation, width int) *Card {
	return &Card{
		conversation: conv,
		style:        DefaultCardStyle(),
		width:        width,
	}
}

// SetSelected sets the selection state
func (c *Card) SetSelected(selected bool) {
	c.selected = selected
}

// Render renders the card
func (c *Card) Render() string {
	// Title
	title := c.conversation.Title
	if title == "" {
		title = "Untitled Conversation"
	}
	titleLine := c.style.Title.Render(truncate(title, c.width-10))

	// Time
	var timeStr string
	if c.conversation.LastMessageAt != nil {
		timeStr = formatRelativeTime(*c.conversation.LastMessageAt)
	} else {
		timeStr = formatRelativeTime(c.conversation.CreatedAt)
	}
	timeLine := c.style.Time.Render(timeStr)

	// Message count
	countStr := fmt.Sprintf("💬 %d", c.conversation.Metadata.MessageCount)
	countLine := c.style.MessageCount.Render(countStr)

	// Combine title line with metadata
	topLine := lipgloss.JoinHorizontal(
		lipgloss.Left,
		titleLine,
		strings.Repeat(" ", max(2, c.width-lipgloss.Width(titleLine)-lipgloss.Width(timeLine)-lipgloss.Width(countLine)-10)),
		countLine,
		"  ",
		timeLine,
	)

	// Summary
	var summaryLine string
	if c.conversation.Summary != "" {
		summary := truncate(c.conversation.Summary, c.width-10)
		summaryLine = c.style.Summary.Render(summary)
	}

	// Tags
	var tagsLine string
	if len(c.conversation.Metadata.Tags) > 0 {
		tags := make([]string, 0, len(c.conversation.Metadata.Tags))
		for _, tag := range c.conversation.Metadata.Tags {
			tags = append(tags, c.style.Tags.Render(tag))
		}
		tagsLine = lipgloss.JoinHorizontal(lipgloss.Left, tags...)
	}

	// Combine all lines
	content := lipgloss.JoinVertical(lipgloss.Left, topLine)
	if summaryLine != "" {
		content = lipgloss.JoinVertical(lipgloss.Left, content, summaryLine)
	}
	if tagsLine != "" {
		content = lipgloss.JoinVertical(lipgloss.Left, content, "", tagsLine)
	}

	// Apply border
	if c.selected {
		return c.style.Selected.Render(content)
	}
	return c.style.Border.Render(content)
}

// Helper functions

func truncate(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen-3] + "..."
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}
