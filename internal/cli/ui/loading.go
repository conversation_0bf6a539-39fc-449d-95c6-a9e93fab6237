// Package ui provides user interface components
package ui

import (
	"fmt"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/charmbracelet/lipgloss"
	"golang.org/x/term"
)

// LoadingScreen provides a simple loading screen without Bubble Tea
type LoadingScreen struct {
	status  string
	percent float64
	done    bool
	mu      sync.Mutex
}

// NewLoadingScreen initializes with default status and zero progress
func NewLoadingScreen() *LoadingScreen {
	return &LoadingScreen{
		status:  "Initializing...",
		percent: 0.0,
		done:    false,
	}
}

// UpdateStatus updates the status message
func (l *LoadingScreen) UpdateStatus(status string) {
	l.mu.Lock()
	defer l.mu.Unlock()
	l.status = status
}

// UpdateProgress updates the progress percentage
func (l *LoadingScreen) UpdateProgress(percent float64) {
	l.mu.Lock()
	defer l.mu.Unlock()
	l.percent = percent
}

// Finish marks the loading as complete
func (l *LoadingScreen) Finish() {
	l.mu.Lock()
	defer l.mu.Unlock()
	l.done = true
}

// Start starts the loading screen display
func (l *LoadingScreen) Start() {
	// Clear screen
	fmt.Print("\033[H\033[2J")

	// ASCII art logo
	logo := `     █████╗ ███████╗███████╗██╗███████╗████████╗ █████╗ ███╗   ██╗████████╗
    ██╔══██╗██╔════╝██╔════╝██║██╔════╝╚══██╔══╝██╔══██╗████╗  ██║╚══██╔══╝
    ███████║███████╗███████╗██║███████╗   ██║   ███████║██╔██╗ ██║   ██║   
    ██╔══██║╚════██║╚════██║██║╚════██║   ██║   ██╔══██║██║╚██╗██║   ██║   
    ██║  ██║███████║███████║██║███████║   ██║   ██║  ██║██║ ╚████║   ██║   
    ╚═╝  ╚═╝╚══════╝╚══════╝╚═╝╚══════╝   ╚═╝   ╚═╝  ╚═╝╚═╝  ╚═══╝   ╚═╝   `

	// Styles
	logoStyle := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("39"))

	// Animation frames for spinner
	spinnerFrames := []string{"⣾", "⣽", "⣻", "⢿", "⡿", "⣟", "⣯", "⣷"}
	spinnerIndex := 0

	// Get terminal size
	width, height := getTerminalSize()

	// Start render loop
	ticker := time.NewTicker(50 * time.Millisecond)
	defer ticker.Stop()

	for range ticker.C {
		l.mu.Lock()
		done := l.done
		status := l.status
		percent := l.percent
		l.mu.Unlock()

		if done {
			break
		}

		// Clear screen and reset cursor
		fmt.Print("\033[H\033[2J")

		// Calculate content height (approximately 20 lines)
		contentHeight := 20
		topPadding := (height - contentHeight) / 2
		if topPadding < 0 {
			topPadding = 0
		}

		// Add vertical padding
		for i := 0; i < topPadding; i++ {
			fmt.Println()
		}

		// Logo width is approximately 80 characters
		logoWidth := 80
		leftPadding := (width - logoWidth) / 2
		if leftPadding < 0 {
			leftPadding = 0
		}

		// Print logo centered
		for _, line := range strings.Split(logo, "\n") {
			fmt.Print(strings.Repeat(" ", leftPadding))
			fmt.Println(logoStyle.Render(line))
		}

		fmt.Println()
		fmt.Println()

		// Tagline
		tagline := "🤖 Your Intelligent Partner"
		taglineStyle := lipgloss.NewStyle().Foreground(lipgloss.Color("243"))
		taglineLen := len(tagline)
		padding := strings.Repeat(" ", (width-taglineLen)/2)
		fmt.Println(padding + taglineStyle.Render(tagline))

		fmt.Println()
		fmt.Println()

		// Progress bar
		progressWidth := 50
		filledWidth := int(float64(progressWidth) * percent)
		emptyWidth := progressWidth - filledWidth

		filled := strings.Repeat("█", filledWidth)
		empty := strings.Repeat("░", emptyWidth)

		progressStyle := lipgloss.NewStyle().Foreground(lipgloss.Color("39"))
		emptyStyle := lipgloss.NewStyle().Foreground(lipgloss.Color("238"))
		percentStyle := lipgloss.NewStyle().
			Foreground(lipgloss.Color("39")).
			Bold(true)

		progressBar := progressStyle.Render(filled) + emptyStyle.Render(empty)
		percentText := percentStyle.Render(fmt.Sprintf(" %3.0f%%", percent*100))

		progressLen := progressWidth + 5 // progress bar + percentage
		padding = strings.Repeat(" ", (width-progressLen)/2)
		fmt.Println(padding + progressBar + percentText)

		fmt.Println()
		fmt.Println()

		// Status with spinner
		statusIcon := "⚡"
		if percent > 0.75 {
			statusIcon = "✨"
		} else if percent > 0.5 {
			statusIcon = "🧠"
		} else if percent > 0.25 {
			statusIcon = "🔧"
		}

		spinner := spinnerFrames[spinnerIndex%len(spinnerFrames)]
		spinnerIndex++

		statusStyle := lipgloss.NewStyle().Foreground(lipgloss.Color("243"))
		statusLine := fmt.Sprintf("%s %s %s", spinner, statusIcon, status)
		statusLen := len(statusLine) - 2 // account for emoji width
		padding = strings.Repeat(" ", (width-statusLen)/2)
		fmt.Println(padding + statusStyle.Render(statusLine))

		fmt.Println()
		fmt.Println()

		// Developer credit
		creditStyle := lipgloss.NewStyle().
			Foreground(lipgloss.Color("241")).
			Italic(true)
		credit := "Developed by Koopa"
		creditLen := len(credit)
		padding = strings.Repeat(" ", (width-creditLen)/2)
		fmt.Println(padding + creditStyle.Render(credit))
	}

	// Clear screen when done
	fmt.Print("\033[H\033[2J")
}

// getTerminalSize returns the width and height of the terminal
func getTerminalSize() (int, int) {
	width, height, err := term.GetSize(int(os.Stdout.Fd()))
	if err != nil {
		// Default size if can't get terminal size
		return 80, 24
	}
	return width, height
}

// ShowLoadingScreen displays a loading screen with progress updates
func ShowLoadingScreen(updateFunc func(*LoadingScreen)) {
	loading := NewLoadingScreen()

	// Start loading screen in background
	done := make(chan struct{})
	go func() {
		loading.Start()
		close(done)
	}()

	// Run updates
	updateFunc(loading)

	// Wait for loading screen to finish
	<-done
}
