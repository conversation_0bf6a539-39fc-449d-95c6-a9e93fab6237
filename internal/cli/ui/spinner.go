// Package ui provides terminal user interface components
package ui

import (
	"fmt"
	"sync/atomic"
	"time"
)

// Spinner provides a simple animated spinner for indicating progress
type Spinner struct {
	frames  []string
	current int
	active  atomic.Bool // Thread-safe boolean
	message string
	prefix  string
}

// NewSpinner uses Unicode braille characters for smooth animation
func NewSpinner(message string) *Spinner {
	return &Spinner{
		frames:  []string{"⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"},
		message: message,
		prefix:  "",
	}
}

// NewSpinnerWithPrefix preserves static prefix text during animation
func NewSpinnerWithPrefix(prefix, message string) *Spinner {
	return &Spinner{
		frames:  []string{"⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"},
		message: message,
		prefix:  prefix,
	}
}

// Start begins the spinner animation
func (s *Spinner) Start() {
	s.active.Store(true)
	go func() {
		// Recover from panic to prevent crash
		defer func() {
			if r := recover(); r != nil {
				// Ensure we mark as inactive if panic occurs
				s.active.Store(false)
			}
		}()

		for s.active.Load() {
			fmt.Printf("\r%s%s %s", s.prefix, s.frames[s.current], s.message)
			s.current = (s.current + 1) % len(s.frames)
			time.Sleep(100 * time.Millisecond)
		}
		// Clear only the spinner part, keep the prefix
		fmt.Printf("\r%s%s\r%s", s.prefix, "                                        ", s.prefix)
	}()
}

// Stop stops the spinner animation
func (s *Spinner) Stop() {
	s.active.Store(false)
	time.Sleep(150 * time.Millisecond) // Give time for cleanup
}
