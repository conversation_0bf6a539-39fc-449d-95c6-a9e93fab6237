// Package ui provides input handling for CLI
package ui

import (
	"bufio"
	"errors"
	"io"
	"strings"
)

// InputReader handles reading user input
type InputReader struct {
	reader *bufio.Reader
}

// NewInputReader creates a new input reader
func NewInputReader(r io.Reader) *InputReader {
	return &InputReader{
		reader: bufio.NewReader(r),
	}
}

// ReadLine reads a line of input from the user
func (r *InputReader) ReadLine() (string, error) {
	line, err := r.reader.ReadString('\n')
	if err != nil {
		return "", err
	}

	// Trim newline characters
	return strings.TrimRight(line, "\r\n"), nil
}

// ReadMultiLine reads multiple lines until EOF or empty line
func (r *InputReader) ReadMultiLine() (string, error) {
	var lines []string

	for {
		line, err := r.ReadLine()
		if err != nil {
			if errors.Is(err, io.EOF) && len(lines) > 0 {
				break
			}
			return "", err
		}

		// Empty line ends multi-line input
		if line == "" && len(lines) > 0 {
			break
		}

		lines = append(lines, line)
	}

	return strings.Join(lines, "\n"), nil
}
