// Package ui provides unified UI components for the CLI
package ui

import (
	"fmt"
	"strings"
	"time"

	"github.com/charmbracelet/bubbles/spinner"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/huh"
	"github.com/charmbracelet/lipgloss"
)

// Components holds all UI components for reuse
type Components struct {
	Spinner spinner.Model
	Styles  *Styles
}

// NewComponents creates a new set of UI components
func NewComponents() *Components {
	s := spinner.New()
	s.Spinner = spinner.Dot
	s.Style = lipgloss.NewStyle().Foreground(lipgloss.Color("205"))

	return &Components{
		Spinner: s,
		Styles:  DefaultStyles(),
	}
}

// Styles holds all style definitions
type Styles struct {
	Title     lipgloss.Style
	Subtitle  lipgloss.Style
	Error     lipgloss.Style
	Success   lipgloss.Style
	Info      lipgloss.Style
	Warning   lipgloss.Style
	User      lipgloss.Style
	Assistant lipgloss.Style
	Tool      lipgloss.Style
	Timestamp lipgloss.Style
	Border    lipgloss.Style
	Focused   lipgloss.Style
	Unfocused lipgloss.Style
}

// DefaultStyles returns the default style set
func DefaultStyles() *Styles {
	return &Styles{
		Title: lipgloss.NewStyle().
			Bold(true).
			Foreground(lipgloss.Color("205")).
			MarginBottom(1),

		Subtitle: lipgloss.NewStyle().
			Foreground(lipgloss.Color("241")).
			MarginBottom(1),

		Error: lipgloss.NewStyle().
			Foreground(lipgloss.Color("196")).
			Bold(true),

		Success: lipgloss.NewStyle().
			Foreground(lipgloss.Color("46")).
			Bold(true),

		Info: lipgloss.NewStyle().
			Foreground(lipgloss.Color("33")),

		Warning: lipgloss.NewStyle().
			Foreground(lipgloss.Color("214")),

		User: lipgloss.NewStyle().
			Foreground(lipgloss.Color("33")).
			Bold(true),

		Assistant: lipgloss.NewStyle().
			Foreground(lipgloss.Color("205")),

		Tool: lipgloss.NewStyle().
			Foreground(lipgloss.Color("241")).
			Italic(true),

		Timestamp: lipgloss.NewStyle().
			Foreground(lipgloss.Color("241")),

		Border: lipgloss.NewStyle().
			Border(lipgloss.RoundedBorder()).
			BorderForeground(lipgloss.Color("241")),

		Focused: lipgloss.NewStyle().
			Border(lipgloss.RoundedBorder()).
			BorderForeground(lipgloss.Color("205")),

		Unfocused: lipgloss.NewStyle().
			Border(lipgloss.RoundedBorder()).
			BorderForeground(lipgloss.Color("241")),
	}
}

// LoadingAnimation provides a simple loading animation
type LoadingAnimation struct {
	spinner  spinner.Model
	message  string
	finished bool
}

// NewLoadingAnimation creates a new loading animation
func NewLoadingAnimation(message string) *LoadingAnimation {
	s := spinner.New()
	s.Spinner = spinner.Dot
	s.Style = lipgloss.NewStyle().Foreground(lipgloss.Color("205"))

	return &LoadingAnimation{
		spinner: s,
		message: message,
	}
}

// Init implements tea.Model
func (l *LoadingAnimation) Init() tea.Cmd {
	return l.spinner.Tick
}

// Update implements tea.Model
func (l *LoadingAnimation) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	if l.finished {
		return l, nil
	}

	var cmd tea.Cmd
	l.spinner, cmd = l.spinner.Update(msg)
	return l, cmd
}

// View implements tea.Model
func (l *LoadingAnimation) View() string {
	if l.finished {
		return ""
	}
	return fmt.Sprintf("%s %s", l.spinner.View(), l.message)
}

// Stop stops the loading animation
func (l *LoadingAnimation) Stop() {
	l.finished = true
}

// Progress shows a progress indicator with percentage
type Progress struct {
	Current int
	Total   int
	Width   int
	Style   lipgloss.Style
}

// NewProgress creates a new progress bar
func NewProgress(total int) *Progress {
	return &Progress{
		Total: total,
		Width: 40,
		Style: lipgloss.NewStyle().Foreground(lipgloss.Color("205")),
	}
}

// View renders the progress bar
func (p *Progress) View() string {
	if p.Total == 0 {
		return ""
	}

	percent := float64(p.Current) / float64(p.Total)
	filled := int(percent * float64(p.Width))

	bar := strings.Repeat("█", filled) + strings.Repeat("░", p.Width-filled)
	percentage := fmt.Sprintf(" %3.0f%%", percent*100)

	return p.Style.Render(bar) + percentage
}

// Increment increases the current progress
func (p *Progress) Increment() {
	if p.Current < p.Total {
		p.Current++
	}
}

// ToolExecution shows tool execution status
type ToolExecution struct {
	Name      string
	Status    string // "running", "success", "error"
	StartTime time.Time
	EndTime   time.Time
	Result    string
}

// View renders the tool execution status
func (t *ToolExecution) View(styles *Styles) string {
	var statusIcon string
	var statusStyle lipgloss.Style

	switch t.Status {
	case "running":
		statusIcon = "⟳"
		statusStyle = styles.Info
	case "success":
		statusIcon = "✓"
		statusStyle = styles.Success
	case "error":
		statusIcon = "✗"
		statusStyle = styles.Error
	default:
		statusIcon = "•"
		statusStyle = styles.Tool
	}

	status := statusStyle.Render(fmt.Sprintf("%s %s", statusIcon, t.Name))

	if t.Status == "running" {
		duration := time.Since(t.StartTime).Round(time.Millisecond)
		return fmt.Sprintf("%s %s", status, styles.Timestamp.Render(duration.String()))
	}

	if t.EndTime.IsZero() {
		return status
	}

	duration := t.EndTime.Sub(t.StartTime).Round(time.Millisecond)
	return fmt.Sprintf("%s %s", status, styles.Timestamp.Render(fmt.Sprintf("(%s)", duration)))
}

// CreateForm creates a Huh form for user input
func CreateForm(fields ...huh.Field) *huh.Form {
	return huh.NewForm(
		huh.NewGroup(fields...),
	).WithTheme(huh.ThemeCharm())
}

// CreateConfirm creates a confirmation dialog
func CreateConfirm(title, description string) *huh.Form {
	return CreateForm(
		huh.NewConfirm().
			Title(title).
			Description(description).
			Affirmative("是").
			Negative("否"),
	)
}

// CreateSelect creates a selection dialog
func CreateSelect[T comparable](title string, options []huh.Option[T]) *huh.Form {
	return CreateForm(
		huh.NewSelect[T]().
			Title(title).
			Options(options...),
	)
}

// CreateInput creates a text input dialog
func CreateInput(title, placeholder string) *huh.Form {
	return CreateForm(
		huh.NewInput().
			Title(title).
			Placeholder(placeholder),
	)
}
