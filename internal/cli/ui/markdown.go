// Package ui provides markdown formatting utilities
package ui

import (
	"fmt"
	"strings"

	"github.com/charmbracelet/lipgloss"
)

// MarkdownFormatter handles markdown rendering for CLI output
type MarkdownFormatter struct {
	styles *Styles
}

// NewMarkdownFormatter creates a new markdown formatter
func NewMarkdownFormatter() *MarkdownFormatter {
	return &MarkdownFormatter{
		styles: DefaultStyles(),
	}
}

// Render renders markdown content with full formatting
func (f *MarkdownFormatter) Render(content string) string {
	// For now, just return the content as-is
	// In the future, we can add markdown parsing and styling
	return content
}

// RenderInline renders markdown content inline (for streaming)
func (f *MarkdownFormatter) RenderInline(content string) string {
	// For streaming, we need to handle partial content
	// Just return as-is for now
	return content
}

// RenderCode renders a code block with syntax highlighting
func (f *MarkdownFormatter) RenderCode(code, language string) string {
	codeStyle := lipgloss.NewStyle().
		Background(lipgloss.Color("236")).
		Foreground(lipgloss.Color("252")).
		Padding(1, 2)

	// Add language indicator if provided
	if language != "" {
		langStyle := lipgloss.NewStyle().
			Foreground(lipgloss.Color("241")).
			Italic(true)

		header := langStyle.Render(language)
		return header + "\n" + codeStyle.Render(code)
	}

	return codeStyle.Render(code)
}

// RenderList renders a list with proper indentation
func (f *MarkdownFormatter) RenderList(items []string, ordered bool) string {
	var result strings.Builder

	for i, item := range items {
		if ordered {
			result.WriteString(f.styles.Info.Render(fmt.Sprintf("%d. ", i+1)))
		} else {
			result.WriteString(f.styles.Info.Render("• "))
		}
		result.WriteString(item)
		if i < len(items)-1 {
			result.WriteString("\n")
		}
	}

	return result.String()
}

// RenderTable renders a simple table
func (f *MarkdownFormatter) RenderTable(headers []string, rows [][]string) string {
	// Simple table rendering
	// Calculate column widths
	widths := make([]int, len(headers))
	for i, h := range headers {
		widths[i] = len(h)
	}

	for _, row := range rows {
		for i, cell := range row {
			if i < len(widths) && len(cell) > widths[i] {
				widths[i] = len(cell)
			}
		}
	}

	// Render headers
	var result strings.Builder
	headerStyle := lipgloss.NewStyle().Bold(true).Foreground(lipgloss.Color("205"))

	for i, h := range headers {
		result.WriteString(headerStyle.Render(padRight(h, widths[i])))
		if i < len(headers)-1 {
			result.WriteString(" | ")
		}
	}
	result.WriteString("\n")

	// Separator
	sepStyle := lipgloss.NewStyle().Foreground(lipgloss.Color("241"))
	for i := range headers {
		result.WriteString(sepStyle.Render(strings.Repeat("─", widths[i])))
		if i < len(headers)-1 {
			result.WriteString("─┼─")
		}
	}
	result.WriteString("\n")

	// Render rows
	cellStyle := lipgloss.NewStyle().Foreground(lipgloss.Color("252"))
	for _, row := range rows {
		for i, cell := range row {
			if i < len(widths) {
				result.WriteString(cellStyle.Render(padRight(cell, widths[i])))
				if i < len(row)-1 {
					result.WriteString(" | ")
				}
			}
		}
		result.WriteString("\n")
	}

	return strings.TrimRight(result.String(), "\n")
}

// padRight pads a string to the right with spaces
func padRight(s string, width int) string {
	if len(s) >= width {
		return s
	}
	return s + strings.Repeat(" ", width-len(s))
}
