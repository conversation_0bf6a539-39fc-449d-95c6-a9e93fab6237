package cli

import (
	"bufio"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/charmbracelet/lipgloss"
	"golang.org/x/term"

	"github.com/koopa0/assistant-go/internal/cli/chat"
	"github.com/koopa0/assistant-go/internal/cli/ui"
)

// UI provides simple command-line interface (no TTY dependency)
type UI struct {
	scanner *bufio.Scanner
}

// NewUI creates new simple UI
func NewUI() *UI {
	return &UI{
		scanner: bufio.NewScanner(os.Stdin),
	}
}

// ShowMenu shows main menu
func (ui *UI) ShowMenu() {
	// Styles
	titleStyle := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("86")).
		MarginBottom(1)

	optionStyle := lipgloss.NewStyle().
		Foreground(lipgloss.Color("214"))

	descStyle := lipgloss.NewStyle().
		Foreground(lipgloss.Color("241"))

	// Clear screen only if interactive
	if term.IsTerminal(int(os.Stdin.Fd())) && term.IsTerminal(int(os.Stdout.Fd())) {
		fmt.Print("\033[H\033[2J")
	}

	// Title
	fmt.Println(titleStyle.Render("🤖 Personal AI Assistant"))
	fmt.Println()

	// Options
	options := []struct {
		key  string
		name string
		desc string
	}{
		{"1", "Conversation Mode", "Enter natural language conversation"},
		{"2", "Quick Question", "Ask a question and get an answer"},
		{"3", "Task Management", "Manage to-do items"},
		{"4", "Learning Mode", "Teach the system new knowledge"},
		{"5", "Search Info", "Intelligent search"},
		{"6", "System Reflection", "View learning progress"},
		{"q", "Exit", "Leave the program"},
	}

	for _, opt := range options {
		fmt.Printf("  %s  %s  %s\n",
			optionStyle.Render("["+opt.key+"]"),
			opt.name,
			descStyle.Render(opt.desc))
	}

	fmt.Println()
}

// GetChoice gets user choice
func (ui *UI) GetChoice() string {
	promptStyle := lipgloss.NewStyle().
		Foreground(lipgloss.Color("86")).
		Bold(true)

	fmt.Print(promptStyle.Render("Please choose: "))

	if ui.scanner.Scan() {
		return strings.TrimSpace(ui.scanner.Text())
	}
	return ""
}

// Prompt shows prompt and gets input
func (ui *UI) Prompt(message string) string {
	promptStyle := lipgloss.NewStyle().
		Foreground(lipgloss.Color("86"))

	fmt.Print(promptStyle.Render(message + ": "))

	if ui.scanner.Scan() {
		return strings.TrimSpace(ui.scanner.Text())
	}
	return ""
}

// ShowMessage shows message
func (ui *UI) ShowMessage(message string, isError bool) {
	var style lipgloss.Style
	if isError {
		style = lipgloss.NewStyle().
			Foreground(lipgloss.Color("196"))
	} else {
		style = lipgloss.NewStyle().
			Foreground(lipgloss.Color("42"))
	}

	fmt.Println(style.Render(message))
}

// WaitForEnter waits for user to press Enter
func (ui *UI) WaitForEnter() {
	fmt.Print("\nPress Enter to continue...")
	ui.scanner.Scan()
}

// isTerminal checks if stdin is a terminal
func isTerminal() bool {
	return term.IsTerminal(int(os.Stdin.Fd()))
}

// runUI runs the simple UI mode (direct conversation)
func runUI(session *Session) error {
	// Skip loading screen if requested or not in terminal
	if os.Getenv("ASSISTANT_NO_LOADING") != "true" && isTerminal() {
		// Show loading screen (faster version)
		ui.ShowLoadingScreen(func(loading *ui.LoadingScreen) {
			// Quick loading steps
			time.Sleep(200 * time.Millisecond)
			loading.UpdateStatus("Initializing AI...")
			loading.UpdateProgress(0.3)

			time.Sleep(300 * time.Millisecond)
			loading.UpdateStatus("Loading memories...")
			loading.UpdateProgress(0.7)

			time.Sleep(200 * time.Millisecond)
			loading.UpdateStatus("Ready!")
			loading.UpdateProgress(1.0)

			time.Sleep(100 * time.Millisecond)
			loading.UpdateStatus("Ready to assist you!")
			loading.UpdateProgress(1.0)

			time.Sleep(800 * time.Millisecond)
			loading.Finish()
		})
	}

	// No language preference needed - using Chinese for personal assistant

	// Check API key configuration
	cfg := session.Config()
	if cfg == nil {
		return fmt.Errorf("configuration not loaded")
	}

	// Check if API keys are configured
	if cfg.AI.ClaudeAPIKey == "" && cfg.AI.GeminiAPIKey == "" {
		// Show API key missing message
		fmt.Println(lipgloss.NewStyle().Foreground(lipgloss.Color("196")).Render(
			"Error: API key not configured",
		))
		fmt.Println()

		fmt.Println("Please follow these steps to configure:")
		fmt.Println("1. Copy .env.example to .env")
		fmt.Println("   cp .env.example .env")
		fmt.Println("2. Edit the .env file and add your API key")
		fmt.Println("   CLAUDE_API_KEY=your-claude-api-key")
		fmt.Println("   or")
		fmt.Println("   GEMINI_API_KEY=your-gemini-api-key")
		fmt.Println("3. Restart the assistant")
		return nil
	}

	// Get user's name
	userName := "User"
	if cfg.Owner.Name != "" && cfg.Owner.Name != "Assistant Owner" {
		userName = cfg.Owner.Name
	}

	// Clear screen and show beautiful welcome screen only if interactive
	if term.IsTerminal(int(os.Stdin.Fd())) && term.IsTerminal(int(os.Stdout.Fd())) {
		fmt.Print("\033[H\033[2J")
		ShowBeautifulWelcome(userName)
	}

	// Run chat interface using the new controller
	controller, err := chat.NewController(session, os.Stdout)
	if err != nil {
		return fmt.Errorf("create chat controller: %w", err)
	}
	return controller.Run()
}

// ShowBeautifulWelcome shows a beautiful welcome screen
func ShowBeautifulWelcome(userName string) {
	// Define styles - matching splash screen
	logoStyle := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("39")). // Bright blue like splash
		Padding(1, 0)

	subtitleStyle := lipgloss.NewStyle().
		Foreground(lipgloss.Color("241")).
		Italic(true)

	nameStyle := lipgloss.NewStyle().
		Foreground(lipgloss.Color("39")).
		Bold(true)

	greetingStyle := lipgloss.NewStyle().
		Foreground(lipgloss.Color("86"))

	helpStyle := lipgloss.NewStyle().
		Foreground(lipgloss.Color("243")).
		Italic(true)

	dividerStyle := lipgloss.NewStyle().
		Foreground(lipgloss.Color("238"))

	// ASCII art logo - same as splash screen
	logo := `
     █████╗ ███████╗███████╗██╗███████╗████████╗ █████╗ ███╗   ██╗████████╗
    ██╔══██╗██╔════╝██╔════╝██║██╔════╝╚══██╔══╝██╔══██╗████╗  ██║╚══██╔══╝
    ███████║███████╗███████╗██║███████╗   ██║   ███████║██╔██╗ ██║   ██║
    ██╔══██║╚════██║╚════██║██║╚════██║   ██║   ██╔══██║██║╚██╗██║   ██║
    ██║  ██║███████║███████║██║███████║   ██║   ██║  ██║██║ ╚████║   ██║
    ╚═╝  ╚═╝╚══════╝╚══════╝╚═╝╚══════╝   ╚═╝   ╚═╝  ╚═╝╚═╝  ╚═══╝   ╚═╝
`

	// Content for personal assistant
	greeting := fmt.Sprintf("Welcome back, %s!", nameStyle.Render(userName))
	ready := "🤖 I'm your AI assistant, ready to help you anytime"
	tip := "💡 Type /help to see available commands"

	// Divider line
	divider := dividerStyle.Render("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")

	// Compose the welcome screen
	content := lipgloss.JoinVertical(
		lipgloss.Center,
		"",
		logoStyle.Render(logo),
		"",
		greetingStyle.Render(greeting),
		"",
		ready,
		"",
		divider,
		"",
		helpStyle.Render(tip),
		"",
		subtitleStyle.Render("Developed by Koopa"),
		"",
	)

	// Center the entire content
	width := 80
	centered := lipgloss.NewStyle().
		Width(width).
		Align(lipgloss.Center).
		Render(content)

	// Print the welcome screen
	fmt.Println(centered)
	fmt.Println()
}
