// Package cli provides command handling for chat interface
package cli

import (
	"fmt"
	"strings"

	"github.com/charmbracelet/lipgloss"
	"github.com/google/uuid"
)

// Command represents a chat command
type Command struct {
	Name        string
	Aliases     []string
	Description string
	Handler     CommandHandler
}

// CommandHandler is a function that handles a command
type CommandHandler func(ctx *Session, args []string) error

// CommandRegistry manages available commands
type CommandRegistry struct {
	commands map[string]*Command
	styles   lipgloss.Style
}

// NewCommandRegistry creates a new command registry
func NewCommandRegistry() *CommandRegistry {
	r := &CommandRegistry{
		commands: make(map[string]*Command),
		styles: lipgloss.NewStyle().
			Foreground(lipgloss.Color("241")),
	}

	// Register default commands
	r.registerDefaultCommands()
	return r
}

// registerDefaultCommands registers all default commands
func (r *CommandRegistry) registerDefaultCommands() {
	r.Register(&Command{
		Name:        "help",
		Aliases:     []string{"h", "?"},
		Description: "Show available commands",
		Handler:     r.handleHelp,
	})

	r.Register(&Command{
		Name:        "clear",
		Aliases:     []string{"c", "cls"},
		Description: "Clear screen",
		Handler:     handleClear,
	})

	r.Register(&Command{
		Name:        "exit",
		Aliases:     []string{"quit", "q"},
		Description: "Exit conversation",
		Handler:     handleExit,
	})

	r.Register(&Command{
		Name:        "new",
		Aliases:     []string{"n"},
		Description: "Start new conversation",
		Handler:     handleNewConversation,
	})

	r.Register(&Command{
		Name:        "history",
		Aliases:     []string{"h"},
		Description: "Show conversation history",
		Handler:     handleHistory,
	})

	r.Register(&Command{
		Name:        "save",
		Aliases:     []string{"s"},
		Description: "Save current conversation",
		Handler:     handleSave,
	})

	r.Register(&Command{
		Name:        "load",
		Aliases:     []string{"l"},
		Description: "Load conversation",
		Handler:     handleLoad,
	})

	r.Register(&Command{
		Name:        "settings",
		Aliases:     []string{"set", "config"},
		Description: "Show or modify settings",
		Handler:     handleSettings,
	})

	r.Register(&Command{
		Name:        "model",
		Aliases:     []string{"m"},
		Description: "Switch AI model",
		Handler:     handleModelSwitch,
	})

	r.Register(&Command{
		Name:        "consolidate",
		Aliases:     []string{"con"},
		Description: "Consolidate and optimize memories",
		Handler:     handleConsolidate,
	})

	r.Register(&Command{
		Name:        "memory",
		Aliases:     []string{"mem"},
		Description: "Browse and search memory/knowledge graph",
		Handler:     handleMemoryBrowser,
	})
}

// Register adds a command to the registry
func (r *CommandRegistry) Register(cmd *Command) {
	r.commands[cmd.Name] = cmd
	for _, alias := range cmd.Aliases {
		r.commands[alias] = cmd
	}
}

// Execute runs a command if it exists
func (r *CommandRegistry) Execute(ctx *Session, input string) (bool, error) {
	if !strings.HasPrefix(input, "/") {
		return false, nil
	}

	parts := strings.Fields(input[1:])
	if len(parts) == 0 {
		return false, nil
	}

	cmdName := parts[0]
	args := parts[1:]

	cmd, exists := r.commands[cmdName]
	if !exists {
		return true, fmt.Errorf("unknown command: /%s\nType /help to see available commands", cmdName)
	}

	return true, cmd.Handler(ctx, args)
}

// handleHelp shows available commands
func (r *CommandRegistry) handleHelp(ctx *Session, args []string) error {
	titleStyle := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("205"))

	cmdStyle := lipgloss.NewStyle().
		Foreground(lipgloss.Color("33"))

	descStyle := lipgloss.NewStyle().
		Foreground(lipgloss.Color("241"))

	fmt.Println(titleStyle.Render("\nAvailable Commands:"))

	// Get unique commands (avoid showing aliases multiple times)
	shown := make(map[string]bool)

	for _, cmd := range r.commands {
		if shown[cmd.Name] {
			continue
		}
		shown[cmd.Name] = true

		cmdText := "/" + cmd.Name
		if len(cmd.Aliases) > 0 {
			aliases := make([]string, len(cmd.Aliases))
			for i, alias := range cmd.Aliases {
				aliases[i] = "/" + alias
			}
			cmdText += " (" + strings.Join(aliases, ", ") + ")"
		}

		fmt.Printf("  %s - %s\n",
			cmdStyle.Render(cmdText),
			descStyle.Render(cmd.Description))
	}

	fmt.Println(titleStyle.Render("\n自然語言指令範例："))
	examples := []struct {
		cmd  string
		desc string
	}{
		{"記住我喜歡咖啡", "儲存個人偏好"},
		{"搜尋關於工作的記憶", "搜尋記憶內容"},
		{"顯示我的設定", "查看當前設定"},
		{"切換到 Gemini", "更換 AI 模型"},
		{"清除記憶", "清除對話記憶"},
	}

	for _, ex := range examples {
		fmt.Printf("  %s - %s\n",
			cmdStyle.Render(ex.cmd),
			descStyle.Render(ex.desc))
	}

	return nil
}

// handleClear clears the screen
func handleClear(ctx *Session, args []string) error {
	fmt.Print("\033[H\033[2J")
	return nil
}

// handleExit exits the chat
func handleExit(ctx *Session, args []string) error {
	return fmt.Errorf("exit")
}

// handleNewConversation starts a new conversation
func handleNewConversation(ctx *Session, args []string) error {
	ctx.SetConversationUUID(uuid.Nil)
	fmt.Println(lipgloss.NewStyle().
		Foreground(lipgloss.Color("46")).
		Render("✓ 已開始新對話"))
	return nil
}

// handleHistory shows conversation history
func handleHistory(ctx *Session, args []string) error {
	// TODO: Implement conversation history display
	fmt.Println("對話歷史功能開發中...")
	return nil
}

// handleSave saves the current conversation
func handleSave(ctx *Session, args []string) error {
	// TODO: Implement conversation saving
	fmt.Println("儲存對話功能開發中...")
	return nil
}

// handleLoad loads a conversation
func handleLoad(ctx *Session, args []string) error {
	// TODO: Implement conversation loading
	fmt.Println("載入對話功能開發中...")
	return nil
}

// handleSettings shows or modifies settings
func handleSettings(ctx *Session, args []string) error {
	if len(args) == 0 {
		// Show current settings
		fmt.Println(lipgloss.NewStyle().
			Bold(true).
			Foreground(lipgloss.Color("205")).
			Render("\n當前設定："))

		settingStyle := lipgloss.NewStyle().Foreground(lipgloss.Color("241"))
		valueStyle := lipgloss.NewStyle().Foreground(lipgloss.Color("33"))

		cfg := ctx.Config()
		opts := ctx.Options()

		fmt.Printf("  %s: %s\n",
			settingStyle.Render("AI 模型"),
			valueStyle.Render(cfg.AI.DefaultModel))
		fmt.Printf("  %s: %s\n",
			settingStyle.Render("Temperature"),
			valueStyle.Render(fmt.Sprintf("%.1f", opts.Temperature)))
		fmt.Printf("  %s: %s\n",
			settingStyle.Render("Max Tokens"),
			valueStyle.Render(fmt.Sprintf("%d", opts.MaxTokens)))

		return nil
	}

	// TODO: Implement setting modification
	fmt.Println("修改設定功能開發中...")
	return nil
}

// handleModelSwitch switches AI model
func handleModelSwitch(ctx *Session, args []string) error {
	if len(args) == 0 {
		fmt.Println("請指定模型名稱，例如: /model claude-3-opus")
		return nil
	}

	// TODO: Implement model switching
	fmt.Printf("切換模型功能開發中... (嘗試切換到: %s)\n", args[0])
	return nil
}

// handleConsolidate runs memory consolidation
func handleConsolidate(ctx *Session, args []string) error {
	// TODO: This function needs to be updated for the new memory system
	return fmt.Errorf("memory consolidation is temporarily unavailable - needs to be updated for the new memory system")
}

// handleMemoryBrowser opens an interactive memory browser
func handleMemoryBrowser(ctx *Session, args []string) error {
	// Check if database is available
	if ctx.db == nil {
		return fmt.Errorf("database not available - memory browser requires database storage")
	}

	// TODO: Memory browser needs to be updated for the new memory system
	return fmt.Errorf("memory browser is temporarily unavailable - UI components need to be updated for the new memory system")
}
