// Package cli provides simplified command-line interface
// This version removes all subcommands for a cleaner, more direct experience
package cli

import (
	"bufio"
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"

	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/spf13/cobra"

	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/koopa0/assistant-go/internal/assistant"
	"github.com/koopa0/assistant-go/internal/platform/config"
	"github.com/koopa0/assistant-go/internal/platform/logger"
)

// SimpleRootCmd creates a simplified root command with no subcommands
type SimpleRootCmd struct {
	cmd     *cobra.Command
	session *Session
}

// NewSimpleRootCmd creates root command without subcommands
func NewSimpleRootCmd(cfg *config.Config, aiService *ai.Service, db *pgxpool.Pool, assistantService assistant.Assistant, log logger.Logger) (*SimpleRootCmd, error) {
	// Create CLI session with all dependencies
	session, err := NewSession(cfg, aiService, db, assistantService, log)
	if err != nil {
		return nil, fmt.Errorf("create session: %w", err)
	}

	rc := &SimpleRootCmd{
		session: session,
	}

	rc.cmd = &cobra.Command{
		Use:   "assistant",
		Short: "AI 智能助理",
		Long: `AI 智能助理 - 自然對話，無需記憶命令

直接開始對話即可。我能理解你的需求，包括：
• 日常對話和問答
• 記憶管理（"記住..."、"搜尋記憶..."）
• 設定調整（"切換到 Gemini"）
• 資訊查詢（"顯示我的偏好"）

輸入 /help 查看可用指令，或直接開始對話。

管道輸入支援：
  cat error.log | assistant "分析這個錯誤"
  echo "Hello" | assistant`,
		Version: "0.1.0",
		Args:    cobra.ArbitraryArgs, // Accept any arguments for piped input
		PersistentPreRunE: func(cmd *cobra.Command, args []string) error {
			// Configure logging based on command context
			configureLogging(cmd, rc.session.Config())
			return nil
		},
		RunE: func(cmd *cobra.Command, args []string) error {
			// Check for version flag manually - handle before session is needed
			if v, _ := cmd.Flags().GetBool("version"); v {
				fmt.Println("Assistant v0.1.0")
				os.Exit(0) // Exit immediately to avoid initialization
			}

			// Check if input is being piped
			stat, _ := os.Stdin.Stat()
			if (stat.Mode() & os.ModeCharDevice) == 0 {
				// Handle piped input
				return handlePipedInput(rc.session, args)
			}

			// Direct entry to UI (which includes welcome screen and chat)
			return runUI(rc.session)
		},
	}

	// Minimal flags - only what's essential
	rc.cmd.Flags().BoolP("version", "v", false, "顯示版本")
	rc.cmd.Flags().StringP("config", "c", "", "配置檔案路徑")

	// Hide help command to keep it clean
	rc.cmd.SetHelpCommand(&cobra.Command{Hidden: true})

	// Custom help
	rc.cmd.SetHelpFunc(func(cmd *cobra.Command, args []string) {
		fmt.Print(`AI Assistant - 智能助理

使用：
  assistant           開始對話
  assistant -v        顯示版本

管道輸入：
  cat file.txt | assistant "總結這個檔案"
  echo "程式碼" | assistant "解釋這段程式碼"
  curl api.com | assistant "分析回應"

在對話中：
  直接輸入問題或需求
  /help    查看可用指令
  /exit    退出

自然語言指令範例：
  "記住我喜歡咖啡"
  "搜尋我的記憶中關於工作的內容"
  "顯示我的設定"
  "切換到 Gemini"

由 Koopa 開發
`)
	})

	return rc, nil
}

// Execute runs the command
func (rc *SimpleRootCmd) Execute() error {
	// Set the command args to ensure it runs even with no arguments
	// This is needed because Cobra shows help by default when no args are provided
	if len(os.Args) == 1 {
		// No arguments provided, explicitly set empty args to trigger RunE
		rc.cmd.SetArgs([]string{})
	}
	return rc.cmd.Execute()
}

// configureLogging configures logging based on command and environment.
// This replaces the standalone shouldUseSilentLogs function with a cobra-integrated approach.
func configureLogging(cmd *cobra.Command, cfg *config.Config) {
	// Check environment variable first
	if os.Getenv("ASSISTANT_SILENT_MODE") == "true" {
		setSilentLogging(cfg)
		return
	}

	// Check if input is being piped - always use silent mode
	stat, _ := os.Stdin.Stat()
	if (stat.Mode() & os.ModeCharDevice) == 0 {
		setSilentLogging(cfg)
		return
	}

	// Silent commands that should have minimal output
	silentCommands := map[string]bool{
		"chat":    true,
		"c":       true,
		"memory":  true,
		"m":       true,
		"config":  true,
		"learn":   true,
		"welcome": true,
		"task":    true,
	}

	// Check if this is a completion command
	if cmd.Name() == "completion" || strings.Contains(cmd.Name(), "completion") {
		setSilentLogging(cfg)
		return
	}

	// Check command name
	if silentCommands[cmd.Name()] {
		setSilentLogging(cfg)
		return
	}

	// For root command (direct chat mode), use silent logging
	if cmd.Name() == "assistant" && cmd.Parent() == nil {
		setSilentLogging(cfg)
		return
	}

	// Default: use configured logging
	logger.SetGlobal(logger.NewFromEnv())
}

// setSilentLogging configures silent/minimal logging mode
func setSilentLogging(cfg *config.Config) {
	_ = os.Setenv("ASSISTANT_SILENT_MODE", "true")
	_ = os.Setenv("LOG_LEVEL", "fatal")
	_ = os.Setenv("WIRE_LOG_LEVEL", "fatal")

	if os.Getenv("ASSISTANT_LOG_TO_FILE") == "true" {
		homeDir, _ := os.UserHomeDir()
		if homeDir != "" {
			logDir := filepath.Join(homeDir, ".assistant", "logs")
			if fileLogger, err := logger.NewFileLogger(logDir, cfg.LogLevel()); err == nil {
				logger.SetGlobal(fileLogger)
				return
			}
		}
	}

	logger.SetGlobal(logger.NewDiscardLogger())
}

// handlePipedInput handles input that's being piped to the assistant
func handlePipedInput(session *Session, args []string) error {
	// Read piped input
	reader := bufio.NewReader(os.Stdin)
	var pipedInput strings.Builder

	for {
		line, err := reader.ReadString('\n')
		if err != nil {
			if err == io.EOF {
				break
			}
			return fmt.Errorf("read piped input: %w", err)
		}
		pipedInput.WriteString(line)
	}

	// Build the query
	var query string
	if len(args) > 0 {
		// If args provided, use them as the query with piped input as context
		query = strings.Join(args, " ")
		if pipedInput.Len() > 0 {
			query = fmt.Sprintf("%s\n\nContext:\n%s", query, pipedInput.String())
		}
	} else {
		// If no args, use piped input as query
		query = pipedInput.String()
	}

	if strings.TrimSpace(query) == "" {
		return fmt.Errorf("no input provided")
	}

	// Process query
	ctx := context.Background()
	req := assistant.Request{
		Query: query,
		Options: assistant.Options{
			Temperature:  session.GetTemperature(),
			MaxTokens:    session.GetMaxTokens(),
			EnableMemory: true,
			EnableTools:  true,
		},
	}

	// Process the request
	resp, err := session.Chat(ctx, req)
	if err != nil {
		return fmt.Errorf("assistant error: %w", err)
	}

	// Output the response
	fmt.Println(resp.Content)

	return nil
}
