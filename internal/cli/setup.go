// Package cli provides first-time setup functionality
package cli

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/charmbracelet/huh"
	"github.com/charmbracelet/lipgloss"

	"github.com/koopa0/assistant-go/internal/platform/config"
)

// SetupWizard runs the first-time setup wizard
type SetupWizard struct {
	config *config.Config
}

// NewSetupWizard creates a new setup wizard
func NewSetupWizard(cfg *config.Config) *SetupWizard {
	return &SetupWizard{
		config: cfg,
	}
}

// Run executes the setup wizard
func (s *SetupWizard) Run() error {
	// Check if this is the first run
	if !s.needsSetup() {
		return nil
	}

	// Show welcome message
	s.showWelcome()

	// Get user information
	userInfo, err := s.collectUserInfo()
	if err != nil {
		return fmt.Errorf("failed to collect user info: %w", err)
	}

	// Update configuration
	s.updateConfig(userInfo)

	// Save configuration
	if err := s.saveConfig(); err != nil {
		return fmt.Errorf("failed to save configuration: %w", err)
	}

	// Show completion message
	s.showCompletion(userInfo.Name)

	return nil
}

// needsSetup checks if setup is needed
func (s *SetupWizard) needsSetup() bool {
	// Check if user has default values
	return s.config.Owner.Name == config.DefaultOwnerName ||
		s.config.Owner.Name == "Assistant Owner" ||
		s.config.Owner.Name == ""
}

// showWelcome displays the welcome message
func (s *SetupWizard) showWelcome() {
	titleStyle := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("205")).
		MarginBottom(1)

	subtitleStyle := lipgloss.NewStyle().
		Foreground(lipgloss.Color("241")).
		MarginBottom(2)

	fmt.Println(titleStyle.Render("歡迎使用 AI Assistant！"))
	fmt.Println(subtitleStyle.Render("讓我們花一分鐘來設定您的個人資訊，這樣我可以更好地為您服務。"))
}

// UserSetupInfo holds user setup information
type UserSetupInfo struct {
	Name     string
	Email    string
	Language string
	Timezone string
	Theme    string
}

// collectUserInfo collects user information through interactive forms
func (s *SetupWizard) collectUserInfo() (*UserSetupInfo, error) {
	var info UserSetupInfo

	// Create the form
	form := huh.NewForm(
		huh.NewGroup(
			huh.NewInput().
				Title("您的名字是什麼？").
				Description("我會用這個名字稱呼您").
				Placeholder("例如：Alice").
				Validate(func(s string) error {
					if strings.TrimSpace(s) == "" {
						return fmt.Errorf("名字不能為空")
					}
					return nil
				}).
				Value(&info.Name),

			huh.NewInput().
				Title("您的電子郵件（選填）").
				Description("用於個人化服務").
				Placeholder("例如：<EMAIL>").
				Value(&info.Email),
		),

		huh.NewGroup(
			huh.NewSelect[string]().
				Title("選擇您偏好的語言").
				Options(
					huh.NewOption("繁體中文", "zh-TW"),
					huh.NewOption("English", "en"),
					huh.NewOption("日文", "jp"),
				).
				Value(&info.Language),

			huh.NewSelect[string]().
				Title("選擇您的時區").
				Options(
					huh.NewOption("台北 (UTC+8)", "Asia/Taipei"),
					huh.NewOption("上海 (UTC+8)", "Asia/Shanghai"),
					huh.NewOption("東京 (UTC+9)", "Asia/Tokyo"),
					huh.NewOption("紐約 (UTC-5)", "America/New_York"),
					huh.NewOption("倫敦 (UTC+0)", "Europe/London"),
				).
				Value(&info.Timezone),

			huh.NewSelect[string]().
				Title("選擇介面主題").
				Options(
					huh.NewOption("深色模式", "dark"),
					huh.NewOption("淺色模式", "light"),
					huh.NewOption("自動", "auto"),
				).
				Value(&info.Theme),
		),
	).WithTheme(huh.ThemeCharm())

	// Run the form
	if err := form.Run(); err != nil {
		return nil, err
	}

	// Set defaults if not selected
	if info.Language == "" {
		info.Language = "zh-TW"
	}
	if info.Timezone == "" {
		info.Timezone = "Asia/Taipei"
	}
	if info.Theme == "" {
		info.Theme = "dark"
	}

	return &info, nil
}

// updateConfig updates the configuration with user info
func (s *SetupWizard) updateConfig(info *UserSetupInfo) {
	s.config.Owner.Name = info.Name
	s.config.Owner.Email = info.Email

	// Update preferences
	if s.config.Owner.Preferences == nil {
		s.config.Owner.Preferences = make(map[string]string)
	}
	s.config.Owner.Preferences["language"] = info.Language
	s.config.Owner.Preferences["timezone"] = info.Timezone
	s.config.Owner.Preferences["theme"] = info.Theme

	// Update app language
	s.config.App.Language = info.Language
}

// saveConfig saves the configuration to assistant.yaml file
func (s *SetupWizard) saveConfig() error {
	// Determine the config file path
	var configPath string

	// First, check if ASSISTANT_CONFIG env var is set
	if envPath := os.Getenv("ASSISTANT_CONFIG"); envPath != "" {
		configPath = envPath
	} else {
		// Try standard locations in order of preference
		home, err := os.UserHomeDir()
		if err != nil {
			return fmt.Errorf("failed to get home directory: %w", err)
		}

		// Check if ./assistant.yaml exists (current directory)
		if _, err := os.Stat("./assistant.yaml"); err == nil {
			configPath = "./assistant.yaml"
		} else {
			// Default to ~/.assistant/assistant.yaml
			configPath = filepath.Join(home, ".assistant", "assistant.yaml")
		}
	}

	// Save configuration to YAML
	return config.SaveToYAML(s.config, configPath)
}

// showCompletion shows the completion message
func (s *SetupWizard) showCompletion(name string) {
	successStyle := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("46"))

	fmt.Println()
	fmt.Println(successStyle.Render("✓ 設定完成！"))
	fmt.Printf("很高興認識您，%s！我已經準備好為您服務了。\n", name)
	fmt.Println()
}

// RunSetupIfNeeded runs the setup wizard if needed
func RunSetupIfNeeded(cfg *config.Config) error {
	wizard := NewSetupWizard(cfg)
	return wizard.Run()
}
