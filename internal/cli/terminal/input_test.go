package terminal

import (
	"testing"
)

func TestFilterInput(t *testing.T) {
	tests := []struct {
		name     string
		input    rune
		wantRune rune
		wantOK   bool
	}{
		{
			name:     "normal character",
			input:    'a',
			wantRune: 'a',
			wantOK:   true,
		},
		{
			name:     "special character",
			input:    '!',
			wantRune: '!',
			wantOK:   true,
		},
		{
			name:     "unicode character",
			input:    '中',
			wantRune: '中',
			wantOK:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotRune, gotOK := filterInput(tt.input)
			if gotRune != tt.wantRune {
				t.Errorf("filterInput() gotRune = %v, want %v", gotRune, tt.wantRune)
			}
			if gotOK != tt.wantOK {
				t.Errorf("filterInput() gotOK = %v, want %v", gotOK, tt.wantOK)
			}
		})
	}
}
