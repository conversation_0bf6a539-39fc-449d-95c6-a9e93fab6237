// Package terminal provides enhanced terminal input handling
package terminal

import (
	"bufio"
	"errors"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"

	"github.com/chzyer/readline"
	"golang.org/x/term"
)

// InputReader provides enhanced terminal input with history and line editing
type InputReader struct {
	instance   *readline.Instance
	scanner    *bufio.Scanner
	prompt     string
	isTerminal bool
}

// NewInputReader creates a new terminal input reader with the specified prompt
func NewInputReader(prompt string) (*InputReader, error) {
	// Check if stdin is a terminal
	isTerminal := term.IsTerminal(int(os.Stdin.Fd()))

	// If not a terminal (e.g., piped input), use simple scanner
	if !isTerminal {
		return &InputReader{
			scanner:    bufio.NewScanner(os.Stdin),
			prompt:     prompt,
			isTerminal: false,
		}, nil
	}

	// Get home directory for history file
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return nil, fmt.Errorf("failed to get home directory: %w", err)
	}

	// Create .assistant directory if it doesn't exist
	assistantDir := filepath.Join(homeDir, ".assistant")
	if mkdirErr := os.MkdirAll(assistantDir, 0o750); mkdirErr != nil {
		return nil, fmt.Errorf("failed to create .assistant directory: %w", mkdirErr)
	}

	// Configure readline
	config := &readline.Config{
		Prompt:                 prompt,
		HistoryFile:            filepath.Join(assistantDir, "history"),
		DisableAutoSaveHistory: false,
		HistoryLimit:           1000,
		InterruptPrompt:        "^C",
		EOFPrompt:              "exit",

		// Enable enhanced features
		VimMode:             false, // Default to emacs mode (more common)
		EnableMask:          false,
		MaskRune:            '*',
		AutoComplete:        createCompleter(),
		FuncFilterInputRune: filterInput,
	}

	instance, err := readline.NewEx(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create readline instance: %w", err)
	}

	return &InputReader{
		instance:   instance,
		prompt:     prompt,
		isTerminal: true,
	}, nil
}

// IsTerminal returns true if input is from a terminal
func (r *InputReader) IsTerminal() bool {
	return r.isTerminal
}

// ReadLine reads a line from the terminal with full line editing support
func (r *InputReader) ReadLine() (string, error) {
	// Use simple scanner for non-terminal input
	if !r.isTerminal {
		fmt.Print(r.prompt)
		// Force flush for non-terminal output
		_ = os.Stdout.Sync()

		if r.scanner.Scan() {
			line := r.scanner.Text()
			return strings.TrimSpace(line), nil
		}
		if err := r.scanner.Err(); err != nil {
			return "", err
		}
		// EOF reached
		return "", fmt.Errorf("exit")
	}

	// Use readline for terminal input
	line, err := r.instance.Readline()
	if errors.Is(err, readline.ErrInterrupt) {
		// Handle Ctrl+C
		return "", fmt.Errorf("interrupted")
	}
	if errors.Is(err, io.EOF) {
		// Handle Ctrl+D
		return "", fmt.Errorf("exit")
	}
	if err != nil {
		return "", err
	}

	return strings.TrimSpace(line), nil
}

// ReadMultiLine reads multiple lines until the user enters an empty line
// This is useful for entering longer messages or code snippets
func (r *InputReader) ReadMultiLine() (string, error) {
	var lines []string
	originalPrompt := r.prompt

	// First line with original prompt
	firstLine, err := r.ReadLine()
	if err != nil {
		return "", err
	}

	// If first line ends with "...", enter multiline mode
	if !strings.HasSuffix(firstLine, "...") {
		return firstLine, nil
	}

	// Remove the "..." suffix
	firstLine = strings.TrimSuffix(firstLine, "...")
	if firstLine != "" {
		lines = append(lines, firstLine)
	}

	// Continue reading lines with a continuation prompt
	r.SetPrompt("... ")
	defer r.SetPrompt(originalPrompt)

	for {
		var line string
		var err error

		if !r.isTerminal {
			// Use scanner for non-terminal input
			fmt.Print(r.prompt)
			if r.scanner.Scan() {
				line = r.scanner.Text()
			} else {
				if scanErr := r.scanner.Err(); scanErr != nil {
					return "", scanErr
				}
				// EOF reached
				return "", fmt.Errorf("exit")
			}
		} else {
			// Use readline for terminal input
			line, err = r.instance.Readline()
			if errors.Is(err, readline.ErrInterrupt) {
				return "", fmt.Errorf("interrupted")
			}
			if errors.Is(err, io.EOF) {
				return "", fmt.Errorf("exit")
			}
			if err != nil {
				return "", err
			}
		}

		// Empty line ends multiline input
		if strings.TrimSpace(line) == "" {
			break
		}

		lines = append(lines, line)
	}

	return strings.Join(lines, "\n"), nil
}

// SetPrompt updates the prompt string
func (r *InputReader) SetPrompt(prompt string) {
	r.prompt = prompt
	if r.isTerminal && r.instance != nil {
		r.instance.SetPrompt(prompt)
	}
}

// Close cleans up the readline instance
func (r *InputReader) Close() error {
	if r.isTerminal && r.instance != nil {
		return r.instance.Close()
	}
	return nil
}

// createCompleter creates an autocomplete handler for common commands
func createCompleter() *readline.PrefixCompleter {
	return readline.NewPrefixCompleter(
		// Command completions
		readline.PcItem("/help",
			readline.PcItem("?"),
		),
		readline.PcItem("/clear",
			readline.PcItem("c"),
		),
		readline.PcItem("/history",
			readline.PcItem("h"),
			readline.PcItem("hist"),
			readline.PcItem("chat"),
			readline.PcItem("chats"),
		),
		readline.PcItem("/chat"),
		readline.PcItem("/chats"),
		readline.PcItem("/new",
			readline.PcItem("n"),
		),
		readline.PcItem("/temp",
			readline.PcItem("temperature"),
		),
		readline.PcItem("/memory",
			readline.PcItem("m"),
		),
		readline.PcItem("/tools",
			readline.PcItem("t"),
		),
		readline.PcItem("/exit",
			readline.PcItem("quit"),
			readline.PcItem("q"),
		),
		readline.PcItem("/quit"),
		readline.PcItem("/q"),
	)
}

// filterInput filters input characters (can be used to prevent certain characters)
func filterInput(r rune) (rune, bool) {
	// Allow all characters by default
	return r, true
}

// ClearScreen clears the terminal screen
func ClearScreen() {
	// Use ANSI escape codes for cross-platform compatibility
	fmt.Print("\033[H\033[2J")
}
