// Package cli provides command-line interface implementations
package cli

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgxpool"

	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/koopa0/assistant-go/internal/assistant"
	"github.com/koopa0/assistant-go/internal/conversation"
	"github.com/koopa0/assistant-go/internal/platform/config"
	"github.com/koopa0/assistant-go/internal/platform/logger"
)

// Session holds all state and dependencies for a CLI session.
// It follows the same pattern as ai.Service and assistant.Assistant.
type Session struct {
	// Core dependencies - set once during initialization
	config    *config.Config
	ai        *ai.Service
	db        *pgxpool.Pool
	assistant assistant.Assistant
	logger    logger.Logger

	// Session state
	conversationID uuid.UUID
	convManager    *conversation.Manager

	// Chat options
	options ChatOptions
}

// ChatOptions configures chat behavior
type ChatOptions struct {
	Temperature float32
	MaxTokens   int
}

// NewSession creates a new CLI session with validated dependencies.
// Returns an error if required dependencies are missing.
func NewSession(cfg *config.Config, ai *ai.Service, db *pgxpool.Pool, assistant assistant.Assistant, log logger.Logger) (*Session, error) {
	if cfg == nil {
		return nil, fmt.Errorf("configuration is required")
	}
	if ai == nil {
		return nil, fmt.Errorf("AI service is required")
	}
	if assistant == nil {
		return nil, fmt.Errorf("assistant is required")
	}
	if log == nil {
		return nil, fmt.Errorf("logger is required")
	}

	s := &Session{
		config:    cfg,
		ai:        ai,
		db:        db,
		assistant: assistant,
		logger:    log,
		options: ChatOptions{
			Temperature: 0.7,
			MaxTokens:   2000,
		},
	}

	// Initialize conversation manager if we have database
	if db != nil {
		convStore := conversation.NewStore(db, conversation.WithLogger(log))
		var err error
		s.convManager, err = conversation.NewManager(convStore,
			conversation.WithManagerLogger(log.WithComponent("conversation")),
			conversation.WithAutoSave(true, 2*time.Second),
		)
		if err != nil {
			log.Warn("failed to create conversation manager", "error", err)
		}
	}

	return s, nil
}

// Config returns the configuration
func (s *Session) Config() *config.Config {
	return s.config
}

// Chat delegates to the internal assistant
func (s *Session) Chat(ctx context.Context, req assistant.Request) (*assistant.Response, error) {
	return s.assistant.Chat(ctx, req)
}

// Stream delegates to the internal assistant
func (s *Session) Stream(ctx context.Context, req assistant.Request) (<-chan assistant.StreamChunk, error) {
	return s.assistant.Stream(ctx, req)
}

// Logger returns the logger as interface{} to avoid import cycles
func (s *Session) Logger() interface{} {
	return s.logger
}

// Database returns the database connection pool as interface{} to avoid import cycles
func (s *Session) Database() interface{} {
	return s.db
}

// ConversationID returns the current conversation ID
func (s *Session) ConversationID() uuid.UUID {
	return s.conversationID
}

// SetConversationUUID updates the conversation ID
func (s *Session) SetConversationUUID(id uuid.UUID) {
	s.conversationID = id
}

// Options returns the current chat options
func (s *Session) Options() ChatOptions {
	return s.options
}

// SetOptions updates the chat options
func (s *Session) SetOptions(opts ChatOptions) {
	s.options = opts
}

// OwnerName returns the owner's name
func (s *Session) OwnerName() string {
	return s.config.Owner.Name
}

// GetTemperature returns the temperature setting
func (s *Session) GetTemperature() float32 {
	return s.options.Temperature
}

// SetTemperature sets the temperature
func (s *Session) SetTemperature(temp float32) {
	s.options.Temperature = temp
}

// GetMaxTokens returns the max tokens setting
func (s *Session) GetMaxTokens() int {
	return s.options.MaxTokens
}

// SetMaxTokens sets the max tokens
func (s *Session) SetMaxTokens(tokens int) {
	s.options.MaxTokens = tokens
}

// ConversationManager returns the conversation manager
func (s *Session) ConversationManager() interface{} {
	return s.convManager
}

// SetConversationID sets the conversation ID from a string
func (s *Session) SetConversationID(idStr string) error {
	// Try to parse as UUID
	id, err := uuid.Parse(idStr)
	if err != nil {
		// Try to load by number (last N conversations)
		// For now, just return error
		return fmt.Errorf("invalid conversation ID: %s", idStr)
	}

	if s.convManager != nil {
		// Use manager to continue conversation
		ctx := context.Background()
		conv, err := s.convManager.Continue(ctx, id)
		if err != nil {
			return err
		}
		s.conversationID = conv.ID
	} else {
		s.conversationID = id
	}

	return nil
}
