// Package chat provides markdown rendering support
package chat

import (
	"fmt"
	"strings"

	"github.com/charmbracelet/glamour"
	"github.com/charmbracelet/lipgloss"
)

// MarkdownRenderer handles markdown rendering for chat messages
type MarkdownRenderer struct {
	renderer          *glamour.TermRenderer
	syntaxHighlighter *SyntaxHighlighter
	enabled           bool
}

// NewMarkdownRenderer creates a new markdown renderer
func NewMarkdownRenderer(enabled bool) (*MarkdownRenderer, error) {
	if !enabled {
		return &MarkdownRenderer{enabled: false}, nil
	}

	// Create renderer with dark style and enhanced code highlighting
	renderer, err := glamour.NewTermRenderer(
		glamour.WithStylePath("dark"),
		glamour.WithWordWrap(100),
		glamour.WithPreservedNewLines(),
	)
	if err != nil {
		return nil, fmt.Errorf("create markdown renderer: %w", err)
	}

	return &MarkdownRenderer{
		renderer:          renderer,
		syntaxHighlighter: NewSyntaxHighlighter(),
		enabled:           enabled,
	}, nil
}

// <PERSON><PERSON> renders markdown content
func (r *MarkdownRenderer) Render(content string) (string, error) {
	if !r.enabled || r.renderer == nil {
		return content, nil
	}

	// Check if content likely contains markdown
	if !containsMarkdown(content) {
		return content, nil
	}

	rendered, err := r.renderer.Render(content)
	if err != nil {
		// Fallback to original content on error
		return content, nil
	}

	// Clean up extra newlines that glamour might add
	rendered = strings.TrimRight(rendered, "\n")

	// Add consistent indentation
	lines := strings.Split(rendered, "\n")
	for i := range lines {
		if lines[i] != "" {
			lines[i] = "  " + lines[i]
		}
	}

	return strings.Join(lines, "\n"), nil
}

// SetEnabled toggles markdown rendering
func (r *MarkdownRenderer) SetEnabled(enabled bool) {
	r.enabled = enabled
}

// IsEnabled returns whether markdown rendering is enabled
func (r *MarkdownRenderer) IsEnabled() bool {
	return r.enabled
}

// containsMarkdown checks if content likely contains markdown
func containsMarkdown(content string) bool {
	// Check for common markdown patterns
	patterns := []string{
		"```",   // Code blocks
		"**",    // Bold
		"*",     // Italic/lists
		"#",     // Headers
		"[",     // Links
		"|",     // Tables
		">",     // Quotes
		"---",   // Horizontal rules
		"```go", // Language-specific code blocks
		"```python",
		"```javascript",
	}

	for _, pattern := range patterns {
		if strings.Contains(content, pattern) {
			return true
		}
	}

	return false
}

// RenderInlineCode renders inline code with styling
func RenderInlineCode(code string) string {
	style := lipgloss.NewStyle().
		Foreground(lipgloss.Color("219")).
		Background(lipgloss.Color("236")).
		Padding(0, 1)
	return style.Render(code)
}
