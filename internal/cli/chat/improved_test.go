package chat

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"strings"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/koopa0/assistant-go/internal/assistant"
)

// mockSession implements Session interface for testing
type mockSession struct {
	convID      uuid.UUID
	temperature float32
	maxTokens   int
	streamFunc  func(context.Context, assistant.Request) (<-chan assistant.StreamChunk, error)
}

func (m *mockSession) Chat(ctx context.Context, req assistant.Request) (*assistant.Response, error) {
	return nil, errors.New("not implemented")
}

func (m *mockSession) Stream(ctx context.Context, req assistant.Request) (<-chan assistant.StreamChunk, error) {
	if m.streamFunc != nil {
		return m.streamFunc(ctx, req)
	}
	return nil, errors.New("not implemented")
}

func (m *mockSession) ConversationID() uuid.UUID {
	return m.convID
}

func (m *mockSession) SetConversationUUID(id uuid.UUID) {
	m.convID = id
}

func (m *mockSession) SetConversationID(idStr string) error {
	parsedID, err := uuid.Parse(idStr)
	if err != nil {
		return err
	}
	m.convID = parsedID
	return nil
}

func (m *mockSession) ConversationManager() interface{} {
	return nil
}

func (m *mockSession) OwnerName() string {
	return "Test User"
}

func (m *mockSession) GetTemperature() float32 {
	return m.temperature
}

func (m *mockSession) SetTemperature(temp float32) {
	m.temperature = temp
}

func (m *mockSession) GetMaxTokens() int {
	return m.maxTokens
}

func (m *mockSession) SetMaxTokens(tokens int) {
	m.maxTokens = tokens
}

func (m *mockSession) Database() interface{} {
	return nil
}

func (m *mockSession) Logger() interface{} {
	return nil
}

func TestImprovedController_StreamError(t *testing.T) {
	tests := []struct {
		name             string
		streamFunc       func(context.Context, assistant.Request) (<-chan assistant.StreamChunk, error)
		expectedOutput   string
		shouldContain    []string
		shouldNotContain []string
	}{
		{
			name: "stream error before content",
			streamFunc: func(ctx context.Context, req assistant.Request) (<-chan assistant.StreamChunk, error) {
				ch := make(chan assistant.StreamChunk, 1)
				go func() {
					defer close(ch)
					// Send error immediately
					ch <- assistant.StreamChunk{
						Error: errors.New("connection failed"),
					}
				}()
				return ch, nil
			},
			shouldNotContain: []string{"◉ Generating response..."}, // Should be cleared
			// Note: Error message is handled by the caller, not in processMessage
		},
		{
			name: "stream error after content",
			streamFunc: func(ctx context.Context, req assistant.Request) (<-chan assistant.StreamChunk, error) {
				ch := make(chan assistant.StreamChunk, 2)
				go func() {
					defer close(ch)
					// Send some content first
					ch <- assistant.StreamChunk{
						Content: "Hello, I can help",
					}
					// Then send error
					ch <- assistant.StreamChunk{
						Error: errors.New("interrupted"),
					}
				}()
				return ch, nil
			},
			shouldContain: []string{
				"Hello, I can help",
			},
			shouldNotContain: []string{"◉ Generating response..."}, // Should be cleared when content starts
		},
		{
			name: "successful stream",
			streamFunc: func(ctx context.Context, req assistant.Request) (<-chan assistant.StreamChunk, error) {
				ch := make(chan assistant.StreamChunk, 3)
				go func() {
					defer close(ch)
					ch <- assistant.StreamChunk{Content: "Hello"}
					ch <- assistant.StreamChunk{Content: " world!"}
					ch <- assistant.StreamChunk{Done: true}
				}()
				return ch, nil
			},
			shouldContain:    []string{"Hello world!"},
			shouldNotContain: []string{"◉ Generating response...", "Error:"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock session
			session := &mockSession{
				temperature: 0.7,
				maxTokens:   1000,
				streamFunc:  tt.streamFunc,
			}

			// Create output buffer
			var output bytes.Buffer

			// Create controller
			controller, err := NewImprovedController(session, &output)
			require.NoError(t, err)

			// Process a test message
			err = controller.processMessage("test input")

			// Check output
			outputStr := output.String()
			fmt.Printf("Test %s output:\n%s\n", tt.name, outputStr)
			fmt.Printf("Raw output bytes: %q\n", outputStr)

			// For error cases, verify error was returned
			if strings.Contains(tt.name, "error") {
				assert.Error(t, err)
				// Error message will be printed by the caller, not in processMessage
			}

			// Verify expected content
			for _, expected := range tt.shouldContain {
				assert.Contains(t, outputStr, expected, "Output should contain: %s", expected)
			}

			// For "shouldNotContain", we need to check if it was cleared
			// The progress indicator might still be in the output but followed by clear sequence
			for _, unexpected := range tt.shouldNotContain {
				if unexpected == "◉ Generating response..." {
					// Check that if it exists, it's followed by a clear sequence
					if strings.Contains(outputStr, unexpected) {
						// It should be cleared with \r\x1b[K
						assert.Contains(t, outputStr, "\r\x1b[K", "Progress indicator should be cleared")
					}
				} else {
					assert.NotContains(t, outputStr, unexpected, "Output should NOT contain: %s", unexpected)
				}
			}
		})
	}
}
