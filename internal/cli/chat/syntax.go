// Package chat provides syntax highlighting support
package chat

import (
	"bytes"
	"fmt"
	"strings"

	"github.com/alecthomas/chroma/v2"
	"github.com/alecthomas/chroma/v2/formatters"
	"github.com/alecthomas/chroma/v2/lexers"
	"github.com/alecthomas/chroma/v2/styles"
)

// SyntaxHighlighter provides code syntax highlighting
type SyntaxHighlighter struct {
	style     *chroma.Style
	formatter chroma.Formatter
}

// NewSyntaxHighlighter creates a new syntax highlighter
func NewSyntaxHighlighter() *SyntaxHighlighter {
	// Use a terminal-friendly style
	style := styles.Get("monokai")
	if style == nil {
		style = styles.Fallback
	}

	// Create a terminal formatter
	formatter := formatters.Get("terminal256")
	if formatter == nil {
		formatter = formatters.Fallback
	}

	return &SyntaxHighlighter{
		style:     style,
		formatter: formatter,
	}
}

// Highlight applies syntax highlighting to code
func (h *SyntaxHighlighter) Highlight(code, language string) (string, error) {
	// Get lexer for the language
	lexer := lexers.Get(language)
	if lexer == nil {
		// Try to analyze the code to detect language
		lexer = lexers.Analyse(code) //nolint:misspell // Analyse is the correct spelling in Chroma API
		if lexer == nil {
			// Fall back to plain text
			return code, nil
		}
	}

	// Tokenize the code
	iterator, err := lexer.Tokenise(nil, code)
	if err != nil {
		return code, fmt.Errorf("tokenize: %w", err)
	}

	// Format the tokens
	var buf bytes.Buffer
	err = h.formatter.Format(&buf, h.style, iterator)
	if err != nil {
		return code, fmt.Errorf("format: %w", err)
	}

	// Remove trailing newline that formatter might add
	result := buf.String()
	result = strings.TrimRight(result, "\n")

	return result, nil
}

// HighlightInline highlights inline code snippets
func (h *SyntaxHighlighter) HighlightInline(code string) string {
	// For inline code, detect language and apply minimal highlighting
	lexer := lexers.Analyse(code) //nolint:misspell // Analyse is the correct spelling in Chroma API
	if lexer == nil {
		return code
	}

	highlighted, err := h.Highlight(code, lexer.Config().Name)
	if err != nil {
		return code
	}

	return highlighted
}

// DetectLanguage attempts to detect the programming language
func DetectLanguage(code string) string {
	lexer := lexers.Analyse(code) //nolint:misspell // Analyse is the correct spelling in Chroma API
	if lexer == nil {
		return ""
	}
	return lexer.Config().Name
}

// FormatCodeBlock formats a code block with proper indentation
func FormatCodeBlock(code, language string, indent string) string {
	lines := strings.Split(code, "\n")
	var result strings.Builder

	// Add language indicator if provided
	if language != "" {
		result.WriteString(indent)
		result.WriteString(fmt.Sprintf("```%s\n", language))
	} else {
		result.WriteString(indent)
		result.WriteString("```\n")
	}

	// Add code lines with indentation
	for _, line := range lines {
		result.WriteString(indent)
		result.WriteString(line)
		result.WriteString("\n")
	}

	// Close code block
	result.WriteString(indent)
	result.WriteString("```")

	return result.String()
}
