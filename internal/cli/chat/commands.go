// Package chat provides command handling for chat interface
package chat

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/google/uuid"
	conversationsUI "github.com/koopa0/assistant-go/internal/cli/ui/views/conversations"
	"github.com/koopa0/assistant-go/internal/conversation"
)

// Command represents a chat command
type Command struct {
	Name        string
	Aliases     []string
	Description string
	Handler     func(args []string) error
}

// CommandRegistry manages available commands
type CommandRegistry struct {
	commands map[string]*Command
	handler  *Handler
}

// NewCommandRegistry creates a new command registry
func NewCommandRegistry(handler *Handler) *CommandRegistry {
	r := &CommandRegistry{
		commands: make(map[string]*Command),
		handler:  handler,
	}

	// Register default commands
	r.registerDefaultCommands()
	return r
}

// registerDefaultCommands registers the default set of commands
func (r *CommandRegistry) registerDefaultCommands() {
	// Help command
	r.Register(&Command{
		Name:        "help",
		Aliases:     []string{"?"},
		Description: "Show available commands",
		Handler:     r.helpCommand,
	})

	// Clear command
	r.Register(&Command{
		Name:        "clear",
		Aliases:     []string{"c"},
		Description: "Clear the screen",
		Handler:     r.clearCommand,
	})

	// Temperature command
	r.Register(&Command{
		Name:        "temp",
		Aliases:     []string{"temperature"},
		Description: "Set temperature (0-2)",
		Handler:     r.tempCommand,
	})

	// Exit command
	r.Register(&Command{
		Name:        "exit",
		Aliases:     []string{"quit", "q"},
		Description: "Exit the chat",
		Handler:     r.exitCommand,
	})

	// Memory command (placeholder)
	r.Register(&Command{
		Name:        "memory",
		Aliases:     []string{"m"},
		Description: "Browse memory (coming soon)",
		Handler:     r.memoryCommand,
	})

	// Conversation commands
	r.Register(&Command{
		Name:        "history",
		Aliases:     []string{"h", "hist", "chat", "chats"},
		Description: "Show conversation history",
		Handler:     r.historyCommand,
	})

	r.Register(&Command{
		Name:        "new",
		Aliases:     []string{"n"},
		Description: "Start a new conversation",
		Handler:     r.newConversationCommand,
	})

	// Tools toggle command
	r.Register(&Command{
		Name:        "tools",
		Aliases:     []string{"t"},
		Description: "Toggle tool execution details",
		Handler:     r.toolsCommand,
	})
}

// Register adds a command to the registry
func (r *CommandRegistry) Register(cmd *Command) {
	r.commands[cmd.Name] = cmd
	for _, alias := range cmd.Aliases {
		r.commands[alias] = cmd
	}
}

// Execute processes a command string
func (r *CommandRegistry) Execute(input string) error {
	parts := strings.Fields(input)
	if len(parts) == 0 || !strings.HasPrefix(parts[0], "/") {
		return fmt.Errorf("not a command")
	}

	cmdName := strings.TrimPrefix(parts[0], "/")
	args := parts[1:]

	cmd, exists := r.commands[cmdName]
	if !exists {
		r.handler.RenderErrorMessage(fmt.Sprintf("Unknown command: /%s", cmdName))
		return nil
	}

	return cmd.Handler(args)
}

// IsCommand checks if input is a command
func (r *CommandRegistry) IsCommand(input string) bool {
	return strings.HasPrefix(strings.TrimSpace(input), "/")
}

// Command handlers

func (r *CommandRegistry) helpCommand(args []string) error {
	var help strings.Builder
	help.WriteString("Available commands:\n\n")

	// Collect unique commands (avoid duplicates from aliases)
	seen := make(map[*Command]bool)
	var uniqueCmds []*Command

	for _, cmd := range r.commands {
		if !seen[cmd] {
			seen[cmd] = true
			uniqueCmds = append(uniqueCmds, cmd)
		}
	}

	// Display commands
	for _, cmd := range uniqueCmds {
		help.WriteString(fmt.Sprintf("  /%s", cmd.Name))
		if len(cmd.Aliases) > 0 {
			help.WriteString(", ")
			for i, alias := range cmd.Aliases {
				help.WriteString(fmt.Sprintf("/%s", alias))
				if i < len(cmd.Aliases)-1 {
					help.WriteString(", ")
				}
			}
		}
		help.WriteString(fmt.Sprintf(" - %s\n", cmd.Description))
	}

	r.handler.RenderSystemMessage(help.String())
	return nil
}

func (r *CommandRegistry) clearCommand(args []string) error {
	// Clear screen using ANSI escape codes
	fmt.Print("\033[H\033[2J")
	return nil
}

func (r *CommandRegistry) tempCommand(args []string) error {
	if len(args) == 0 {
		current := r.handler.session.GetTemperature()
		r.handler.RenderSystemMessage(fmt.Sprintf("Current temperature: %.1f", current))
		return nil
	}

	temp, err := strconv.ParseFloat(args[0], 32)
	if err != nil || temp < 0 || temp > 2 {
		r.handler.RenderErrorMessage("Temperature must be a number between 0 and 2")
		return nil
	}

	// Update temperature in session
	r.handler.session.SetTemperature(float32(temp))

	r.handler.RenderSystemMessage(fmt.Sprintf("Temperature set to %.1f", temp))
	return nil
}

func (r *CommandRegistry) exitCommand(args []string) error {
	return fmt.Errorf("exit requested")
}

func (r *CommandRegistry) memoryCommand(args []string) error {
	r.handler.RenderSystemMessage("Memory browser coming soon...")
	return nil
}

func (r *CommandRegistry) toolsCommand(args []string) error {
	// Toggle tool details display
	showDetails := r.handler.ToggleToolDetails()

	if showDetails {
		r.handler.RenderSystemMessage("Tool execution details: ON 🔧")
	} else {
		r.handler.RenderSystemMessage("Tool execution details: OFF (silent mode)")
	}

	return nil
}

func (r *CommandRegistry) historyCommand(args []string) error {
	// Get conversation manager from session
	session := r.handler.session
	convManager, ok := session.(interface{ ConversationManager() interface{} })
	if !ok {
		r.handler.RenderErrorMessage("Conversation management not available")
		return nil
	}

	mgr := convManager.ConversationManager()
	if mgr == nil {
		r.handler.RenderErrorMessage("No conversation manager configured")
		return nil
	}

	// Get conversation lister
	lister, ok := mgr.(interface {
		ListConversations(ctx context.Context, limit int) ([]*conversation.Conversation, error)
	})
	if !ok {
		r.handler.RenderErrorMessage("Conversation listing not supported")
		return nil
	}

	// List conversations
	ctx := context.Background()
	conversations, err := lister.ListConversations(ctx, 20) // Get 20 recent conversations
	if err != nil {
		r.handler.RenderErrorMessage(fmt.Sprintf("Failed to list conversations: %v", err))
		return nil
	}

	if len(conversations) == 0 {
		r.handler.RenderSystemMessage("No conversations found. Start chatting to create one!")
		return nil
	}

	// Run the conversation list UI
	listModel := conversationsUI.NewModel(conversations)
	p := tea.NewProgram(listModel, tea.WithAltScreen())

	finalModel, err := p.Run()
	if err != nil {
		r.handler.RenderErrorMessage(fmt.Sprintf("UI error: %v", err))
		return nil
	}

	// Check if a conversation was selected
	if m, ok := finalModel.(conversationsUI.Model); ok {
		if selected := m.SelectedConversation(); selected != nil {
			// Continue the selected conversation
			return r.continueConversation(selected.ID.String())
		}
	}

	return nil
}

// continueConversation handles the actual conversation continuation
func (r *CommandRegistry) continueConversation(idStr string) error {
	// Get conversation manager from session
	session := r.handler.session
	convManager, ok := session.(interface {
		ConversationManager() interface{}
		SetConversationID(id string) error
	})
	if !ok {
		r.handler.RenderErrorMessage("Conversation continuation not supported")
		return nil
	}

	// Try to set the conversation ID
	if err := convManager.SetConversationID(idStr); err != nil {
		r.handler.RenderErrorMessage(fmt.Sprintf("Failed to continue conversation: %v", err))
		return nil
	}

	r.handler.RenderSystemMessage("✅ Switched to conversation")
	r.handler.RenderSystemMessage("You can now continue your previous conversation.")

	return nil
}

func (r *CommandRegistry) newConversationCommand(args []string) error {
	// Reset conversation ID to start fresh
	r.handler.session.SetConversationUUID(uuid.Nil)
	r.handler.RenderSystemMessage("✨ Started a new conversation!")
	return nil
}

// ParseCommand splits a command string into command name and arguments
func ParseCommand(input string) (string, []string) {
	parts := strings.Fields(input)
	if len(parts) == 0 {
		return "", nil
	}
	return parts[0], parts[1:]
}
