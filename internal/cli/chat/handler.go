// Package chat provides chat functionality for the CLI
package chat

import (
	"context"
	"fmt"
	"io"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"

	"github.com/koopa0/assistant-go/internal/assistant"
	"github.com/koopa0/assistant-go/internal/cli/ui/components/chat"
	"github.com/koopa0/assistant-go/internal/cli/ui/components/styles"
)

// Session defines the interface for session operations
type Session interface {
	// Chat operations - what we actually need from the assistant
	Cha<PERSON>(ctx context.Context, req assistant.Request) (*assistant.Response, error)
	Stream(ctx context.Context, req assistant.Request) (<-chan assistant.StreamChunk, error)

	// Conversation management
	ConversationID() uuid.UUID
	SetConversationUUID(id uuid.UUID)
	SetConversationID(idStr string) error
	ConversationManager() interface{}
	OwnerName() string

	// Configuration
	GetTemperature() float32
	SetTemperature(float32)
	GetMaxTokens() int
	SetMaxTokens(int)

	// Resources (as interface{} to avoid import cycles)
	Database() interface{}
	Logger() interface{}
}

// <PERSON><PERSON> handles chat interactions with the assistant.
type Handler struct {
	session         Session
	output          io.Writer
	renderer        *chat.MessageRenderer
	styles          *styles.Styles
	toolStates      map[string]*ToolState
	mu              sync.RWMutex
	showToolDetails bool // Toggle for tool execution details
}

// ToolState tracks the execution state of a tool.
type ToolState struct {
	Name      string
	StartTime time.Time
	EndTime   time.Time
	Status    string // "running", "success", "error"
	Duration  time.Duration
}

// NewHandler creates a new chat handler.
func NewHandler(session Session, output io.Writer) (*Handler, error) {
	if session == nil {
		return nil, fmt.Errorf("session cannot be nil")
	}
	if output == nil {
		return nil, fmt.Errorf("output cannot be nil")
	}

	s := styles.DefaultStyles()
	renderer := chat.NewMessageRenderer(s, 80) // Default width

	return &Handler{
		session:         session,
		output:          output,
		renderer:        renderer,
		styles:          s,
		toolStates:      make(map[string]*ToolState),
		showToolDetails: false, // Default to minimal display
	}, nil
}

// ToggleToolDetails toggles the display of tool execution details
func (h *Handler) ToggleToolDetails() bool {
	h.mu.Lock()
	defer h.mu.Unlock()

	h.showToolDetails = !h.showToolDetails

	// Update renderer mode
	if h.showToolDetails {
		h.renderer.SetToolDisplayMode(chat.ToolDisplayDetail)
	} else {
		h.renderer.SetToolDisplayMode(chat.ToolDisplaySilent)
	}

	return h.showToolDetails
}

// ProcessStreamingResponse handles streaming responses with visualization.
func (h *Handler) ProcessStreamingResponse(req assistant.Request) error {
	ctx := context.Background()
	stream, err := h.session.Stream(ctx, req)
	if err != nil {
		return fmt.Errorf("start stream: %w", err)
	}

	// No label needed with enhanced renderer

	var content strings.Builder
	var toolsExecuted []string

	for chunk := range stream {
		if chunk.Error != nil {
			return fmt.Errorf("stream error: %w", chunk.Error)
		}

		// Handle content
		if chunk.Content != "" {
			content.WriteString(chunk.Content)
			fmt.Fprint(h.output, chunk.Content)
		}

		// Handle tool calls silently
		if len(chunk.ToolCalls) > 0 {
			for _, tool := range chunk.ToolCalls {
				// Only track if showing details
				if h.showToolDetails {
					h.trackToolStart(tool.Name)
				}
				toolsExecuted = append(toolsExecuted, tool.Name)
			}
		}

		// Update conversation ID when done
		if chunk.Done {
			if h.session.ConversationID() == uuid.Nil {
				h.session.SetConversationUUID(req.ConversationID)
			}

			// Mark tools as completed if showing details
			if h.showToolDetails {
				for _, toolName := range toolsExecuted {
					h.trackToolEnd(toolName, nil)
				}
			}
		}
	}

	fmt.Fprintln(h.output) // End with newline

	// Only show tool summary if in detail mode
	if h.showToolDetails && len(toolsExecuted) > 0 {
		h.showToolSummary(toolsExecuted)
	}

	return nil
}

// ProcessNormalResponse handles non-streaming responses with visualization.
func (h *Handler) ProcessNormalResponse(req assistant.Request) error {
	// Show elegant thinking animation
	fmt.Fprint(h.output, h.renderer.RenderThinking())

	ctx := context.Background()
	resp, err := h.session.Chat(ctx, req)
	if err != nil {
		// Clear thinking indicator
		fmt.Fprint(h.output, "\r\033[K")
		return fmt.Errorf("chat request: %w", err)
	}

	// Clear thinking indicator
	fmt.Fprint(h.output, "\r\033[K")

	// Create message with proper styling
	msg := chat.Message{
		Type:    chat.AssistantMessage,
		Content: resp.Content,
	}

	// Add tool info if available
	if resp.Metadata.ToolsUsed && len(resp.Metadata.ToolNames) > 0 {
		msg.Tool = &chat.ToolInfo{
			Name:     resp.Metadata.ToolNames[0],
			Status:   "success",
			Duration: resp.Metadata.Duration.Round(time.Millisecond).String(),
		}
	}

	// Render the message
	rendered := h.renderer.Render(msg)
	fmt.Fprintln(h.output, rendered)

	// Update conversation ID if new
	if h.session.ConversationID() == uuid.Nil {
		h.session.SetConversationUUID(resp.ConversationID)
	}

	return nil
}

// RenderUserMessage renders a user message with styling.
func (h *Handler) RenderUserMessage(content string) {
	msg := chat.Message{
		Type:    chat.UserMessage,
		Content: content,
	}
	rendered := h.renderer.Render(msg)
	fmt.Fprintln(h.output, rendered)
}

// RenderSystemMessage renders a system message.
func (h *Handler) RenderSystemMessage(content string) {
	msg := chat.Message{
		Type:    chat.SystemMessage,
		Content: content,
	}
	rendered := h.renderer.Render(msg)
	fmt.Fprintln(h.output, rendered)
}

// RenderErrorMessage renders an error message.
func (h *Handler) RenderErrorMessage(content string) {
	msg := chat.Message{
		Type:    chat.ErrorMessage,
		Content: content,
	}
	rendered := h.renderer.Render(msg)
	fmt.Fprintln(h.output, rendered)
}

// UpdateWidth updates the renderer width for responsive design.
func (h *Handler) UpdateWidth(width int) {
	h.renderer.UpdateWidth(width)
}

// trackToolStart records when a tool starts executing.
func (h *Handler) trackToolStart(toolName string) {
	h.mu.Lock()
	defer h.mu.Unlock()

	h.toolStates[toolName] = &ToolState{
		Name:      toolName,
		StartTime: time.Now(),
		Status:    "running",
	}

	// Show minimal tool execution indicator
	if h.showToolDetails {
		fmt.Fprintf(h.output, "\n%s %s %s\n",
			h.styles.Tool.Render("🔧"),
			h.styles.Info.Render(toolName),
			h.styles.Timestamp.Render("executing..."),
		)
	}
}

// trackToolEnd records when a tool finishes executing.
func (h *Handler) trackToolEnd(toolName string, err error) {
	h.mu.Lock()
	defer h.mu.Unlock()

	if state, exists := h.toolStates[toolName]; exists {
		state.EndTime = time.Now()
		state.Duration = state.EndTime.Sub(state.StartTime)

		if err != nil {
			state.Status = "error"
		} else {
			state.Status = "success"
		}

		// Update the inline display only if showing details
		if h.showToolDetails {
			icon := "✓"
			style := h.styles.Success
			if state.Status == "error" {
				icon = "✗"
				style = h.styles.Error
			}

			fmt.Fprintf(h.output, "\033[1A\033[K%s %s %s\n",
				style.Render(icon),
				h.styles.Info.Render(toolName),
				h.styles.Timestamp.Render(fmt.Sprintf("(%s)", state.Duration.Round(time.Millisecond))),
			)
		}
	}
}

// showToolSummary displays a summary of executed tools.
func (h *Handler) showToolSummary(toolNames []string) {
	h.mu.RLock()
	defer h.mu.RUnlock()

	var parts []string
	for _, name := range toolNames {
		if state, exists := h.toolStates[name]; exists {
			icon := "✓"
			if state.Status == "error" {
				icon = "✗"
			}

			part := fmt.Sprintf("%s %s (%s)", icon, name, state.Duration.Round(time.Millisecond))
			parts = append(parts, part)
		}
	}

	if len(parts) > 0 {
		summary := h.styles.Tool.Render("Tools: " + strings.Join(parts, " | "))
		fmt.Fprintf(h.output, "%s\n", summary)
	}

	// Clear tool states for next interaction
	h.toolStates = make(map[string]*ToolState)
}
