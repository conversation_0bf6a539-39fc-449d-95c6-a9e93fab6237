// Package chat provides the main chat functionality
package chat

import (
	"io"
	"os"

	"golang.org/x/term"
)

// Controller orchestrates the chat interface
type Controller struct {
	session  Session
	handler  *Hand<PERSON>
	commands *CommandRegistry
	output   io.Writer
}

// NewController creates a new chat controller
func NewController(session Session, output io.Writer) (*Controller, error) {
	handler, err := NewHandler(session, output)
	if err != nil {
		return nil, err
	}

	// Update handler width if terminal
	if width, _, err := term.GetSize(int(os.Stdout.Fd())); err == nil && width > 0 {
		handler.UpdateWidth(width)
	}

	return &Controller{
		session:  session,
		handler:  handler,
		commands: NewCommandRegistry(handler),
		output:   output,
	}, nil
}

// Run starts the interactive chat session
func (c *Controller) Run() error {
	// Use improved terminal interface
	improved, err := NewImprovedController(c.session, c.output)
	if err != nil {
		return err
	}
	return improved.Run()
}
