// Package chat provides legacy chat handling functionality for backward compatibility
package chat

import (
	"context"
	"fmt"
	"io"
	"os"
	"strings"

	"github.com/google/uuid"

	"github.com/koopa0/assistant-go/internal/assistant"
)

// ErrExitRequested is returned when the user requests to exit
var ErrExitRequested = fmt.Errorf("exit requested")

// LegacyHandler processes chat interactions (legacy).
type LegacyHandler struct {
	session Session
	output  io.Writer
}

// NewChatHandler creates a handler for chat interactions (legacy).
func NewChatHandler(session Session, output io.Writer) (*LegacyHandler, error) {
	if session == nil {
		return nil, fmt.Errorf("session is required")
	}
	if output == nil {
		output = os.Stdout
	}

	return &LegacyHandler{
		session: session,
		output:  output,
	}, nil
}

// ProcessInput handles a single user input.
func (h *LegacyHandler) ProcessInput(input string) error {
	input = strings.TrimSpace(input)
	if input == "" {
		return nil
	}

	// Check if input is a command
	if strings.HasPrefix(input, "/") {
		return h.handleCommand(input)
	}

	// Process as chat message
	return h.handleChatMessage(input)
}

// handleCommand processes command inputs.
func (h *LegacyHandler) handleCommand(input string) error {
	cmd, args := parseCommand(input)

	switch cmd {
	case "/help", "/h":
		return h.showHelp()
	case "/clear", "/c":
		h.session.SetConversationUUID(uuid.Nil)
		fmt.Fprintln(h.output, "Started new conversation")
		return nil
	case "/exit", "/quit", "/q":
		return ErrExitRequested
	case "/temp":
		return h.setTemperature(args)
	default:
		return fmt.Errorf("unknown command: %s", cmd)
	}
}

// handleChatMessage processes regular chat messages.
func (h *LegacyHandler) handleChatMessage(input string) error {
	// Build request
	req := assistant.Request{
		Query:          input,
		ConversationID: h.session.ConversationID(),
		Options: assistant.Options{
			Temperature:  h.session.GetTemperature(),
			MaxTokens:    h.session.GetMaxTokens(),
			EnableMemory: true,
			EnableTools:  true,
		},
	}

	// Always use streaming for consistency
	return h.processStreamingResponse(req)
}

// processStreamingResponse handles streaming AI responses.
func (h *LegacyHandler) processStreamingResponse(req assistant.Request) error {
	ctx := context.Background()
	stream, err := h.session.Stream(ctx, req)
	if err != nil {
		return fmt.Errorf("start stream: %w", err)
	}

	var hasContent bool
	for chunk := range stream {
		if chunk.Error != nil {
			return fmt.Errorf("stream error: %w", chunk.Error)
		}

		if chunk.Content != "" {
			fmt.Fprint(h.output, chunk.Content)
			hasContent = true
		}

		// Update conversation ID when done
		if chunk.Done && h.session.ConversationID() == uuid.Nil {
			h.session.SetConversationUUID(req.ConversationID)
		}
	}

	if hasContent {
		fmt.Fprintln(h.output) // End with newline
	}

	return nil
}

// showHelp displays available commands.
func (h *LegacyHandler) showHelp() error {
	help := `Available commands:
  /help, /h        Show this help message
  /clear, /c       Start a new conversation
  /exit, /quit, /q Exit the chat
  /temp <value>    Set temperature (0-2)

Type your message to chat with the assistant.`

	fmt.Fprintln(h.output, help)
	return nil
}

// setTemperature updates the temperature setting.
func (h *LegacyHandler) setTemperature(args string) error {
	var temp float32
	if _, err := fmt.Sscanf(args, "%f", &temp); err != nil {
		return fmt.Errorf("invalid temperature value")
	}

	if temp < 0 || temp > 2 {
		return fmt.Errorf("temperature must be between 0 and 2")
	}

	h.session.SetTemperature(temp)

	fmt.Fprintf(h.output, "Temperature set to %.1f\n", temp)
	return nil
}

// parseCommand splits a command string into command and arguments.
func parseCommand(input string) (cmd string, args string) {
	parts := strings.SplitN(input, " ", 2)
	cmd = strings.ToLower(parts[0])
	if len(parts) > 1 {
		args = strings.TrimSpace(parts[1])
	}
	return
}
