// Package chat provides improved chat functionality
package chat

import (
	"context"
	"fmt"
	"io"
	"os"
	"strings"
	"time"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgxpool"

	"github.com/koopa0/assistant-go/internal/assistant"
	"github.com/koopa0/assistant-go/internal/cli/terminal"
	conversationsUI "github.com/koopa0/assistant-go/internal/cli/ui/views/conversations"

	// memoryUI "github.com/koopa0/assistant-go/internal/cli/ui/views/memory" // Temporarily disabled
	"github.com/koopa0/assistant-go/internal/conversation"
	"github.com/koopa0/assistant-go/internal/platform/logger"
)

// ImprovedController provides a cleaner terminal chat experience
type ImprovedController struct {
	session          Session
	reader           *terminal.InputReader
	output           io.Writer
	markdownRenderer *MarkdownRenderer
	styles           struct {
		assistant lipgloss.Style
		system    lipgloss.Style
		error     lipgloss.Style
		tool      lipgloss.Style
	}
}

// NewImprovedController creates an improved chat controller
func NewImprovedController(session Session, output io.Writer) (*ImprovedController, error) {
	if session == nil {
		return nil, fmt.Errorf("session is required")
	}
	if output == nil {
		output = io.Writer(os.Stdout)
	}

	c := &ImprovedController{
		session: session,
		output:  output,
	}

	// Setup markdown renderer
	markdownRenderer, err := NewMarkdownRenderer(true)
	if err != nil {
		// Continue without markdown rendering if it fails
		markdownRenderer = &MarkdownRenderer{enabled: false}
	}
	c.markdownRenderer = markdownRenderer

	// Setup styles - minimal and clean
	c.styles.assistant = lipgloss.NewStyle().
		Foreground(lipgloss.Color("252"))

	c.styles.system = lipgloss.NewStyle().
		Foreground(lipgloss.Color("243")).
		Italic(true)

	c.styles.error = lipgloss.NewStyle().
		Foreground(lipgloss.Color("196")).
		Bold(true)

	c.styles.tool = lipgloss.NewStyle().
		Foreground(lipgloss.Color("241"))

	return c, nil
}

// Run starts the improved chat interface
func (c *ImprovedController) Run() error {
	// Create input reader with prompt
	prompt := fmt.Sprintf("%s> ", c.session.OwnerName())
	reader, err := terminal.NewInputReader(prompt)
	if err != nil {
		return fmt.Errorf("create input reader: %w", err)
	}
	defer reader.Close()
	c.reader = reader

	// UI tools are now registered at assistant startup, no need to register here

	// Simple welcome
	fmt.Fprintln(c.output)
	fmt.Fprintln(c.output, c.styles.system.Render("AI Assistant Ready. Type /help for commands."))
	fmt.Fprintln(c.output)

	// Main loop
	for {
		// Read input
		input, err := c.reader.ReadLine()
		if err != nil {
			if strings.Contains(err.Error(), "exit") || strings.Contains(err.Error(), "interrupt") {
				fmt.Fprintln(c.output, "\n"+c.styles.system.Render("Goodbye!"))
				return nil
			}
			return fmt.Errorf("read input: %w", err)
		}

		input = strings.TrimSpace(input)
		if input == "" {
			continue
		}

		// Process commands
		if strings.HasPrefix(input, "/") {
			if err := c.handleCommand(input); err != nil {
				if strings.Contains(err.Error(), "exit") {
					fmt.Fprintln(c.output, "\n"+c.styles.system.Render("Goodbye!"))
					return nil
				}
				fmt.Fprintln(c.output, c.styles.error.Render("Error: "+err.Error()))
			}
			continue
		}

		// Process chat message (always streaming)
		if err := c.processMessage(input); err != nil {
			fmt.Fprintln(c.output, c.styles.error.Render("Error: "+err.Error()))
		}

		// Add spacing for next input
		fmt.Fprintln(c.output)
	}
}

// processMessage handles chat messages with streaming
func (c *ImprovedController) processMessage(input string) error {
	// Build request
	req := assistant.Request{
		Query:          input,
		ConversationID: c.session.ConversationID(),
		Options: assistant.Options{
			Temperature:  c.session.GetTemperature(),
			MaxTokens:    c.session.GetMaxTokens(),
			EnableMemory: true,
			EnableTools:  true,
		},
	}

	// Start streaming
	ctx := context.Background()
	stream, err := c.session.Stream(ctx, req)
	if err != nil {
		return fmt.Errorf("start stream: %w", err)
	}

	// Print newline before response
	fmt.Fprintln(c.output)

	// Show initial streaming indicator
	fmt.Fprint(c.output, c.styles.system.Render("  ◉ Generating response..."))

	// Stream the response
	var hasContent bool
	var hasProgressIndicator bool = true // We start with the progress indicator shown
	var lastUpdate time.Time
	var fullContent strings.Builder // Collect full content for markdown rendering

	for chunk := range stream {
		if chunk.Error != nil {
			// Clear the progress indicator properly
			if !hasContent {
				// Clear the "◉ Generating response..." line
				fmt.Fprint(c.output, "\r\033[K") // Clear line
			} else {
				// If we already have content, ensure we're on a new line
				fmt.Fprintln(c.output) // End the line
			}
			return fmt.Errorf("stream error: %w", chunk.Error)
		}

		// Status updates have been removed from the streaming interface
		/*
			if chunk.Status != nil {
				// Only use carriage return if we haven't started content yet
				if !hasContent {
					fmt.Fprint(c.output, "\r\033[K")
				} else {
					// If we have content, put status on new line
					fmt.Fprintln(c.output)
				}

				// Display status based on type
				switch chunk.Status.Type {
				case assistant.StatusThinking:
					fmt.Fprint(c.output, c.styles.system.Render("  🤔 Thinking..."))
					hasStatus = true
				case assistant.StatusGenerating:
					fmt.Fprint(c.output, c.styles.system.Render("  ✍️  Generating response..."))
					hasStatus = true
				case assistant.StatusExecutingTool:
					toolMsg := fmt.Sprintf("  🔧 Executing %s...", chunk.Status.Tool)
					fmt.Fprint(c.output, c.styles.tool.Render(toolMsg))
					hasStatus = true
				case assistant.StatusToolComplete:
					// Show completion on new line with proper formatting
					if hasStatus && !hasContent {
						fmt.Fprintln(c.output) // End the executing line
					}
					toolMsg := fmt.Sprintf("  ✅ Completed %s", chunk.Status.Tool)
					fmt.Fprintln(c.output, c.styles.tool.Render(toolMsg))
					hasStatus = false // Reset for next tool
				case assistant.StatusComplete:
					// Don't show completion status - we'll show content instead
				}
				lastUpdate = time.Now()
				continue
			}
		*/

		// Handle content
		if chunk.Content != "" {
			if !hasContent {
				// Clear progress indicator if showing one
				if hasProgressIndicator {
					fmt.Fprint(c.output, "\r\033[K") // Clear line
				}
				// Start content on new line after tool completions
				fmt.Fprintln(c.output)
				// Add initial indentation
				fmt.Fprint(c.output, "  ")
				hasContent = true
				hasProgressIndicator = false
			}

			// Display content in real-time
			// Handle newlines with proper indentation
			lines := strings.Split(chunk.Content, "\n")
			for i, line := range lines {
				if i > 0 {
					fmt.Fprint(c.output, "\n  ") // Add indentation for new lines
				}
				fmt.Fprint(c.output, line)
			}

			// Also collect for potential markdown rendering
			fullContent.WriteString(chunk.Content)
		} else if !hasContent && time.Since(lastUpdate) > 500*time.Millisecond {
			// Update progress indicator with animation (only if no status update)
			frames := []string{"◉", "◎", "◉", "◎"}
			frame := frames[(time.Now().UnixMilli()/250)%int64(len(frames))]
			fmt.Fprintf(c.output, "\r%s", c.styles.system.Render(fmt.Sprintf("  %s Generating response...", frame)))
			lastUpdate = time.Now()
		}

		// Update conversation ID when done
		if chunk.Done {
			if c.session.ConversationID() == uuid.Nil && req.ConversationID != uuid.Nil {
				c.session.SetConversationUUID(req.ConversationID)
			}

			// Since we've already displayed content in real-time, just add final newline
			if hasContent {
				fmt.Fprintln(c.output) // End with newline
			} else if hasProgressIndicator {
				// If we finished without any content, clear the progress indicator
				fmt.Fprint(c.output, "\r\033[K") // Clear line
			}
		}
	}

	// Final cleanup: if stream ended without Done signal
	if !hasContent && hasProgressIndicator {
		fmt.Fprint(c.output, "\r\033[K") // Clear line
	}

	return nil
}

// handleCommand processes simple commands
func (c *ImprovedController) handleCommand(input string) error {
	cmd := strings.Fields(input)[0]

	switch cmd {
	case "/help", "/?":
		help := `Available commands:
  /help    Show this help
  /clear   Start a new conversation
  /exit    Exit the chat
  /history Show conversation history
  /search  Search conversations
  /memory  Browse memory graph
  /temp    Set temperature (0-2)`
		fmt.Fprintln(c.output, c.styles.system.Render(help))
		return nil

	case "/clear":
		c.session.SetConversationUUID(uuid.Nil)
		fmt.Fprintln(c.output, c.styles.system.Render("✨ New conversation started"))
		return nil

	case "/exit", "/quit":
		return fmt.Errorf("exit")

	case "/history":
		// Delegate to the conversation UI
		fmt.Fprintln(c.output, c.styles.system.Render("Opening conversation history..."))
		if err := c.showConversationHistory(); err != nil {
			return err
		}
		return nil

	case "/search":
		// Parse search query
		parts := strings.Fields(input)
		if len(parts) < 2 {
			fmt.Fprintln(c.output, c.styles.error.Render("Usage: /search <query>"))
			return nil
		}
		searchQuery := strings.Join(parts[1:], " ")

		fmt.Fprintln(c.output, c.styles.system.Render(fmt.Sprintf("Searching for: %s...", searchQuery)))
		if err := c.searchConversations(searchQuery); err != nil {
			return err
		}
		return nil

	case "/memory":
		// Parse search query if provided
		parts := strings.Fields(input)
		var searchQuery string
		if len(parts) > 1 {
			searchQuery = strings.Join(parts[1:], " ")
		}

		fmt.Fprintln(c.output, c.styles.system.Render("Opening memory browser..."))
		if err := c.showMemoryBrowser(searchQuery); err != nil {
			return err
		}
		return nil

	case "/temp":
		parts := strings.Fields(input)
		if len(parts) < 2 {
			fmt.Fprintf(c.output, "%s\n", c.styles.system.Render(fmt.Sprintf("Current temperature: %.1f", c.session.GetTemperature())))
			return nil
		}
		var temp float32
		if _, err := fmt.Sscanf(parts[1], "%f", &temp); err != nil || temp < 0 || temp > 2 {
			return fmt.Errorf("temperature must be between 0 and 2")
		}
		c.session.SetTemperature(temp)
		fmt.Fprintf(c.output, "%s\n", c.styles.system.Render(fmt.Sprintf("Temperature set to %.1f", temp)))
		return nil

	default:
		return fmt.Errorf("unknown command: %s", cmd)
	}
}

// showConversationHistory displays the conversation history UI
func (c *ImprovedController) showConversationHistory() error {
	// Get conversation manager from session
	convManager, ok := c.session.(interface{ ConversationManager() interface{} })
	if !ok {
		fmt.Fprintln(c.output, c.styles.error.Render("Conversation management not available"))
		return nil
	}

	mgr := convManager.ConversationManager()
	if mgr == nil {
		fmt.Fprintln(c.output, c.styles.error.Render("No conversation manager configured"))
		return nil
	}

	// Get conversation lister interface
	lister, ok := mgr.(interface {
		ListConversations(ctx context.Context, limit int) ([]*conversation.Conversation, error)
	})
	if !ok {
		fmt.Fprintln(c.output, c.styles.error.Render("Conversation listing not supported"))
		return nil
	}

	// List recent conversations
	ctx := context.Background()
	conversations, err := lister.ListConversations(ctx, 20)
	if err != nil {
		return fmt.Errorf("list conversations: %w", err)
	}

	if len(conversations) == 0 {
		fmt.Fprintln(c.output, c.styles.system.Render("No conversations found. Start chatting to create one!"))
		return nil
	}

	// Run the conversation list UI
	listModel := conversationsUI.NewModel(conversations)
	p := tea.NewProgram(listModel, tea.WithAltScreen())

	finalModel, err := p.Run()
	if err != nil {
		return fmt.Errorf("run conversation UI: %w", err)
	}

	// Check if a conversation was selected
	if m, ok := finalModel.(conversationsUI.Model); ok {
		if selected := m.SelectedConversation(); selected != nil {
			// Switch to the selected conversation
			return c.switchToConversation(selected.ID.String())
		}
	}

	return nil
}

// switchToConversation switches the current session to a different conversation
func (c *ImprovedController) switchToConversation(idStr string) error {
	// Try to parse the ID first
	id, err := uuid.Parse(idStr)
	if err != nil {
		return fmt.Errorf("invalid conversation ID: %w", err)
	}

	// Update the session's conversation ID
	c.session.SetConversationUUID(id)

	// Show success message
	fmt.Fprintln(c.output, c.styles.system.Render("✅ Switched to conversation"))
	fmt.Fprintln(c.output, c.styles.system.Render("You can now continue your previous conversation."))

	return nil
}

// searchConversations searches and displays conversations
func (c *ImprovedController) searchConversations(query string) error {
	// Get conversation manager from session
	convManager, ok := c.session.(interface{ ConversationManager() interface{} })
	if !ok {
		fmt.Fprintln(c.output, c.styles.error.Render("Conversation management not available"))
		return nil
	}

	mgr := convManager.ConversationManager()
	if mgr == nil {
		fmt.Fprintln(c.output, c.styles.error.Render("No conversation manager configured"))
		return nil
	}

	// Get conversation store interface
	storeGetter, ok := mgr.(interface{ Store() interface{} })
	if !ok {
		fmt.Fprintln(c.output, c.styles.error.Render("Conversation store not available"))
		return nil
	}

	store, ok := storeGetter.Store().(conversation.Store)
	if !ok {
		fmt.Fprintln(c.output, c.styles.error.Render("Invalid conversation store"))
		return nil
	}

	// Search conversations
	ctx := context.Background()
	results, err := store.Search(ctx, query, 10)
	if err != nil {
		return fmt.Errorf("search conversations: %w", err)
	}

	if len(results) == 0 {
		fmt.Fprintln(c.output, c.styles.system.Render("No conversations found matching your search."))
		return nil
	}

	// Display results
	fmt.Fprintln(c.output, c.styles.system.Render(fmt.Sprintf("Found %d conversations:", len(results))))
	fmt.Fprintln(c.output)

	for i, result := range results {
		// Format result
		title := result.HighlightedTitle
		if title == "" {
			title = "Untitled"
		}

		// Replace highlight markers with color
		title = strings.ReplaceAll(title, "<<", "\033[93m") // Yellow
		title = strings.ReplaceAll(title, ">>", "\033[0m")  // Reset

		timeStr := ""
		if result.Conversation.LastMessageAt != nil {
			timeStr = result.Conversation.LastMessageAt.Format("2006-01-02 15:04")
		} else {
			timeStr = result.Conversation.CreatedAt.Format("2006-01-02 15:04")
		}

		fmt.Fprintf(c.output, "  [%d] %s (%s)\n", i+1, title, timeStr)

		if result.HighlightedSummary != "" {
			summary := result.HighlightedSummary
			summary = strings.ReplaceAll(summary, "<<", "\033[93m")
			summary = strings.ReplaceAll(summary, ">>", "\033[0m")
			if len(summary) > 60 {
				summary = summary[:57] + "..."
			}
			fmt.Fprintf(c.output, "      %s\n", summary)
		}
		fmt.Fprintln(c.output)
	}

	// Ask for selection
	fmt.Fprintln(c.output, c.styles.system.Render("Enter number to continue conversation (or press Enter to cancel):"))

	// Read input
	selection, err := c.reader.ReadLine()
	if err != nil || selection == "" {
		return nil
	}

	// Parse selection
	var num int
	if _, err := fmt.Sscanf(selection, "%d", &num); err != nil || num < 1 || num > len(results) {
		fmt.Fprintln(c.output, c.styles.error.Render("Invalid selection"))
		return nil
	}

	// Switch to selected conversation
	selected := results[num-1].Conversation
	return c.switchToConversation(selected.ID.String())
}

// showMemoryBrowser displays the memory browser UI
func (c *ImprovedController) showMemoryBrowser(searchQuery string) error {
	// Get database from session
	dbInterface := c.session.Database()
	if dbInterface == nil {
		fmt.Fprintln(c.output, c.styles.error.Render("Memory browser not available - no database configured"))
		return nil
	}

	_, ok := dbInterface.(*pgxpool.Pool)
	if !ok {
		fmt.Fprintln(c.output, c.styles.error.Render("Invalid database connection type"))
		return nil
	}

	// Get logger from session
	logInterface := c.session.Logger()
	var log logger.Logger
	if logInterface != nil {
		if l, ok := logInterface.(logger.Logger); ok {
			log = l
		}
	}
	if log == nil {
		log = logger.NewNoOpLogger()
	}
	_ = log // Mark as intentionally unused for future use

	// TODO: Memory browser UI needs to be updated for the new memory system
	// The browser components have been temporarily disabled
	return fmt.Errorf("memory browser is temporarily unavailable - UI components need to be updated for the new memory system")
}
