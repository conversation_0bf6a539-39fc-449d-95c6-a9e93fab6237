package convert

import (
	"math"
	"testing"
)

func TestSafeIntToInt32(t *testing.T) {
	tests := []struct {
		name string
		in   int
		want int32
	}{
		{"zero", 0, 0},
		{"positive small", 100, 100},
		{"negative small", -100, -100},
		{"max int32", math.MaxInt32, math.MaxInt32},
		{"min int32", math.MinInt32, math.MinInt32},
		{"overflow positive", math.MaxInt32 + 1, math.MaxInt32},
		{"overflow negative", math.MinInt32 - 1, math.MinInt32},
		{"large positive", 1 << 40, math.MaxInt32}, // Only relevant on 64-bit
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := SafeIntToInt32(tt.in)
			if got != tt.want {
				t.<PERSON>rrorf("SafeIntToInt32(%d) = %d, want %d", tt.in, got, tt.want)
			}
		})
	}
}

func TestSafeInt64ToInt(t *testing.T) {
	const maxInt = int(^uint(0) >> 1)
	const minInt = -maxInt - 1

	tests := []struct {
		name string
		in   int64
		want int
	}{
		{"zero", 0, 0},
		{"positive small", 100, 100},
		{"negative small", -100, -100},
		{"max int32", math.MaxInt32, math.MaxInt32},
		{"min int32", math.MinInt32, math.MinInt32},
	}

	// Add platform-specific tests
	if maxInt > math.MaxInt32 {
		// 64-bit platform
		tests = append(tests, []struct {
			name string
			in   int64
			want int
		}{
			{"max int64 on 64-bit", math.MaxInt64, maxInt},
			{"min int64 on 64-bit", math.MinInt64, minInt},
			{"large positive", 1 << 40, 1 << 40},
			{"large negative", -(1 << 40), -(1 << 40)},
		}...)
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := SafeInt64ToInt(tt.in)
			if got != tt.want {
				t.Errorf("SafeInt64ToInt(%d) = %d, want %d", tt.in, got, tt.want)
			}
		})
	}
}
