package convert

import "math"

// SafeIntToInt32 safely converts int to int32 with clamping.
// Returns math.MaxInt32 if the input exceeds int32 range.
func SafeIntToInt32(n int) int32 {
	if n > math.MaxInt32 {
		return math.MaxInt32
	}
	if n < math.MinInt32 {
		return math.MinInt32
	}
	return int32(n)
}

// SafeIntToInt64 safely converts int to int64.
// This is always safe on 64-bit systems where int is int64.
// On 32-bit systems, int is int32, so this is also safe.
func SafeIntToInt64(n int) int64 {
	return int64(n)
}

// SafeInt64ToInt safely converts int64 to int with clamping.
// Returns max/min int if the input exceeds int range.
func SafeInt64ToInt(n int64) int {
	const maxInt = int(^uint(0) >> 1)
	const minInt = -maxInt - 1

	if n > int64(maxInt) {
		return maxInt
	}
	if n < int64(minInt) {
		return minInt
	}
	return int(n)
}

// SafeInt32ToInt safely converts int32 to int.
// This is always safe as int is at least 32 bits on all platforms.
func SafeInt32ToInt(n int32) int {
	return int(n)
}
