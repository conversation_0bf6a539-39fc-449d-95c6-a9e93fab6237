package convert

import (
	"math"
	"testing"

	"github.com/koopa0/assistant-go/internal/ai"
)

func TestClampMaxTokens(t *testing.T) {
	tests := []struct {
		name string
		in   int
		want int
	}{
		{"zero", 0, 4096},
		{"negative", -100, 4096},
		{"small positive", 100, 100},
		{"reasonable", 8192, 8192},
		{"max allowed", 1000000, 1000000},
		{"over max", 2000000, 1000000},
		{"way over max", math.MaxInt32, 1000000},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := ClampMaxTokens(tt.in)
			if got != tt.want {
				t.Errorf("ClampMaxTokens(%d) = %d, want %d", tt.in, got, tt.want)
			}
		})
	}
}

func TestUsageAddWithOverflow(t *testing.T) {
	const maxInt = int(^uint(0) >> 1)

	// Calculate safe values that won't overflow in the test setup
	halfMax := maxInt / 2
	quarterMax := maxInt / 4
	threeQuartersMax := halfMax + quarterMax

	tests := []struct {
		name  string
		base  ai.Usage
		other ai.Usage
		want  ai.Usage
	}{
		{
			name:  "normal addition",
			base:  ai.Usage{Input: 100, Output: 200, Total: 300},
			other: ai.Usage{Input: 50, Output: 100, Total: 150},
			want:  ai.Usage{Input: 150, Output: 300, Total: 450},
		},
		{
			name:  "input overflow",
			base:  ai.Usage{Input: threeQuartersMax, Output: 100, Total: 200},
			other: ai.Usage{Input: halfMax, Output: 50, Total: 70},
			want:  ai.Usage{Input: maxInt, Output: 150, Total: 270},
		},
		{
			name:  "all fields overflow",
			base:  ai.Usage{Input: threeQuartersMax, Output: threeQuartersMax, Total: threeQuartersMax},
			other: ai.Usage{Input: halfMax, Output: halfMax, Total: halfMax},
			want:  ai.Usage{Input: maxInt, Output: maxInt, Total: maxInt},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.base
			got.Add(tt.other)
			if got != tt.want {
				t.Errorf("Usage.Add() = %+v, want %+v", got, tt.want)
			}
		})
	}
}
