package logger_test

import (
	"os"
	"testing"

	"github.com/koopa0/assistant-go/internal/platform/logger"
)

func TestCharmLogger(t *testing.T) {
	// Skip in CI
	if os.Getenv("CI") == "true" {
		t.Skip("Skipping visual test in CI")
	}

	// Create different types of loggers
	tests := []struct {
		name   string
		logger logger.Logger
	}{
		{
			name:   "Default CharmLogger",
			logger: logger.NewCharmLogger(),
		},
		{
			name:   "Tool Context Logger",
			logger: logger.CharmLoggerForContext("tool"),
		},
		{
			name:   "AI Context Logger",
			logger: logger.CharmLoggerForContext("ai"),
		},
		{
			name:   "Memory Context Logger",
			logger: logger.CharmLoggerForContext("memory"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			log := tt.logger

			// Test different log levels
			log.Debug("Debug message", "component", "test", "iteration", 1)
			log.Info("Info message", "status", "running", "progress", 0.5)
			log.Warn("Warning message", "issue", "slow response", "latency", "200ms")
			log.Error("Error message", "error", "connection failed", "retry", 3)

			// Test with component
			compLog := log.WithComponent("database")
			compLog.Info("Component log", "table", "users", "action", "query")
		})
	}
}

func TestCharmLoggerStyles(t *testing.T) {
	if os.Getenv("CI") == "true" {
		t.Skip("Skipping visual test in CI")
	}

	// This test is for visual inspection
	log := logger.NewCharmLogger()

	t.Log("\n=== Charm Logger Style Demo ===\n")

	// Different scenarios
	scenarios := []struct {
		name string
		fn   func()
	}{
		{
			name: "Tool Execution",
			fn: func() {
				toolLog := log.WithComponent("web_search")
				toolLog.Debug("Starting tool execution", "query", "Go best practices")
				toolLog.Info("Searching web", "engine", "google", "results", 10)
				toolLog.Info("Tool completed", "duration", "1.2s", "success", true)
			},
		},
		{
			name: "AI Processing",
			fn: func() {
				aiLog := log.WithComponent("claude")
				aiLog.Info("Processing request", "model", "claude-3", "tokens", 150)
				aiLog.Debug("Context window", "used", 2048, "total", 100000)
				aiLog.Warn("Rate limit approaching", "remaining", 100, "reset_in", "5m")
			},
		},
		{
			name: "Error Handling",
			fn: func() {
				errLog := log.WithComponent("api")
				errLog.Error("API request failed",
					"endpoint", "/v1/chat",
					"status", 500,
					"error", "Internal server error",
					"retry_after", "30s",
				)
			},
		},
	}

	for _, s := range scenarios {
		t.Log("\n--- " + s.name + " ---")
		s.fn()
	}
}

func ExampleCharmLogger() {
	// Different ways to create and use the logger

	// 1. Global logger
	logger.Info("Application started", "version", "1.0.0")

	// 2. Component-specific logger
	dbLog := logger.Global().WithComponent("database")
	dbLog.Debug("Connecting to database", "host", "localhost", "port", 5432)

	// 3. Context-specific logger
	toolLog := logger.CharmLoggerForContext("tool")
	toolLog.Info("Executing tool", "name", "memory_search", "query", "previous conversations")

	// 4. Structured logging
	logger.Info("User action",
		"user", "alice",
		"action", "send_message",
		"conversation_id", "123e4567-e89b-12d3-a456-426614174000",
		"timestamp", "2024-01-15T10:30:00Z",
	)
}
