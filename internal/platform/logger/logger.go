// Package logger provides logging functionality
package logger

import (
	"context"
	"fmt"
	"log"
	"log/slog"
	"os"
	"sync/atomic"

	"github.com/koopa0/assistant-go/internal/platform/config"
	"github.com/koopa0/assistant-go/internal/platform/env"
)

// Logger interface for logging
type Logger interface {
	Debug(msg string, fields ...any)
	Info(msg string, fields ...any)
	Warn(msg string, fields ...any)
	Error(msg string, fields ...any)
	WithComponent(component string) Logger
}

// ConsoleLogger is a simple console logger
type ConsoleLogger struct {
	logger    *log.Logger
	config    Config
	component string
}

// NewConsoleLogger creates a new console logger
func NewConsoleLogger() Logger {
	// Check for silent mode
	if env.GetBoolDefault("ASSISTANT_SILENT_MODE", false) {
		return NewDiscardLogger()
	}
	return &ConsoleLogger{
		logger: log.New(os.Stdout, "", log.LstdFlags),
		config: DefaultConfig(),
	}
}

// WithComponent returns a logger for a specific component
func (l *ConsoleLogger) WithComponent(component string) Logger {
	return &ConsoleLogger{
		logger:    l.logger,
		config:    l.config,
		component: component,
	}
}

// formatFields formats the fields for logging
func formatFields(fields []any) string {
	if len(fields) == 0 {
		return ""
	}

	result := " ["
	for i := 0; i < len(fields); i += 2 {
		if i+1 < len(fields) {
			result += fmt.Sprintf("%v=%v ", fields[i], fields[i+1])
		}
	}
	result += "]"
	return result
}

// Debug logs a debug message
func (l *ConsoleLogger) Debug(msg string, fields ...any) {
	if !l.config.ShouldLog(LevelDebug, l.component) {
		return
	}
	l.logger.Printf("DEBUG: %s%s", msg, formatFields(fields))
}

// Info logs an info message
func (l *ConsoleLogger) Info(msg string, fields ...any) {
	if !l.config.ShouldLog(LevelInfo, l.component) {
		return
	}
	l.logger.Printf("INFO: %s%s", msg, formatFields(fields))
}

// Warn logs a warning message
func (l *ConsoleLogger) Warn(msg string, fields ...any) {
	if !l.config.ShouldLog(LevelWarn, l.component) {
		return
	}
	l.logger.Printf("WARN: %s%s", msg, formatFields(fields))
}

// Error logs an error message
func (l *ConsoleLogger) Error(msg string, fields ...any) {
	if !l.config.ShouldLog(LevelError, l.component) {
		return
	}
	l.logger.Printf("ERROR: %s%s", msg, formatFields(fields))
}

// SlogLogger wraps slog for structured logging
type SlogLogger struct {
	logger    *slog.Logger
	config    Config
	component string
}

// NewSlogLogger creates a new slog-based logger
func NewSlogLogger(handler slog.Handler) Logger {
	logger := &SlogLogger{
		logger: slog.New(handler),
		config: DefaultConfig(),
	}
	// Update config based on environment
	if levelStr := os.Getenv("LOG_LEVEL"); levelStr != "" {
		logger.config.Level = ParseLevel(levelStr)
	}
	return logger
}

// NewDefaultSlogLogger creates a slog logger with default text handler
func NewDefaultSlogLogger() Logger {
	opts := &slog.HandlerOptions{
		Level: slog.LevelWarn, // Changed from Info to Warn to reduce noise
	}
	handler := slog.NewTextHandler(os.Stdout, opts)
	return NewSlogLogger(handler)
}

// WithComponent returns a logger for a specific component
func (l *SlogLogger) WithComponent(component string) Logger {
	return &SlogLogger{
		logger:    l.logger.With("component", component),
		config:    l.config,
		component: component,
	}
}

// Debug logs a debug message
func (l *SlogLogger) Debug(msg string, fields ...any) {
	if !l.config.ShouldLog(LevelDebug, l.component) {
		return
	}
	l.logger.LogAttrs(context.Background(), slog.LevelDebug, msg, convertToAttrs(fields)...)
}

// Info logs an info message
func (l *SlogLogger) Info(msg string, fields ...any) {
	if !l.config.ShouldLog(LevelInfo, l.component) {
		return
	}
	l.logger.LogAttrs(context.Background(), slog.LevelInfo, msg, convertToAttrs(fields)...)
}

// Warn logs a warning message
func (l *SlogLogger) Warn(msg string, fields ...any) {
	if !l.config.ShouldLog(LevelWarn, l.component) {
		return
	}
	l.logger.LogAttrs(context.Background(), slog.LevelWarn, msg, convertToAttrs(fields)...)
}

// Error logs an error message
func (l *SlogLogger) Error(msg string, fields ...any) {
	if !l.config.ShouldLog(LevelError, l.component) {
		return
	}
	l.logger.LogAttrs(context.Background(), slog.LevelError, msg, convertToAttrs(fields)...)
}

// convertToAttrs converts field pairs to slog attributes
func convertToAttrs(fields []any) []slog.Attr {
	attrs := make([]slog.Attr, 0, len(fields)/2)
	for i := 0; i < len(fields); i += 2 {
		if i+1 < len(fields) {
			key, ok := fields[i].(string)
			if !ok {
				key = fmt.Sprintf("%v", fields[i])
			}
			attrs = append(attrs, slog.Any(key, fields[i+1]))
		}
	}
	return attrs
}

// loggerWrapper wraps a Logger to ensure consistent type for atomic.Value
type loggerWrapper struct {
	logger Logger
}

// Global logger instance (thread-safe)
var globalLogger atomic.Value

// initializeGlobalLogger sets up the default logger
// This is called lazily on first access
func initializeGlobalLogger() {
	// Use CharmLogger by default for better user experience
	globalLogger.CompareAndSwap(nil, &loggerWrapper{logger: NewCharmLogger()})
}

// Global returns the global logger instance
func Global() Logger {
	if val := globalLogger.Load(); val != nil {
		if wrapper, ok := val.(*loggerWrapper); ok && wrapper.logger != nil {
			return wrapper.logger
		}
	}
	// Initialize default logger if not set
	initializeGlobalLogger()
	// Try again after initialization
	if val := globalLogger.Load(); val != nil {
		if wrapper, ok := val.(*loggerWrapper); ok && wrapper.logger != nil {
			return wrapper.logger
		}
	}
	// Fallback to a new console logger if somehow the global is nil
	return NewConsoleLogger()
}

// SetGlobal sets the global logger instance (thread-safe)
// If logger is nil, it sets a default console logger instead of panicking
func SetGlobal(logger Logger) {
	if logger == nil {
		// Use a default logger instead of panicking
		logger = NewConsoleLogger()
	}
	// Store wrapped logger to ensure consistent type
	globalLogger.Store(&loggerWrapper{logger: logger})
}

// Debug logs a debug message using the global logger
func Debug(msg string, fields ...any) {
	Global().Debug(msg, fields...)
}

// Info logs an info message using the global logger
func Info(msg string, fields ...any) {
	Global().Info(msg, fields...)
}

// Warn logs a warning message using the global logger
func Warn(msg string, fields ...any) {
	Global().Warn(msg, fields...)
}

// Error logs an error message using the global logger
func Error(msg string, fields ...any) {
	Global().Error(msg, fields...)
}

// NewFromEnv creates a logger based on environment configuration
// LOG_BACKEND: "charm" or "slog" (default: "charm")
// LOG_FORMAT: "json", "text", or "logfmt" (default: "text")
// LOG_LEVEL: "debug", "info", "warn", "error" (default: "info")
func NewFromEnv() Logger {
	// Check for silent mode first
	if env.GetBoolDefault("ASSISTANT_SILENT_MODE", false) {
		return NewDiscardLogger()
	}

	// Check which backend to use
	backend := os.Getenv("LOG_BACKEND")
	if backend == "" {
		backend = "charm" // Default to charmbracelet/log for better UX
	}

	// Use charmbracelet/log if specified
	if backend == "charm" {
		return NewCharmLoggerFromEnv()
	}

	// Otherwise use slog
	format := os.Getenv("LOG_FORMAT")
	levelStr := os.Getenv("LOG_LEVEL")

	// Parse log level
	var level slog.Level
	switch levelStr {
	case LevelDebugStr:
		level = slog.LevelDebug
	case LevelInfoStr:
		level = slog.LevelInfo
	case LevelWarnStr:
		level = slog.LevelWarn
	case LevelErrorStr:
		level = slog.LevelError
	case LevelFatalStr:
		// For fatal, return a discard logger that suppresses all output
		return NewDiscardLogger()
	default:
		// Default to info level in production, warn in development
		if os.Getenv("APP_MODE") == config.ModeProduction {
			level = slog.LevelInfo
		} else {
			level = slog.LevelWarn // Reduce noise in development
		}
	}

	opts := &slog.HandlerOptions{
		Level: level,
	}

	// Create handler based on format
	var handler slog.Handler
	if format == "json" {
		handler = slog.NewJSONHandler(os.Stdout, opts)
	} else {
		handler = slog.NewTextHandler(os.Stdout, opts)
	}

	return NewSlogLogger(handler)
}
