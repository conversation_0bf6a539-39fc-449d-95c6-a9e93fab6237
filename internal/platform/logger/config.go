// Package logger provides structured logging with level control.
package logger

import (
	"os"
	"strings"

	"github.com/koopa0/assistant-go/internal/platform/env"
)

// LogLevel represents the severity of a log message.
type LogLevel int

const (
	LevelDebug LogLevel = iota
	LevelInfo
	LevelWarn
	LevelError
	LevelFatal
)

// Log level string constants
const (
	LevelDebugStr = "debug"
	LevelInfoStr  = "info"
	LevelWarnStr  = "warn"
	LevelErrorStr = "error"
	LevelFatalStr = "fatal"
)

// ParseLevel parses a string log level.
func ParseLevel(level string) LogLevel {
	switch strings.ToLower(level) {
	case LevelDebugStr:
		return LevelDebug
	case LevelInfoStr:
		return LevelInfo
	case LevelWarnStr, "warning":
		return LevelWarn
	case LevelErrorStr:
		return LevelError
	case LevelFatalStr:
		return LevelFatal
	default:
		return LevelInfo
	}
}

// Config holds logger configuration.
type Config struct {
	Level      LogLevel
	Format     string // "text" or "json"
	SilentMode bool   // Suppress all output except errors

	// Component-specific levels
	ComponentLevels map[string]LogLevel
}

// DefaultConfig returns sensible defaults.
func DefaultConfig() Config {
	// Check for silent mode
	silentMode := env.GetBoolDefault("ASSISTANT_SILENT_MODE", false)

	// In chat mode, default to info level to reduce noise
	level := LevelInfo
	if os.Getenv("LOG_LEVEL") == LevelDebugStr {
		level = LevelDebug
	} else if os.Getenv("LOG_LEVEL") == LevelFatalStr || silentMode {
		level = LevelFatal
	}

	return Config{
		Level:      level,
		Format:     "text",
		SilentMode: silentMode,
		ComponentLevels: map[string]LogLevel{
			"mcp":       LevelInfo, // Reduce MCP debug noise
			"memory":    LevelInfo, // Reduce memory debug noise
			"assistant": LevelInfo, // Reduce assistant debug noise
			"streaming": LevelWarn, // Only show streaming errors
			"database":  LevelWarn, // Only show database errors
		},
	}
}

// ShouldLog returns true if a message at the given level should be logged.
func (c Config) ShouldLog(level LogLevel, component string) bool {
	if c.SilentMode && level < LevelError {
		return false
	}

	// Check component-specific level first
	if componentLevel, ok := c.ComponentLevels[component]; ok {
		return level >= componentLevel
	}

	return level >= c.Level
}
