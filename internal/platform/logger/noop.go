// Package logger provides a no-op logger implementation
package logger

// NoOpLogger is a logger that does nothing
// Useful as a default when no logger is provided
type NoOpLogger struct{}

// NewNoOpLogger creates a new no-op logger
func NewNoOpLogger() Logger {
	return &NoOpLogger{}
}

// Debug does nothing
func (n *NoOpLogger) Debug(msg string, fields ...any) {}

// Info does nothing
func (n *NoOpLogger) Info(msg string, fields ...any) {}

// Warn does nothing
func (n *NoOpLogger) Warn(msg string, fields ...any) {}

// Error does nothing
func (n *NoOpLogger) Error(msg string, fields ...any) {}

// WithComponent returns itself
func (n *NoOpLogger) WithComponent(component string) Logger {
	return n
}
