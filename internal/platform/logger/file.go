package logger

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// FileLogger logs to a file using slog
type FileLogger struct {
	logger *slog.Logger
	file   *os.File
}

// NewFileLogger creates a new file logger with slog
func NewFileLogger(logDir string, level string) (Logger, error) {
	// Clean and validate logDir to prevent directory traversal
	logDir = filepath.Clean(logDir)
	if strings.Contains(logDir, "..") {
		return nil, fmt.Errorf("invalid log directory: contains directory traversal")
	}

	// Create log directory if it doesn't exist
	if err := os.MkdirAll(logDir, 0o750); err != nil {
		return nil, fmt.Errorf("failed to create log directory: %w", err)
	}

	// Create log file with timestamp
	logFile := filepath.Join(logDir, fmt.Sprintf("assistant_%s.log", time.Now().Format("2006-01-02")))
	file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0o600) // #nosec G304 - path is constructed from safe components
	if err != nil {
		return nil, fmt.Errorf("failed to open log file: %w", err)
	}

	// Parse log level
	var slogLevel slog.Level
	switch level {
	case "debug":
		slogLevel = slog.LevelDebug
	case "info":
		slogLevel = slog.LevelInfo
	case "warn":
		slogLevel = slog.LevelWarn
	case "error":
		slogLevel = slog.LevelError
	default:
		slogLevel = slog.LevelInfo
	}

	opts := &slog.HandlerOptions{
		Level: slogLevel,
	}
	handler := slog.NewTextHandler(file, opts)

	return &FileLogger{
		logger: slog.New(handler),
		file:   file,
	}, nil
}

// Debug logs a debug message
func (l *FileLogger) Debug(msg string, fields ...any) {
	l.logger.LogAttrs(context.Background(), slog.LevelDebug, msg, convertToAttrs(fields)...)
}

// Info logs an info message
func (l *FileLogger) Info(msg string, fields ...any) {
	l.logger.LogAttrs(context.Background(), slog.LevelInfo, msg, convertToAttrs(fields)...)
}

// Warn logs a warning message
func (l *FileLogger) Warn(msg string, fields ...any) {
	l.logger.LogAttrs(context.Background(), slog.LevelWarn, msg, convertToAttrs(fields)...)
}

// Error logs an error message
func (l *FileLogger) Error(msg string, fields ...any) {
	l.logger.LogAttrs(context.Background(), slog.LevelError, msg, convertToAttrs(fields)...)
}

// WithComponent returns a logger for a specific component
func (l *FileLogger) WithComponent(component string) Logger {
	return l // FileLogger doesn't use component filtering
}

// Close closes the log file
func (l *FileLogger) Close() error {
	if l.file != nil {
		return l.file.Close()
	}
	return nil
}

// StderrLogger logs to stderr using slog
type StderrLogger struct {
	logger *slog.Logger
}

// NewStderrLogger creates a logger that outputs to stderr with slog
func NewStderrLogger(level string) Logger {
	// Parse log level
	var slogLevel slog.Level
	switch level {
	case "debug":
		slogLevel = slog.LevelDebug
	case "info":
		slogLevel = slog.LevelInfo
	case "warn":
		slogLevel = slog.LevelWarn
	case "error":
		slogLevel = slog.LevelError
	default:
		slogLevel = slog.LevelInfo
	}

	opts := &slog.HandlerOptions{
		Level: slogLevel,
	}
	handler := slog.NewTextHandler(os.Stderr, opts)

	return &StderrLogger{
		logger: slog.New(handler),
	}
}

// Debug logs a debug message
func (l *StderrLogger) Debug(msg string, fields ...any) {
	l.logger.LogAttrs(context.Background(), slog.LevelDebug, msg, convertToAttrs(fields)...)
}

// Info logs an info message
func (l *StderrLogger) Info(msg string, fields ...any) {
	l.logger.LogAttrs(context.Background(), slog.LevelInfo, msg, convertToAttrs(fields)...)
}

// Warn logs a warning message
func (l *StderrLogger) Warn(msg string, fields ...any) {
	l.logger.LogAttrs(context.Background(), slog.LevelWarn, msg, convertToAttrs(fields)...)
}

// Error logs an error message
func (l *StderrLogger) Error(msg string, fields ...any) {
	l.logger.LogAttrs(context.Background(), slog.LevelError, msg, convertToAttrs(fields)...)
}

// WithComponent returns a logger for a specific component
func (l *StderrLogger) WithComponent(component string) Logger {
	return l // StderrLogger doesn't use component filtering
}

// DiscardLogger discards all logs (for silent mode)
type DiscardLogger struct{}

// NewDiscardLogger creates a logger that discards all output
func NewDiscardLogger() Logger {
	return &DiscardLogger{}
}

// Debug implements Logger
func (l *DiscardLogger) Debug(msg string, fields ...any) {}

// Info implements Logger
func (l *DiscardLogger) Info(msg string, fields ...any) {}

// Warn implements Logger
func (l *DiscardLogger) Warn(msg string, fields ...any) {}

// Error implements Logger
func (l *DiscardLogger) Error(msg string, fields ...any) {}

// WithComponent implements Logger
func (l *DiscardLogger) WithComponent(component string) Logger {
	return l // DiscardLogger doesn't use component filtering
}

// NullLogger is equivalent to DiscardLogger
type NullLogger = DiscardLogger

// NewNullLogger creates a logger that discards all output
func NewNullLogger() Logger {
	return NewDiscardLogger()
}
