// Package logger provides logging functionality
package logger

import (
	"os"
	"strings"
	"time"

	"github.com/charmbracelet/lipgloss"
	"github.com/charmbracelet/log"
)

// CharmLogger implements Logger interface using charmbracelet/log
type CharmLogger struct {
	logger    *log.Logger
	config    Config
	component string
}

// NewCharmLogger creates a new logger using charmbracelet/log
func NewCharmLogger() Logger {
	// Check for silent mode
	if os.Getenv("ASSISTANT_SILENT_MODE") == "true" {
		return NewDiscardLogger()
	}

	logger := log.New(os.Stderr)

	// Configure based on environment
	levelStr := os.Getenv("LOG_LEVEL")
	level := parseCharmLevel(levelStr)
	logger.SetLevel(level)

	// Set custom styles for better visual experience
	styles := createLogStyles()
	logger.SetStyles(styles)

	// Configure other options
	logger.SetTimeFormat(time.Kitchen)
	logger.SetReportTimestamp(true)
	logger.SetReportCaller(false) // Keep it clean

	// Add prefix for assistant
	logger.SetPrefix("🤖 Assistant")

	return &CharmLogger{
		logger: logger,
		config: DefaultConfig(),
	}
}

// NewCharmLoggerWithOptions creates a logger with custom options
func NewCharmLoggerWithOptions(opts log.Options) Logger {
	logger := log.NewWithOptions(os.Stderr, opts)
	return &CharmLogger{
		logger: logger,
		config: DefaultConfig(),
	}
}

// WithComponent returns a logger for a specific component
func (l *CharmLogger) WithComponent(component string) Logger {
	// Create a new logger with component context
	newLogger := l.logger.With("component", component)
	return &CharmLogger{
		logger:    newLogger,
		config:    l.config,
		component: component,
	}
}

// Debug logs a debug message
func (l *CharmLogger) Debug(msg string, fields ...any) {
	if !l.config.ShouldLog(LevelDebug, l.component) {
		return
	}
	l.logger.Debug(msg, fields...)
}

// Info logs an info message
func (l *CharmLogger) Info(msg string, fields ...any) {
	if !l.config.ShouldLog(LevelInfo, l.component) {
		return
	}
	l.logger.Info(msg, fields...)
}

// Warn logs a warning message
func (l *CharmLogger) Warn(msg string, fields ...any) {
	if !l.config.ShouldLog(LevelWarn, l.component) {
		return
	}
	l.logger.Warn(msg, fields...)
}

// Error logs an error message
func (l *CharmLogger) Error(msg string, fields ...any) {
	if !l.config.ShouldLog(LevelError, l.component) {
		return
	}
	l.logger.Error(msg, fields...)
}

// parseCharmLevel converts string level to charmbracelet/log Level
func parseCharmLevel(levelStr string) log.Level {
	switch strings.ToLower(levelStr) {
	case LevelDebugStr:
		return log.DebugLevel
	case LevelInfoStr:
		return log.InfoLevel
	case LevelWarnStr:
		return log.WarnLevel
	case LevelErrorStr:
		return log.ErrorLevel
	case LevelFatalStr:
		return log.FatalLevel
	default:
		// Default to info level
		if os.Getenv("APP_MODE") == "production" {
			return log.InfoLevel
		}
		return log.WarnLevel // Less noise in development
	}
}

// createLogStyles creates custom styles for different log levels
func createLogStyles() *log.Styles {
	styles := log.DefaultStyles()

	// Customize level styles with beautiful colors
	styles.Levels[log.DebugLevel] = lipgloss.NewStyle().
		SetString("DEBUG").
		Padding(0, 1).
		Foreground(lipgloss.Color("63")) // Dim blue

	styles.Levels[log.InfoLevel] = lipgloss.NewStyle().
		SetString("INFO").
		Padding(0, 1).
		Foreground(lipgloss.Color("86")) // Cyan

	styles.Levels[log.WarnLevel] = lipgloss.NewStyle().
		SetString("WARN").
		Padding(0, 1).
		Foreground(lipgloss.Color("228")) // Yellow

	styles.Levels[log.ErrorLevel] = lipgloss.NewStyle().
		SetString("ERROR").
		Padding(0, 1).
		Foreground(lipgloss.Color("203")). // Red
		Bold(true)

	styles.Levels[log.FatalLevel] = lipgloss.NewStyle().
		SetString("FATAL").
		Padding(0, 1).
		Background(lipgloss.Color("203")). // Red background
		Foreground(lipgloss.Color("0")).   // Black text
		Bold(true)

	// Customize other styles
	styles.Timestamp = lipgloss.NewStyle().
		Foreground(lipgloss.Color("244")) // Dim gray

	styles.Prefix = lipgloss.NewStyle().
		Foreground(lipgloss.Color("39")). // Bright blue
		Bold(true)

	styles.Message = lipgloss.NewStyle().
		Foreground(lipgloss.Color("255")) // White

	styles.Key = lipgloss.NewStyle().
		Foreground(lipgloss.Color("141")). // Purple
		Faint(true)

	styles.Value = lipgloss.NewStyle().
		Foreground(lipgloss.Color("86")) // Cyan

	return styles
}

// NewCharmLoggerFromEnv creates a charmbracelet logger based on environment
func NewCharmLoggerFromEnv() Logger {
	// Check for silent mode first
	if os.Getenv("ASSISTANT_SILENT_MODE") == "true" {
		return NewDiscardLogger()
	}

	logger := log.New(os.Stderr)

	// Configure level
	levelStr := os.Getenv("LOG_LEVEL")
	level := parseCharmLevel(levelStr)
	logger.SetLevel(level)

	// Configure format
	format := os.Getenv("LOG_FORMAT")
	switch format {
	case "json":
		logger.SetFormatter(log.JSONFormatter)
	case "logfmt":
		logger.SetFormatter(log.LogfmtFormatter)
	default:
		logger.SetFormatter(log.TextFormatter)
		// Apply custom styles only for text format
		logger.SetStyles(createLogStyles())
	}

	// Configure other options based on environment
	if os.Getenv("LOG_TIMESTAMP") != "false" {
		logger.SetReportTimestamp(true)
		timeFormat := os.Getenv("LOG_TIME_FORMAT")
		if timeFormat == "" {
			timeFormat = time.Kitchen
		}
		logger.SetTimeFormat(timeFormat)
	}

	if os.Getenv("LOG_CALLER") == "true" {
		logger.SetReportCaller(true)
	}

	// Set prefix if provided
	if prefix := os.Getenv("LOG_PREFIX"); prefix != "" {
		logger.SetPrefix(prefix)
	} else {
		logger.SetPrefix("🤖 Assistant")
	}

	return &CharmLogger{
		logger: logger,
		config: DefaultConfig(),
	}
}

// CharmLoggerForContext creates specialized loggers for different contexts
func CharmLoggerForContext(context string) Logger {
	logger := log.New(os.Stderr)

	// Base configuration
	logger.SetLevel(log.InfoLevel)
	logger.SetTimeFormat(time.Kitchen)
	logger.SetReportTimestamp(true)
	logger.SetStyles(createLogStyles())

	// Context-specific configuration
	switch context {
	case "tool":
		logger.SetPrefix("🔧 Tool")
		logger.SetLevel(log.DebugLevel) // More verbose for tools
	case "ai":
		logger.SetPrefix("🧠 AI")
	case "memory":
		logger.SetPrefix("💾 Memory")
	case "chat":
		logger.SetPrefix("💬 Chat")
	case "knowledge":
		logger.SetPrefix("📚 Knowledge")
	default:
		logger.SetPrefix("🤖 Assistant")
	}

	return &CharmLogger{
		logger: logger,
		config: DefaultConfig(),
	}
}
