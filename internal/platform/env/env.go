// Package env provides utilities for environment variable handling
package env

import (
	"os"
	"strconv"
)

// GetBool retrieves an environment variable and parses it as a boolean.
// Returns the boolean value and true if the variable exists and is valid,
// otherwise returns false and false.
// Accepts: "true", "false", "1", "0", "yes", "no" (case insensitive)
func GetBool(key string) (value bool, exists bool) {
	str := os.Getenv(key)
	if str == "" {
		return false, false
	}

	val, err := strconv.ParseBool(str)
	if err != nil {
		// Try additional formats
		switch str {
		case "yes", "YES", "Yes":
			return true, true
		case "no", "NO", "No":
			return false, true
		default:
			return false, false
		}
	}

	return val, true
}

// GetBoolDefault retrieves an environment variable as a boolean with a default value.
// If the variable doesn't exist or can't be parsed, returns the default.
func GetBoolDefault(key string, defaultValue bool) bool {
	val, exists := GetBool(key)
	if !exists {
		return defaultValue
	}
	return val
}

// GetString retrieves an environment variable with a default value.
// If the variable doesn't exist, returns the default.
func GetString(key string, defaultValue string) string {
	if val := os.Getenv(key); val != "" {
		return val
	}
	return defaultValue
}
