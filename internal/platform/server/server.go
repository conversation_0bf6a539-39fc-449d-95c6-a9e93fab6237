// Package server provides application initialization and lifecycle management.
package server

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/jackc/pgx/v5/pgxpool"

	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/koopa0/assistant-go/internal/ai/claude"
	"github.com/koopa0/assistant-go/internal/ai/gemini"
	"github.com/koopa0/assistant-go/internal/assistant"
	"github.com/koopa0/assistant-go/internal/platform/config"
	"github.com/koopa0/assistant-go/internal/platform/logger"
	"github.com/koopa0/assistant-go/internal/storage/database"
)

// Server manages the application lifecycle and dependencies.
type Server struct {
	config    config.Config
	logger    logger.Logger
	db        *pgxpool.Pool
	aiService ai.Client
	assistant assistant.Assistant

	// Shutdown management
	shutdownCh chan struct{}
	wg         sync.WaitGroup
}

// Config holds server configuration options.
type Config struct {
	Verbose        bool
	DisableColors  bool
	DisableMemory  bool
	GracefulPeriod time.Duration
}

// New creates a new server instance with all dependencies initialized.
func New(cfg Config) (*Server, error) {
	// Load configuration
	appConfig, err := loadConfig()
	if err != nil {
		return nil, fmt.Errorf("load config: %w", err)
	}

	// Initialize logger
	log := initLogger(appConfig, cfg.Verbose, cfg.DisableColors)

	// Initialize database (optional)
	db, err := initDatabase(context.Background(), appConfig, log)
	if err != nil {
		log.Error("Failed to initialize database", "error", err)
		// Continue without database - some features will be disabled
	}

	// Initialize AI service
	aiService, err := initAIService(context.Background(), appConfig, log)
	if err != nil {
		return nil, fmt.Errorf("init AI service: %w", err)
	}

	// Check if AI client supports hybrid capabilities
	if _, ok := aiService.(ai.Hybrid); !ok && db != nil && !cfg.DisableMemory {
		log.Warn("AI client doesn't support embeddings - memory features will be limited")
	}

	// Initialize assistant
	assist, err := assistant.New(db, aiService, appConfig, log)
	if err != nil {
		return nil, fmt.Errorf("init assistant: %w", err)
	}

	return &Server{
		config:     appConfig,
		logger:     log,
		db:         db,
		aiService:  aiService,
		assistant:  assist,
		shutdownCh: make(chan struct{}),
	}, nil
}

// Run starts the server and blocks until shutdown.
func (s *Server) Run(ctx context.Context) error {
	// Set up signal handling
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)

	// Start background services
	s.startBackgroundServices(ctx)

	s.logger.Info("Server started", "version", s.config.App.Version)

	// Wait for shutdown signal
	select {
	case <-ctx.Done():
		s.logger.Info("Context canceled, shutting down")
	case sig := <-sigCh:
		s.logger.Info("Received signal, shutting down", "signal", sig)
	}

	// Graceful shutdown
	return s.Shutdown(context.Background())
}

// Shutdown gracefully shuts down the server.
func (s *Server) Shutdown(ctx context.Context) error {
	s.logger.Info("Starting graceful shutdown")

	// Signal all goroutines to stop
	close(s.shutdownCh)

	// Wait for background tasks with timeout
	done := make(chan struct{})
	go func() {
		s.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		s.logger.Info("All background tasks completed")
	case <-ctx.Done():
		s.logger.Warn("Shutdown timeout exceeded, forcing shutdown")
	}

	// Close resources
	var errs []error

	if s.assistant != nil {
		if err := s.assistant.Close(); err != nil {
			errs = append(errs, fmt.Errorf("close assistant: %w", err))
		}
	}

	if s.db != nil {
		s.db.Close()
	}

	if len(errs) > 0 {
		return fmt.Errorf("shutdown errors: %v", errs)
	}

	s.logger.Info("Graceful shutdown completed")
	return nil
}

// GetAssistant returns the assistant instance.
func (s *Server) GetAssistant() assistant.Assistant {
	return s.assistant
}

// GetConfig returns the configuration.
func (s *Server) GetConfig() config.Config {
	return s.config
}

// GetLogger returns the logger.
func (s *Server) GetLogger() logger.Logger {
	return s.logger
}

// GetDatabase returns the database connection pool.
func (s *Server) GetDatabase() *pgxpool.Pool {
	return s.db
}

// GetAIService returns the AI service.
func (s *Server) GetAIService() ai.Client {
	return s.aiService
}

// Private helper methods

func (s *Server) startBackgroundServices(ctx context.Context) {
	// Start health monitoring if we have multiple providers
	if s.aiService != nil {
		s.wg.Add(1)
		go func() {
			defer s.wg.Done()
			s.monitorHealth(ctx)
		}()
	}
}

func (s *Server) monitorHealth(ctx context.Context) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-s.shutdownCh:
			return
		case <-ticker.C:
			// Check AI service health
			if s.aiService != nil {
				if !s.aiService.IsAvailable(ctx) {
					s.logger.Warn("AI service health check failed")
				}
			}
		}
	}
}

func loadConfig() (config.Config, error) {
	// config.Load doesn't take parameters - it looks for the file itself
	cfg, err := config.Load()
	if err != nil {
		return config.Config{}, err
	}
	return *cfg, nil
}

func initLogger(cfg config.Config, verbose, disableColors bool) logger.Logger {
	// Only set log level if not already set by main
	if os.Getenv("LOG_LEVEL") == "" {
		// Set environment variables for logger configuration
		if verbose {
			_ = os.Setenv("LOG_LEVEL", "debug") // #nosec G104 - Setenv unlikely to fail
		} else {
			_ = os.Setenv("LOG_LEVEL", cfg.Observability.LogLevel) // #nosec G104 - Setenv unlikely to fail
		}
	}

	// Only set log format if not already set
	if os.Getenv("LOG_FORMAT") == "" {
		_ = os.Setenv("LOG_FORMAT", cfg.Observability.LogFormat) // #nosec G104 - Setenv unlikely to fail
	}

	if disableColors {
		_ = os.Setenv("NO_COLOR", "1") // #nosec G104 - Setenv unlikely to fail
	}

	// Use the charm logger which respects environment variables
	return logger.NewCharmLoggerFromEnv()
}

func initDatabase(ctx context.Context, cfg config.Config, log logger.Logger) (*pgxpool.Pool, error) {
	if cfg.Database.URL == "" {
		return nil, nil
	}

	log.Info("Initializing database connection")

	dbCfg := &database.Config{
		DSN: cfg.Database.URL,
	}

	client, err := database.NewClient(ctx, dbCfg)
	if err != nil {
		return nil, fmt.Errorf("create database client: %w", err)
	}

	// Run migrations
	if err := client.RunMigrations(); err != nil {
		return nil, fmt.Errorf("run migrations: %w", err)
	}

	log.Info("Database initialized successfully")
	return client.Pool(), nil
}

func initAIService(ctx context.Context, cfg config.Config, log logger.Logger) (ai.Client, error) {
	log.Info("Initializing AI service")

	// Create clients map
	clients := make(map[ai.Provider]ai.Client)

	// Claude provider
	if cfg.AI.ClaudeAPIKey != "" {
		claudeClient, err := claude.New(cfg.AI.ClaudeAPIKey)
		if err != nil {
			log.Warn("Failed to create Claude client", "error", err)
		} else {
			clients[ai.Claude] = claudeClient
			log.Debug("Claude provider configured")
		}
	}

	// Gemini provider
	if cfg.AI.GeminiAPIKey != "" {
		geminiClient, err := gemini.New(ctx, cfg.AI.GeminiAPIKey)
		if err != nil {
			log.Warn("Failed to create Gemini client", "error", err)
		} else {
			clients[ai.Gemini] = geminiClient
			log.Debug("Gemini provider configured")
		}
	}

	if len(clients) == 0 {
		return nil, fmt.Errorf("no AI providers configured")
	}

	// Map provider string to enum
	defaultProvider := ai.Claude
	switch cfg.AI.Provider {
	case "claude":
		defaultProvider = ai.Claude
	case "gemini":
		defaultProvider = ai.Gemini
	case "ollama":
		defaultProvider = ai.Ollama
	}

	// Create AI service
	service, err := ai.NewService(clients, defaultProvider, ai.WithLogger(log))
	if err != nil {
		return nil, fmt.Errorf("create AI service: %w", err)
	}

	log.Info("AI service initialized",
		"defaultProvider", defaultProvider,
		"availableProviders", len(clients))

	return service, nil
}
