package config

import (
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestConfig(t *testing.T) {
	// Save and restore environment
	oldEnv := os.Environ()
	t.Cleanup(func() {
		os.Clearenv()
		for _, e := range oldEnv {
			pair := splitEnvVar(e)
			if len(pair) == 2 {
				os.Setenv(pair[0], pair[1])
			}
		}
	})

	t.Run("DefaultConfig", func(t *testing.T) {
		cfg := Default()
		require.NotNil(t, cfg)

		// Test App defaults
		assert.Equal(t, "development", cfg.App.Mode)
		assert.Equal(t, "0.1.0", cfg.App.Version)
		assert.Equal(t, "", cfg.App.Language)

		// Test Database defaults
		assert.Equal(t, DefaultDatabaseURL, cfg.Database.URL)
		assert.Equal(t, 30, cfg.Database.MaxConnections)
		assert.Equal(t, 5, cfg.Database.MinConnections)

		// Test Server defaults
		assert.Equal(t, DefaultHTTPPort, cfg.Server.Address)
		assert.False(t, cfg.Server.EnableTLS)

		// Test AI defaults
		assert.Equal(t, "claude", cfg.AI.Provider)
		assert.Equal(t, "claude-3.5-sonnet", cfg.AI.DefaultModel)

		// Test Security defaults
		assert.Equal(t, 100, cfg.Security.RateLimitRPS)
		assert.Equal(t, 200, cfg.Security.RateLimitBurst)
		assert.False(t, cfg.Security.TLSSkipVerify)

		// Test Observability defaults
		assert.Equal(t, "warn", cfg.Observability.LogLevel)
		assert.Equal(t, "text", cfg.Observability.LogFormat)
		assert.False(t, cfg.Observability.EnableProfiling)

		// Test Owner defaults
		assert.Equal(t, DefaultOwnerName, cfg.Owner.Name)
		assert.NotNil(t, cfg.Owner.Preferences)

		// Test MCP defaults
		assert.NotNil(t, cfg.MCP)
		assert.False(t, cfg.MCP.Enabled)
		assert.Equal(t, "assistant-go", cfg.MCP.ClientName)
		assert.True(t, cfg.MCP.AutoDiscover)
	})

	t.Run("LoadFromAssistantYAML", func(t *testing.T) {
		// Clear environment first to ensure clean state
		os.Clearenv()

		// Create a temporary assistant.yaml file
		tmpDir := t.TempDir()
		configPath := filepath.Join(tmpDir, "assistant.yaml")

		yamlContent := `
app:
  mode: development
  language: en

database:
  url: "postgres://test:test@localhost/testdb"
  max_connections: 25

ai:
  provider: "claude"
  claude_api_key: "test-claude-key"
  gemini_api_key: "test-gemini-key"

observability:
  log_level: "debug"

security:
  rate_limit_rps: 10
`
		err := os.WriteFile(configPath, []byte(yamlContent), 0o600)
		require.NoError(t, err)

		// Set ASSISTANT_CONFIG to point to our test file
		os.Setenv("ASSISTANT_CONFIG", configPath)
		defer os.Unsetenv("ASSISTANT_CONFIG")

		cfg, err := Load()
		require.NoError(t, err)

		assert.Equal(t, "postgres://test:test@localhost/testdb", cfg.Database.URL)
		assert.Equal(t, "claude", cfg.AI.Provider)
		assert.Equal(t, "test-claude-key", cfg.AI.ClaudeAPIKey)
		assert.Equal(t, "test-gemini-key", cfg.AI.GeminiAPIKey)
		assert.Equal(t, "debug", cfg.Observability.LogLevel)
		assert.Equal(t, 10, cfg.Security.RateLimitRPS)
	})

	t.Run("LoadFromYAML", func(t *testing.T) {
		// Clear environment to ensure no interference
		os.Clearenv()

		// Create a temporary YAML config file
		tmpDir := t.TempDir()
		configPath := filepath.Join(tmpDir, "test-config.yaml")

		yamlContent := `
database:
  url: "postgres://testuser:testpass@localhost/testdb" # nosec G101 - test credentials
  max_connections: 20

ai:
  provider: "claude"
  claude_api_key: "yaml-claude-key"

observability:
  log_level: "warn"

security:
  rate_limit_rps: 5

mcp:
  servers:
    - name: memory
      command: "test-memory-server"
`
		err := os.WriteFile(configPath, []byte(yamlContent), 0o600)
		require.NoError(t, err)

		cfg, err := LoadFromYAML(configPath)
		require.NoError(t, err)

		assert.Equal(t, "postgres://testuser:testpass@localhost/testdb", cfg.Database.URL)
		assert.Equal(t, 20, cfg.Database.MaxConnections)
		assert.Equal(t, "claude", cfg.AI.Provider)
		assert.Equal(t, "yaml-claude-key", cfg.AI.ClaudeAPIKey)
		assert.Equal(t, "warn", cfg.Observability.LogLevel)
		assert.Equal(t, 5, cfg.Security.RateLimitRPS)
		assert.NotNil(t, cfg.MCP)
		assert.Equal(t, 1, len(cfg.MCP.Servers))
		assert.Equal(t, "memory", cfg.MCP.Servers[0].Name)
		assert.Equal(t, "test-memory-server", cfg.MCP.Servers[0].Command)
	})

	t.Run("APIKeyEnvironmentOverride", func(t *testing.T) {
		// Clear environment first
		os.Clearenv()

		// Create a temporary assistant.yaml with base API keys
		tmpDir := t.TempDir()
		configPath := filepath.Join(tmpDir, "assistant.yaml")

		yamlContent := `
ai:
  provider: "claude"
  claude_api_key: "yaml-claude-key"
  gemini_api_key: "yaml-gemini-key"
`
		err := os.WriteFile(configPath, []byte(yamlContent), 0o600)
		require.NoError(t, err)

		// Set environment variables that should override API keys only
		os.Setenv("CLAUDE_API_KEY", "env-claude-key")
		os.Setenv("GEMINI_API_KEY", "env-gemini-key")

		// Set ASSISTANT_CONFIG to point to our test file
		os.Setenv("ASSISTANT_CONFIG", configPath)
		defer os.Unsetenv("ASSISTANT_CONFIG")

		cfg, err := Load()
		require.NoError(t, err)

		// Check that only API keys were overridden from environment
		assert.Equal(t, "claude", cfg.AI.Provider)             // Not overridden
		assert.Equal(t, "env-claude-key", cfg.AI.ClaudeAPIKey) // Overridden from env
		assert.Equal(t, "env-gemini-key", cfg.AI.GeminiAPIKey) // Overridden from env
	})

	t.Run("LogLevel", func(t *testing.T) {
		cfg := &Config{
			Observability: ObservabilityConfig{
				LogLevel: "debug",
			},
		}
		assert.Equal(t, "debug", cfg.LogLevel())
	})

	t.Run("LoadWithMissingAPIKeys", func(t *testing.T) {
		// Clear all API keys
		os.Clearenv()

		// Create a temporary assistant.yaml without API keys
		tmpDir := t.TempDir()
		configPath := filepath.Join(tmpDir, "assistant.yaml")

		yamlContent := `
app:
  mode: development

database:
  url: "postgres://test:test@localhost/testdb"

ai:
  provider: "claude"
  # No API keys provided

observability:
  log_level: "debug"
`
		err := os.WriteFile(configPath, []byte(yamlContent), 0o600)
		require.NoError(t, err)

		// Set ASSISTANT_CONFIG to point to our test file
		os.Setenv("ASSISTANT_CONFIG", configPath)
		defer os.Unsetenv("ASSISTANT_CONFIG")

		_, err = Load()
		// Should error when no API keys are provided
		require.Error(t, err)
		assert.Contains(t, err.Error(), "at least one AI provider API key is required")
	})

	t.Run("MCPServerConfig", func(t *testing.T) {
		cfg := Default()
		require.NotNil(t, cfg)
		require.NotNil(t, cfg.MCP)

		// Add a test MCP server
		cfg.MCP.Servers = append(cfg.MCP.Servers, MCPServerConfig{
			Name:    "test-server",
			Command: "test-cmd",
			Args:    []string{"--test"},
			Env:     map[string]string{"TEST": "true"},
		})

		assert.Equal(t, 1, len(cfg.MCP.Servers))
		assert.Equal(t, "test-server", cfg.MCP.Servers[0].Name)
		assert.Equal(t, "test-cmd", cfg.MCP.Servers[0].Command)
		assert.Equal(t, []string{"--test"}, cfg.MCP.Servers[0].Args)
		assert.Equal(t, "true", cfg.MCP.Servers[0].Env["TEST"])
	})

	t.Run("ConfigMethods", func(t *testing.T) {
		cfg := Default()

		// Test mode methods
		assert.True(t, cfg.IsDevelopment())
		assert.False(t, cfg.IsProduction())

		// Change to production mode
		cfg.App.Mode = "production"
		assert.False(t, cfg.IsDevelopment())
		assert.True(t, cfg.IsProduction())

		// Reset cfg for consistent state
		cfg = Default()

		// Test MCP method
		assert.False(t, cfg.HasMCP()) // Default has MCP but it's disabled
		cfg.MCP.Enabled = true
		assert.False(t, cfg.HasMCP()) // Still no servers
		cfg.MCP.Servers = append(cfg.MCP.Servers, MCPServerConfig{Name: "test"})
		assert.True(t, cfg.HasMCP()) // Now it has MCP
		cfg.MCP = nil
		assert.False(t, cfg.HasMCP())

		// Reset and test AI provider methods
		cfg = Default()
		cfg.AI.ClaudeAPIKey = "test-key"
		assert.True(t, cfg.HasClaude())
		assert.False(t, cfg.HasGemini())

		cfg.AI.ClaudeAPIKey = ""
		cfg.AI.GeminiAPIKey = "test-key"
		assert.False(t, cfg.HasClaude())
		assert.True(t, cfg.HasGemini())

		// Test getter methods
		assert.Equal(t, DefaultDatabaseURL, cfg.DatabaseURL())
		assert.Equal(t, "", cfg.ClaudeAPIKey())
		assert.Equal(t, "test-key", cfg.GeminiAPIKey())
		assert.Equal(t, DefaultHTTPPort, cfg.ServerAddress())
		assert.Equal(t, 30, cfg.DBMaxConnections())
		assert.Equal(t, 5, cfg.DBMinConnections())
		assert.Equal(t, 30, cfg.ShutdownTimeout()) // 30 seconds
		assert.Equal(t, "warn", cfg.LogLevel())
		assert.Equal(t, "text", cfg.LogFormat())
		assert.Equal(t, "development", cfg.AppMode()) // We reset to default above
		assert.Equal(t, "", cfg.SearXNGURL())
	})

	t.Run("ConfigValidation", func(t *testing.T) {
		// Test validation - validate() doesn't check API keys, Load() does
		cfg := Default()

		// Test production mode validations
		cfg.App.Mode = "production"
		cfg.Security.JWTSecret = "short" // Less than 32 chars
		err := cfg.validate()
		require.Error(t, err)
		assert.Contains(t, err.Error(), "JWT_SECRET must be at least 32 characters in production")

		// Fix JWT secret
		cfg.Security.JWTSecret = "this-is-a-very-long-jwt-secret-key-for-production"
		err = cfg.validate()
		assert.NoError(t, err)

		// Test TLS validation in production
		cfg.Server.EnableTLS = true
		err = cfg.validate()
		require.Error(t, err)
		assert.Contains(t, err.Error(), "TLS certificate and key file paths must be provided")

		// Test TLS skip verify in production
		cfg.Server.EnableTLS = false
		cfg.Security.TLSSkipVerify = true
		err = cfg.validate()
		require.Error(t, err)
		assert.Contains(t, err.Error(), "skipping TLS verification is not allowed in production")

		// Test database connection pool validation
		cfg = Default()
		cfg.Database.MinConnections = 100
		cfg.Database.MaxConnections = 10
		err = cfg.validate()
		require.Error(t, err)
		assert.Contains(t, err.Error(), "DB_MIN_CONNECTIONS cannot be greater than DB_MAX_CONNECTIONS")

		// Test health check validation
		cfg = Default()
		cfg.Health.CheckInterval = 3 * time.Second
		err = cfg.validate()
		require.Error(t, err)
		assert.Contains(t, err.Error(), "HEALTH_CHECK_INTERVAL must be at least 5 seconds")

		cfg.Health.CheckInterval = 10 * time.Second
		cfg.Health.CheckTimeout = 20 * time.Second
		err = cfg.validate()
		require.Error(t, err)
		assert.Contains(t, err.Error(), "HEALTH_CHECK_TIMEOUT cannot be greater than HEALTH_CHECK_INTERVAL")
	})
}

func TestLoadFromEnvironment(t *testing.T) {
	// Save and restore environment
	oldEnv := os.Environ()
	t.Cleanup(func() {
		os.Clearenv()
		for _, e := range oldEnv {
			pair := splitEnvVar(e)
			if len(pair) == 2 {
				os.Setenv(pair[0], pair[1])
			}
		}
	})

	t.Run("LoadWithoutConfig", func(t *testing.T) {
		// Clear environment
		os.Clearenv()

		// Set required API key
		os.Setenv("CLAUDE_API_KEY", "test-claude-key-from-env")

		// Set HOME to a directory without assistant.yaml
		tmpDir := t.TempDir()
		os.Setenv("HOME", tmpDir)

		// Load() requires assistant.yaml to exist
		_, err := Load()
		require.Error(t, err)
		assert.Contains(t, err.Error(), "assistant.yaml not found")
	})
}

func splitEnvVar(env string) []string {
	for i := 0; i < len(env); i++ {
		if env[i] == '=' {
			return []string{env[:i], env[i+1:]}
		}
	}
	return []string{env}
}
