package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"gopkg.in/yaml.v3"
)

// LoadFromYAML loads configuration from assistant.yaml file
// This is the single source of configuration - no other config files
func LoadFromYAML(path string) (*Config, error) {
	// Start with default configuration
	cfg := Default()

	if path == "" {
		return cfg, nil
	}

	// Clean and validate path to prevent directory traversal
	path = filepath.Clean(path)
	if strings.Contains(path, "..") {
		return nil, fmt.Errorf("invalid config path: contains directory traversal")
	}

	// Read file
	data, err := os.ReadFile(path)
	if err != nil {
		if os.IsNotExist(err) {
			// File doesn't exist is not an error, just use defaults
			return cfg, nil
		}
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	// Parse YAML directly into Config struct
	if err := yaml.Unmarshal(data, cfg); err != nil {
		return nil, fmt.Errorf("failed to parse YAML: %w", err)
	}

	return cfg, nil
}

// SaveToYAML saves configuration to a YAML file
func SaveToYAML(cfg *Config, path string) error {
	// Clean and validate path
	path = filepath.Clean(path)
	if strings.Contains(path, "..") {
		return fmt.Errorf("invalid config path: contains directory traversal")
	}

	// Ensure directory exists
	dir := filepath.Dir(path)
	if err := os.MkdirAll(dir, 0750); err != nil {
		return fmt.Errorf("failed to create config directory: %w", err)
	}

	// Marshal configuration to YAML
	data, err := yaml.Marshal(cfg)
	if err != nil {
		return fmt.Errorf("failed to marshal config to YAML: %w", err)
	}

	// Write to file with appropriate permissions
	if err := os.WriteFile(path, data, 0600); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}

	return nil
}
