// Package config provides application configuration management
// Reads configuration from environment variables and validates required parameters
package config

import (
	"fmt"
	"os"
	"time"
	// "github.com/koopa0/assistant-go/internal/platform/logger"
)

// Default values for system configuration
const (
	// DefaultOwnerName for the personal assistant owner
	DefaultOwnerName = "Owner"

	// DefaultHTTPPort for the web interface
	DefaultHTTPPort = ":8100"

	// DefaultGRPCPort for gRPC services (if used)
	DefaultGRPCPort = ":8200"

	// DefaultDatabaseURL for local development
	DefaultDatabaseURL = "postgres://localhost/assistant?sslmode=disable"

	// Application modes
	ModeProduction  = "production"
	ModeDevelopment = "development"
	ModeTest        = "test"
)

// Environment variable names - only kept for API key security
const (
	// API keys can be set via env for security reasons
	EnvClaudeAPIKey = "CLAUDE_API_KEY" // #nosec G101 - Not a hardcoded credential, just env var name
	EnvGeminiAPIKey = "GEMINI_API_KEY" // #nosec G101 - Not a hardcoded credential, just env var name
)

// Default returns a configuration with all default values set.
// This is the single source of truth for default configuration.
func Default() *Config {
	return &Config{
		App: AppConfig{
			Mode:     "development",
			Version:  "0.1.0",
			Language: "", // Empty means auto-detect
		},
		Database: DatabaseConfig{
			URL:            DefaultDatabaseURL,
			MaxConnections: 30,
			MinConnections: 5,
			MaxIdleTime:    15 * time.Minute,
			MaxLifetime:    1 * time.Hour,
		},
		Server: ServerConfig{
			Address:         DefaultHTTPPort,
			ShutdownTimeout: 30 * time.Second,
			EnableTLS:       false,
		},
		AI: AIConfig{
			RequestTimeout: 30 * time.Second,
			Provider:       "claude",
			DefaultModel:   "claude-3.5-sonnet",
		},
		Security: SecurityConfig{
			JWTSecret:      "development-secret-key-32-chars-minimum",
			JWTExpiration:  24 * time.Hour,
			RateLimitRPS:   100,
			RateLimitBurst: 200,
			TLSSkipVerify:  false,
			AllowedOrigins: []string{"http://localhost:3000", "http://localhost:8100"},
		},
		Health: HealthConfig{
			CheckInterval: 30 * time.Second,
			CheckTimeout:  10 * time.Second,
		},
		Observability: ObservabilityConfig{
			LogLevel:        "warn",
			LogFormat:       "text",
			EnableProfiling: false,
		},
		Tools: ToolsConfig{
			SearXNGURL: "",
		},
		Owner: OwnerConfig{
			Name:        DefaultOwnerName,
			Email:       "",
			Preferences: make(map[string]string),
		},
		MCP: &MCPConfig{
			Enabled:           false,
			ClientName:        "assistant-go",
			ClientVersion:     "0.1.0",
			AutoDiscover:      true,
			DiscoveryInterval: 5 * time.Minute,
			Timeout:           30 * time.Second,
			Servers:           []MCPServerConfig{},
		},
	}
}

// Config defines the complete application configuration structure.
// It uses nested structs for better organization and clarity.
type Config struct {
	App           AppConfig           `yaml:"app"`
	Database      DatabaseConfig      `yaml:"database"`
	Server        ServerConfig        `yaml:"server"`
	AI            AIConfig            `yaml:"ai"`
	Security      SecurityConfig      `yaml:"security"`
	Health        HealthConfig        `yaml:"health"`
	Observability ObservabilityConfig `yaml:"observability"`
	Tools         ToolsConfig         `yaml:"tools"`
	Owner         OwnerConfig         `yaml:"owner"`
	MCP           *MCPConfig          `yaml:"mcp"` // Optional MCP configuration
}

// AppConfig contains basic application settings.
type AppConfig struct {
	Mode     string `yaml:"mode"` // development or production
	Version  string `yaml:"version"`
	Language string `yaml:"language"` // en, zh-TW
}

// DatabaseConfig contains database connection settings.
type DatabaseConfig struct {
	URL            string        `yaml:"url"`
	MaxConnections int           `yaml:"max_connections"`
	MinConnections int           `yaml:"min_connections"`
	MaxIdleTime    time.Duration `yaml:"max_idle_time"`
	MaxLifetime    time.Duration `yaml:"max_lifetime"`
}

// ServerConfig contains HTTP server settings.
type ServerConfig struct {
	Address         string        `yaml:"address"` // e.g., :8100
	ShutdownTimeout time.Duration `yaml:"shutdown_timeout"`
	EnableTLS       bool          `yaml:"enable_tls"`
	TLSCertFile     string        `yaml:"tls_cert_file"`
	TLSKeyFile      string        `yaml:"tls_key_file"`
}

// AIConfig contains AI provider settings.
type AIConfig struct {
	Provider       string        `yaml:"provider"` // claude or gemini
	ClaudeAPIKey   string        `yaml:"claude_api_key"`
	GeminiAPIKey   string        `yaml:"gemini_api_key"`
	RequestTimeout time.Duration `yaml:"request_timeout"`
	DefaultModel   string        `yaml:"default_model"` // Default model to use (e.g., "claude-3.5-sonnet")
}

// SecurityConfig contains security-related settings.
type SecurityConfig struct {
	JWTSecret      string        `yaml:"jwt_secret"`
	JWTExpiration  time.Duration `yaml:"jwt_expiration"`
	RateLimitRPS   int           `yaml:"rate_limit_rps"` // Requests per second
	RateLimitBurst int           `yaml:"rate_limit_burst"`
	AllowedOrigins []string      `yaml:"allowed_origins"`
	TLSSkipVerify  bool          `yaml:"tls_skip_verify"` // Dev only
}

// HealthConfig contains health check settings.
type HealthConfig struct {
	CheckInterval time.Duration `yaml:"check_interval"`
	CheckTimeout  time.Duration `yaml:"check_timeout"`
}

// ObservabilityConfig contains monitoring and profiling settings.
type ObservabilityConfig struct {
	LogLevel        string `yaml:"log_level"`  // debug, info, warn, error
	LogFormat       string `yaml:"log_format"` // text or json
	EnableProfiling bool   `yaml:"enable_profiling"`
}

// ToolsConfig contains external tool configurations.
type ToolsConfig struct {
	SearXNGURL string `yaml:"searxng_url"`
	FileSystem struct {
		AllowedDirs []string `yaml:"allowed_dirs"`
		EnableWrite bool     `yaml:"enable_write"`
		CreateDirs  bool     `yaml:"create_dirs"`
	} `yaml:"filesystem"`
}

// OwnerConfig contains personal assistant owner information.
type OwnerConfig struct {
	Name        string            `yaml:"name"`
	Email       string            `yaml:"email"`
	Preferences map[string]string `yaml:"preferences"`
}

// MCPConfig contains Model Context Protocol configurations.
type MCPConfig struct {
	Enabled           bool              `yaml:"enabled"`            // Enable MCP integration
	ClientName        string            `yaml:"client_name"`        // MCP client name
	ClientVersion     string            `yaml:"client_version"`     // MCP client version
	Servers           []MCPServerConfig `yaml:"servers"`            // List of MCP servers
	AutoDiscover      bool              `yaml:"auto_discover"`      // Auto-discover tools from servers
	DiscoveryInterval time.Duration     `yaml:"discovery_interval"` // How often to re-discover tools
	Timeout           time.Duration     `yaml:"timeout"`            // Connection timeout
}

// MCPServerConfig defines configuration for a single MCP server.
type MCPServerConfig struct {
	Name        string            `yaml:"name"`         // Server identifier
	Command     string            `yaml:"command"`      // Command to launch server
	Args        []string          `yaml:"args"`         // Command arguments
	Env         map[string]string `yaml:"env"`          // Environment variables
	AutoConnect bool              `yaml:"auto_connect"` // Connect automatically on startup
}

// Load loads configuration from assistant.yaml file.
// This is the ONLY configuration method - no config.yaml, minimal env vars.
func Load() (*Config, error) {
	// Check for explicit config file path first
	var configPaths []string
	if configFile := os.Getenv("ASSISTANT_CONFIG"); configFile != "" {
		// Use explicit config file if specified
		configPaths = []string{configFile}
	} else {
		// Only look for assistant.yaml in current directory
		configPaths = []string{
			"./assistant.yaml",
		}
	}

	var configPath string
	for _, path := range configPaths {
		if _, err := os.Stat(path); err == nil {
			configPath = path
			break
		}
	}

	if configPath == "" {
		if configFile := os.Getenv("ASSISTANT_CONFIG"); configFile != "" {
			return nil, fmt.Errorf("config file not found: %s", configFile)
		}
		return nil, fmt.Errorf("assistant.yaml not found in current directory. Please create one from configs/assistant.yaml.example")
	}

	// Load configuration from YAML (starts with defaults)
	cfg, err := LoadFromYAML(configPath)
	if err != nil {
		return nil, fmt.Errorf("load config from YAML: %w", err)
	}

	// Allow environment variable overrides for specific settings
	if v := os.Getenv("DATABASE_URL"); v != "" {
		cfg.Database.URL = v
	}
	if v := os.Getenv("CLAUDE_API_KEY"); v != "" {
		cfg.AI.ClaudeAPIKey = v
	}
	if v := os.Getenv("GEMINI_API_KEY"); v != "" {
		cfg.AI.GeminiAPIKey = v
	}

	// Validate configuration
	if err := cfg.validate(); err != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", err)
	}

	// Additional validation for production
	if cfg.App.Mode == ModeProduction && cfg.Database.URL == "" {
		return nil, fmt.Errorf("DATABASE_URL is required in production mode")
	}

	// At least one AI provider is required
	if cfg.AI.ClaudeAPIKey == "" && cfg.AI.GeminiAPIKey == "" {
		return nil, fmt.Errorf("at least one AI provider API key is required (CLAUDE_API_KEY or GEMINI_API_KEY)")
	}

	return cfg, nil
}

// validate validates configuration completeness and validity.
func (c *Config) validate() error {
	// Production environment validation
	if c.App.Mode == ModeProduction {
		// JWT secret length check
		if len(c.Security.JWTSecret) < 32 {
			return fmt.Errorf("JWT_SECRET must be at least 32 characters in production")
		}

		// TLS configuration check
		if c.Server.EnableTLS {
			if c.Server.TLSCertFile == "" || c.Server.TLSKeyFile == "" {
				return fmt.Errorf("TLS certificate and key file paths must be provided when TLS is enabled")
			}
		}

		// Security checks
		if c.Security.TLSSkipVerify {
			return fmt.Errorf("skipping TLS verification is not allowed in production")
		}
	}

	// Database connection pool validation
	if c.Database.MinConnections > c.Database.MaxConnections {
		return fmt.Errorf("DB_MIN_CONNECTIONS cannot be greater than DB_MAX_CONNECTIONS")
	}

	// Health check validation
	if c.Health.CheckInterval < 5*time.Second {
		return fmt.Errorf("HEALTH_CHECK_INTERVAL must be at least 5 seconds")
	}
	if c.Health.CheckTimeout > c.Health.CheckInterval {
		return fmt.Errorf("HEALTH_CHECK_TIMEOUT cannot be greater than HEALTH_CHECK_INTERVAL")
	}

	return nil
}

// IsProduction checks if running in production mode.
func (c *Config) IsProduction() bool {
	return c.App.Mode == ModeProduction
}

// IsDevelopment checks if running in development mode.
func (c *Config) IsDevelopment() bool {
	return c.App.Mode == "development"
}

// HasMCP checks if MCP is configured and enabled.
func (c *Config) HasMCP() bool {
	return c.MCP != nil && c.MCP.Enabled && len(c.MCP.Servers) > 0
}

// HasClaude checks if Claude API is configured.
func (c *Config) HasClaude() bool {
	return c.AI.ClaudeAPIKey != ""
}

// HasGemini checks if Gemini API is configured.
func (c *Config) HasGemini() bool {
	return c.AI.GeminiAPIKey != ""
}

// Getter methods for compatibility with existing code

// DatabaseURL returns the database connection string
func (c *Config) DatabaseURL() string {
	return c.Database.URL
}

// ClaudeAPIKey returns the Claude API key
func (c *Config) ClaudeAPIKey() string {
	return c.AI.ClaudeAPIKey
}

// GeminiAPIKey returns the Gemini API key
func (c *Config) GeminiAPIKey() string {
	return c.AI.GeminiAPIKey
}

// ServerAddress returns the server listen address
func (c *Config) ServerAddress() string {
	return c.Server.Address
}

// DBMaxConnections returns the maximum database connections
func (c *Config) DBMaxConnections() int {
	return c.Database.MaxConnections
}

// DBMinConnections returns the minimum database connections
func (c *Config) DBMinConnections() int {
	return c.Database.MinConnections
}

// ShutdownTimeout returns the shutdown timeout
func (c *Config) ShutdownTimeout() int {
	return int(c.Server.ShutdownTimeout.Seconds())
}

// LogLevel returns the log level
func (c *Config) LogLevel() string {
	return c.Observability.LogLevel
}

// LogFormat returns the log format
func (c *Config) LogFormat() string {
	return c.Observability.LogFormat
}

// AppMode returns the application mode
func (c *Config) AppMode() string {
	return c.App.Mode
}

// SearXNGURL returns the SearXNG URL
func (c *Config) SearXNGURL() string {
	return c.Tools.SearXNGURL
}
