// Package shutdown provides a graceful shutdown manager for coordinating the
// termination of services and background goroutines.
package shutdown

import (
	"context"
	"errors"
	"sync"
	"time"

	"github.com/koopa0/assistant-go/internal/platform/logger"
)

// Manager coordinates a graceful shutdown. It allows registering shutdown
// hooks and managing background goroutines that should be waited for upon shutdown.
type Shutdown struct {
	logger      logger.Logger
	hooks       []hook
	mu          sync.Mutex
	shutdownCtx context.Context
	cancel      context.CancelFunc
	wg          sync.WaitGroup
	timeout     time.Duration
}

// hook represents a function to be called during shutdown.
type hook struct {
	Name string
	Fn   func(context.Context) error
}

// New creates a new shutdown Manager.
// It takes a logger for output and a timeout for the graceful shutdown process.
func New(logger logger.Logger, timeout time.Duration) *Shutdown {
	ctx, cancel := context.WithCancel(context.Background())
	return &Shutdown{
		logger:      logger,
		hooks:       make([]hook, 0),
		shutdownCtx: ctx,
		cancel:      cancel,
		timeout:     timeout,
	}
}

// RegisterFunc registers a function to be called during shutdown.
// Hooks are executed in the reverse order of registration.
func (m *Shutdown) RegisterFunc(name string, fn func(context.Context) error) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.hooks = append(m.hooks, hook{Name: name, Fn: fn})
	m.logger.Debug("registered shutdown hook", "name", name)
}

// Go starts a function in a new goroutine that is managed by the shutdown manager.
// The provided context will be canceled when the shutdown process begins.
func (m *Shutdown) Go(name string, fn func(context.Context)) {
	m.wg.Add(1)
	go func() {
		defer m.wg.Done()
		m.logger.Debug("starting managed goroutine", "name", name)
		fn(m.shutdownCtx)
		m.logger.Debug("managed goroutine completed", "name", name)
	}()
}

// Shutdown initiates the graceful shutdown process.
// It first waits for all managed goroutines to complete, then executes all
// registered shutdown hooks.
func (m *Shutdown) Shutdown(ctx context.Context) error {
	m.logger.Debug("starting graceful shutdown", "timeout", m.timeout)
	m.cancel()

	// Create a context with a timeout for the entire shutdown process.
	shutdownCtx, cancel := context.WithTimeout(ctx, m.timeout)
	defer cancel()

	// Wait for all managed goroutines to finish.
	done := make(chan struct{})
	go func() {
		m.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		m.logger.Debug("all managed goroutines completed")
	case <-shutdownCtx.Done():
		m.logger.Warn("timeout waiting for goroutines to complete")
	}

	// Execute shutdown hooks in reverse order.
	m.mu.Lock()
	hooks := make([]hook, len(m.hooks))
	copy(hooks, m.hooks)
	m.mu.Unlock()

	var errs []error
	for i := len(hooks) - 1; i >= 0; i-- {
		h := hooks[i]
		m.logger.Debug("executing shutdown hook", "name", h.Name)

		if err := h.Fn(shutdownCtx); err != nil {
			m.logger.Error("shutdown hook failed", "name", h.Name, "error", err)
			errs = append(errs, err)
		} else {
			m.logger.Debug("shutdown hook completed", "name", h.Name)
		}
	}

	if len(errs) > 0 {
		return errors.Join(errs...)
	}

	m.logger.Debug("graceful shutdown completed")
	return nil
}
