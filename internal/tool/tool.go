// Package tool provides a unified interface for AI tool execution.
//
// This package defines the core Tool interface and common functionality
// for all tools in the system. Tools are the bridge between AI reasoning
// and real-world actions, enabling the AI to perform tasks like file
// operations, web searches, calculations, and more.
package tool

import (
	"context"
	"encoding/json"

	"github.com/koopa0/assistant-go/internal/ai"
)

// Tool names
const (
	ToolMemory     = "memory"
	ToolDateTime   = "datetime"
	ToolWebSearch  = "web_search"
	ToolCalculator = "calculator"
)

// Tool represents an executable tool.
// This interface matches the existing tool implementation pattern.
type Tool interface {
	// Name returns the tool name
	Name() string
	// Description returns the tool description
	Description() string
	// Execute runs the tool with given input
	Execute(ctx context.Context, input any) (any, error)
	// InputSchema returns the JSON Schema for input parameters
	InputSchema() json.RawMessage
}

// ToolCall represents a request to execute a tool.
// This is imported from ai package for compatibility.
type ToolCall = ai.ToolCall

// ToolResult represents the result of tool execution.
// This is imported from ai package for compatibility.
type ToolResult = ai.ToolResult

// BaseTool provides common tool functionality.
// Embed this to quickly implement the Tool interface.
type BaseTool struct {
	name        string
	description string
	schema      json.RawMessage
}

// NewBaseTool creates a new base tool.
func NewBaseTool(name, description string, schema json.RawMessage) BaseTool {
	return BaseTool{
		name:        name,
		description: description,
		schema:      schema,
	}
}

// Name returns the tool name.
func (b BaseTool) Name() string {
	return b.name
}

// Description returns the tool description.
func (b BaseTool) Description() string {
	return b.description
}

// InputSchema returns the JSON Schema.
func (b BaseTool) InputSchema() json.RawMessage {
	return b.schema
}

// GetToolParams converts tools to AI provider format.
// This is used for registering tools with AI services.
func GetToolParams(tools []Tool) []ai.Tool {
	params := make([]ai.Tool, len(tools))
	for i, tool := range tools {
		var schema ai.Schema
		if jsonSchema := tool.InputSchema(); len(jsonSchema) > 0 {
			if err := json.Unmarshal(jsonSchema, &schema); err != nil {
				// If unmarshal fails, create a basic schema
				schema = ai.Schema{
					Type: "object",
				}
			}
		} else {
			// Default schema if none provided
			schema = ai.Schema{
				Type: "object",
			}
		}

		params[i] = ai.Tool{
			Name:        tool.Name(),
			Description: tool.Description(),
			InputSchema: schema,
		}
	}
	return params
}
