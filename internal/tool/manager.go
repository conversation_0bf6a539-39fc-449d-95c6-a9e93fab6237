// Package tool provides unified tool management.
package tool

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"

	"github.com/koopa0/assistant-go/internal/ai"
)

// Manager provides unified tool management with a simple registry.
// Tools are registered by name and executed dynamically.
type Manager struct {
	// Registry for all tools
	registry map[string]Tool
	mu       sync.RWMutex

	// Configuration
	config Config
}

// Config for tools initialization
type Config struct {
	FileSystem FileSystemConfig
	Web        WebConfig
}

// FileSystemConfig contains configuration for file system tools
type FileSystemConfig struct {
	AllowedDirs []string
	EnableWrite bool
	CreateDirs  bool
	MaxFileSize int64
}

// WebConfig contains configuration for web tools
type WebConfig struct {
	UserAgent string
}

// NewManager creates a new tool manager with the given configuration.
func NewManager(cfg Config) *Manager {
	m := &Manager{
		registry: make(map[string]Tool),
		config:   cfg,
	}

	// Built-in tools are now registered separately via RegisterBuiltinTools

	return m
}

// Register registers a tool in the manager.
func (m *Manager) Register(tool Tool) error {
	if tool == nil {
		return fmt.Errorf("cannot register nil tool")
	}

	name := tool.Name()
	if name == "" {
		return fmt.Errorf("tool has empty name")
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	m.registry[name] = tool
	return nil
}

// Unregister removes a tool from the registry.
func (m *Manager) Unregister(name string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if _, exists := m.registry[name]; !exists {
		return fmt.Errorf("tool '%s' not found", name)
	}

	delete(m.registry, name)
	return nil
}

// RegisterTools registers multiple tools at once with optional prefix.
func (m *Manager) RegisterTools(tools []Tool, prefix string) error {
	for _, tool := range tools {
		if tool == nil {
			continue
		}

		// Apply prefix if provided
		if prefix != "" {
			tool = &prefixedTool{
				Tool:   tool,
				prefix: prefix,
			}
		}

		if err := m.Register(tool); err != nil {
			return fmt.Errorf("register tool %s: %w", tool.Name(), err)
		}
	}
	return nil
}

// Get retrieves a tool by name.
func (m *Manager) Get(name string) (Tool, bool) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	tool, ok := m.registry[name]
	return tool, ok
}

// List returns all available tools.
func (m *Manager) List() []Tool {
	m.mu.RLock()
	defer m.mu.RUnlock()

	tools := make([]Tool, 0, len(m.registry))
	for _, tool := range m.registry {
		tools = append(tools, tool)
	}
	return tools
}

// Execute executes a tool by name with the given input.
func (m *Manager) Execute(ctx context.Context, call ToolCall) ToolResult {
	tool, ok := m.Get(call.Name)
	if !ok {
		return ToolResult{
			ID:      call.ID,
			Content: fmt.Sprintf("tool '%s' not found", call.Name),
			Error:   fmt.Errorf("tool '%s' not found", call.Name),
		}
	}

	// Convert ToolInput to the format expected by the tool
	input := m.prepareInput(call)

	// Execute the tool
	result, err := tool.Execute(ctx, input)
	if err != nil {
		return ToolResult{
			ID:      call.ID,
			Content: fmt.Sprintf("error: %v", err),
			Error:   err,
		}
	}

	// Convert result to string
	content := m.formatResult(result)

	return ToolResult{
		ID:      call.ID,
		Content: content,
	}
}

// prepareInput converts Arguments to the format expected by tools.
func (m *Manager) prepareInput(call ToolCall) any {
	if len(call.Args) == 0 {
		return nil
	}

	// Build input map from Arguments
	input := make(map[string]any)

	// Extract all arguments as their actual types
	for key, raw := range call.Args {
		var value any
		if err := json.Unmarshal(raw, &value); err == nil {
			input[key] = value
		}
	}

	return input
}

// formatResult converts tool result to string.
func (m *Manager) formatResult(result any) string {
	switch v := result.(type) {
	case string:
		return v
	case []byte:
		return string(v)
	case error:
		return fmt.Sprintf("error: %v", v)
	default:
		// Marshal complex results as JSON
		data, err := json.Marshal(result)
		if err != nil {
			return fmt.Sprintf("failed to format result: %v", err)
		}
		return string(data)
	}
}

// GetToolParams returns tool parameters for AI systems.
func (m *Manager) GetToolParams() []ai.Tool {
	tools := m.List()
	return GetToolParams(tools)
}

// HasTool checks if a tool exists.
func (m *Manager) HasTool(name string) bool {
	_, exists := m.Get(name)
	return exists
}

// prefixedTool wraps a tool to add a prefix to its name.
type prefixedTool struct {
	Tool
	prefix string
}

func (t *prefixedTool) Name() string {
	return t.prefix + "." + t.Tool.Name()
}
