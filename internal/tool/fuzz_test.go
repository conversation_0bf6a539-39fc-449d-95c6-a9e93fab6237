package tool

import (
	"context"
	"encoding/json"
	"strings"
	"testing"
)

// FuzzExecuteInput tests tool execution with various inputs
func FuzzExecuteInput(f *testing.F) {
	// Add seed corpus
	f.Add(`{"query": "test search"}`, "search")
	f.Add(`{"path": "/tmp/test.txt", "content": "hello"}`, "write_file")
	f.Add(`{"invalid": "json"}`, "unknown_tool")
	f.Add(`{"nested": {"deep": {"value": "test"}}}`, "process")
	f.Add(`{"array": [1, 2, 3, "test"]}`, "array_tool")
	f.Add(`{"unicode": "你好世界 🌍"}`, "unicode_tool")
	f.Add(`{"empty": ""}`, "empty_tool")
	f.Add(`{"number": 123.456}`, "number_tool")
	f.Add(`{"boolean": true}`, "bool_tool")
	f.Add(`null`, "null_tool")

	// Create a simple mock tool for testing
	mockTool := &mockTool{
		name:        "fuzz_tool",
		description: "Tool for fuzzing",
		executeFunc: func(ctx context.Context, input any) (any, error) {
			// Just return the input to test parsing
			return input, nil
		},
	}

	f.Fuzz(func(t *testing.T, inputJSON string, toolName string) {
		// Try to parse the input as JSON
		var input map[string]any
		err := json.Unmarshal([]byte(inputJSON), &input)
		
		// Execute should handle both valid and invalid JSON gracefully
		ctx := context.Background()
		result, execErr := mockTool.Execute(ctx, input)
		
		if err != nil {
			// If JSON parsing failed, Execute should handle nil or empty input
			if execErr == nil && result != nil {
				// Tool should handle invalid input gracefully
				t.Logf("Tool handled invalid JSON input: %v", result)
			}
		} else {
			// Valid JSON was provided
			if execErr != nil {
				t.Logf("Tool returned error for valid JSON: %v", execErr)
			}
		}

		// Should never panic
	})
}

// FuzzToolRegistry tests the tool registry with various names
func FuzzToolRegistry(f *testing.F) {
	// Add seed corpus
	f.Add("valid_tool", "A valid tool")
	f.Add("", "Empty name")
	f.Add(strings.Repeat("a", 100), "Long name")
	f.Add("tool-with-dashes", "Dashed name")
	f.Add("tool_with_underscores", "Underscored name")
	f.Add("ToolWithCaps", "Mixed case")
	f.Add("tool.with.dots", "Dotted name")
	f.Add("tool/with/slashes", "Slashed name")
	f.Add("tool with spaces", "Spaced name")
	f.Add("工具", "Unicode name")
	f.Add("tool\nwith\nnewlines", "Newlines")
	f.Add("tool\twith\ttabs", "Tabs")

	f.Fuzz(func(t *testing.T, name string, description string) {
		manager := NewManager()
		
		// Create a mock tool
		tool := &mockTool{
			name:        name,
			description: description,
			executeFunc: func(ctx context.Context, input any) (any, error) {
				return "executed", nil
			},
		}

		// Register should handle any name gracefully
		err := manager.Register(tool)
		
		if name == "" {
			// Empty names should be rejected
			if err == nil {
				t.Error("Expected error for empty tool name")
			}
		} else {
			// Non-empty names might be normalized or accepted
			if err != nil {
				t.Logf("Registration failed for name '%s': %v", name, err)
			} else {
				// Try to retrieve the tool
				retrieved, exists := manager.Get(name)
				if !exists {
					// Tool might be registered under normalized name
					t.Logf("Tool registered but not retrievable with original name: %s", name)
				} else if retrieved.Name() != name {
					t.Logf("Tool name was normalized from '%s' to '%s'", name, retrieved.Name())
				}
			}
		}

		// List should never panic
		tools := manager.List()
		_ = tools
	})
}

// FuzzJSONSchema tests JSON schema parsing
func FuzzJSONSchema(f *testing.F) {
	// Add seed corpus - various JSON schema structures
	f.Add(`{"type": "object", "properties": {"name": {"type": "string"}}}`)
	f.Add(`{"type": "array", "items": {"type": "number"}}`)
	f.Add(`{"type": "string", "enum": ["a", "b", "c"]}`)
	f.Add(`{"type": "number", "minimum": 0, "maximum": 100}`)
	f.Add(`{"type": "boolean"}`)
	f.Add(`{"type": "null"}`)
	f.Add(`{"type": ["string", "null"]}`)
	f.Add(`{"allOf": [{"type": "string"}, {"minLength": 5}]}`)
	f.Add(`{"oneOf": [{"type": "number"}, {"type": "string"}]}`)
	f.Add(`{"$ref": "#/definitions/address"}`)
	f.Add(`invalid json schema`)
	f.Add(`{"type": "object", "properties": {}}`)
	f.Add(`{}`)

	f.Fuzz(func(t *testing.T, schemaJSON string) {
		// Try to parse as JSON
		var schema map[string]any
		err := json.Unmarshal([]byte(schemaJSON), &schema)
		
		if err != nil {
			// Invalid JSON should be handled gracefully
			t.Logf("Invalid JSON schema: %v", err)
			return
		}

		// Create a mock tool with this schema
		tool := &mockTool{
			name:        "schema_test",
			description: "Testing schema parsing",
			inputSchema: json.RawMessage(schemaJSON),
			executeFunc: func(ctx context.Context, input any) (any, error) {
				return input, nil
			},
		}

		// InputSchema should never panic
		schema = tool.InputSchema()
		if schema == nil {
			t.Logf("Schema parsing returned nil for: %s", schemaJSON)
		}
	})
}

// FuzzToolExecution tests tool execution with various parameter combinations
func FuzzToolExecution(f *testing.F) {
	// Add seed corpus with different parameter types
	f.Add("search", `{"query": "test"}`, 5000) // timeout in ms
	f.Add("file_read", `{"path": "/etc/passwd"}`, 1000)
	f.Add("calculation", `{"expression": "2+2"}`, 100)
	f.Add("echo", `{"message": "hello\nworld"}`, 10000)
	f.Add("process", `{"command": "ls -la"}`, 30000)
	f.Add("", `{}`, 0)
	f.Add("test", `null`, -1000)

	manager := NewManager()

	// Register some mock tools
	tools := []Tool{
		&mockTool{
			name: "search",
			executeFunc: func(ctx context.Context, input any) (any, error) {
				return map[string]any{"results": []string{"result1", "result2"}}, nil
			},
		},
		&mockTool{
			name: "echo",
			executeFunc: func(ctx context.Context, input any) (any, error) {
				return input, nil
			},
		},
		&mockTool{
			name: "error_tool",
			executeFunc: func(ctx context.Context, input any) (any, error) {
				return nil, context.DeadlineExceeded
			},
		},
	}

	for _, tool := range tools {
		manager.Register(tool)
	}

	f.Fuzz(func(t *testing.T, toolName string, inputJSON string, timeoutMs int) {
		// Parse input
		var input map[string]any
		json.Unmarshal([]byte(inputJSON), &input)

		// Get tool
		tool, exists := manager.Get(toolName)
		if !exists {
			// Tool doesn't exist - this is expected for some fuzz inputs
			return
		}

		// Create context with timeout if specified
		ctx := context.Background()
		if timeoutMs > 0 && timeoutMs < 60000 {
			var cancel context.CancelFunc
			ctx, cancel = context.WithTimeout(ctx, timeoutMs*1000000) // convert to nanoseconds
			defer cancel()
		}

		// Execute should handle any input gracefully
		result, err := tool.Execute(ctx, input)
		
		// Log interesting cases
		if err != nil && err == context.DeadlineExceeded {
			t.Logf("Execution timed out for tool %s with timeout %dms", toolName, timeoutMs)
		}
		
		_ = result // Result can be anything
	})
}

// mockTool implementation for fuzzing
type mockTool struct {
	name        string
	description string
	inputSchema json.RawMessage
	executeFunc func(context.Context, any) (any, error)
}

func (t *mockTool) Name() string {
	return t.name
}

func (t *mockTool) Description() string {
	return t.description
}

func (t *mockTool) InputSchema() json.RawMessage {
	if t.inputSchema != nil {
		return t.inputSchema
	}
	return json.RawMessage(`{"type": "object"}`)
}

func (t *mockTool) Execute(ctx context.Context, input any) (any, error) {
	if t.executeFunc != nil {
		return t.executeFunc(ctx, input)
	}
	return input, nil
}