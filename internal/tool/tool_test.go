package tool_test

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/koopa0/assistant-go/internal/tool"
	"github.com/koopa0/assistant-go/internal/tool/file"
	tooltime "github.com/koopa0/assistant-go/internal/tool/time"
)

// TestToolInterface ensures all tools implement the interface correctly
func TestToolInterface(t *testing.T) {
	tools := []tool.Tool{
		tooltime.NewCurrentTimeTool(),
		tooltime.NewTimeDiffTool(),
		file.NewReadFileTool([]string{"."}, 1024*1024),
		file.NewListDirectoryTool([]string{"."}),
		file.NewWriteFileTool([]string{"./test"}, true),
	}

	for _, tl := range tools {
		t.Run(tl.Name(), func(t *testing.T) {
			// Test Name
			if name := tl.Name(); name == "" {
				t.Error("Tool name should not be empty")
			}

			// Test Description
			if desc := tl.Description(); desc == "" {
				t.Error("Tool description should not be empty")
			}

			// Test InputSchema
			schema := tl.InputSchema()
			var schemaMap map[string]any
			if err := json.Unmarshal(schema, &schemaMap); err != nil {
				t.Errorf("Invalid JSON schema: %v", err)
			}

			// Verify schema has type field
			if _, ok := schemaMap["type"]; !ok {
				t.Error("Schema missing 'type' field")
			}
		})
	}
}

// TestToolManager tests the tool manager functionality
func TestToolManager(t *testing.T) {
	cfg := tool.Config{
		FileSystem: tool.FileSystemConfig{
			AllowedDirs: []string{"."},
			EnableWrite: true,
			MaxFileSize: 1024 * 1024,
		},
	}

	mgr := tool.NewManager(cfg)

	t.Run("Register and List", func(t *testing.T) {
		// Register a tool
		timeTool := tooltime.NewCurrentTimeTool()
		err := mgr.Register(timeTool)
		if err != nil {
			t.Fatalf("Failed to register tool: %v", err)
		}

		// List tools
		tools := mgr.List()
		if len(tools) != 1 {
			t.Errorf("Expected 1 tool, got %d", len(tools))
		}

		// Get tool by name
		found, exists := mgr.Get("current_time")
		if !exists {
			t.Error("Tool not found by name")
		}
		if found == nil {
			t.Error("Tool is nil")
		}
	})

	t.Run("Execute Tool", func(t *testing.T) {
		// This would test the Execute method
		// Skipping actual execution test as it requires AI types
	})
}

// Table-driven tests for time tools
func TestTimeTools(t *testing.T) {
	tests := []struct {
		name     string
		tool     tool.Tool
		input    any
		validate func(t *testing.T, result any, err error)
	}{
		{
			name:  "CurrentTime_Default",
			tool:  tooltime.NewCurrentTimeTool(),
			input: nil,
			validate: func(t *testing.T, result any, err error) {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
				if result == nil {
					t.Error("Expected result, got nil")
				}
				// Check result is a map with expected fields
				if m, ok := result.(map[string]any); ok {
					if _, ok := m["time"]; !ok {
						t.Error("Result missing 'time' field")
					}
				} else {
					t.Error("Result should be a map")
				}
			},
		},
		{
			name: "CurrentTime_WithTimezone",
			tool: tooltime.NewCurrentTimeTool(),
			input: map[string]any{
				"timezone": "America/New_York",
			},
			validate: func(t *testing.T, result any, err error) {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
				if m, ok := result.(map[string]any); ok {
					if tz, ok := m["timezone"].(string); !ok || tz != "America/New_York" {
						t.Error("Timezone not set correctly")
					}
				}
			},
		},
		{
			name: "TimeDiff_SimpleHours",
			tool: tooltime.NewTimeDiffTool(),
			input: map[string]any{
				"from": "-3h",
			},
			validate: func(t *testing.T, result any, err error) {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
				if m, ok := result.(map[string]any); ok {
					if hours, ok := m["hours"].(float64); !ok || hours < 2.9 || hours > 3.1 {
						t.Errorf("Expected ~3 hours, got %v", hours)
					}
				}
			},
		},
	}

	ctx := context.Background()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := tt.tool.Execute(ctx, tt.input)
			tt.validate(t, result, err)
		})
	}
}

// Benchmark tests
func BenchmarkCurrentTimeTool(b *testing.B) {
	tool := tooltime.NewCurrentTimeTool()
	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = tool.Execute(ctx, nil)
	}
}

// Fuzzing test for time diff tool
func FuzzTimeDiff(f *testing.F) {
	tool := tooltime.NewTimeDiffTool()
	ctx := context.Background()

	// Add seed corpus
	f.Add("-1h")
	f.Add("2h30m")
	f.Add("-24h")
	f.Add("invalid")

	f.Fuzz(func(t *testing.T, input string) {
		// Test should not panic
		_, _ = tool.Execute(ctx, map[string]any{
			"from": input,
		})
	})
}
