package web

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewWebExtractor(t *testing.T) {
	tests := []struct {
		name   string
		config Config
		check  func(t *testing.T, we *WebExtractor)
	}{
		{
			name:   "default config",
			config: Config{},
			check: func(t *testing.T, we *WebExtractor) {
				assert.NotNil(t, we.httpClient)
				assert.Empty(t, we.searxngURL)
				assert.Equal(t, 30*time.Second, we.httpClient.Timeout)
			},
		},
		{
			name: "custom config",
			config: Config{
				SearXNGURL:   "http://localhost:8888",
				HTTPTimeout:  10 * time.Second,
				MaxRedirects: 3,
				UserAgent:    "TestAgent/1.0",
			},
			check: func(t *testing.T, we *WebExtractor) {
				assert.NotNil(t, we.httpClient)
				assert.Equal(t, "http://localhost:8888", we.searxngURL)
				assert.Equal(t, 10*time.Second, we.httpClient.Timeout)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			we := NewWebExtractor(tt.config)
			tt.check(t, we)
		})
	}
}

func TestTransportWithUserAgent(t *testing.T) {
	// Create test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Echo back the User-Agent header
		w.Write([]byte(r.Header.Get("User-Agent")))
	}))
	defer server.Close()

	transport := &transportWithUserAgent{
		userAgent: "TestAgent/1.0",
		base:      http.DefaultTransport,
	}

	client := &http.Client{Transport: transport}

	// Test request without User-Agent
	req, _ := http.NewRequest("GET", server.URL, nil)
	resp, err := client.Do(req)
	require.NoError(t, err)
	defer resp.Body.Close()

	body := make([]byte, 100)
	n, _ := resp.Body.Read(body)
	assert.Equal(t, "TestAgent/1.0", string(body[:n]))

	// Test request with existing User-Agent (should not override)
	req2, _ := http.NewRequest("GET", server.URL, nil)
	req2.Header.Set("User-Agent", "CustomAgent/2.0")
	resp2, err := client.Do(req2)
	require.NoError(t, err)
	defer resp2.Body.Close()

	body2 := make([]byte, 100)
	n2, _ := resp2.Body.Read(body2)
	assert.Equal(t, "CustomAgent/2.0", string(body2[:n2]))
}

func TestWebSearchTool(t *testing.T) {
	// Create mock SearXNG server
	mockSearXNG := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		assert.Equal(t, "/search", r.URL.Path)
		query := r.URL.Query()
		assert.Equal(t, "test query", query.Get("q"))
		assert.Equal(t, "json", query.Get("format"))

		response := map[string]any{
			"query": query.Get("q"),
			"results": []map[string]any{
				{
					"title":   "Test Result 1",
					"url":     "https://example.com/1",
					"content": "This is test result 1",
					"engine":  "google",
				},
				{
					"title":   "Test Result 2",
					"url":     "https://example.com/2",
					"content": "This is test result 2",
					"engine":  "duckduckgo",
				},
			},
			"number_of_results": 2,
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}))
	defer mockSearXNG.Close()

	extractor := NewWebExtractor(Config{
		SearXNGURL: mockSearXNG.URL,
	})
	tool := &WebSearchTool{extractor: extractor}
	ctx := context.Background()

	tests := []struct {
		name        string
		input       any
		validateRes func(t *testing.T, result any, err error)
	}{
		{
			name:  "missing query",
			input: map[string]any{},
			validateRes: func(t *testing.T, result any, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "query is required")
			},
		},
		{
			name: "basic search",
			input: map[string]any{
				"query": "test query",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, "test query", res["query"])
				assert.Equal(t, 2, res["count"])
				results := res["results"].([]map[string]any)
				assert.Len(t, results, 2)
				assert.Equal(t, "Test Result 1", results[0]["title"])
			},
		},
		{
			name: "search with max results",
			input: map[string]any{
				"query":       "test query",
				"max_results": float64(1),
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				results := res["results"].([]map[string]any)
				assert.Len(t, results, 1)
			},
		},
		{
			name: "search with categories",
			input: map[string]any{
				"query":      "test query",
				"categories": []any{"general", "news"},
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.NotNil(t, res["results"])
			},
		},
		{
			name: "search with all parameters",
			input: map[string]any{
				"query":       "test query",
				"max_results": float64(5),
				"categories":  []any{"general"},
				"language":    "en",
				"time_range":  "week",
				"safe_search": true,
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, "test query", res["query"])
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := tool.Execute(ctx, tt.input)
			tt.validateRes(t, result, err)
		})
	}
}

func TestWebSearchTool_DuckDuckGo(t *testing.T) {
	// Skip in CI environment to avoid flaky network tests
	if os.Getenv("CI") == "true" {
		t.Skip("Skipping DuckDuckGo test in CI environment")
	}

	// Test without SearXNG (fallback to DuckDuckGo)
	extractor := NewWebExtractor(Config{})
	tool := &WebSearchTool{extractor: extractor}
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	result, err := tool.Execute(ctx, map[string]any{
		"query": "golang testing",
	})

	require.NoError(t, err)
	res := result.(map[string]any)
	assert.Equal(t, "golang testing", res["query"])
	assert.Contains(t, res["source"], "DuckDuckGo")
	assert.NotNil(t, res["results"])
	assert.Greater(t, res["count"], 0)
}

func TestWebSearchTool_Metadata(t *testing.T) {
	tool := &WebSearchTool{}

	assert.Equal(t, "web_search", tool.Name())
	assert.Contains(t, tool.Description(), "Search the web")

	// Verify schema is valid JSON
	var schema map[string]any
	err := json.Unmarshal(tool.InputSchema(), &schema)
	assert.NoError(t, err)
	assert.Equal(t, "object", schema["type"])

	// Check required fields
	required := schema["required"].([]any)
	assert.Contains(t, required, "query")
}

func TestWebScrapeTool(t *testing.T) {
	// Create mock web server
	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case "/test":
			w.Header().Set("Content-Type", "text/html")
			w.Write([]byte("<html><body><h1>Test Page</h1></body></html>"))
		case "/json":
			w.Header().Set("Content-Type", "application/json")
			w.Write([]byte(`{"status": "ok"}`))
		default:
			w.WriteHeader(404)
		}
	}))
	defer mockServer.Close()

	extractor := NewWebExtractor(Config{})
	tool := &WebScrapeTool{extractor: extractor}
	ctx := context.Background()

	tests := []struct {
		name        string
		input       any
		validateRes func(t *testing.T, result any, err error)
	}{
		{
			name:  "missing URL",
			input: map[string]any{},
			validateRes: func(t *testing.T, result any, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "URL is required")
			},
		},
		{
			name: "scrape HTML page",
			input: map[string]any{
				"url": mockServer.URL + "/test",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, mockServer.URL+"/test", res["url"])
				assert.Equal(t, 200, res["status"])
				assert.Contains(t, res["contentType"].(string), "text/html")
			},
		},
		{
			name: "scrape JSON page",
			input: map[string]any{
				"url": mockServer.URL + "/json",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, 200, res["status"])
				assert.Contains(t, res["contentType"].(string), "application/json")
			},
		},
		{
			name: "scrape with selectors",
			input: map[string]any{
				"url": mockServer.URL + "/test",
				"selectors": map[string]any{
					"title": "h1",
					"body":  "body",
				},
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, 200, res["status"])
			},
		},
		{
			name: "scrape with options",
			input: map[string]any{
				"url":         mockServer.URL + "/test",
				"extract_all": false,
				"format":      "html",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.NotNil(t, res)
			},
		},
		{
			name: "404 page",
			input: map[string]any{
				"url": mockServer.URL + "/notfound",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, 404, res["status"])
			},
		},
		{
			name: "invalid URL",
			input: map[string]any{
				"url": "not-a-url",
			},
			validateRes: func(t *testing.T, result any, err error) {
				assert.Error(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := tool.Execute(ctx, tt.input)
			tt.validateRes(t, result, err)
		})
	}
}

func TestWebScrapeTool_Metadata(t *testing.T) {
	tool := &WebScrapeTool{}

	assert.Equal(t, "web_scrape", tool.Name())
	assert.Contains(t, tool.Description(), "Extract structured data")

	// Verify schema is valid JSON
	var schema map[string]any
	err := json.Unmarshal(tool.InputSchema(), &schema)
	assert.NoError(t, err)
	assert.Equal(t, "object", schema["type"])

	// Check required fields
	required := schema["required"].([]any)
	assert.Contains(t, required, "url")
}

func TestWebExtractTool(t *testing.T) {
	// Create mock servers
	mockSearch := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		response := map[string]any{
			"query": r.URL.Query().Get("q"),
			"results": []map[string]any{
				{
					"title":   "Result 1",
					"url":     "https://example.com/1",
					"content": "Content 1",
					"engine":  "test",
				},
				{
					"title":   "Result 2",
					"url":     "https://example.com/2",
					"content": "Content 2",
					"engine":  "test",
				},
			},
			"number_of_results": 2,
		}
		json.NewEncoder(w).Encode(response)
	}))
	defer mockSearch.Close()

	mockWeb := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "text/html")
		w.Write([]byte("<html><body>Test content</body></html>"))
	}))
	defer mockWeb.Close()

	// Update search results to use mock web server URLs
	extractor := &WebExtractor{
		httpClient: &http.Client{Timeout: 5 * time.Second},
		searxngURL: mockSearch.URL,
	}

	// Override the search to return our mock URLs
	mockSearchWithURLs := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		response := map[string]any{
			"query": r.URL.Query().Get("q"),
			"results": []map[string]any{
				{
					"title":   "Result 1",
					"url":     mockWeb.URL + "/page1",
					"content": "Content 1",
					"engine":  "test",
				},
				{
					"title":   "Result 2",
					"url":     mockWeb.URL + "/page2",
					"content": "Content 2",
					"engine":  "test",
				},
			},
			"number_of_results": 2,
		}
		json.NewEncoder(w).Encode(response)
	}))
	defer mockSearchWithURLs.Close()

	extractor.searxngURL = mockSearchWithURLs.URL
	tool := &WebExtractTool{extractor: extractor}
	ctx := context.Background()

	tests := []struct {
		name        string
		input       any
		validateRes func(t *testing.T, result any, err error)
	}{
		{
			name:  "missing query",
			input: map[string]any{},
			validateRes: func(t *testing.T, result any, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "query is required")
			},
		},
		{
			name: "basic extraction",
			input: map[string]any{
				"query": "test extraction",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, "test extraction", res["query"])
				assert.Equal(t, "summary", res["type"])
				extracted := res["extracted"].([]map[string]any)
				assert.Greater(t, len(extracted), 0)
			},
		},
		{
			name: "extraction with parameters",
			input: map[string]any{
				"query":        "test query",
				"max_pages":    float64(1),
				"extract_type": "full",
				"depth":        float64(2),
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, "full", res["type"])
				extracted := res["extracted"].([]map[string]any)
				assert.LessOrEqual(t, len(extracted), 1)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := tool.Execute(ctx, tt.input)
			tt.validateRes(t, result, err)
		})
	}
}

func TestWebExtractTool_NoResults(t *testing.T) {
	// Mock search that returns no results
	mockSearch := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		response := map[string]any{
			"query":             r.URL.Query().Get("q"),
			"results":           []map[string]any{},
			"number_of_results": 0,
		}
		json.NewEncoder(w).Encode(response)
	}))
	defer mockSearch.Close()

	extractor := NewWebExtractor(Config{
		SearXNGURL: mockSearch.URL,
	})
	tool := &WebExtractTool{extractor: extractor}
	ctx := context.Background()

	result, err := tool.Execute(ctx, map[string]any{
		"query": "no results query",
	})

	require.NoError(t, err)
	res := result.(map[string]any)
	assert.Equal(t, "no results query", res["query"])
	assert.Contains(t, res["message"], "No search results found")
}

func TestWebExtractTool_Metadata(t *testing.T) {
	tool := &WebExtractTool{}

	assert.Equal(t, "web_extract", tool.Name())
	assert.Contains(t, tool.Description(), "Search and extract")

	// Verify schema is valid JSON
	var schema map[string]any
	err := json.Unmarshal(tool.InputSchema(), &schema)
	assert.NoError(t, err)
	assert.Equal(t, "object", schema["type"])

	// Check required fields
	required := schema["required"].([]any)
	assert.Contains(t, required, "query")
}

// Test redirect handling
func TestWebExtractor_Redirects(t *testing.T) {
	redirectCount := 0
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if redirectCount < 3 {
			redirectCount++
			http.Redirect(w, r, fmt.Sprintf("/redirect%d", redirectCount), http.StatusFound)
			return
		}
		w.Write([]byte("Final page"))
	}))
	defer server.Close()

	extractor := NewWebExtractor(Config{
		MaxRedirects: 5,
	})

	req, _ := http.NewRequest("GET", server.URL, nil)
	resp, err := extractor.httpClient.Do(req)
	require.NoError(t, err)
	defer resp.Body.Close()

	// Should follow redirects
	body := make([]byte, 100)
	n, _ := resp.Body.Read(body)
	assert.Equal(t, "Final page", string(body[:n]))
}

// Test too many redirects
func TestWebExtractor_TooManyRedirects(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Always redirect
		http.Redirect(w, r, "/loop", http.StatusFound)
	}))
	defer server.Close()

	extractor := NewWebExtractor(Config{
		MaxRedirects: 2,
	})

	req, _ := http.NewRequest("GET", server.URL, nil)
	resp, err := extractor.httpClient.Do(req)
	if resp != nil {
		defer resp.Body.Close()
	}
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "redirect")
}

// Fuzzing tests
func FuzzWebSearchInput(f *testing.F) {
	// Add seed corpus
	f.Add(`{"query": "test"}`)
	f.Add(`{"query": "test", "max_results": 10}`)
	f.Add(`{"query": "test", "categories": ["general", "news"]}`)
	f.Add(`{}`)
	f.Add(`null`)
	f.Add(`{"query": "` + strings.Repeat("a", 1000) + `"}`)

	extractor := NewWebExtractor(Config{})
	tool := &WebSearchTool{extractor: extractor}
	ctx := context.Background()

	f.Fuzz(func(t *testing.T, inputJSON string) {
		var input map[string]any
		if inputJSON != "null" && inputJSON != "" {
			json.Unmarshal([]byte(inputJSON), &input)
		}

		// Should not panic
		_, _ = tool.Execute(ctx, input)
	})
}

// Benchmark tests
func BenchmarkWebSearchTool(b *testing.B) {
	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		response := map[string]any{
			"query": "benchmark",
			"results": []map[string]any{
				{"title": "Result", "url": "https://example.com", "content": "Test"},
			},
			"number_of_results": 1,
		}
		json.NewEncoder(w).Encode(response)
	}))
	defer mockServer.Close()

	extractor := NewWebExtractor(Config{
		SearXNGURL: mockServer.URL,
	})
	tool := &WebSearchTool{extractor: extractor}
	ctx := context.Background()
	input := map[string]any{"query": "benchmark test"}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = tool.Execute(ctx, input)
	}
}

func BenchmarkWebScrapeTool(b *testing.B) {
	mockServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Write([]byte("<html><body>Benchmark content</body></html>"))
	}))
	defer mockServer.Close()

	extractor := NewWebExtractor(Config{})
	tool := &WebScrapeTool{extractor: extractor}
	ctx := context.Background()
	input := map[string]any{"url": mockServer.URL}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = tool.Execute(ctx, input)
	}
}
