// Package web provides web tools that can be registered independently.
package web

import (
	"context"
	"encoding/json"
)

// Tool interface matches the main tool interface to avoid circular imports.
type Tool interface {
	Name() string
	Description() string
	Execute(ctx context.Context, input any) (any, error)
	InputSchema() json.RawMessage
}

// GetWebTools returns all web tools configured with the given settings.
func GetWebTools(cfg Config) []Tool {
	extractor := NewWebExtractor(cfg)

	tools := []Tool{
		&WebSearchTool{extractor: extractor},
		&WebScrapeTool{extractor: extractor},
		&WebExtractTool{extractor: extractor},
	}

	// Note: SearXNG functionality is integrated into WebSearchTool
	// No need for a separate SearXNG tool

	return tools
}
