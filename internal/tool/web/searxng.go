// Package web provides SearXNG integration for web search.
package web

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// SearXNGClient provides access to SearXNG search API.
type SearXNGClient struct {
	baseURL    string
	httpClient *http.Client
	apiKey     string // Optional API key if SearXNG instance requires it
}

// NewSearXNGClient creates a new SearXNG client.
func NewSearXNGClient(baseURL string, apiKey string) *SearXNGClient {
	return &SearXNGClient{
		baseURL: strings.TrimRight(baseURL, "/"),
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		apiKey: apiKey,
	}
}

// SearchParams holds SearXNG search parameters.
type SearchParams struct {
	Query      string   `json:"q"`
	Categories []string `json:"categories,omitempty"`
	Engines    []string `json:"engines,omitempty"`
	Language   string   `json:"language,omitempty"`
	Pageno     int      `json:"pageno,omitempty"`
	TimeRange  string   `json:"time_range,omitempty"`
	SafeSearch int      `json:"safesearch,omitempty"`
	Format     string   `json:"format"`
}

// SearchResult represents a SearXNG search result.
type SearchResult struct {
	Query               string             `json:"query"`
	NumberOfResults     int                `json:"number_of_results"`
	Results             []SearchResultItem `json:"results"`
	Suggestions         []string           `json:"suggestions"`
	Answers             []string           `json:"answers"`
	Infoboxes           []map[string]any   `json:"infoboxes"`
	UnresponsiveEngines []string           `json:"unresponsive_engines"`
}

// SearchResultItem represents a single search result.
type SearchResultItem struct {
	URL           string     `json:"url"`
	Title         string     `json:"title"`
	Content       string     `json:"content"`
	Engine        string     `json:"engine"`
	Score         float64    `json:"score"`
	Category      string     `json:"category"`
	PrettyURL     string     `json:"pretty_url"`
	PublishedDate *time.Time `json:"publishedDate,omitempty"`
	Thumbnail     string     `json:"thumbnail,omitempty"`
}

// Search performs a search using SearXNG.
func (c *SearXNGClient) Search(ctx context.Context, params SearchParams) (*SearchResult, error) {
	// Set default format
	if params.Format == "" {
		params.Format = "json"
	}

	// Build URL
	searchURL, err := url.Parse(c.baseURL + "/search")
	if err != nil {
		return nil, fmt.Errorf("parse URL: %w", err)
	}

	// Build query parameters
	q := url.Values{}
	q.Set("q", params.Query)
	q.Set("format", params.Format)

	if len(params.Categories) > 0 {
		q.Set("categories", strings.Join(params.Categories, ","))
	}
	if len(params.Engines) > 0 {
		q.Set("engines", strings.Join(params.Engines, ","))
	}
	if params.Language != "" {
		q.Set("language", params.Language)
	}
	if params.Pageno > 0 {
		q.Set("pageno", fmt.Sprintf("%d", params.Pageno))
	}
	if params.TimeRange != "" {
		q.Set("time_range", params.TimeRange)
	}
	q.Set("safesearch", fmt.Sprintf("%d", params.SafeSearch))

	searchURL.RawQuery = q.Encode()

	// Create request
	req, err := http.NewRequestWithContext(ctx, "GET", searchURL.String(), nil)
	if err != nil {
		return nil, fmt.Errorf("create request: %w", err)
	}

	// Add headers
	req.Header.Set("Accept", "application/json")
	if c.apiKey != "" {
		req.Header.Set("Authorization", "Bearer "+c.apiKey)
	}

	// Execute request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("execute request: %w", err)
	}
	defer resp.Body.Close()

	// Check status
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("search failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Parse response
	var result SearchResult
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("decode response: %w", err)
	}

	return &result, nil
}

// GetEngines returns available search engines.
func (c *SearXNGClient) GetEngines(ctx context.Context) (map[string]EngineInfo, error) {
	enginesURL := c.baseURL + "/engines"

	req, err := http.NewRequestWithContext(ctx, "GET", enginesURL, nil)
	if err != nil {
		return nil, fmt.Errorf("create request: %w", err)
	}

	req.Header.Set("Accept", "application/json")
	if c.apiKey != "" {
		req.Header.Set("Authorization", "Bearer "+c.apiKey)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("execute request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("get engines failed with status %d", resp.StatusCode)
	}

	var engines map[string]EngineInfo
	if err := json.NewDecoder(resp.Body).Decode(&engines); err != nil {
		return nil, fmt.Errorf("decode response: %w", err)
	}

	return engines, nil
}

// EngineInfo represents information about a search engine.
type EngineInfo struct {
	Name       string   `json:"name"`
	Categories []string `json:"categories"`
	Enabled    bool     `json:"enabled"`
	Shortcut   string   `json:"shortcut"`
	TimeRange  bool     `json:"time_range_support"`
	SafeSearch bool     `json:"safesearch"`
}

// SearXNGTool provides a tool interface for SearXNG searches.
type SearXNGTool struct {
	client *SearXNGClient
}

// NewSearXNGTool creates a new SearXNG tool.
func NewSearXNGTool(baseURL string, apiKey string) *SearXNGTool {
	return &SearXNGTool{
		client: NewSearXNGClient(baseURL, apiKey),
	}
}

// Name returns the tool name.
func (t *SearXNGTool) Name() string {
	return "searxng_search"
}

// Description returns the tool description.
func (t *SearXNGTool) Description() string {
	return "Search the web using SearXNG meta-search engine with privacy protection"
}

// Execute performs a SearXNG search.
func (t *SearXNGTool) Execute(ctx context.Context, input any) (any, error) {
	// Parse input parameters
	params := SearchParams{
		SafeSearch: 1,
		Format:     "json",
	}

	if inputMap, ok := input.(map[string]any); ok {
		if query, ok := inputMap["query"].(string); ok {
			params.Query = query
		}
		if categories, ok := inputMap["categories"].([]any); ok {
			params.Categories = make([]string, len(categories))
			for i, cat := range categories {
				if catStr, ok := cat.(string); ok {
					params.Categories[i] = catStr
				}
			}
		}
		if engines, ok := inputMap["engines"].([]any); ok {
			params.Engines = make([]string, len(engines))
			for i, eng := range engines {
				if engStr, ok := eng.(string); ok {
					params.Engines[i] = engStr
				}
			}
		}
		if lang, ok := inputMap["language"].(string); ok {
			params.Language = lang
		}
		if pageno, ok := inputMap["page"].(float64); ok {
			params.Pageno = int(pageno)
		}
		if timeRange, ok := inputMap["time_range"].(string); ok {
			params.TimeRange = timeRange
		}
		if safeSearch, ok := inputMap["safe_search"].(float64); ok {
			params.SafeSearch = int(safeSearch)
		}
	}

	if params.Query == "" {
		return nil, fmt.Errorf("query is required")
	}

	// Perform search
	result, err := t.client.Search(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("search failed: %w", err)
	}

	// Format results for output
	formattedResults := make([]map[string]any, 0, len(result.Results))
	for _, r := range result.Results {
		item := map[string]any{
			"title":      r.Title,
			"url":        r.URL,
			"snippet":    r.Content,
			"engine":     r.Engine,
			"score":      r.Score,
			"pretty_url": r.PrettyURL,
		}
		if r.PublishedDate != nil {
			item["published"] = r.PublishedDate.Format(time.RFC3339)
		}
		if r.Thumbnail != "" {
			item["thumbnail"] = r.Thumbnail
		}
		formattedResults = append(formattedResults, item)
	}

	output := map[string]any{
		"query":                params.Query,
		"results":              formattedResults,
		"result_count":         len(formattedResults),
		"total_results":        result.NumberOfResults,
		"suggestions":          result.Suggestions,
		"unresponsive_engines": result.UnresponsiveEngines,
	}

	// Add answers if available
	if len(result.Answers) > 0 {
		output["answers"] = result.Answers
	}

	// Add infoboxes if available
	if len(result.Infoboxes) > 0 {
		output["infoboxes"] = result.Infoboxes
	}

	return output, nil
}

// InputSchema returns the JSON schema for the input.
func (t *SearXNGTool) InputSchema() json.RawMessage {
	return json.RawMessage(`{
		"type": "object",
		"properties": {
			"query": {
				"type": "string",
				"description": "Search query"
			},
			"categories": {
				"type": "array",
				"items": {
					"type": "string"
				},
				"description": "Search categories (general, images, news, videos, music, files, social_media, etc.)"
			},
			"engines": {
				"type": "array",
				"items": {
					"type": "string"
				},
				"description": "Specific search engines to use (google, bing, duckduckgo, etc.)"
			},
			"language": {
				"type": "string",
				"description": "Language code (e.g., 'en-US', 'zh-CN')"
			},
			"page": {
				"type": "integer",
				"description": "Page number for pagination",
				"default": 1
			},
			"time_range": {
				"type": "string",
				"enum": ["", "day", "week", "month", "year"],
				"description": "Time range filter"
			},
			"safe_search": {
				"type": "integer",
				"description": "Safe search level (0=off, 1=moderate, 2=strict)",
				"default": 1,
				"minimum": 0,
				"maximum": 2
			}
		},
		"required": ["query"]
	}`)
}
