// Package web provides unified web data extraction tools.
package web

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// WebExtractor provides unified web data extraction capabilities.
type WebExtractor struct {
	httpClient *http.Client
	searxngURL string
}

// Config holds web extractor configuration.
type Config struct {
	SearXNGURL   string
	HTTPTimeout  time.Duration
	MaxRedirects int
	UserAgent    string
}

// NewWebExtractor creates a new web extractor.
func NewWebExtractor(cfg Config) *WebExtractor {
	if cfg.HTTPTimeout == 0 {
		cfg.HTTPTimeout = 30 * time.Second
	}
	if cfg.UserAgent == "" {
		cfg.UserAgent = "Assistant-Go/1.0"
	}
	if cfg.MaxRedirects == 0 {
		cfg.MaxRedirects = 5
	}

	client := &http.Client{
		Timeout: cfg.HTTPTimeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if len(via) >= cfg.MaxRedirects {
				return fmt.Errorf("too many redirects")
			}
			return nil
		},
	}

	// Set default User-Agent
	client.Transport = &transportWithUserAgent{
		userAgent: cfg.UserAgent,
		base:      http.DefaultTransport,
	}

	return &WebExtractor{
		httpClient: client,
		searxngURL: cfg.SearXNGURL,
	}
}

// transportWithUserAgent adds User-Agent to all requests.
type transportWithUserAgent struct {
	userAgent string
	base      http.RoundTripper
}

func (t *transportWithUserAgent) RoundTrip(req *http.Request) (*http.Response, error) {
	if req.Header.Get("User-Agent") == "" {
		req.Header.Set("User-Agent", t.userAgent)
	}
	return t.base.RoundTrip(req)
}

// WebSearchTool performs web searches using SearXNG or other search engines.
type WebSearchTool struct {
	extractor *WebExtractor
}

// Name returns the tool name.
func (t *WebSearchTool) Name() string {
	return "web_search"
}

// Description returns the tool description.
func (t *WebSearchTool) Description() string {
	return "Search the web for information using search engines"
}

// searchParams holds parameters for web search
type searchParams struct {
	Query      string
	MaxResults int
	Categories []string
	Language   string
	TimeRange  string
	SafeSearch bool
}

// Execute performs a web search.
func (t *WebSearchTool) Execute(ctx context.Context, input any) (any, error) {
	// Parse input
	params := searchParams{
		MaxResults: 10,
		SafeSearch: true,
	}

	if inputMap, ok := input.(map[string]any); ok {
		if query, ok := inputMap["query"].(string); ok {
			params.Query = query
		}
		if maxResults, ok := inputMap["max_results"].(float64); ok {
			params.MaxResults = int(maxResults)
		}
		if categories, ok := inputMap["categories"].([]any); ok {
			params.Categories = make([]string, len(categories))
			for i, cat := range categories {
				if catStr, ok := cat.(string); ok {
					params.Categories[i] = catStr
				}
			}
		}
		if lang, ok := inputMap["language"].(string); ok {
			params.Language = lang
		}
		if timeRange, ok := inputMap["time_range"].(string); ok {
			params.TimeRange = timeRange
		}
		if safe, ok := inputMap["safe_search"].(bool); ok {
			params.SafeSearch = safe
		}
	}

	if params.Query == "" {
		return nil, fmt.Errorf("query is required")
	}

	// Use SearXNG if configured
	if t.extractor.searxngURL != "" {
		return t.searchWithSearXNG(ctx, params)
	}

	// Fallback to a simple DuckDuckGo HTML search
	return t.searchWithDuckDuckGo(ctx, params)
}

// InputSchema returns the JSON schema for the input.
func (t *WebSearchTool) InputSchema() json.RawMessage {
	return json.RawMessage(`{
		"type": "object",
		"properties": {
			"query": {
				"type": "string",
				"description": "Search query"
			},
			"max_results": {
				"type": "integer",
				"description": "Maximum number of results",
				"default": 10
			},
			"categories": {
				"type": "array",
				"items": {
					"type": "string"
				},
				"description": "Search categories (general, images, news, videos, etc.)"
			},
			"language": {
				"type": "string",
				"description": "Language code (e.g., 'en', 'zh')"
			},
			"time_range": {
				"type": "string",
				"enum": ["day", "week", "month", "year"],
				"description": "Time range filter"
			},
			"safe_search": {
				"type": "boolean",
				"description": "Enable safe search",
				"default": true
			}
		},
		"required": ["query"]
	}`)
}

// searchWithSearXNG performs search using SearXNG instance.
func (t *WebSearchTool) searchWithSearXNG(ctx context.Context, params searchParams) (any, error) {
	// Build search URL
	searchURL, err := url.Parse(t.extractor.searxngURL + "/search")
	if err != nil {
		return nil, fmt.Errorf("parse searxng URL: %w", err)
	}

	query := url.Values{}
	query.Set("q", params.Query)
	query.Set("format", "json")

	if len(params.Categories) > 0 {
		query.Set("categories", strings.Join(params.Categories, ","))
	}
	if params.Language != "" {
		query.Set("language", params.Language)
	}
	if params.TimeRange != "" {
		query.Set("time_range", params.TimeRange)
	}
	if params.SafeSearch {
		query.Set("safesearch", "1")
	}

	searchURL.RawQuery = query.Encode()

	// Create request
	req, err := http.NewRequestWithContext(ctx, "GET", searchURL.String(), nil)
	if err != nil {
		return nil, fmt.Errorf("create request: %w", err)
	}

	// Execute request
	resp, err := t.extractor.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("execute request to %s: %w", searchURL.String(), err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("SearXNG returned status %d: %s", resp.StatusCode, string(body))
	}

	// Parse response
	var result struct {
		Query   string `json:"query"`
		Results []struct {
			Title   string `json:"title"`
			URL     string `json:"url"`
			Content string `json:"content"`
			Engine  string `json:"engine"`
		} `json:"results"`
		NumberOfResults int `json:"number_of_results"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("decode response: %w", err)
	}

	// Format results
	results := make([]map[string]any, 0, len(result.Results))
	for i, r := range result.Results {
		if i >= params.MaxResults {
			break
		}
		results = append(results, map[string]any{
			"title":   r.Title,
			"url":     r.URL,
			"snippet": r.Content,
			"source":  r.Engine,
		})
	}

	return map[string]any{
		"query":   params.Query,
		"results": results,
		"count":   len(results),
		"total":   result.NumberOfResults,
	}, nil
}

// searchWithDuckDuckGo performs a simple DuckDuckGo search.
func (t *WebSearchTool) searchWithDuckDuckGo(ctx context.Context, params searchParams) (any, error) {
	// Use DuckDuckGo Instant Answer API
	searchURL := fmt.Sprintf("https://api.duckduckgo.com/?q=%s&format=json&no_html=1&skip_disambig=1", url.QueryEscape(params.Query))

	req, err := http.NewRequestWithContext(ctx, "GET", searchURL, nil)
	if err != nil {
		return nil, fmt.Errorf("create request: %w", err)
	}

	resp, err := t.extractor.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("execute request: %w", err)
	}
	defer resp.Body.Close()

	// Parse DuckDuckGo API response
	var ddgResponse struct {
		Abstract       string `json:"Abstract"`
		AbstractText   string `json:"AbstractText"`
		AbstractSource string `json:"AbstractSource"`
		AbstractURL    string `json:"AbstractURL"`
		Answer         string `json:"Answer"`
		AnswerType     string `json:"AnswerType"`
		RelatedTopics  []struct {
			Text     string `json:"Text"`
			FirstURL string `json:"FirstURL"`
		} `json:"RelatedTopics"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&ddgResponse); err != nil {
		return nil, fmt.Errorf("decode response: %w", err)
	}

	// Format results
	results := make([]map[string]any, 0)

	// Add abstract if available
	if ddgResponse.AbstractText != "" {
		results = append(results, map[string]any{
			"title":   "Summary",
			"url":     ddgResponse.AbstractURL,
			"snippet": ddgResponse.AbstractText,
			"source":  ddgResponse.AbstractSource,
		})
	}

	// Add answer if available
	if ddgResponse.Answer != "" {
		results = append(results, map[string]any{
			"title":   "Direct Answer",
			"url":     "",
			"snippet": ddgResponse.Answer,
			"source":  "DuckDuckGo",
		})
	}

	// Add related topics
	for i, topic := range ddgResponse.RelatedTopics {
		if i >= params.MaxResults {
			break
		}
		if topic.Text != "" {
			results = append(results, map[string]any{
				"title":   fmt.Sprintf("Related %d", i+1),
				"url":     topic.FirstURL,
				"snippet": topic.Text,
				"source":  "DuckDuckGo",
			})
		}
	}

	// If no results, return a helpful message
	if len(results) == 0 {
		results = append(results, map[string]any{
			"title":   "No results found",
			"url":     "",
			"snippet": fmt.Sprintf("No direct results found for '%s'. Try different search terms or check web directly.", params.Query),
			"source":  "DuckDuckGo",
		})
	}

	return map[string]any{
		"query":   params.Query,
		"results": results,
		"count":   len(results),
		"total":   len(results),
		"source":  "DuckDuckGo Instant Answer API",
	}, nil
}

// WebScrapeTool extracts structured data from web pages.
type WebScrapeTool struct {
	extractor *WebExtractor
}

// Name returns the tool name.
func (t *WebScrapeTool) Name() string {
	return "web_scrape"
}

// Description returns the tool description.
func (t *WebScrapeTool) Description() string {
	return "Extract structured data from web pages"
}

// Execute scrapes a web page.
func (t *WebScrapeTool) Execute(ctx context.Context, input any) (any, error) {
	params := struct {
		URL        string            `json:"url"`
		Selectors  map[string]string `json:"selectors,omitempty"`
		ExtractAll bool              `json:"extract_all,omitempty"`
		Format     string            `json:"format,omitempty"`
	}{
		ExtractAll: true,
		Format:     "text",
	}

	// Parse input
	if inputMap, ok := input.(map[string]any); ok {
		if url, ok := inputMap["url"].(string); ok {
			params.URL = url
		}
		if selectors, ok := inputMap["selectors"].(map[string]any); ok {
			params.Selectors = make(map[string]string)
			for k, v := range selectors {
				if vs, ok := v.(string); ok {
					params.Selectors[k] = vs
				}
			}
		}
		if extractAll, ok := inputMap["extract_all"].(bool); ok {
			params.ExtractAll = extractAll
		}
		if format, ok := inputMap["format"].(string); ok {
			params.Format = format
		}
	}

	if params.URL == "" {
		return nil, fmt.Errorf("URL is required")
	}

	// Fetch the page
	req, err := http.NewRequestWithContext(ctx, "GET", params.URL, nil)
	if err != nil {
		return nil, fmt.Errorf("create request: %w", err)
	}

	resp, err := t.extractor.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("fetch page: %w", err)
	}
	defer resp.Body.Close()

	// For now, return basic page info
	// In a real implementation, you would use colly or goquery to parse HTML
	return map[string]any{
		"url":         params.URL,
		"status":      resp.StatusCode,
		"contentType": resp.Header.Get("Content-Type"),
		"message":     "Basic scraping completed. For full HTML parsing, colly integration needed.",
	}, nil
}

// InputSchema returns the JSON schema for the input.
func (t *WebScrapeTool) InputSchema() json.RawMessage {
	return json.RawMessage(`{
		"type": "object",
		"properties": {
			"url": {
				"type": "string",
				"description": "URL to scrape"
			},
			"selectors": {
				"type": "object",
				"description": "CSS selectors to extract specific elements",
				"additionalProperties": {"type": "string"}
			},
			"extract_all": {
				"type": "boolean",
				"description": "Extract all text content",
				"default": true
			},
			"format": {
				"type": "string",
				"enum": ["text", "html", "json"],
				"description": "Output format",
				"default": "text"
			}
		},
		"required": ["url"]
	}`)
}

// WebExtractTool combines search and scraping for comprehensive data extraction.
type WebExtractTool struct {
	extractor *WebExtractor
}

// Name returns the tool name.
func (t *WebExtractTool) Name() string {
	return "web_extract"
}

// Description returns the tool description.
func (t *WebExtractTool) Description() string {
	return "Search and extract comprehensive information from the web"
}

// Execute performs comprehensive web data extraction.
func (t *WebExtractTool) Execute(ctx context.Context, input any) (any, error) {
	params := struct {
		Query       string `json:"query"`
		MaxPages    int    `json:"max_pages,omitempty"`
		ExtractType string `json:"extract_type,omitempty"`
		Depth       int    `json:"depth,omitempty"`
	}{
		MaxPages:    3,
		ExtractType: "summary",
		Depth:       1,
	}

	// Parse input
	if inputMap, ok := input.(map[string]any); ok {
		if query, ok := inputMap["query"].(string); ok {
			params.Query = query
		}
		if maxPages, ok := inputMap["max_pages"].(float64); ok {
			params.MaxPages = int(maxPages)
		}
		if extractType, ok := inputMap["extract_type"].(string); ok {
			params.ExtractType = extractType
		}
		if depth, ok := inputMap["depth"].(float64); ok {
			params.Depth = int(depth)
		}
	}

	if params.Query == "" {
		return nil, fmt.Errorf("query is required")
	}

	// Step 1: Search for relevant pages
	searchTool := &WebSearchTool{extractor: t.extractor}
	searchResult, err := searchTool.Execute(ctx, map[string]any{
		"query":       params.Query,
		"max_results": params.MaxPages,
	})
	if err != nil {
		return nil, fmt.Errorf("search failed: %w", err)
	}

	// Extract search results
	searchData, ok := searchResult.(map[string]any)
	if !ok {
		return nil, fmt.Errorf("unexpected search result format")
	}

	results, ok := searchData["results"].([]map[string]any)
	if !ok || len(results) == 0 {
		return map[string]any{
			"query":   params.Query,
			"message": "No search results found",
		}, nil
	}

	// Step 2: Extract data from top results
	extractedData := make([]map[string]any, 0, len(results))
	scrapeTool := &WebScrapeTool{extractor: t.extractor}

	for i, result := range results {
		if i >= params.MaxPages {
			break
		}

		url, ok := result["url"].(string)
		if !ok {
			continue
		}

		// Scrape each page
		scrapeResult, err := scrapeTool.Execute(ctx, map[string]any{
			"url":         url,
			"extract_all": true,
		})
		if err != nil {
			// Log error but continue with other pages
			extractedData = append(extractedData, map[string]any{
				"url":   url,
				"error": err.Error(),
			})
			continue
		}

		// Combine search and scrape data
		if scrapeData, ok := scrapeResult.(map[string]any); ok {
			combined := map[string]any{
				"title":   result["title"],
				"url":     url,
				"snippet": result["snippet"],
				"scraped": scrapeData,
			}
			extractedData = append(extractedData, combined)
		}
	}

	return map[string]any{
		"query":     params.Query,
		"extracted": extractedData,
		"count":     len(extractedData),
		"type":      params.ExtractType,
	}, nil
}

// InputSchema returns the JSON schema for the input.
func (t *WebExtractTool) InputSchema() json.RawMessage {
	return json.RawMessage(`{
		"type": "object",
		"properties": {
			"query": {
				"type": "string",
				"description": "Search query for finding relevant web pages"
			},
			"max_pages": {
				"type": "integer",
				"description": "Maximum number of pages to extract from",
				"default": 3
			},
			"extract_type": {
				"type": "string",
				"enum": ["summary", "full", "structured"],
				"description": "Type of extraction",
				"default": "summary"
			},
			"depth": {
				"type": "integer",
				"description": "Crawl depth for following links",
				"default": 1
			}
		},
		"required": ["query"]
	}`)
}
