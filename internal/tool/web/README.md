# Web Data Extraction Framework

## Overview

This package provides a unified web data extraction framework that integrates multiple approaches:

1. **Direct HTTP Fetching** - Simple HTTP requests for basic content retrieval
2. **SearXNG Integration** - Privacy-focused meta-search engine for web search
3. **Colly-based Scraping** - Advanced web scraping with CSS selectors and crawling
4. **Unified Web Extraction** - Combines search and scraping for comprehensive data extraction

## Architecture

```
web/
├── web.go           # Core web extractor and unified tools
├── searxng.go       # SearXNG client and search tool
├── colly.go # Colly-based advanced scraping
└── README.md        # This file
```

## Tools Provided

### 1. web_search

Basic web search that uses SearXNG if configured, or falls back to simple search.

```go
input := map[string]any{
    "query":       "golang tutorials",
    "max_results": 10,
    "categories":  []string{"general"},
    "language":    "en",
}
```

### 2. web_scrape

Extract structured data from web pages using CSS selectors.

```go
input := map[string]any{
    "url": "https://example.com",
    "selectors": map[string]string{
        "title":   "h1",
        "content": "article",
    },
    "extract_all": true,
}
```

### 3. web_extract

Combines search and scraping - searches for pages then extracts data.

```go
input := map[string]any{
    "query":        "machine learning papers",
    "max_pages":    3,
    "extract_type": "summary",
}
```

### 4. colly_scrape

Advanced scraping with link following, pattern matching, and crawling.

```go
input := map[string]any{
    "url":           "https://example.com",
    "follow_links":  true,
    "link_pattern":  "/blog/",
    "max_pages":     10,
    "extract_links": true,
}
```

### 5. searxng_search

Direct SearXNG search with full control over search parameters.

```go
input := map[string]any{
    "query":      "AI news",
    "engines":    []string{"google", "bing"},
    "time_range": "month",
    "safe_search": 1,
}
```

## Configuration

### Environment Variables

```bash
# SearXNG instance URL (optional)
SEARXNG_URL=https://searx.example.com

# HTTP timeout in seconds
WEB_TIMEOUT=30

# User agent string
WEB_USER_AGENT="Assistant-Go/1.0"
```

### Code Configuration

```go
// Web extractor configuration
webCfg := web.Config{
    SearXNGURL:   "https://searx.example.com",
    HTTPTimeout:  30 * time.Second,
    MaxRedirects: 5,
    UserAgent:    "MyBot/1.0",
}

// Colly scraper configuration
scraperCfg := web.ScraperConfig{
    MaxDepth:       2,
    Async:          true,
    Parallelism:    4,
    Delay:          1 * time.Second,
    RandomDelay:    500 * time.Millisecond,
    CacheDir:       "/tmp/colly-cache",
    AllowedDomains: []string{"example.com"},
}
```

## Integration with Assistant

The web tools are automatically registered when the coordinator is initialized:

```go
// In coordinator initialization
if err := tool.RegisterBuiltinTools(toolMgr, &cfg, log); err != nil {
    return nil, fmt.Errorf("register built-in tools: %w", err)
}
```

This registers all web tools, making them available to the AI assistant for:

- Searching the web for current information
- Extracting data from specific websites
- Following links and crawling sites
- Combining search and extraction for research tasks

## Usage Examples

### Basic Web Search

```go
// AI can use: "Search for recent Go 1.21 features"
result := ai.CallTool("web_search", map[string]any{
    "query": "Go 1.21 new features",
    "time_range": "month",
})
```

### Extract Product Information

```go
// AI can use: "Extract product details from this page"
result := ai.CallTool("colly_scrape", map[string]any{
    "url": "https://shop.example.com/product/123",
    "selectors": map[string]string{
        "name":  "h1.product-title",
        "price": ".price-tag",
        "description": ".product-description",
    },
})
```

### Research Task

```go
// AI can use: "Research recent developments in quantum computing"
result := ai.CallTool("web_extract", map[string]any{
    "query": "quantum computing breakthroughs 2024",
    "max_pages": 5,
    "extract_type": "full",
})
```

## Privacy and Ethics

1. **Respect robots.txt** - The Colly scraper respects robots.txt by default
2. **Rate limiting** - Built-in delays prevent overwhelming servers
3. **User agent** - Always identifies itself properly
4. **SearXNG** - When configured, provides privacy-preserving search

## Future Enhancements

1. **JavaScript rendering** - Integration with headless browsers for SPA scraping
2. **Content extraction** - Smart content extraction using readability algorithms
3. **Data persistence** - Cache and store scraped data for offline analysis
4. **Pattern learning** - Learn extraction patterns from examples
5. **API integration** - Direct API calls when available instead of scraping

## Dependencies

- `github.com/gocolly/colly/v2` - Web scraping framework
- Standard library `net/http` - HTTP client
- SearXNG instance (optional) - For privacy-focused search

## Best Practices

1. Always check if an API is available before scraping
2. Respect rate limits and add delays between requests
3. Use specific selectors rather than broad extraction
4. Cache results when appropriate to avoid repeated requests
5. Handle errors gracefully and provide fallback options
