// Package web provides web interaction tools
package web

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"golang.org/x/net/html"
)

// ScraperTool implements web page content extraction
type ScraperTool struct {
	httpClient     *http.Client
	userAgent      string
	maxContentSize int
}

// ScrapeRequest represents scraping parameters
type ScrapeRequest struct {
	URL           string `json:"url"`
	ExtractLinks  bool   `json:"extract_links,omitempty"`
	ExtractImages bool   `json:"extract_images,omitempty"`
}

// ScrapeResponse represents scraping results
type ScrapeResponse struct {
	URL     string   `json:"url"`
	Title   string   `json:"title"`
	Content string   `json:"content"`
	Links   []string `json:"links,omitempty"`
	Images  []string `json:"images,omitempty"`
}

// NewScraperTool initializes with 30s timeout and 1MB content limit
func NewScraperTool() *ScraperTool {
	return &ScraperTool{
		httpClient:     defaultHTTPClient,
		userAgent:      "Mozilla/5.0 (compatible; AssistantGo/1.0)",
		maxContentSize: 1024 * 1024, // 1MB
	}
}

// Name returns the tool name
func (t *ScraperTool) Name() string {
	return "web_scraper"
}

// Description returns the tool description
func (t *ScraperTool) Description() string {
	return "Extracts text content from web pages"
}

// InputSchema returns the JSON Schema for input parameters
func (t *ScraperTool) InputSchema() json.RawMessage {
	schema := `{
		"type": "object",
		"properties": {
			"url": {
				"type": "string",
				"description": "URL to scrape"
			},
			"extract_links": {
				"type": "boolean",
				"description": "Extract all links from the page",
				"default": false
			},
			"extract_images": {
				"type": "boolean",
				"description": "Extract all image URLs from the page",
				"default": false
			}
		},
		"required": ["url"]
	}`
	return json.RawMessage(schema)
}

// ExecuteJSON executes scraping with JSON input/output
func (t *ScraperTool) ExecuteJSON(ctx context.Context, input json.RawMessage) (json.RawMessage, error) {
	var req ScrapeRequest
	if err := json.Unmarshal(input, &req); err != nil {
		return nil, fmt.Errorf("failed to parse input: %w", err)
	}

	if req.URL == "" {
		return nil, fmt.Errorf("url cannot be empty")
	}

	response, err := t.scrape(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("scraping failed: %w", err)
	}

	return json.Marshal(response)
}

// Execute performs scraping with generic input/output
func (t *ScraperTool) Execute(ctx context.Context, input any) (any, error) {
	// Handle string input directly as URL
	if url, ok := input.(string); ok {
		return t.scrape(ctx, ScrapeRequest{URL: url})
	}

	// Otherwise convert to JSON and use JSON method
	inputJSON, err := json.Marshal(input)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal input: %w", err)
	}

	outputJSON, err := t.ExecuteJSON(ctx, inputJSON)
	if err != nil {
		return nil, err
	}

	var result ScrapeResponse
	if err := json.Unmarshal(outputJSON, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal output: %w", err)
	}

	return result, nil
}

// scrape performs the actual scraping
func (t *ScraperTool) scrape(ctx context.Context, req ScrapeRequest) (*ScrapeResponse, error) {
	// Normalize URL
	url := strings.TrimSpace(req.URL)
	if !strings.HasPrefix(url, "http://") && !strings.HasPrefix(url, "https://") {
		url = "https://" + url
	}

	// Create request with context
	httpReq, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("User-Agent", t.userAgent)

	// Execute request
	resp, err := t.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch page: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("server returned status %d", resp.StatusCode)
	}

	// Read body with size limit
	limitedReader := io.LimitReader(resp.Body, int64(t.maxContentSize))
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// Parse HTML
	doc, err := html.Parse(strings.NewReader(string(body)))
	if err != nil {
		return nil, fmt.Errorf("failed to parse HTML: %w", err)
	}

	// Extract data
	response := &ScrapeResponse{
		URL: url,
	}

	t.extractContent(doc, response, req)

	return response, nil
}

// extractContent extracts text content from HTML
func (t *ScraperTool) extractContent(n *html.Node, response *ScrapeResponse, req ScrapeRequest) {
	// State tracking for content extraction
	var (
		content  strings.Builder
		title    string
		links    []string
		images   []string
		inScript bool
		inStyle  bool
	)

	// Recursive function to walk the HTML tree
	var extract func(*html.Node)
	extract = func(n *html.Node) {
		if n.Type == html.ElementNode {
			switch n.Data {
			case "script", "noscript":
				inScript = true
			case "style":
				inStyle = true
			case "title":
				// Extract title text
				if n.FirstChild != nil && n.FirstChild.Type == html.TextNode {
					title = strings.TrimSpace(n.FirstChild.Data)
				}
			case "a":
				// Extract links if requested
				if req.ExtractLinks {
					for _, attr := range n.Attr {
						if attr.Key == "href" && attr.Val != "" {
							links = append(links, attr.Val)
						}
					}
				}
			case "img":
				// Extract images if requested
				if req.ExtractImages {
					for _, attr := range n.Attr {
						if attr.Key == "src" && attr.Val != "" {
							images = append(images, attr.Val)
						}
					}
				}
			case "h1", "h2", "h3", "h4", "h5", "h6", "p", "li", "td", "th", "article", "section", "main", "div", "span":
				// These elements often contain meaningful text
			}
		}

		// Extract text content
		if n.Type == html.TextNode && !inScript && !inStyle {
			text := strings.TrimSpace(n.Data)
			if text != "" {
				content.WriteString(text)
				content.WriteString(" ")
			}
		}

		// Process children
		for c := n.FirstChild; c != nil; c = c.NextSibling {
			extract(c)
		}

		// Reset state after leaving script/style
		if n.Type == html.ElementNode {
			switch n.Data {
			case "script", "noscript":
				inScript = false
			case "style":
				inStyle = false
			}
		}
	}

	extract(n)

	// Set response fields
	response.Title = title
	response.Content = strings.TrimSpace(content.String())
	if req.ExtractLinks {
		response.Links = links
	}
	if req.ExtractImages {
		response.Images = images
	}
}
