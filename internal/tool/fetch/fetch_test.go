package fetch

import (
	"context"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewFetchTool(t *testing.T) {
	tool := NewFetchTool()

	assert.NotNil(t, tool)
	assert.NotNil(t, tool.client)
	assert.Equal(t, 30*time.Second, tool.client.Timeout)
}

func TestFetchTool_Name(t *testing.T) {
	tool := NewFetchTool()
	assert.Equal(t, "fetch_url", tool.Name())
}

func TestFetchTool_Description(t *testing.T) {
	tool := NewFetchTool()
	assert.Equal(t, "Fetch content from a URL using HTTP GET, POST, PUT, or DELETE", tool.Description())
}

func TestFetchTool_Execute(t *testing.T) {
	tests := []struct {
		name        string
		input       any
		setupServer func(w http.ResponseWriter, r *http.Request)
		wantErr     bool
		errContains string
		checkResult func(t *testing.T, result any)
	}{
		{
			name: "successful GET request",
			input: map[string]any{
				"url": "", // Will be set to test server URL
			},
			setupServer: func(w http.ResponseWriter, r *http.Request) {
				assert.Equal(t, "GET", r.Method)
				w.Header().Set("Content-Type", "text/plain")
				w.WriteHeader(http.StatusOK)
				w.Write([]byte("Hello, World!"))
			},
			wantErr: false,
			checkResult: func(t *testing.T, result any) {
				resp, ok := result.(map[string]any)
				require.True(t, ok)
				assert.Equal(t, 200, resp["status"])
				assert.Equal(t, "Hello, World!", resp["body"])
				headers, ok := resp["headers"].(map[string]string)
				require.True(t, ok)
				assert.Equal(t, "text/plain", headers["Content-Type"])
			},
		},
		{
			name: "successful POST request with body",
			input: map[string]any{
				"url":    "", // Will be set to test server URL
				"method": "POST",
				"body":   `{"name": "test"}`,
				"headers": map[string]any{
					"Content-Type": "application/json",
				},
			},
			setupServer: func(w http.ResponseWriter, r *http.Request) {
				assert.Equal(t, "POST", r.Method)
				assert.Equal(t, "application/json", r.Header.Get("Content-Type"))

				body, err := io.ReadAll(r.Body)
				require.NoError(t, err)
				assert.Equal(t, `{"name": "test"}`, string(body))

				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusCreated)
				w.Write([]byte(`{"id": 123, "name": "test"}`))
			},
			wantErr: false,
			checkResult: func(t *testing.T, result any) {
				resp, ok := result.(map[string]any)
				require.True(t, ok)
				assert.Equal(t, 201, resp["status"])
				assert.Equal(t, `{"id": 123, "name": "test"}`, resp["body"])
			},
		},
		{
			name: "PUT request",
			input: map[string]any{
				"url":    "", // Will be set to test server URL
				"method": "PUT",
				"body":   "updated content",
			},
			setupServer: func(w http.ResponseWriter, r *http.Request) {
				assert.Equal(t, "PUT", r.Method)
				w.WriteHeader(http.StatusNoContent)
			},
			wantErr: false,
			checkResult: func(t *testing.T, result any) {
				resp, ok := result.(map[string]any)
				require.True(t, ok)
				assert.Equal(t, 204, resp["status"])
			},
		},
		{
			name: "DELETE request",
			input: map[string]any{
				"url":    "", // Will be set to test server URL
				"method": "DELETE",
			},
			setupServer: func(w http.ResponseWriter, r *http.Request) {
				assert.Equal(t, "DELETE", r.Method)
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{"message": "deleted"}`))
			},
			wantErr: false,
			checkResult: func(t *testing.T, result any) {
				resp, ok := result.(map[string]any)
				require.True(t, ok)
				assert.Equal(t, 200, resp["status"])
				assert.Equal(t, `{"message": "deleted"}`, resp["body"])
			},
		},
		{
			name: "server error",
			input: map[string]any{
				"url": "", // Will be set to test server URL
			},
			setupServer: func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusInternalServerError)
				w.Write([]byte("Internal Server Error"))
			},
			wantErr: false, // FetchTool returns error in response, not as error
			checkResult: func(t *testing.T, result any) {
				resp, ok := result.(map[string]any)
				require.True(t, ok)
				assert.Equal(t, 500, resp["status"])
				assert.Equal(t, "Internal Server Error", resp["body"])
			},
		},
		{
			name:        "missing URL",
			input:       map[string]any{},
			wantErr:     true,
			errContains: "URL is required",
		},
		{
			name:        "invalid input type",
			input:       "not a map",
			wantErr:     true,
			errContains: "URL is required",
		},
		{
			name: "invalid URL",
			input: map[string]any{
				"url": "not-a-valid-url",
			},
			wantErr:     true,
			errContains: "unsupported protocol scheme",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var server *httptest.Server
			if tt.setupServer != nil {
				server = httptest.NewServer(http.HandlerFunc(tt.setupServer))
				defer server.Close()

				// Update URL in input if it's a map
				if inputMap, ok := tt.input.(map[string]any); ok {
					if _, hasURL := inputMap["url"]; hasURL {
						inputMap["url"] = server.URL
					}
				}
			}

			tool := NewFetchTool()
			ctx := context.Background()

			result, err := tool.Execute(ctx, tt.input)

			if tt.wantErr {
				require.Error(t, err)
				if tt.errContains != "" {
					assert.Contains(t, err.Error(), tt.errContains)
				}
			} else {
				require.NoError(t, err)
				if tt.checkResult != nil {
					tt.checkResult(t, result)
				}
			}
		})
	}
}

func TestFetchTool_ExecuteWithTimeout(t *testing.T) {
	// Create a server that delays response
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(2 * time.Second) // Delay longer than we'll wait
		w.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	tool := NewFetchTool()
	tool.client.Timeout = 100 * time.Millisecond // Short timeout

	ctx := context.Background()
	input := map[string]any{
		"url": server.URL,
	}

	_, err := tool.Execute(ctx, input)
	require.Error(t, err)
	assert.Contains(t, err.Error(), "Client.Timeout exceeded")
}

func TestFetchTool_ExecuteWithContext(t *testing.T) {
	// Create a server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(100 * time.Millisecond)
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()

	tool := NewFetchTool()

	// Create a context that will be canceled
	ctx, cancel := context.WithCancel(context.Background())

	input := map[string]any{
		"url": server.URL,
	}

	// Cancel the context immediately
	cancel()

	_, err := tool.Execute(ctx, input)
	require.Error(t, err)
	assert.Contains(t, err.Error(), "context canceled")
}

func TestFetchTool_ExecuteWithHeaders(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Echo back custom headers
		if auth := r.Header.Get("Authorization"); auth != "" {
			w.Header().Set("Echo-Auth", auth)
		}
		if custom := r.Header.Get("X-Custom-Header"); custom != "" {
			w.Header().Set("Echo-Custom", custom)
		}
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("Headers received"))
	}))
	defer server.Close()

	tool := NewFetchTool()
	ctx := context.Background()

	input := map[string]any{
		"url": server.URL,
		"headers": map[string]any{
			"Authorization":   "Bearer token123",
			"X-Custom-Header": "custom-value",
		},
	}

	result, err := tool.Execute(ctx, input)
	require.NoError(t, err)

	resp, ok := result.(map[string]any)
	require.True(t, ok)

	headers, ok := resp["headers"].(map[string]string)
	require.True(t, ok)

	assert.Equal(t, "Bearer token123", headers["Echo-Auth"])
	assert.Equal(t, "custom-value", headers["Echo-Custom"])
}

// Tests for CheckURLTool

func TestNewCheckURLTool(t *testing.T) {
	tool := NewCheckURLTool()

	assert.NotNil(t, tool)
	assert.NotNil(t, tool.client)
	assert.Equal(t, 10*time.Second, tool.client.Timeout)
}

func TestCheckURLTool_Name(t *testing.T) {
	tool := NewCheckURLTool()
	assert.Equal(t, "check_url", tool.Name())
}

func TestCheckURLTool_Description(t *testing.T) {
	tool := NewCheckURLTool()
	assert.Equal(t, "Check if a URL is accessible and get basic information about it", tool.Description())
}

func TestCheckURLTool_Execute(t *testing.T) {
	tests := []struct {
		name        string
		input       any
		setupServer func(w http.ResponseWriter, r *http.Request)
		wantErr     bool
		errContains string
		checkResult func(t *testing.T, result any)
	}{
		{
			name: "successful check",
			input: map[string]any{
				"url": "", // Will be set to test server URL
			},
			setupServer: func(w http.ResponseWriter, r *http.Request) {
				assert.Equal(t, "HEAD", r.Method)
				w.Header().Set("Content-Type", "text/html")
				w.Header().Set("Content-Length", "12345")
				w.WriteHeader(http.StatusOK)
			},
			wantErr: false,
			checkResult: func(t *testing.T, result any) {
				resp, ok := result.(map[string]any)
				require.True(t, ok)
				assert.Equal(t, true, resp["accessible"])
				assert.Equal(t, 200, resp["status"])
				assert.Equal(t, "200 OK", resp["statusText"])
				assert.Contains(t, resp["contentType"], "text/html")
				assert.Equal(t, "12345", resp["contentLength"])
				assert.NotNil(t, resp["responseTime"])
			},
		},
		{
			name: "not found",
			input: map[string]any{
				"url": "", // Will be set to test server URL
			},
			setupServer: func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusNotFound)
			},
			wantErr: false,
			checkResult: func(t *testing.T, result any) {
				resp, ok := result.(map[string]any)
				require.True(t, ok)
				assert.Equal(t, false, resp["accessible"])
				assert.Equal(t, 404, resp["status"])
			},
		},
		{
			name: "redirect with follow",
			input: map[string]any{
				"url":             "", // Will be set to test server URL
				"follow_redirect": true,
			},
			setupServer: func(w http.ResponseWriter, r *http.Request) {
				if r.URL.Path == "/" {
					w.Header().Set("Location", "/redirected")
					w.WriteHeader(http.StatusFound)
				} else {
					w.WriteHeader(http.StatusOK)
				}
			},
			wantErr: false,
			checkResult: func(t *testing.T, result any) {
				resp, ok := result.(map[string]any)
				require.True(t, ok)
				assert.Equal(t, true, resp["accessible"])
				assert.Contains(t, resp["finalURL"].(string), "/redirected")
			},
		},
		{
			name: "redirect without follow",
			input: map[string]any{
				"url":             "", // Will be set to test server URL
				"follow_redirect": false,
			},
			setupServer: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Location", "/redirected")
				w.WriteHeader(http.StatusFound)
			},
			wantErr: false,
			checkResult: func(t *testing.T, result any) {
				resp, ok := result.(map[string]any)
				require.True(t, ok)
				assert.Equal(t, 302, resp["status"])
			},
		},
		{
			name:        "missing URL",
			input:       map[string]any{},
			wantErr:     true,
			errContains: "URL is required",
		},
		{
			name:        "invalid input type",
			input:       "not a map",
			wantErr:     true,
			errContains: "URL is required",
		},
		{
			name: "unreachable URL",
			input: map[string]any{
				"url": "http://localhost:99999/unreachable",
			},
			wantErr: false, // CheckURLTool returns error in result, not as error
			checkResult: func(t *testing.T, result any) {
				resp, ok := result.(map[string]any)
				require.True(t, ok)
				assert.Equal(t, false, resp["accessible"])
				assert.NotEmpty(t, resp["error"])
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var server *httptest.Server
			if tt.setupServer != nil {
				server = httptest.NewServer(http.HandlerFunc(tt.setupServer))
				defer server.Close()

				// Update URL in input if it's a map
				if inputMap, ok := tt.input.(map[string]any); ok {
					if _, hasURL := inputMap["url"]; hasURL {
						inputMap["url"] = server.URL
					}
				}
			}

			tool := NewCheckURLTool()
			ctx := context.Background()

			result, err := tool.Execute(ctx, tt.input)

			if tt.wantErr {
				require.Error(t, err)
				if tt.errContains != "" {
					assert.Contains(t, err.Error(), tt.errContains)
				}
			} else {
				require.NoError(t, err)
				if tt.checkResult != nil {
					tt.checkResult(t, result)
				}
			}
		})
	}
}

func TestCheckURLTool_InputSchema(t *testing.T) {
	tool := NewCheckURLTool()
	schema := tool.InputSchema()

	assert.NotNil(t, schema)
	assert.Contains(t, string(schema), "url")
	assert.Contains(t, string(schema), "follow_redirect")
}

func TestFetchTool_InputSchema(t *testing.T) {
	tool := NewFetchTool()
	schema := tool.InputSchema()

	assert.NotNil(t, schema)
	assert.Contains(t, string(schema), "url")
	assert.Contains(t, string(schema), "method")
	assert.Contains(t, string(schema), "headers")
	assert.Contains(t, string(schema), "body")
}
