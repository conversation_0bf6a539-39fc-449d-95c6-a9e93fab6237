// Package fetch provides HTTP fetching tools.
package fetch

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

// FetchTool provides HTTP fetching capabilities.
type FetchTool struct {
	client *http.Client
}

// NewFetchTool creates a new fetch tool.
func NewFetchTool() *FetchTool {
	return &FetchTool{
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// Name returns the tool name.
func (t *FetchTool) Name() string {
	return "fetch_url"
}

// Description returns the tool description.
func (t *FetchTool) Description() string {
	return "Fetch content from a URL using HTTP GET, POST, PUT, or DELETE"
}

// Execute performs the HTTP request.
func (t *FetchTool) Execute(ctx context.Context, input any) (any, error) {
	params := struct {
		URL     string            `json:"url"`
		Method  string            `json:"method,omitempty"`
		Headers map[string]string `json:"headers,omitempty"`
		Body    string            `json:"body,omitempty"`
	}{
		Method: "GET",
	}

	// Parse input
	if inputMap, ok := input.(map[string]any); ok {
		if url, ok := inputMap["url"].(string); ok {
			params.URL = url
		}
		if method, ok := inputMap["method"].(string); ok {
			params.Method = strings.ToUpper(method)
		}
		if headers, ok := inputMap["headers"].(map[string]any); ok {
			params.Headers = make(map[string]string)
			for k, v := range headers {
				if vs, ok := v.(string); ok {
					params.Headers[k] = vs
				}
			}
		}
		if body, ok := inputMap["body"].(string); ok {
			params.Body = body
		}
	}

	if params.URL == "" {
		return nil, fmt.Errorf("URL is required")
	}

	// Create request
	var bodyReader io.Reader
	if params.Body != "" {
		bodyReader = strings.NewReader(params.Body)
	}

	req, err := http.NewRequestWithContext(ctx, params.Method, params.URL, bodyReader)
	if err != nil {
		return nil, fmt.Errorf("create request: %w", err)
	}

	// Set headers
	for k, v := range params.Headers {
		req.Header.Set(k, v)
	}

	// Default headers
	if req.Header.Get("User-Agent") == "" {
		req.Header.Set("User-Agent", "Assistant-Go/1.0")
	}

	// Execute request
	resp, err := t.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("execute request: %w", err)
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response: %w", err)
	}

	// Prepare result
	result := map[string]any{
		"status":     resp.StatusCode,
		"statusText": resp.Status,
		"headers":    flattenHeaders(resp.Header),
		"body":       string(body),
		"url":        params.URL,
		"method":     params.Method,
	}

	// Try to parse JSON response
	if strings.Contains(resp.Header.Get("Content-Type"), "application/json") {
		var jsonBody any
		if err := json.Unmarshal(body, &jsonBody); err == nil {
			result["json"] = jsonBody
		}
	}

	return result, nil
}

// InputSchema returns the JSON schema for the input.
func (t *FetchTool) InputSchema() json.RawMessage {
	return json.RawMessage(`{
		"type": "object",
		"properties": {
			"url": {
				"type": "string",
				"description": "The URL to fetch"
			},
			"method": {
				"type": "string",
				"enum": ["GET", "POST", "PUT", "DELETE", "HEAD", "OPTIONS", "PATCH"],
				"description": "HTTP method",
				"default": "GET"
			},
			"headers": {
				"type": "object",
				"description": "HTTP headers to send with the request",
				"additionalProperties": {
					"type": "string"
				}
			},
			"body": {
				"type": "string",
				"description": "Request body (for POST, PUT, PATCH)"
			}
		},
		"required": ["url"]
	}`)
}

// CheckURLTool checks if a URL is accessible.
type CheckURLTool struct {
	client *http.Client
}

// NewCheckURLTool creates a new URL checker tool.
func NewCheckURLTool() *CheckURLTool {
	return &CheckURLTool{
		client: &http.Client{
			Timeout: 10 * time.Second,
			CheckRedirect: func(req *http.Request, via []*http.Request) error {
				// Allow up to 5 redirects
				if len(via) >= 5 {
					return fmt.Errorf("too many redirects")
				}
				return nil
			},
		},
	}
}

// Name returns the tool name.
func (t *CheckURLTool) Name() string {
	return "check_url"
}

// Description returns the tool description.
func (t *CheckURLTool) Description() string {
	return "Check if a URL is accessible and get basic information about it"
}

// Execute checks the URL.
func (t *CheckURLTool) Execute(ctx context.Context, input any) (any, error) {
	params := struct {
		URL            string `json:"url"`
		FollowRedirect bool   `json:"follow_redirect,omitempty"`
	}{
		FollowRedirect: true,
	}

	// Parse input
	if inputMap, ok := input.(map[string]any); ok {
		if url, ok := inputMap["url"].(string); ok {
			params.URL = url
		}
		if follow, ok := inputMap["follow_redirect"].(bool); ok {
			params.FollowRedirect = follow
		}
	}

	if params.URL == "" {
		return nil, fmt.Errorf("URL is required")
	}

	// Create request
	req, err := http.NewRequestWithContext(ctx, "HEAD", params.URL, nil)
	if err != nil {
		return nil, fmt.Errorf("create request: %w", err)
	}

	req.Header.Set("User-Agent", "Assistant-Go/1.0")

	// Configure redirect policy
	client := t.client
	if !params.FollowRedirect {
		client = &http.Client{
			Timeout: t.client.Timeout,
			CheckRedirect: func(req *http.Request, via []*http.Request) error {
				return http.ErrUseLastResponse
			},
		}
	}

	// Execute request
	start := time.Now()
	resp, err := client.Do(req)
	elapsed := time.Since(start)

	if err != nil {
		return map[string]any{
			"accessible":   false,
			"error":        err.Error(),
			"url":          params.URL,
			"responseTime": elapsed.Milliseconds(),
		}, nil
	}
	defer resp.Body.Close()

	result := map[string]any{
		"accessible":   resp.StatusCode < 400,
		"status":       resp.StatusCode,
		"statusText":   resp.Status,
		"url":          params.URL,
		"finalURL":     resp.Request.URL.String(),
		"responseTime": elapsed.Milliseconds(),
		"headers":      flattenHeaders(resp.Header),
	}

	// Add redirect info
	if resp.Request.URL.String() != params.URL {
		result["redirected"] = true
		result["redirectCount"] = len(resp.Request.Response.Request.URL.String())
	}

	// Add content info
	if contentType := resp.Header.Get("Content-Type"); contentType != "" {
		result["contentType"] = contentType
	}
	if contentLength := resp.Header.Get("Content-Length"); contentLength != "" {
		result["contentLength"] = contentLength
	}

	return result, nil
}

// InputSchema returns the JSON schema for the input.
func (t *CheckURLTool) InputSchema() json.RawMessage {
	return json.RawMessage(`{
		"type": "object",
		"properties": {
			"url": {
				"type": "string",
				"description": "The URL to check"
			},
			"follow_redirect": {
				"type": "boolean",
				"description": "Whether to follow redirects",
				"default": true
			}
		},
		"required": ["url"]
	}`)
}

// flattenHeaders converts http.Header to a simple map.
func flattenHeaders(headers http.Header) map[string]string {
	result := make(map[string]string)
	for k, v := range headers {
		if len(v) > 0 {
			result[k] = v[0]
		}
	}
	return result
}
