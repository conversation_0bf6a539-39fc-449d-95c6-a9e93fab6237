package ui_test

import (
	"bytes"
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/koopa0/assistant-go/internal/tool"
	"github.com/koopa0/assistant-go/internal/tool/ui"
)

func TestListTool(t *testing.T) {
	// Create a non-interactive output
	var buf bytes.Buffer
	output := ui.ForceNonInteractive(&buf)

	tool := ui.NewListTool(output)

	// Verify tool properties
	assert.Equal(t, "ui_list", tool.Name())
	assert.Contains(t, tool.Description(), "Display a list of options")

	// Test execution with non-interactive terminal
	input := map[string]interface{}{
		"title":   "Select a color",
		"options": []string{"Red", "Green", "Blue"},
	}

	_, err := tool.Execute(context.Background(), input)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "requires interactive terminal")
}

func TestConfirmTool(t *testing.T) {
	// Create a non-interactive output
	var buf bytes.Buffer
	output := ui.ForceNonInteractive(&buf)

	tool := ui.NewConfirmTool(output)

	// Verify tool properties
	assert.Equal(t, "ui_confirm", tool.Name())
	assert.Contains(t, tool.Description(), "Ask the user for yes/no confirmation")

	// Test execution with non-interactive terminal
	input := map[string]interface{}{
		"title":   "Confirm Action",
		"message": "Are you sure?",
	}

	_, err := tool.Execute(context.Background(), input)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "requires interactive terminal")
}

func TestFormTool(t *testing.T) {
	// Create a non-interactive output
	var buf bytes.Buffer
	output := ui.ForceNonInteractive(&buf)

	tool := ui.NewFormTool(output)

	// Verify tool properties
	assert.Equal(t, "ui_form", tool.Name())
	assert.Contains(t, tool.Description(), "Display a form for structured data input")

	// Test execution with non-interactive terminal
	input := map[string]interface{}{
		"title": "User Information",
		"fields": []map[string]interface{}{
			{
				"name":  "username",
				"label": "Username",
				"type":  "text",
			},
		},
	}

	_, err := tool.Execute(context.Background(), input)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "requires interactive terminal")
}

func TestRegisterUITools(t *testing.T) {
	// Create mock tool manager
	registered := []string{}
	manager := &mockToolManager{
		registerFunc: func(t interface{ Name() string }) error {
			registered = append(registered, t.Name())
			return nil
		},
	}

	// Create output
	var buf bytes.Buffer
	output := ui.ForceNonInteractive(&buf)

	// Register tools
	err := ui.RegisterUITools(manager, output)
	require.NoError(t, err)

	// Verify all tools were registered
	assert.ElementsMatch(t, []string{"ui_list", "ui_confirm", "ui_form"}, registered)
}

// mockToolManager implements the expected interface for RegisterUITools
type mockToolManager struct {
	registerFunc func(interface{ Name() string }) error
}

func (m *mockToolManager) Register(t tool.Tool) error {
	return m.registerFunc(t)
}

func TestTerminalOutput(t *testing.T) {
	t.Run("non-interactive", func(t *testing.T) {
		var buf bytes.Buffer
		output := ui.NewTerminalOutput(&buf)

		// Buffer is not a terminal
		assert.False(t, output.IsInteractive())

		// Test write
		n, err := output.Write([]byte("test"))
		assert.NoError(t, err)
		assert.Equal(t, 4, n)
		assert.Equal(t, "test", buf.String())
	})

	t.Run("force interactive", func(t *testing.T) {
		var buf bytes.Buffer
		output := ui.ForceInteractive(&buf)

		assert.True(t, output.IsInteractive())
	})

	t.Run("force non-interactive", func(t *testing.T) {
		var buf bytes.Buffer
		output := ui.ForceNonInteractive(&buf)

		assert.False(t, output.IsInteractive())
	})
}
