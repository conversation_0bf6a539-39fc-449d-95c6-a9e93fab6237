package ui

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"

	"github.com/charmbracelet/bubbles/textinput"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

// FormDialog handles form input
type FormDialog struct {
	output Output
	params *FormParams
	result *FormResult
}

// NewFormDialog creates a new form dialog
func NewFormDialog(output Output, params *FormParams) *FormDialog {
	// Apply defaults
	params.SetDefaults()

	return &FormDialog{
		output: output,
		params: params,
		result: &FormResult{
			Values:   make(map[string]interface{}),
			Canceled: false,
		},
	}
}

// Run executes the form dialog
func (d *FormDialog) Run(ctx context.Context) (*FormResult, error) {
	// Check if we can run interactively
	if !d.output.IsInteractive() {
		return nil, fmt.Errorf("form dialog requires interactive terminal")
	}

	// Validate form parameters
	if err := d.validateFormParams(); err != nil {
		return nil, fmt.Errorf("invalid form parameters: %w", err)
	}

	// Create the model
	model := &formModel{
		params:      d.params,
		inputs:      make([]textinput.Model, len(d.params.Fields)),
		fieldErrors: make([]string, len(d.params.Fields)),
		values:      make(map[string]interface{}),
		submitted:   false,
		canceled:    false,
	}

	// Initialize text inputs
	for i, field := range d.params.Fields {
		ti := textinput.New()
		ti.Placeholder = field.Placeholder
		ti.CharLimit = 255
		if field.MaxLength > 0 {
			ti.CharLimit = field.MaxLength
		}

		// Set input type
		switch field.Type {
		case "password":
			ti.EchoMode = textinput.EchoPassword
			ti.EchoCharacter = '•'
		case "email":
			// Could add email validation
		case "number":
			// Will validate in Update
		}

		// Set default value
		if field.Default != "" {
			ti.SetValue(field.Default)
		}

		// Focus first input
		if i == 0 {
			ti.Focus()
		}

		model.inputs[i] = ti
	}

	// Run the program
	p := tea.NewProgram(model)
	finalModel, err := p.Run()
	if err != nil {
		return nil, fmt.Errorf("run form dialog: %w", err)
	}

	// Extract result
	if m, ok := finalModel.(*formModel); ok {
		d.result.Values = m.values
		d.result.Canceled = m.canceled

		// If canceled, return with canceled flag
		if m.canceled {
			d.result.Canceled = true
			d.result.Values = make(map[string]interface{})
			d.result.Error = "User canceled the form"
		} else if !m.submitted {
			// Form was not submitted properly
			d.result.Canceled = true
			d.result.Values = make(map[string]interface{})
			d.result.Error = "Form was closed without submission"

			// Collect validation errors if any
			var validationErrors []string
			for i, err := range m.fieldErrors {
				if err != "" {
					fieldName := d.params.Fields[i].Label
					validationErrors = append(validationErrors, fmt.Sprintf("%s: %s", fieldName, err))
				}
			}
			if len(validationErrors) > 0 {
				d.result.ValidationMsg = strings.Join(validationErrors, "; ")
			}
		} else if m.submitted && len(m.values) == 0 {
			// Submitted but no values collected
			d.result.Error = "Form submitted but no values were collected"
		}
	} else {
		// Failed to get model
		return nil, fmt.Errorf("failed to extract form result")
	}

	// Debug: Log the result for troubleshooting
	if d.result.Canceled {
		fmt.Fprintf(d.output, "\n[Form Result: Canceled - %s]\n", d.result.Error)
	} else if len(d.result.Values) > 0 {
		fmt.Fprintf(d.output, "\n[Form Result: Success - Collected %d values: ", len(d.result.Values))
		for k, v := range d.result.Values {
			fmt.Fprintf(d.output, "%s=%v ", k, v)
		}
		fmt.Fprintf(d.output, "]\n")
	} else {
		fmt.Fprintf(d.output, "\n[Form Result: No values collected]\n")
	}

	return d.result, nil
}

// validateFormParams validates the form parameters
func (d *FormDialog) validateFormParams() error {
	if d.params == nil {
		return fmt.Errorf("form parameters are nil")
	}

	if d.params.Title == "" {
		return fmt.Errorf("form title is required")
	}

	if len(d.params.Fields) == 0 {
		return fmt.Errorf("at least one field is required")
	}

	// Check each field
	for i, field := range d.params.Fields {
		if field.Name == "" {
			return fmt.Errorf("field %d: name is required", i+1)
		}
		if field.Label == "" {
			return fmt.Errorf("field %d (%s): label is required", i+1, field.Name)
		}
		if field.Type == "" {
			return fmt.Errorf("field %d (%s): type is required", i+1, field.Name)
		}

		// Validate field type
		validTypes := map[string]bool{
			"text": true, "number": true, "email": true,
			"password": true, "select": true, "multiline": true,
		}
		if !validTypes[field.Type] {
			return fmt.Errorf("field %d (%s): invalid type %s", i+1, field.Name, field.Type)
		}
	}

	return nil
}

// formModel is the Bubble Tea model for form input
type formModel struct {
	params       *FormParams
	inputs       []textinput.Model
	currentField int
	fieldErrors  []string
	values       map[string]interface{}
	canceled     bool
	submitted    bool
}

func (m *formModel) Init() tea.Cmd {
	return textinput.Blink
}

func (m *formModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "esc":
			m.canceled = true
			return m, tea.Quit

		case "tab", "down":
			// Move to next field
			if m.currentField < len(m.inputs)-1 {
				m.inputs[m.currentField].Blur()
				m.currentField++
				m.inputs[m.currentField].Focus()
				return m, textinput.Blink
			}

		case "shift+tab", "up":
			// Move to previous field
			if m.currentField > 0 {
				m.inputs[m.currentField].Blur()
				m.currentField--
				m.inputs[m.currentField].Focus()
				return m, textinput.Blink
			}

		case "enter":
			// Validate and submit
			if m.validateForm() {
				m.collectValues()
				m.submitted = true
				return m, tea.Quit
			} else {
				// Validation failed - move to first field with error
				for i, err := range m.fieldErrors {
					if err != "" {
						if i != m.currentField {
							m.inputs[m.currentField].Blur()
							m.currentField = i
							m.inputs[m.currentField].Focus()
						}
						break
					}
				}
			}
		}
	}

	// Update current input
	var cmd tea.Cmd
	m.inputs[m.currentField], cmd = m.inputs[m.currentField].Update(msg)
	cmds = append(cmds, cmd)

	return m, tea.Batch(cmds...)
}

func (m *formModel) View() string {
	if m.canceled || m.submitted {
		return ""
	}

	var s strings.Builder

	// Title
	titleStyle := lipgloss.NewStyle().Bold(true).Foreground(lipgloss.Color("211"))
	s.WriteString(titleStyle.Render(m.params.Title))
	s.WriteString("\n")

	// Description
	if m.params.Description != "" {
		descStyle := lipgloss.NewStyle().Foreground(lipgloss.Color("245"))
		s.WriteString(descStyle.Render(m.params.Description))
		s.WriteString("\n")
	}
	s.WriteString("\n")

	// Fields
	labelStyle := lipgloss.NewStyle().Bold(true).Width(20)
	errorStyle := lipgloss.NewStyle().Foreground(lipgloss.Color("196"))
	requiredStyle := lipgloss.NewStyle().Foreground(lipgloss.Color("196"))

	for i, field := range m.params.Fields {
		// Label
		label := field.Label
		if field.Required {
			label += requiredStyle.Render(" *")
		}
		s.WriteString(labelStyle.Render(label))
		s.WriteString("\n")

		// Input
		s.WriteString(m.inputs[i].View())
		s.WriteString("\n")

		// Error message
		if m.fieldErrors[i] != "" {
			s.WriteString(errorStyle.Render("  " + m.fieldErrors[i]))
			s.WriteString("\n")
		}

		s.WriteString("\n")
	}

	// Buttons
	s.WriteString("\n")
	helpStyle := lipgloss.NewStyle().Foreground(lipgloss.Color("241"))
	s.WriteString(helpStyle.Render("Tab: Next field | Enter: Submit | Esc: Cancel"))

	return s.String()
}

func (m *formModel) validateForm() bool {
	valid := true

	for i, field := range m.params.Fields {
		value := strings.TrimSpace(m.inputs[i].Value())
		m.fieldErrors[i] = ""

		// Required field check
		if field.Required && value == "" {
			m.fieldErrors[i] = "This field is required"
			valid = false
			continue
		}

		// Skip validation for empty optional fields
		if value == "" {
			continue
		}

		// Security: Check for common injection patterns
		if containsMaliciousPatterns(value) {
			m.fieldErrors[i] = "Invalid characters detected"
			valid = false
			continue
		}

		// Type validation
		switch field.Type {
		case "number":
			if _, err := strconv.ParseFloat(value, 64); err != nil {
				m.fieldErrors[i] = "Please enter a valid number"
				valid = false
			}

		case "email":
			emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
			if !emailRegex.MatchString(value) {
				m.fieldErrors[i] = "Please enter a valid email address"
				valid = false
			}

		case "text", "password":
			// Ensure no control characters
			if containsControlChars(value) {
				m.fieldErrors[i] = "Invalid characters in input"
				valid = false
			}
		}

		// Custom validation regex
		if field.Validation != "" {
			if regex, err := regexp.Compile(field.Validation); err == nil {
				if !regex.MatchString(value) {
					m.fieldErrors[i] = "Invalid format"
					valid = false
				}
			}
		}

		// Length validation
		if field.MinLength > 0 && len(value) < field.MinLength {
			m.fieldErrors[i] = fmt.Sprintf("Minimum length is %d characters", field.MinLength)
			valid = false
		}
		if field.MaxLength > 0 && len(value) > field.MaxLength {
			m.fieldErrors[i] = fmt.Sprintf("Maximum length is %d characters", field.MaxLength)
			valid = false
		}
	}

	return valid
}

// containsMaliciousPatterns checks for common injection patterns
func containsMaliciousPatterns(s string) bool {
	patterns := []string{
		"<script", "</script>", "javascript:", "onclick=", "onerror=",
		"'; DROP TABLE", "'; DELETE FROM", "' OR '1'='1",
		"../", "..\\", "%2e%2e", "%252e%252e",
	}

	lower := strings.ToLower(s)
	for _, pattern := range patterns {
		if strings.Contains(lower, pattern) {
			return true
		}
	}
	return false
}

// containsControlChars checks for control characters
func containsControlChars(s string) bool {
	for _, r := range s {
		if r < 32 && r != '\t' && r != '\n' && r != '\r' {
			return true
		}
	}
	return false
}

func (m *formModel) collectValues() {
	// Ensure values map is initialized
	if m.values == nil {
		m.values = make(map[string]interface{})
	}

	for i, field := range m.params.Fields {
		value := strings.TrimSpace(m.inputs[i].Value())

		// Skip empty optional fields
		if value == "" && !field.Required {
			continue
		}

		// Always store non-empty values with valid field names
		if value != "" && field.Name != "" {
			// Convert to appropriate type
			switch field.Type {
			case "number":
				if v, err := strconv.ParseFloat(value, 64); err == nil {
					m.values[field.Name] = v
				} else {
					// Store as string if conversion fails
					m.values[field.Name] = value
				}
			default:
				m.values[field.Name] = value
			}
		} else if value != "" && field.Name == "" {
			// Log warning for missing field name
			// Use a generated key based on index
			fallbackKey := fmt.Sprintf("field_%d", i+1)
			m.values[fallbackKey] = value
		}
	}
}
