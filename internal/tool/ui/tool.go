// Package ui provides UI tools for assistant interaction
package ui

import (
	"context"
	"encoding/json"
	"fmt"
	"io"

	"github.com/koopa0/assistant-go/internal/tool"
)

// Output represents the interface for displaying UI elements
type Output interface {
	io.Writer
	// IsInteractive returns true if the output supports interactive UI
	IsInteractive() bool
}

// ListTool provides a selection dialog
type ListTool struct {
	tool.BaseTool
	output Output
}

// NewListTool creates a new list selection tool
func NewListTool(output Output) *ListTool {
	schema := json.RawMessage(`{
		"type": "object",
		"properties": {
			"title": {
				"type": "string",
				"description": "Title of the selection dialog"
			},
			"message": {
				"type": "string",
				"description": "Optional message to display"
			},
			"options": {
				"type": "array",
				"items": {
					"type": "string"
				},
				"minItems": 1,
				"description": "List of options to choose from"
			},
			"multi_select": {
				"type": "boolean",
				"description": "Allow multiple selections",
				"default": false
			},
			"required": {
				"type": "boolean",
				"description": "Whether selection is required",
				"default": true
			}
		},
		"required": ["title", "options"]
	}`)

	return &ListTool{
		BaseTool: tool.NewBaseTool(
			"ui_list",
			"Display a list of options for the user to select from. Use this when you need the user to choose from multiple options. Process the result immediately - if canceled, acknowledge it and don't retry automatically.",
			schema,
		),
		output: output,
	}
}

// Execute runs the list selection dialog
func (t *ListTool) Execute(ctx context.Context, input any) (any, error) {
	if t.output == nil || !t.output.IsInteractive() {
		return nil, fmt.Errorf("ui_list requires interactive terminal")
	}

	// Parse input parameters
	params := &ListParams{}
	if err := parseInput(input, params); err != nil {
		return nil, fmt.Errorf("parse input: %w", err)
	}

	// Create and run the list dialog
	dialog := NewListDialog(t.output, params)
	result, err := dialog.Run(ctx)
	if err != nil {
		return nil, err
	}

	return result, nil
}

// ConfirmTool provides a yes/no confirmation dialog
type ConfirmTool struct {
	tool.BaseTool
	output Output
}

// NewConfirmTool creates a new confirmation tool
func NewConfirmTool(output Output) *ConfirmTool {
	schema := json.RawMessage(`{
		"type": "object",
		"properties": {
			"title": {
				"type": "string",
				"description": "Title of the confirmation dialog"
			},
			"message": {
				"type": "string",
				"description": "Question or message to confirm"
			},
			"default": {
				"type": "boolean",
				"description": "Default value if user just presses enter",
				"default": false
			}
		},
		"required": ["title", "message"]
	}`)

	return &ConfirmTool{
		BaseTool: tool.NewBaseTool(
			"ui_confirm",
			"Ask the user for yes/no confirmation. Use this when you need explicit user consent or confirmation before proceeding. Accept the user's choice - if they say no or cancel, respect that decision.",
			schema,
		),
		output: output,
	}
}

// Execute runs the confirmation dialog
func (t *ConfirmTool) Execute(ctx context.Context, input any) (any, error) {
	if t.output == nil || !t.output.IsInteractive() {
		return nil, fmt.Errorf("ui_confirm requires interactive terminal")
	}

	// Parse input parameters
	params := &ConfirmParams{}
	if err := parseInput(input, params); err != nil {
		return nil, fmt.Errorf("parse input: %w", err)
	}

	// Create and run the confirmation dialog
	dialog := NewConfirmDialog(t.output, params)
	result, err := dialog.Run(ctx)
	if err != nil {
		return nil, err
	}

	return result, nil
}

// FormTool provides a structured form input dialog
type FormTool struct {
	tool.BaseTool
	output Output
}

// NewFormTool creates a new form input tool
func NewFormTool(output Output) *FormTool {
	schema := json.RawMessage(`{
		"type": "object",
		"properties": {
			"title": {
				"type": "string",
				"description": "Form title"
			},
			"description": {
				"type": "string",
				"description": "Form description"
			},
			"fields": {
				"type": "array",
				"items": {
					"type": "object",
					"properties": {
						"name": {
							"type": "string",
							"description": "Field identifier"
						},
						"label": {
							"type": "string",
							"description": "Display label"
						},
						"type": {
							"type": "string",
							"enum": ["text", "number", "email", "password", "select", "multiline"],
							"description": "Field type"
						},
						"required": {
							"type": "boolean",
							"description": "Whether field is required"
						},
						"default": {
							"type": "string",
							"description": "Default value"
						},
						"placeholder": {
							"type": "string",
							"description": "Placeholder text"
						}
					},
					"required": ["name", "label", "type"]
				},
				"minItems": 1,
				"description": "Form fields"
			}
		},
		"required": ["title", "fields"]
	}`)

	return &FormTool{
		BaseTool: tool.NewBaseTool(
			"ui_form",
			"Display a form for structured data input from the user. Use this when you need to collect multiple pieces of information in a structured way. IMPORTANT: Only show ONE form per user request. If the form is canceled or has errors, explain what happened and ask the user before showing another form. Never automatically retry a failed form.",
			schema,
		),
		output: output,
	}
}

// Execute runs the form dialog
func (t *FormTool) Execute(ctx context.Context, input any) (any, error) {
	if t.output == nil || !t.output.IsInteractive() {
		return nil, fmt.Errorf("ui_form requires interactive terminal")
	}

	// Parse input parameters
	params := &FormParams{}
	if err := parseInput(input, params); err != nil {
		return nil, fmt.Errorf("parse input: %w", err)
	}

	// Create and run the form dialog
	dialog := NewFormDialog(t.output, params)
	result, err := dialog.Run(ctx)
	if err != nil {
		return nil, err
	}

	return result, nil
}

// parseInput converts generic input to typed parameters
func parseInput(input any, target any) error {
	// Handle different input types
	switch v := input.(type) {
	case map[string]any:
		// Convert map to JSON then unmarshal
		data, err := json.Marshal(v)
		if err != nil {
			return fmt.Errorf("marshal input: %w", err)
		}
		if err := json.Unmarshal(data, target); err != nil {
			return fmt.Errorf("unmarshal to target: %w", err)
		}
		return nil
	case string:
		// Try to parse as JSON
		if err := json.Unmarshal([]byte(v), target); err != nil {
			return fmt.Errorf("unmarshal string input: %w", err)
		}
		return nil
	case []byte:
		// Direct JSON unmarshal
		if err := json.Unmarshal(v, target); err != nil {
			return fmt.Errorf("unmarshal bytes input: %w", err)
		}
		return nil
	default:
		// Try direct assignment or JSON round-trip
		data, err := json.Marshal(input)
		if err != nil {
			return fmt.Errorf("cannot convert input type %T: %w", input, err)
		}
		if err := json.Unmarshal(data, target); err != nil {
			return fmt.Errorf("unmarshal converted input: %w", err)
		}
		return nil
	}
}

// RegisterUITools registers all UI tools with the tool manager
func RegisterUITools(manager interface{ Register(tool.Tool) error }, output Output) error {
	tools := []tool.Tool{
		NewListTool(output),
		NewConfirmTool(output),
		NewFormTool(output),
	}

	for _, t := range tools {
		if err := manager.Register(t); err != nil {
			return fmt.Errorf("register %s: %w", t.Name(), err)
		}
	}

	return nil
}
