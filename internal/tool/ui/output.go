package ui

import (
	"io"
	"os"

	"golang.org/x/term"
)

// TerminalOutput wraps an io.Writer and provides terminal capability detection
type TerminalOutput struct {
	writer        io.Writer
	isInteractive bool
}

// NewTerminalOutput creates a new output wrapper with terminal detection
func NewTerminalOutput(w io.Writer) Output {
	output := &TerminalOutput{
		writer: w,
	}

	// Check if the writer is connected to a terminal
	if f, ok := w.(*os.File); ok {
		output.isInteractive = term.IsTerminal(int(f.Fd()))
	} else {
		// Default to non-interactive for non-file writers
		output.isInteractive = false
	}

	return output
}

// Write implements io.Writer
func (t *TerminalOutput) Write(p []byte) (n int, err error) {
	return t.writer.Write(p)
}

// IsInteractive returns true if the output supports interactive UI
func (t *TerminalOutput) IsInteractive() bool {
	return t.isInteractive
}

// ForceInteractive creates an output that's always interactive (for testing)
func ForceInteractive(w io.Writer) Output {
	return &TerminalOutput{
		writer:        w,
		isInteractive: true,
	}
}

// ForceNonInteractive creates an output that's never interactive (for testing)
func ForceNonInteractive(w io.Writer) Output {
	return &TerminalOutput{
		writer:        w,
		isInteractive: false,
	}
}
