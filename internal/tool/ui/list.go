package ui

import (
	"context"
	"fmt"
	"io"
	"strings"

	"github.com/charmbracelet/bubbles/list"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

// ListDialog handles list selection UI
type ListDialog struct {
	output Output
	params *ListParams
	result *ListResult
}

// NewListDialog creates a new list dialog
func NewListDialog(output Output, params *ListParams) *ListDialog {
	// Apply defaults
	params.SetDefaults()

	return &ListDialog{
		output: output,
		params: params,
		result: &ListResult{
			Selected: []string{},
			Canceled: false,
		},
	}
}

// Run executes the list dialog
func (d *ListDialog) Run(ctx context.Context) (*ListResult, error) {
	// Check if we can run interactively
	if !d.output.IsInteractive() {
		return nil, fmt.Errorf("list dialog requires interactive terminal")
	}

	// Create the model
	model := &listModel{
		params:      d.params,
		selected:    make(map[int]bool),
		multiSelect: d.params.MultiSelect,
		required:    d.params.Required,
	}

	// Create items for the list
	items := make([]list.Item, len(d.params.Options))
	for i, opt := range d.params.Options {
		items[i] = listItem{
			title: opt,
			index: i,
		}
	}

	// Create the list
	const defaultWidth = 60
	const listHeight = 10

	l := list.New(items, &listItemDelegate{model: model}, defaultWidth, listHeight)
	l.Title = d.params.Title
	l.SetShowStatusBar(false)
	l.SetFilteringEnabled(true)
	l.Styles.Title = titleStyle
	l.Styles.PaginationStyle = paginationStyle
	l.Styles.HelpStyle = helpStyle

	model.list = l

	// Run the program
	p := tea.NewProgram(model)
	finalModel, err := p.Run()
	if err != nil {
		return nil, fmt.Errorf("run list dialog: %w", err)
	}

	// Extract result
	if m, ok := finalModel.(*listModel); ok {
		if m.canceled {
			d.result.Canceled = true
		} else {
			// Get selected items
			for idx := range m.selected {
				if idx < len(d.params.Options) {
					d.result.Selected = append(d.result.Selected, d.params.Options[idx])
				}
			}
		}
	}

	return d.result, nil
}

// listItem implements list.Item
type listItem struct {
	title string
	index int
}

func (i listItem) FilterValue() string { return i.title }
func (i listItem) Title() string       { return i.title }
func (i listItem) Description() string { return "" }

// listItemDelegate handles item rendering with access to the model
type listItemDelegate struct {
	model *listModel
}

func (d *listItemDelegate) Height() int                             { return 1 }
func (d *listItemDelegate) Spacing() int                            { return 0 }
func (d *listItemDelegate) Update(_ tea.Msg, _ *list.Model) tea.Cmd { return nil }

func (d *listItemDelegate) Render(w io.Writer, m list.Model, index int, item list.Item) {
	i, ok := item.(listItem)
	if !ok {
		return
	}

	str := i.title

	// Check if selected (for multi-select)
	if d.model.multiSelect {
		if d.model.selected[i.index] {
			str = "[✓] " + str
		} else {
			str = "[ ] " + str
		}
	}

	// Apply style based on cursor position
	if index == m.Index() {
		fmt.Fprint(w, selectedItemStyle.Render(str))
	} else {
		fmt.Fprint(w, itemStyle.Render(str))
	}
}

// listModel is the Bubble Tea model for list selection
type listModel struct {
	list        list.Model
	params      *ListParams
	selected    map[int]bool
	multiSelect bool
	required    bool
	canceled    bool
}

func (m *listModel) Init() tea.Cmd {
	return nil
}

func (m *listModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.list.SetWidth(msg.Width)
		return m, nil

	case tea.KeyMsg:
		switch keypress := msg.String(); keypress {
		case "ctrl+c", "esc":
			m.canceled = true
			return m, tea.Quit

		case "enter":
			if m.multiSelect {
				// Confirm selection
				if !m.required || len(m.selected) > 0 {
					return m, tea.Quit
				}
			} else {
				// Single select - mark current item
				i, ok := m.list.SelectedItem().(listItem)
				if ok {
					m.selected = map[int]bool{i.index: true}
					return m, tea.Quit
				}
			}

		case " ":
			if m.multiSelect {
				// Toggle selection
				i, ok := m.list.SelectedItem().(listItem)
				if ok {
					if m.selected[i.index] {
						delete(m.selected, i.index)
					} else {
						m.selected[i.index] = true
					}
				}
			}
		}
	}

	var cmd tea.Cmd
	m.list, cmd = m.list.Update(msg)
	return m, cmd
}

func (m *listModel) View() string {
	if m.canceled {
		return ""
	}

	var b strings.Builder
	b.WriteString(m.list.View())

	// Add help text
	if m.multiSelect {
		b.WriteString("\n")
		if m.required && len(m.selected) == 0 {
			b.WriteString(helpStyle.Render("Space: Toggle | Enter: Confirm (select at least one)"))
		} else {
			b.WriteString(helpStyle.Render("Space: Toggle | Enter: Confirm | Esc: Cancel"))
		}
	} else {
		b.WriteString("\n")
		b.WriteString(helpStyle.Render("Enter: Select | Esc: Cancel"))
	}

	return b.String()
}

// Styles
var (
	titleStyle = lipgloss.NewStyle().
			Bold(true).
			Foreground(lipgloss.Color("211"))

	itemStyle = lipgloss.NewStyle().
			PaddingLeft(2)

	selectedItemStyle = lipgloss.NewStyle().
				PaddingLeft(2).
				Foreground(lipgloss.Color("170"))

	paginationStyle = lipgloss.NewStyle().
			PaddingLeft(2)

	helpStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("241"))
)
