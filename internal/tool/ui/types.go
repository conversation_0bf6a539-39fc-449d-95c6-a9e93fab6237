package ui

import "fmt"

// Common errors
var (
	ErrUserCanceled = fmt.<PERSON><PERSON><PERSON>("user canceled")
	ErrNoOutput     = fmt.<PERSON><PERSON><PERSON>("no output device")
)

// ListParams defines parameters for the list selection tool
type ListParams struct {
	Title       string   `json:"title"`
	Message     string   `json:"message,omitempty"`
	Options     []string `json:"options"`
	MultiSelect bool     `json:"multi_select,omitempty"`
	Required    bool     `json:"required,omitempty"`
}

// ListResult contains the result of a list selection
type ListResult struct {
	Selected []string `json:"selected"`
	Canceled bool     `json:"canceled"`
}

// ConfirmParams defines parameters for the confirmation tool
type ConfirmParams struct {
	Title    string `json:"title"`
	Message  string `json:"message"`
	Default  bool   `json:"default,omitempty"`
	YesLabel string `json:"yes_label,omitempty"`
	NoLabel  string `json:"no_label,omitempty"`
}

// ConfirmResult contains the result of a confirmation
type ConfirmResult struct {
	Confirmed bool `json:"confirmed"`
	Canceled  bool `json:"canceled"`
}

// FormField defines a field in a form
type FormField struct {
	Name        string   `json:"name"`
	Label       string   `json:"label"`
	Type        string   `json:"type"` // text, number, email, password, select, multiline
	Required    bool     `json:"required,omitempty"`
	Default     string   `json:"default,omitempty"`
	Placeholder string   `json:"placeholder,omitempty"`
	Options     []string `json:"options,omitempty"`    // For select fields
	Validation  string   `json:"validation,omitempty"` // Regex pattern
	MinLength   int      `json:"min_length,omitempty"`
	MaxLength   int      `json:"max_length,omitempty"`
}

// FormParams defines parameters for the form tool
type FormParams struct {
	Title       string      `json:"title"`
	Description string      `json:"description,omitempty"`
	Fields      []FormField `json:"fields"`
	SubmitLabel string      `json:"submit_label,omitempty"`
	CancelLabel string      `json:"cancel_label,omitempty"`
}

// FormResult contains the result of a form submission
type FormResult struct {
	Values        map[string]interface{} `json:"values"`
	Canceled      bool                   `json:"canceled"`
	Error         string                 `json:"error,omitempty"`
	ValidationMsg string                 `json:"validation_message,omitempty"`
}

// SetDefaults applies default values to parameters

// SetDefaults for ListParams
func (p *ListParams) SetDefaults() {
	if !p.Required && len(p.Options) > 0 {
		p.Required = true
	}
}

// SetDefaults for ConfirmParams
func (p *ConfirmParams) SetDefaults() {
	if p.YesLabel == "" {
		p.YesLabel = "Yes"
	}
	if p.NoLabel == "" {
		p.NoLabel = "No"
	}
}

// SetDefaults for FormParams
func (p *FormParams) SetDefaults() {
	if p.SubmitLabel == "" {
		p.SubmitLabel = "Submit"
	}
	if p.CancelLabel == "" {
		p.CancelLabel = "Cancel"
	}
}
