package ui

import (
	"context"
	"fmt"
	"strings"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

// ConfirmDialog handles confirmation prompts
type ConfirmDialog struct {
	output Output
	params *ConfirmParams
	result *ConfirmResult
}

// NewConfirmDialog creates a new confirmation dialog
func NewConfirmDialog(output Output, params *ConfirmParams) *ConfirmDialog {
	// Apply defaults
	params.SetDefaults()

	return &ConfirmDialog{
		output: output,
		params: params,
		result: &ConfirmResult{
			Confirmed: false,
			Canceled:  false,
		},
	}
}

// Run executes the confirmation dialog
func (d *ConfirmDialog) Run(ctx context.Context) (*ConfirmResult, error) {
	// Check if we can run interactively
	if !d.output.IsInteractive() {
		return nil, fmt.Errorf("confirm dialog requires interactive terminal")
	}

	// Create the model
	model := &confirmModel{
		params:      d.params,
		confirmed:   d.params.Default,
		highlighted: d.params.Default,
	}

	// Run the program
	p := tea.NewProgram(model)
	finalModel, err := p.Run()
	if err != nil {
		return nil, fmt.Errorf("run confirm dialog: %w", err)
	}

	// Extract result
	if m, ok := finalModel.(*confirmModel); ok {
		d.result.Confirmed = m.confirmed
		d.result.Canceled = m.canceled
	}

	return d.result, nil
}

// confirmModel is the Bubble Tea model for confirmation
type confirmModel struct {
	params      *ConfirmParams
	confirmed   bool
	highlighted bool // Which button is highlighted
	canceled    bool
}

func (m *confirmModel) Init() tea.Cmd {
	return nil
}

func (m *confirmModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "esc":
			m.canceled = true
			return m, tea.Quit

		case "enter":
			m.confirmed = m.highlighted
			return m, tea.Quit

		case "y", "Y":
			m.confirmed = true
			return m, tea.Quit

		case "n", "N":
			m.confirmed = false
			return m, tea.Quit

		case "left", "h", "tab":
			m.highlighted = !m.highlighted

		case "right", "l":
			m.highlighted = !m.highlighted
		}
	}

	return m, nil
}

func (m *confirmModel) View() string {
	if m.canceled {
		return ""
	}

	var s strings.Builder

	// Title
	titleStyle := lipgloss.NewStyle().Bold(true).Foreground(lipgloss.Color("211"))
	s.WriteString(titleStyle.Render(m.params.Title))
	s.WriteString("\n\n")

	// Message
	messageStyle := lipgloss.NewStyle().Foreground(lipgloss.Color("252"))
	s.WriteString(messageStyle.Render(m.params.Message))
	s.WriteString("\n\n")

	// Buttons
	buttonStyle := lipgloss.NewStyle().
		Padding(0, 2).
		Border(lipgloss.RoundedBorder()).
		BorderForeground(lipgloss.Color("240"))

	selectedButtonStyle := buttonStyle.
		BorderForeground(lipgloss.Color("170")).
		Foreground(lipgloss.Color("170"))

	// Yes button
	yesButton := m.params.YesLabel
	if m.highlighted {
		yesButton = selectedButtonStyle.Render(yesButton)
	} else {
		yesButton = buttonStyle.Render(yesButton)
	}

	// No button
	noButton := m.params.NoLabel
	if !m.highlighted {
		noButton = selectedButtonStyle.Render(noButton)
	} else {
		noButton = buttonStyle.Render(noButton)
	}

	// Layout buttons horizontally
	buttons := lipgloss.JoinHorizontal(lipgloss.Left, yesButton, "  ", noButton)
	s.WriteString(buttons)
	s.WriteString("\n\n")

	// Help text
	helpStyle := lipgloss.NewStyle().Foreground(lipgloss.Color("241"))
	s.WriteString(helpStyle.Render("←/→: Navigate | Enter: Confirm | Y/N: Quick select | Esc: Cancel"))

	return s.String()
}
