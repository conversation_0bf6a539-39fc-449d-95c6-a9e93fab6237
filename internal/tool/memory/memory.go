// Package memory provides memory management tools for the AI assistant.
package memory

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/google/uuid"
	"github.com/koopa0/assistant-go/internal/memory"
	"github.com/koopa0/assistant-go/internal/tool"
)

// DeleteMemoryTool allows the AI to delete specific memories.
type DeleteMemoryTool struct {
	tool.BaseTool
	memService *memory.Service
}

// NewDeleteMemoryTool creates a new delete memory tool.
func NewDeleteMemoryTool(memService *memory.Service) *DeleteMemoryTool {
	schema := json.RawMessage(`{
		"type": "object",
		"properties": {
			"memory_id": {
				"type": "string",
				"description": "The UUID of the memory to delete"
			},
			"reason": {
				"type": "string",
				"description": "Reason for deleting this memory"
			}
		},
		"required": ["memory_id", "reason"]
	}`)

	return &DeleteMemoryTool{
		BaseTool: tool.NewBaseTool(
			"delete_memory",
			"Delete a specific memory that is outdated, incorrect, or conflicting",
			schema,
		),
		memService: memService,
	}
}

// Execute deletes the specified memory.
func (t *DeleteMemoryTool) Execute(ctx context.Context, input any) (any, error) {
	// Parse input
	var params struct {
		MemoryID string `json:"memory_id"`
		Reason   string `json:"reason"`
	}

	inputBytes, err := json.Marshal(input)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal input: %w", err)
	}

	if unmarshalErr := json.Unmarshal(inputBytes, &params); unmarshalErr != nil {
		return nil, fmt.Errorf("invalid parameters: %w", unmarshalErr)
	}

	// Validate memory ID
	memID, err := uuid.Parse(params.MemoryID)
	if err != nil {
		return nil, fmt.Errorf("invalid memory ID format: %w", err)
	}

	// Delete the memory
	if err := t.memService.Delete(ctx, memID.String()); err != nil {
		return nil, fmt.Errorf("failed to delete memory: %w", err)
	}

	// Return result
	result := map[string]interface{}{
		"success":           true,
		"deleted_memory_id": params.MemoryID,
		"reason":            params.Reason,
	}

	return result, nil
}

// ResolveConflictsTool allows the AI to resolve memory conflicts.
type ResolveConflictsTool struct {
	tool.BaseTool
	memService *memory.Service
}

// NewResolveConflictsTool creates a new resolve conflicts tool.
func NewResolveConflictsTool(memService *memory.Service) *ResolveConflictsTool {
	schema := json.RawMessage(`{
		"type": "object",
		"properties": {
			"conflicting_memory_ids": {
				"type": "array",
				"items": {"type": "string"},
				"description": "UUIDs of all conflicting memories"
			},
			"keep_memory_id": {
				"type": "string",
				"description": "UUID of the memory to keep"
			},
			"reason": {
				"type": "string",
				"description": "Reason for this resolution"
			}
		},
		"required": ["conflicting_memory_ids", "keep_memory_id", "reason"]
	}`)

	return &ResolveConflictsTool{
		BaseTool: tool.NewBaseTool(
			"resolve_memory_conflicts",
			"Resolve conflicts between multiple memories by keeping the most recent or accurate one",
			schema,
		),
		memService: memService,
	}
}

// Execute resolves memory conflicts by deleting all but the chosen memory.
func (t *ResolveConflictsTool) Execute(ctx context.Context, input any) (any, error) {
	// Parse input
	var params struct {
		ConflictingMemoryIDs []string `json:"conflicting_memory_ids"`
		KeepMemoryID         string   `json:"keep_memory_id"`
		Reason               string   `json:"reason"`
	}

	inputBytes, marshalErr := json.Marshal(input)
	if marshalErr != nil {
		return nil, fmt.Errorf("failed to marshal input: %w", marshalErr)
	}

	if unmarshalErr := json.Unmarshal(inputBytes, &params); unmarshalErr != nil {
		return nil, fmt.Errorf("invalid parameters: %w", unmarshalErr)
	}

	// Validate keep memory ID
	keepID, err := uuid.Parse(params.KeepMemoryID)
	if err != nil {
		return nil, fmt.Errorf("invalid keep memory ID format: %w", err)
	}

	// Validate that keep ID is in the conflict list
	found := false
	for _, id := range params.ConflictingMemoryIDs {
		if id == params.KeepMemoryID {
			found = true
			break
		}
	}
	if !found {
		return nil, fmt.Errorf("keep_memory_id must be in conflicting_memory_ids list")
	}

	// Delete all conflicting memories except the one to keep
	deletedCount := 0
	var deleteErrors []string

	for _, memIDStr := range params.ConflictingMemoryIDs {
		if memIDStr == params.KeepMemoryID {
			continue // Skip the one we want to keep
		}

		// Validate and delete
		if _, err := uuid.Parse(memIDStr); err != nil {
			deleteErrors = append(deleteErrors, fmt.Sprintf("%s: invalid UUID", memIDStr))
			continue
		}

		if err := t.memService.Delete(ctx, memIDStr); err != nil {
			deleteErrors = append(deleteErrors, fmt.Sprintf("%s: %v", memIDStr, err))
			continue
		}

		deletedCount++
	}

	// Return result
	result := map[string]interface{}{
		"success":        deletedCount > 0,
		"deleted_count":  deletedCount,
		"kept_memory_id": keepID.String(),
		"reason":         params.Reason,
	}

	if len(deleteErrors) > 0 {
		result["errors"] = deleteErrors
	}

	return result, nil
}

// GetMemoryTools returns all memory management tools.
func GetMemoryTools(memService *memory.Service) []tool.Tool {
	if memService == nil {
		return []tool.Tool{}
	}

	return []tool.Tool{
		NewDeleteMemoryTool(memService),
		NewResolveConflictsTool(memService),
	}
}
