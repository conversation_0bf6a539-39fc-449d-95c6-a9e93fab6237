// testing_framework.go provides comprehensive testing framework for tools
// following Go philosophy: test behavior, not implementation
package tool

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"

	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/koopa0/assistant-go/internal/platform/logger"
)

// TestCase represents a single tool test case
type TestCase struct {
	Name        string
	Tool        Tool
	Input       any
	Expected    func(result any) error
	ShouldError bool
}

// TestSuite runs comprehensive tests on tools
type TestSuite struct {
	logger logger.Logger
}

// NewTestSuite creates a new test suite
func NewTestSuite(logger logger.Logger) *TestSuite {
	return &TestSuite{logger: logger}
}

// RunToolTests executes all tool test cases
func (ts *TestSuite) RunToolTests(t *testing.T, cases []TestCase) {
	for _, tc := range cases {
		t.Run(tc.Name, func(t *testing.T) {
			ctx := context.Background()
			result, err := tc.Tool.Execute(ctx, tc.Input)

			if tc.ShouldError && err == nil {
				t.Errorf("Expected error but got none")
				return
			}

			if !tc.ShouldError && err != nil {
				t.<PERSON>("Unexpected error: %v", err)
				return
			}

			if tc.Expected != nil && err == nil {
				if validationErr := tc.Expected(result); validationErr != nil {
					t.Errorf("Result validation failed: %v", validationErr)
				}
			}

			// Log result for debugging
			ts.logger.Debug("Test result",
				"test", tc.Name,
				"result", result,
				"error", err)
		})
	}
}

// VerifyToolRegistration checks if tools are properly registered
func (ts *TestSuite) VerifyToolRegistration(mgr *Manager) error {
	tools := mgr.List()

	ts.logger.Info("Registered tools", "count", len(tools))

	// Expected tools based on our implementation
	expectedTools := []string{
		"current_time",
		"time_diff",
		"read_file",
		"write_file",
		"list_directory",
		"web_search",
		"web_scrape",
		"web_extract",
		"http_fetch",
		"http_check",
	}

	missing := []string{}
	for _, expected := range expectedTools {
		found := false
		for _, tool := range tools {
			if tool.Name() == expected {
				found = true
				break
			}
		}
		if !found {
			missing = append(missing, expected)
		}
	}

	if len(missing) > 0 {
		return fmt.Errorf("missing tools: %v", missing)
	}

	return nil
}

// TestAIToolIntegration tests the complete flow from AI to tool execution
func (ts *TestSuite) TestAIToolIntegration(mgr *Manager) error {
	// Create a mock tool call as AI would generate
	// Create arguments
	args := make(ai.Arguments)
	_ = args.Set("timezone", "Asia/Taipei")

	toolCall := ai.ToolCall{
		ID:   "test-001",
		Name: "current_time",
		Args: args,
	}

	// Execute through manager
	result := mgr.Execute(context.Background(), toolCall)

	// Verify result
	if result.Error != nil {
		return fmt.Errorf("tool execution failed: %s", result.Content)
	}

	if result.Content == "" {
		return fmt.Errorf("empty tool result")
	}

	// Use structured logging
	ts.logger.Info("AI tool integration test passed",
		"tool", toolCall.Name,
		"result", result.Content)

	return nil
}

// TestToolSchema verifies tool schemas are valid
func (ts *TestSuite) TestToolSchema(tools []Tool) error {
	for _, t := range tools {
		schema := t.InputSchema()

		// Verify it's valid JSON
		var parsed map[string]any
		if err := json.Unmarshal(schema, &parsed); err != nil {
			return fmt.Errorf("invalid schema for %s: %w", t.Name(), err)
		}

		// Check required fields
		if _, ok := parsed["type"]; !ok {
			return fmt.Errorf("schema for %s missing 'type' field", t.Name())
		}

		ts.logger.Debug("Schema validated",
			"tool", t.Name(),
			"schema", string(schema))
	}

	return nil
}

// RunComprehensiveTest runs all tests in sequence
func RunComprehensiveTest() {
	testLogger := logger.NewConsoleLogger()
	suite := NewTestSuite(testLogger)

	fmt.Println("=== Starting Comprehensive Tool Testing ===")

	// Step 1: Test individual tools
	fmt.Println("\n1. Testing Individual Tools")
	testIndividualTools(suite)

	// Step 2: Test tool registration
	fmt.Println("\n2. Testing Tool Registration")
	mgr := createToolManager(testLogger)
	if err := suite.VerifyToolRegistration(mgr); err != nil {
		logger.Error("Registration test failed",
			"error", err)
	} else {
		fmt.Println("✅ All tools registered correctly")
	}

	// Step 3: Test AI integration
	fmt.Println("\n3. Testing AI Tool Integration")
	if err := suite.TestAIToolIntegration(mgr); err != nil {
		logger.Error("AI integration test failed",
			"error", err)
	} else {
		fmt.Println("✅ AI tool integration working")
	}

	// Step 4: Test schemas
	fmt.Println("\n4. Testing Tool Schemas")
	tools := mgr.List()
	if err := suite.TestToolSchema(tools); err != nil {
		logger.Error("Schema test failed",
			"error", err)
	} else {
		fmt.Println("✅ All tool schemas valid")
	}

	fmt.Println("\n=== Testing Complete ===")
}

func testIndividualTools(suite *TestSuite) {
	// This would be expanded with actual tool tests
	fmt.Println("Testing time tools...")
	fmt.Println("Testing file tools...")
	fmt.Println("Testing web tools...")
}

func createToolManager(logger logger.Logger) *Manager {
	cfg := Config{
		FileSystem: FileSystemConfig{
			EnableWrite: true,
			CreateDirs:  true,
		},
	}
	mgr := NewManager(cfg)
	// Register tools would happen here
	// This is a simplified version
	return mgr
}
