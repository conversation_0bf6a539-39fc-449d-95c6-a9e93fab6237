// Package system provides system-level tools for the assistant.
package system

import (
	"context"
	"encoding/json"
	"fmt"
	"time"
)

// ToolHistoryProvider interface for accessing tool execution history
type ToolHistoryProvider interface {
	GetToolHistory(limit int) []ToolExecution
}

// ToolExecution records a tool execution for auditing
type ToolExecution struct {
	Timestamp time.Time              `json:"timestamp"`
	Name      string                 `json:"name"`
	Args      map[string]interface{} `json:"args"`
	Success   bool                   `json:"success"`
	Error     string                 `json:"error,omitempty"`
	ResultLen int                    `json:"result_length"`
	CallID    string                 `json:"call_id"`
}

// ToolHistoryTool provides access to tool execution history
type ToolH<PERSON>oryTool struct {
	provider ToolHistoryProvider
}

// NewToolHistoryTool creates a new tool history tool
func NewToolHistoryTool(provider ToolHistoryProvider) *ToolHistoryTool {
	return &ToolHistoryTool{
		provider: provider,
	}
}

// Name returns the tool name
func (t *ToolHistoryTool) Name() string {
	return "tool_history"
}

// Description returns the tool description
func (t *ToolHistoryTool) Description() string {
	return "Get recent tool execution history for debugging and auditing"
}

// Execute retrieves tool execution history
func (t *ToolHistoryTool) Execute(ctx context.Context, input interface{}) (interface{}, error) {
	params := struct {
		Limit int `json:"limit"`
	}{
		Limit: 10, // Default to last 10 executions
	}

	// Parse input
	if inputMap, ok := input.(map[string]interface{}); ok {
		if limit, ok := inputMap["limit"].(float64); ok {
			params.Limit = int(limit)
		}
	}

	// Get history
	history := t.provider.GetToolHistory(params.Limit)

	// Format response
	response := map[string]interface{}{
		"count":      len(history),
		"executions": history,
		"summary":    t.generateSummary(history),
	}

	return response, nil
}

// InputSchema returns the JSON schema for the input
func (t *ToolHistoryTool) InputSchema() json.RawMessage {
	return json.RawMessage(`{
		"type": "object",
		"properties": {
			"limit": {
				"type": "integer",
				"description": "Number of recent executions to retrieve",
				"default": 10,
				"minimum": 1,
				"maximum": 100
			}
		}
	}`)
}

// generateSummary creates a summary of tool usage
func (t *ToolHistoryTool) generateSummary(history []ToolExecution) map[string]interface{} {
	if len(history) == 0 {
		return map[string]interface{}{
			"message": "No tool executions recorded",
		}
	}

	// Count by tool name
	toolCounts := make(map[string]int)
	successCount := 0
	totalResultSize := 0

	for _, exec := range history {
		toolCounts[exec.Name]++
		if exec.Success {
			successCount++
			totalResultSize += exec.ResultLen
		}
	}

	// Find most recent execution
	var mostRecent time.Time
	for _, exec := range history {
		if exec.Timestamp.After(mostRecent) {
			mostRecent = exec.Timestamp
		}
	}

	return map[string]interface{}{
		"total_executions": len(history),
		"successful":       successCount,
		"failed":           len(history) - successCount,
		"tool_usage":       toolCounts,
		"most_recent":      mostRecent.Format(time.RFC3339),
		"avg_result_size":  totalResultSize / len(history),
		"time_span":        fmt.Sprintf("%.0f minutes", time.Since(history[len(history)-1].Timestamp).Minutes()),
	}
}
