package time

import (
	"context"
	"encoding/json"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestCurrentTimeTool(t *testing.T) {
	tool := NewCurrentTimeTool()
	ctx := context.Background()

	tests := []struct {
		name        string
		input       any
		validateRes func(t *testing.T, result any, err error)
	}{
		{
			name:  "default format and timezone",
			input: nil,
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, "RFC3339", res["format"])
				assert.NotEmpty(t, res["time"])
				assert.NotEmpty(t, res["timezone"])
				// Verify it's a valid RFC3339 time
				_, err = time.Parse(time.RFC3339, res["time"].(string))
				assert.NoError(t, err)
			},
		},
		{
			name: "RFC822 format",
			input: map[string]any{
				"format": "RFC822",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, "RFC822", res["format"])
				// Verify it's a valid RFC822 time
				_, err = time.Parse(time.RFC822, res["time"].(string))
				assert.NoError(t, err)
			},
		},
		{
			name: "Kitchen format",
			input: map[string]any{
				"format": "Kitchen",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, "Kitchen", res["format"])
				// Kitchen format example: "3:04PM"
				timeStr := res["time"].(string)
				assert.Contains(t, timeStr, ":")
				assert.Contains(t, []string{"AM", "PM"}, timeStr[len(timeStr)-2:])
			},
		},
		{
			name: "UTC timezone",
			input: map[string]any{
				"timezone": "UTC",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, "UTC", res["timezone"])
			},
		},
		{
			name: "specific timezone",
			input: map[string]any{
				"timezone": "America/New_York",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, "America/New_York", res["timezone"])
			},
		},
		{
			name: "invalid timezone",
			input: map[string]any{
				"timezone": "Invalid/Timezone",
			},
			validateRes: func(t *testing.T, result any, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "invalid timezone")
			},
		},
		{
			name: "Unix format",
			input: map[string]any{
				"format": "Unix",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, "Unix", res["format"])
				// Unix timestamp should be numeric
				timeStr := res["time"].(string)
				assert.Regexp(t, `^\d+$`, timeStr)
			},
		},
		{
			name: "UnixMilli format",
			input: map[string]any{
				"format": "UnixMilli",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, "UnixMilli", res["format"])
				// Unix millisecond timestamp should be numeric
				timeStr := res["time"].(string)
				assert.Regexp(t, `^\d+$`, timeStr)
			},
		},
		{
			name: "ISO8601 format",
			input: map[string]any{
				"format": "ISO8601",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, "ISO8601", res["format"])
				// Verify ISO8601 format
				_, err = time.Parse("2006-01-02T15:04:05Z07:00", res["time"].(string))
				assert.NoError(t, err)
			},
		},
		{
			name: "Date only format",
			input: map[string]any{
				"format": "Date",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, "Date", res["format"])
				// Date format: "2006-01-02"
				assert.Regexp(t, `^\d{4}-\d{2}-\d{2}$`, res["time"].(string))
			},
		},
		{
			name: "Time only format",
			input: map[string]any{
				"format": "Time",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, "Time", res["format"])
				// Time format: "15:04:05"
				assert.Regexp(t, `^\d{2}:\d{2}:\d{2}$`, res["time"].(string))
			},
		},
		{
			name: "DateTime format",
			input: map[string]any{
				"format": "DateTime",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, "DateTime", res["format"])
				// DateTime format: "2006-01-02 15:04:05"
				assert.Regexp(t, `^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$`, res["time"].(string))
			},
		},
		{
			name: "custom format",
			input: map[string]any{
				"format": "Jan 2, 2006",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, "Jan 2, 2006", res["format"])
				// Verify custom format
				_, err = time.Parse("Jan 2, 2006", res["time"].(string))
				assert.NoError(t, err)
			},
		},
		{
			name: "UnixDate format",
			input: map[string]any{
				"format": "UnixDate",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, "UnixDate", res["format"])
				_, err = time.Parse(time.UnixDate, res["time"].(string))
				assert.NoError(t, err)
			},
		},
		{
			name: "Stamp format",
			input: map[string]any{
				"format": "Stamp",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, "Stamp", res["format"])
				// Stamp format doesn't include year, so we can't parse it back
				assert.NotEmpty(t, res["time"])
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := tool.Execute(ctx, tt.input)
			tt.validateRes(t, result, err)
		})
	}
}

func TestCurrentTimeTool_Metadata(t *testing.T) {
	tool := NewCurrentTimeTool()

	assert.Equal(t, "current_time", tool.Name())
	assert.Contains(t, tool.Description(), "Get current time")

	// Verify schema is valid JSON
	var schema map[string]any
	err := json.Unmarshal(tool.InputSchema(), &schema)
	assert.NoError(t, err)
	assert.Equal(t, "object", schema["type"])
}

func TestTimeDiffTool(t *testing.T) {
	tool := NewTimeDiffTool()
	ctx := context.Background()

	// Fixed time for testing
	fixedTime := time.Date(2024, 1, 15, 10, 30, 0, 0, time.UTC)
	oneHourLater := fixedTime.Add(time.Hour)
	oneDayLater := fixedTime.Add(24 * time.Hour)

	tests := []struct {
		name        string
		input       any
		validateRes func(t *testing.T, result any, err error)
	}{
		{
			name:  "missing from time",
			input: map[string]any{},
			validateRes: func(t *testing.T, result any, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "'from' time is required")
			},
		},
		{
			name: "invalid from time",
			input: map[string]any{
				"from": "invalid-time",
			},
			validateRes: func(t *testing.T, result any, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "invalid 'from' time")
			},
		},
		{
			name: "invalid to time",
			input: map[string]any{
				"from": fixedTime.Format(time.RFC3339),
				"to":   "invalid-time",
			},
			validateRes: func(t *testing.T, result any, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "invalid 'to' time")
			},
		},
		{
			name: "one hour difference",
			input: map[string]any{
				"from": fixedTime.Format(time.RFC3339),
				"to":   oneHourLater.Format(time.RFC3339),
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, 3600.0, res["seconds"])
				assert.Equal(t, 60.0, res["minutes"])
				assert.Equal(t, 1.0, res["hours"])
				assert.Equal(t, "1.0 hours", res["result"])
			},
		},
		{
			name: "one day difference",
			input: map[string]any{
				"from": fixedTime.Format(time.RFC3339),
				"to":   oneDayLater.Format(time.RFC3339),
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, 24.0, res["hours"])
				assert.Equal(t, 1.0, res["days"])
				assert.Equal(t, "1.0 days", res["result"])
			},
		},
		{
			name: "specific unit - seconds",
			input: map[string]any{
				"from": fixedTime.Format(time.RFC3339),
				"to":   oneHourLater.Format(time.RFC3339),
				"unit": "seconds",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, 3600.0, res["result"])
			},
		},
		{
			name: "specific unit - minutes",
			input: map[string]any{
				"from": fixedTime.Format(time.RFC3339),
				"to":   oneHourLater.Format(time.RFC3339),
				"unit": "minutes",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, 60.0, res["result"])
			},
		},
		{
			name: "specific unit - hours",
			input: map[string]any{
				"from": fixedTime.Format(time.RFC3339),
				"to":   oneDayLater.Format(time.RFC3339),
				"unit": "hours",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, 24.0, res["result"])
			},
		},
		{
			name: "specific unit - days",
			input: map[string]any{
				"from": fixedTime.Format(time.RFC3339),
				"to":   oneDayLater.Format(time.RFC3339),
				"unit": "days",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, 1.0, res["result"])
			},
		},
		{
			name: "specific unit - milliseconds",
			input: map[string]any{
				"from": fixedTime.Format(time.RFC3339),
				"to":   fixedTime.Add(500 * time.Millisecond).Format(time.RFC3339Nano),
				"unit": "milliseconds",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, int64(500), res["result"])
			},
		},
		{
			name: "30 seconds difference - auto format",
			input: map[string]any{
				"from": fixedTime.Format(time.RFC3339),
				"to":   fixedTime.Add(30 * time.Second).Format(time.RFC3339),
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, "30 seconds", res["result"])
			},
		},
		{
			name: "30 minutes difference - auto format",
			input: map[string]any{
				"from": fixedTime.Format(time.RFC3339),
				"to":   fixedTime.Add(30 * time.Minute).Format(time.RFC3339),
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, "30.0 minutes", res["result"])
			},
		},
		{
			name: "relative time - yesterday",
			input: map[string]any{
				"from": "yesterday",
				"to":   "today",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				// Should be approximately 24 hours
				hours := res["hours"].(float64)
				assert.InDelta(t, 24.0, hours, 1.0)
			},
		},
		{
			name: "relative time - duration",
			input: map[string]any{
				"from": "-1h",
				"to":   "now",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				// Should be approximately 1 hour
				hours := res["hours"].(float64)
				assert.InDelta(t, 1.0, hours, 0.1)
			},
		},
		{
			name: "tomorrow",
			input: map[string]any{
				"from": "today",
				"to":   "tomorrow",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				days := res["days"].(float64)
				assert.InDelta(t, 1.0, days, 0.1)
			},
		},
		{
			name: "date only format",
			input: map[string]any{
				"from": "2024-01-01",
				"to":   "2024-01-15",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, 14.0, res["days"])
			},
		},
		{
			name: "time only format",
			input: map[string]any{
				"from": "10:30:00",
				"to":   "11:30:00",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, 1.0, res["hours"])
			},
		},
		{
			name: "ISO8601 format",
			input: map[string]any{
				"from": "2024-01-15T10:30:00Z",
				"to":   "2024-01-15T11:30:00Z",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				assert.Equal(t, 1.0, res["hours"])
			},
		},
		{
			name: "default to now",
			input: map[string]any{
				"from": "-30m",
			},
			validateRes: func(t *testing.T, result any, err error) {
				require.NoError(t, err)
				res := result.(map[string]any)
				// Should be approximately 30 minutes
				minutes := res["minutes"].(float64)
				assert.InDelta(t, 30.0, minutes, 1.0)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := tool.Execute(ctx, tt.input)
			tt.validateRes(t, result, err)
		})
	}
}

func TestTimeDiffTool_Metadata(t *testing.T) {
	tool := NewTimeDiffTool()

	assert.Equal(t, "time_diff", tool.Name())
	assert.Contains(t, tool.Description(), "Calculate the difference")

	// Verify schema is valid JSON
	var schema map[string]any
	err := json.Unmarshal(tool.InputSchema(), &schema)
	assert.NoError(t, err)
	assert.Equal(t, "object", schema["type"])

	// Check required fields
	required := schema["required"].([]any)
	assert.Contains(t, required, "from")
}

func TestParseTime(t *testing.T) {
	tests := []struct {
		name    string
		input   string
		wantErr bool
		check   func(t *testing.T, result time.Time)
	}{
		{
			name:    "RFC3339",
			input:   "2024-01-15T10:30:00Z",
			wantErr: false,
			check: func(t *testing.T, result time.Time) {
				assert.Equal(t, 2024, result.Year())
				assert.Equal(t, time.January, result.Month())
				assert.Equal(t, 15, result.Day())
			},
		},
		{
			name:    "RFC3339Nano",
			input:   "2024-01-15T10:30:00.123456789Z",
			wantErr: false,
		},
		{
			name:    "date time with space",
			input:   "2024-01-15 10:30:00",
			wantErr: false,
		},
		{
			name:    "date only",
			input:   "2024-01-15",
			wantErr: false,
		},
		{
			name:    "time only",
			input:   "10:30:00",
			wantErr: false,
		},
		{
			name:    "now",
			input:   "now",
			wantErr: false,
			check: func(t *testing.T, result time.Time) {
				assert.WithinDuration(t, time.Now(), result, time.Second)
			},
		},
		{
			name:    "today",
			input:   "today",
			wantErr: false,
			check: func(t *testing.T, result time.Time) {
				now := time.Now()
				assert.Equal(t, now.Year(), result.Year())
				assert.Equal(t, now.Month(), result.Month())
				assert.Equal(t, now.Day(), result.Day())
				assert.Equal(t, 0, result.Hour())
				assert.Equal(t, 0, result.Minute())
			},
		},
		{
			name:    "yesterday",
			input:   "yesterday",
			wantErr: false,
			check: func(t *testing.T, result time.Time) {
				yesterday := time.Now().AddDate(0, 0, -1)
				assert.Equal(t, yesterday.Year(), result.Year())
				assert.Equal(t, yesterday.Month(), result.Month())
				assert.Equal(t, yesterday.Day(), result.Day())
			},
		},
		{
			name:    "tomorrow",
			input:   "tomorrow",
			wantErr: false,
			check: func(t *testing.T, result time.Time) {
				tomorrow := time.Now().AddDate(0, 0, 1)
				assert.Equal(t, tomorrow.Year(), result.Year())
				assert.Equal(t, tomorrow.Month(), result.Month())
				assert.Equal(t, tomorrow.Day(), result.Day())
			},
		},
		{
			name:    "duration -1h",
			input:   "-1h",
			wantErr: false,
			check: func(t *testing.T, result time.Time) {
				assert.WithinDuration(t, time.Now().Add(-time.Hour), result, time.Second)
			},
		},
		{
			name:    "duration +30m",
			input:   "+30m",
			wantErr: false,
			check: func(t *testing.T, result time.Time) {
				assert.WithinDuration(t, time.Now().Add(30*time.Minute), result, time.Second)
			},
		},
		{
			name:    "invalid format",
			input:   "not-a-time",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := parseTime(tt.input)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				if tt.check != nil {
					tt.check(t, result)
				}
			}
		})
	}
}

// Fuzzing test for time parsing
func FuzzParseTime(f *testing.F) {
	// Add seed corpus
	f.Add("2024-01-15T10:30:00Z")
	f.Add("2024-01-15")
	f.Add("10:30:00")
	f.Add("now")
	f.Add("today")
	f.Add("yesterday")
	f.Add("-1h")
	f.Add("+30m")
	f.Add("invalid")
	f.Add("")
	f.Add(strings.Repeat("2024-", 100))

	f.Fuzz(func(t *testing.T, input string) {
		// Should not panic
		_, _ = parseTime(input)
	})
}

// Fuzzing test for CurrentTimeTool
func FuzzCurrentTimeTool(f *testing.F) {
	// Add seed corpus
	f.Add(`{"format": "RFC3339", "timezone": "UTC"}`)
	f.Add(`{"format": "Unix"}`)
	f.Add(`{"timezone": "America/New_York"}`)
	f.Add(`{}`)
	f.Add(`null`)
	f.Add(`{"format": ""}`)
	f.Add(`{"format": "custom format", "timezone": "invalid"}`)

	tool := NewCurrentTimeTool()
	ctx := context.Background()

	f.Fuzz(func(t *testing.T, inputJSON string) {
		var input map[string]any
		if inputJSON != "null" && inputJSON != "" {
			json.Unmarshal([]byte(inputJSON), &input)
		}

		// Should not panic
		_, _ = tool.Execute(ctx, input)
	})
}

// Benchmark tests
func BenchmarkCurrentTimeTool(b *testing.B) {
	tool := NewCurrentTimeTool()
	ctx := context.Background()
	input := map[string]any{
		"format":   "RFC3339",
		"timezone": "UTC",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = tool.Execute(ctx, input)
	}
}

func BenchmarkTimeDiffTool(b *testing.B) {
	tool := NewTimeDiffTool()
	ctx := context.Background()
	now := time.Now()
	input := map[string]any{
		"from": now.Add(-time.Hour).Format(time.RFC3339),
		"to":   now.Format(time.RFC3339),
		"unit": "minutes",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = tool.Execute(ctx, input)
	}
}

func BenchmarkParseTime(b *testing.B) {
	inputs := []string{
		"2024-01-15T10:30:00Z",
		"2024-01-15",
		"now",
		"-1h",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = parseTime(inputs[i%len(inputs)])
	}
}
