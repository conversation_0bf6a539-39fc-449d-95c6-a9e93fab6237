// Package time provides time and date utility tools.
package time

import (
	"context"
	"encoding/json"
	"fmt"
	"time"
)

// CurrentTimeTool provides current time in various formats.
type CurrentTimeTool struct{}

// NewCurrentTimeTool creates a new current time tool.
func NewCurrentTimeTool() *CurrentTimeTool {
	return &CurrentTimeTool{}
}

// Name returns the tool name.
func (t *CurrentTimeTool) Name() string {
	return "current_time"
}

// Description returns the tool description.
func (t *CurrentTimeTool) Description() string {
	return "Get current time, supports various formats and timezones. Use for answering questions like \"What time is it?\" or \"What's the current time?\""
}

// Execute returns the current time.
func (t *CurrentTimeTool) Execute(ctx context.Context, input any) (any, error) {
	params := struct {
		Format   string `json:"format,omitempty"`
		Timezone string `json:"timezone,omitempty"`
	}{
		Format:   "RFC3339",
		Timezone: "Local",
	}

	// Parse input
	if inputMap, ok := input.(map[string]any); ok {
		if format, ok := inputMap["format"].(string); ok {
			params.Format = format
		}
		if tz, ok := inputMap["timezone"].(string); ok {
			params.Timezone = tz
		}
	}

	// Get current time
	now := time.Now()

	// Apply timezone if specified
	if params.Timezone != "" && params.Timezone != "Local" {
		loc, err := time.LoadLocation(params.Timezone)
		if err != nil {
			return nil, fmt.Errorf("invalid timezone %s: %w", params.Timezone, err)
		}
		now = now.In(loc)
	}

	// Format time based on requested format
	var result string
	switch params.Format {
	case "RFC3339":
		result = now.Format(time.RFC3339)
	case "RFC822":
		result = now.Format(time.RFC822)
	case "UnixDate":
		result = now.Format(time.UnixDate)
	case "Kitchen":
		result = now.Format(time.Kitchen)
	case "Stamp":
		result = now.Format(time.Stamp)
	case "ISO8601":
		result = now.Format("2006-01-02T15:04:05Z07:00")
	case "Date":
		result = now.Format("2006-01-02")
	case "Time":
		result = now.Format("15:04:05")
	case "DateTime":
		result = now.Format("2006-01-02 15:04:05")
	case "Unix":
		result = fmt.Sprintf("%d", now.Unix())
	case "UnixMilli":
		result = fmt.Sprintf("%d", now.UnixMilli())
	default:
		// Try to use the format string directly
		result = now.Format(params.Format)
	}

	return map[string]any{
		"time":     result,
		"timezone": now.Location().String(),
		"format":   params.Format,
	}, nil
}

// InputSchema returns the JSON schema for the input.
func (t *CurrentTimeTool) InputSchema() json.RawMessage {
	return json.RawMessage(`{
		"type": "object",
		"properties": {
			"format": {
				"type": "string",
				"description": "Time format: RFC3339, RFC822, UnixDate, Kitchen, ISO8601, Unix, or custom format",
				"default": "RFC3339"
			},
			"timezone": {
				"type": "string",
				"description": "Timezone name (e.g., 'America/New_York', 'UTC', 'Local')",
				"default": "Local"
			}
		}
	}`)
}

// DiffTool calculates time differences.
type DiffTool struct{}

// NewTimeDiffTool creates a new time difference tool.
func NewTimeDiffTool() *DiffTool {
	return &DiffTool{}
}

// Name returns the tool name.
func (t *DiffTool) Name() string {
	return "time_diff"
}

// Description returns the tool description.
func (t *DiffTool) Description() string {
	return "Calculate the difference between two times or dates"
}

// Execute calculates time difference.
func (t *DiffTool) Execute(ctx context.Context, input any) (any, error) {
	params := struct {
		From string `json:"from"`
		To   string `json:"to,omitempty"`
		Unit string `json:"unit,omitempty"`
	}{
		Unit: "auto",
	}

	// Parse input
	if inputMap, ok := input.(map[string]any); ok {
		if from, ok := inputMap["from"].(string); ok {
			params.From = from
		}
		if to, ok := inputMap["to"].(string); ok {
			params.To = to
		}
		if unit, ok := inputMap["unit"].(string); ok {
			params.Unit = unit
		}
	}

	if params.From == "" {
		return nil, fmt.Errorf("'from' time is required")
	}

	// Parse from time
	fromTime, err := parseTime(params.From)
	if err != nil {
		return nil, fmt.Errorf("invalid 'from' time: %w", err)
	}

	// Parse to time (default to now)
	var toTime time.Time
	if params.To == "" {
		toTime = time.Now()
	} else {
		toTime, err = parseTime(params.To)
		if err != nil {
			return nil, fmt.Errorf("invalid 'to' time: %w", err)
		}
	}

	// Calculate difference
	diff := toTime.Sub(fromTime)

	result := map[string]any{
		"from":         fromTime.Format(time.RFC3339),
		"to":           toTime.Format(time.RFC3339),
		"duration":     diff.String(),
		"seconds":      diff.Seconds(),
		"minutes":      diff.Minutes(),
		"hours":        diff.Hours(),
		"days":         diff.Hours() / 24,
		"milliseconds": diff.Milliseconds(),
	}

	// Add specific unit if requested
	switch params.Unit {
	case "seconds":
		result["result"] = diff.Seconds()
	case "minutes":
		result["result"] = diff.Minutes()
	case "hours":
		result["result"] = diff.Hours()
	case "days":
		result["result"] = diff.Hours() / 24
	case "milliseconds":
		result["result"] = diff.Milliseconds()
	default:
		// Auto format
		if diff < time.Minute {
			result["result"] = fmt.Sprintf("%.0f seconds", diff.Seconds())
		} else if diff < time.Hour {
			result["result"] = fmt.Sprintf("%.1f minutes", diff.Minutes())
		} else if diff < 24*time.Hour {
			result["result"] = fmt.Sprintf("%.1f hours", diff.Hours())
		} else {
			result["result"] = fmt.Sprintf("%.1f days", diff.Hours()/24)
		}
	}

	return result, nil
}

// InputSchema returns the JSON schema for the input.
func (t *DiffTool) InputSchema() json.RawMessage {
	return json.RawMessage(`{
		"type": "object",
		"properties": {
			"from": {
				"type": "string",
				"description": "Start time (ISO8601, RFC3339, or relative like '-1h', 'yesterday')"
			},
			"to": {
				"type": "string",
				"description": "End time (defaults to now)"
			},
			"unit": {
				"type": "string",
				"enum": ["auto", "seconds", "minutes", "hours", "days", "milliseconds"],
				"description": "Output unit",
				"default": "auto"
			}
		},
		"required": ["from"]
	}`)
}

// parseTime parses various time formats including relative times.
func parseTime(s string) (time.Time, error) {
	// Try standard formats
	formats := []string{
		time.RFC3339,
		time.RFC3339Nano,
		"2006-01-02T15:04:05Z",
		"2006-01-02 15:04:05",
		"2006-01-02",
		"15:04:05",
	}

	for _, format := range formats {
		if t, err := time.Parse(format, s); err == nil {
			return t, nil
		}
	}

	// Try relative times
	now := time.Now()
	switch s {
	case "now":
		return now, nil
	case "today":
		return time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()), nil
	case "yesterday":
		return time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).AddDate(0, 0, -1), nil
	case "tomorrow":
		return time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).AddDate(0, 0, 1), nil
	}

	// Try duration format (e.g., "-1h", "+30m")
	if d, err := time.ParseDuration(s); err == nil {
		return now.Add(d), nil
	}

	return time.Time{}, fmt.Errorf("unable to parse time: %s", s)
}
