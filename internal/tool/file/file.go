// Package filesystem provides comprehensive file system tools.
package file

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
)

// ListDirectoryTool lists files and directories.
type ListDirectoryTool struct {
	allowedDirs []string
}

// NewListDirectoryTool creates a new directory listing tool.
func NewListDirectoryTool(allowedDirs []string) *ListDirectoryTool {
	return &ListDirectoryTool{
		allowedDirs: allowedDirs,
	}
}

// Name returns the tool name.
func (t *ListDirectoryTool) Name() string {
	return "list_directory"
}

// Description returns the tool description.
func (t *ListDirectoryTool) Description() string {
	return "List files and directories in a given path"
}

// Execute lists directory contents.
func (t *ListDirectoryTool) Execute(ctx context.Context, input any) (any, error) {
	params := struct {
		Path      string `json:"path"`
		Recursive bool   `json:"recursive,omitempty"`
		Pattern   string `json:"pattern,omitempty"`
	}{}

	// Parse input
	if inputMap, ok := input.(map[string]any); ok {
		if path, ok := inputMap["path"].(string); ok {
			params.Path = path
		}
		if recursive, ok := inputMap["recursive"].(bool); ok {
			params.Recursive = recursive
		}
		if pattern, ok := inputMap["pattern"].(string); ok {
			params.Pattern = pattern
		}
	}

	if params.Path == "" {
		return nil, fmt.Errorf("path is required")
	}

	// Security check
	if !t.isAllowed(params.Path) {
		return nil, fmt.Errorf("access denied: path is outside allowed directories")
	}

	// Clean path
	cleanPath := filepath.Clean(params.Path)

	// Check if path exists
	info, err := os.Stat(cleanPath)
	if err != nil {
		return nil, fmt.Errorf("path error: %w", err)
	}

	if !info.IsDir() {
		return nil, fmt.Errorf("path is not a directory")
	}

	// List directory
	var entries []map[string]any

	if params.Recursive {
		if err = filepath.Walk(cleanPath, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return nil // Skip errors
			}

			// Apply pattern filter if specified
			if params.Pattern != "" {
				matched, _ := filepath.Match(params.Pattern, filepath.Base(path))
				if !matched {
					return nil
				}
			}

			relPath, _ := filepath.Rel(cleanPath, path)
			if relPath == "." {
				return nil // Skip root
			}

			entries = append(entries, fileInfo(path, info))
			return nil
		}); err != nil {
			return nil, fmt.Errorf("walk directory: %w", err)
		}
	} else {
		files, err := os.ReadDir(cleanPath)
		if err != nil {
			return nil, fmt.Errorf("read directory: %w", err)
		}

		for _, file := range files {
			// Apply pattern filter if specified
			if params.Pattern != "" {
				matched, _ := filepath.Match(params.Pattern, file.Name())
				if !matched {
					continue
				}
			}

			info, err := file.Info()
			if err != nil {
				continue
			}

			fullPath := filepath.Join(cleanPath, file.Name())
			entries = append(entries, fileInfo(fullPath, info))
		}
	}

	return map[string]any{
		"path":    cleanPath,
		"entries": entries,
		"count":   len(entries),
	}, nil
}

// InputSchema returns the JSON schema for the input.
func (t *ListDirectoryTool) InputSchema() json.RawMessage {
	return json.RawMessage(`{
		"type": "object",
		"properties": {
			"path": {
				"type": "string",
				"description": "Directory path to list"
			},
			"recursive": {
				"type": "boolean",
				"description": "List directories recursively",
				"default": false
			},
			"pattern": {
				"type": "string",
				"description": "File pattern to match (e.g., '*.txt')"
			}
		},
		"required": ["path"]
	}`)
}

// ReadFileTool reads file contents.
type ReadFileTool struct {
	allowedDirs []string
	maxFileSize int64
}

// NewReadFileTool creates a new file reading tool.
func NewReadFileTool(allowedDirs []string, maxFileSize int64) *ReadFileTool {
	if maxFileSize == 0 {
		maxFileSize = 10 * 1024 * 1024 // 10MB default
	}
	return &ReadFileTool{
		allowedDirs: allowedDirs,
		maxFileSize: maxFileSize,
	}
}

// Name returns the tool name.
func (t *ReadFileTool) Name() string {
	return "read_file"
}

// Description returns the tool description.
func (t *ReadFileTool) Description() string {
	return "Read the contents of a file"
}

// Execute reads file contents.
func (t *ReadFileTool) Execute(ctx context.Context, input any) (any, error) {
	params := struct {
		Path     string `json:"path"`
		Encoding string `json:"encoding,omitempty"`
	}{
		Encoding: "utf-8",
	}

	// Parse input
	if inputMap, ok := input.(map[string]any); ok {
		if path, ok := inputMap["path"].(string); ok {
			params.Path = path
		}
		if encoding, ok := inputMap["encoding"].(string); ok {
			params.Encoding = encoding
		}
	}

	if params.Path == "" {
		return nil, fmt.Errorf("path is required")
	}

	// Security check
	if !t.isAllowed(params.Path) {
		return nil, fmt.Errorf("access denied: path is outside allowed directories")
	}

	// Clean path
	cleanPath := filepath.Clean(params.Path)

	// Check file info
	info, err := os.Stat(cleanPath)
	if err != nil {
		return nil, fmt.Errorf("file error: %w", err)
	}

	if info.IsDir() {
		return nil, fmt.Errorf("path is a directory, not a file")
	}

	if info.Size() > t.maxFileSize {
		return nil, fmt.Errorf("file too large: %d bytes (max: %d)", info.Size(), t.maxFileSize)
	}

	// Read file
	content, err := os.ReadFile(cleanPath)
	if err != nil {
		return nil, fmt.Errorf("read file: %w", err)
	}

	return map[string]any{
		"path":     cleanPath,
		"content":  string(content),
		"size":     info.Size(),
		"modified": info.ModTime().Format("2006-01-02T15:04:05Z07:00"),
		"encoding": params.Encoding,
	}, nil
}

// InputSchema returns the JSON schema for the input.
func (t *ReadFileTool) InputSchema() json.RawMessage {
	return json.RawMessage(`{
		"type": "object",
		"properties": {
			"path": {
				"type": "string",
				"description": "File path to read"
			},
			"encoding": {
				"type": "string",
				"description": "File encoding",
				"default": "utf-8"
			}
		},
		"required": ["path"]
	}`)
}

// WriteFileTool writes content to files.
type WriteFileTool struct {
	allowedDirs []string
	createDirs  bool
}

// NewWriteFileTool creates a new file writing tool.
func NewWriteFileTool(allowedDirs []string, createDirs bool) *WriteFileTool {
	return &WriteFileTool{
		allowedDirs: allowedDirs,
		createDirs:  createDirs,
	}
}

// Name returns the tool name.
func (t *WriteFileTool) Name() string {
	return "write_file"
}

// Description returns the tool description.
func (t *WriteFileTool) Description() string {
	return "Write content to a file"
}

// Execute writes file contents.
func (t *WriteFileTool) Execute(ctx context.Context, input any) (any, error) {
	params := struct {
		Path    string `json:"path"`
		Content string `json:"content"`
		Mode    string `json:"mode,omitempty"`
	}{
		Mode: "overwrite",
	}

	// Parse input
	if inputMap, ok := input.(map[string]any); ok {
		if path, ok := inputMap["path"].(string); ok {
			params.Path = path
		}
		if content, ok := inputMap["content"].(string); ok {
			params.Content = content
		}
		if mode, ok := inputMap["mode"].(string); ok {
			params.Mode = mode
		}
	}

	if params.Path == "" {
		return nil, fmt.Errorf("path is required")
	}

	// Security check
	if !t.isAllowed(params.Path) {
		return nil, fmt.Errorf("access denied: path is outside allowed directories")
	}

	// Clean path
	cleanPath := filepath.Clean(params.Path)

	// Create directory if needed
	dir := filepath.Dir(cleanPath)
	if t.createDirs {
		if err := os.MkdirAll(dir, 0o700); err != nil {
			return nil, fmt.Errorf("create directory: %w", err)
		}
	}

	// Determine write flags
	var flags int
	switch params.Mode {
	case "append":
		flags = os.O_CREATE | os.O_WRONLY | os.O_APPEND
	case "overwrite":
		flags = os.O_CREATE | os.O_WRONLY | os.O_TRUNC
	default:
		return nil, fmt.Errorf("invalid mode: %s (use 'overwrite' or 'append')", params.Mode)
	}

	// Open file
	file, err := os.OpenFile(cleanPath, flags, 0o600) // #nosec G304 - path is validated by isAllowed() and cleaned
	if err != nil {
		return nil, fmt.Errorf("open file: %w", err)
	}
	defer file.Close()

	// Write content
	n, err := io.WriteString(file, params.Content)
	if err != nil {
		return nil, fmt.Errorf("write file: %w", err)
	}

	// Get file info
	info, _ := file.Stat()

	return map[string]any{
		"path":    cleanPath,
		"written": n,
		"mode":    params.Mode,
		"size":    info.Size(),
	}, nil
}

// InputSchema returns the JSON schema for the input.
func (t *WriteFileTool) InputSchema() json.RawMessage {
	return json.RawMessage(`{
		"type": "object",
		"properties": {
			"path": {
				"type": "string",
				"description": "File path to write"
			},
			"content": {
				"type": "string",
				"description": "Content to write to the file"
			},
			"mode": {
				"type": "string",
				"enum": ["overwrite", "append"],
				"description": "Write mode",
				"default": "overwrite"
			}
		},
		"required": ["path", "content"]
	}`)
}

// Common helper functions

// isAllowed checks if a path is within allowed directories.
func (t *ListDirectoryTool) isAllowed(path string) bool {
	if len(t.allowedDirs) == 0 {
		return true // No restrictions
	}

	absPath, err := filepath.Abs(path)
	if err != nil {
		return false
	}

	for _, allowed := range t.allowedDirs {
		absAllowed, err := filepath.Abs(allowed)
		if err != nil {
			continue
		}

		// Check if path is within allowed directory
		rel, err := filepath.Rel(absAllowed, absPath)
		if err != nil {
			continue
		}

		// If relative path doesn't start with "..", it's within allowed
		if !strings.HasPrefix(rel, "..") {
			return true
		}
	}

	return false
}

// isAllowed for ReadFileTool
func (t *ReadFileTool) isAllowed(path string) bool {
	if len(t.allowedDirs) == 0 {
		return true
	}

	absPath, err := filepath.Abs(path)
	if err != nil {
		return false
	}

	for _, allowed := range t.allowedDirs {
		absAllowed, err := filepath.Abs(allowed)
		if err != nil {
			continue
		}

		rel, err := filepath.Rel(absAllowed, absPath)
		if err != nil {
			continue
		}

		if !strings.HasPrefix(rel, "..") {
			return true
		}
	}

	return false
}

// isAllowed for WriteFileTool
func (t *WriteFileTool) isAllowed(path string) bool {
	if len(t.allowedDirs) == 0 {
		return true
	}

	absPath, err := filepath.Abs(path)
	if err != nil {
		return false
	}

	for _, allowed := range t.allowedDirs {
		absAllowed, err := filepath.Abs(allowed)
		if err != nil {
			continue
		}

		rel, err := filepath.Rel(absAllowed, absPath)
		if err != nil {
			continue
		}

		if !strings.HasPrefix(rel, "..") {
			return true
		}
	}

	return false
}

// fileInfo creates a file info map.
func fileInfo(path string, info os.FileInfo) map[string]any {
	return map[string]any{
		"name":     info.Name(),
		"path":     path,
		"size":     info.Size(),
		"isDir":    info.IsDir(),
		"modified": info.ModTime().Format("2006-01-02T15:04:05Z07:00"),
		"mode":     info.Mode().String(),
	}
}
