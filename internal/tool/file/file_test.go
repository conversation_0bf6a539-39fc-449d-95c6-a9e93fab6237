package file

import (
	"context"
	"os"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestReadFileTool(t *testing.T) {
	// Create temp file
	tmpDir := t.TempDir()
	testFile := filepath.Join(tmpDir, "test.txt")
	testContent := "Hello, Go testing!"

	err := os.WriteFile(testFile, []byte(testContent), 0o600)
	require.NoError(t, err)

	// Test cases
	tests := []struct {
		name    string
		input   map[string]any
		wantErr bool
		check   func(t *testing.T, result any)
	}{
		{
			name: "read existing file",
			input: map[string]any{
				"path": testFile,
			},
			wantErr: false,
			check: func(t *testing.T, result any) {
				res, ok := result.(map[string]any)
				require.True(t, ok)
				assert.Equal(t, testContent, res["content"])
				assert.Equal(t, testFile, res["path"])
			},
		},
		{
			name: "read non-existent file",
			input: map[string]any{
				"path": filepath.Join(tmpDir, "missing.txt"),
			},
			wantErr: true,
		},
		{
			name:    "missing path parameter",
			input:   map[string]any{},
			wantErr: true,
		},
	}

	tool := NewReadFileTool(nil, 0)

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := tool.Execute(context.Background(), tt.input)

			if tt.wantErr {
				assert.Error(t, err)
				return
			}

			require.NoError(t, err)
			if tt.check != nil {
				tt.check(t, result)
			}
		})
	}
}

func TestWriteFileTool(t *testing.T) {
	tmpDir := t.TempDir()

	tests := []struct {
		name    string
		input   map[string]any
		wantErr bool
		check   func(t *testing.T, result any)
	}{
		{
			name: "write new file",
			input: map[string]any{
				"path":    filepath.Join(tmpDir, "new.txt"),
				"content": "Test content",
				"mode":    "overwrite",
			},
			wantErr: false,
			check: func(t *testing.T, result any) {
				res, ok := result.(map[string]any)
				require.True(t, ok)
				assert.Equal(t, 12, res["written"]) // "Test content" = 12 bytes

				// Verify file exists
				content, err := os.ReadFile(filepath.Join(tmpDir, "new.txt"))
				require.NoError(t, err)
				assert.Equal(t, "Test content", string(content))
			},
		},
		{
			name: "append to file",
			input: map[string]any{
				"path":    filepath.Join(tmpDir, "append.txt"),
				"content": " appended",
				"mode":    "append",
			},
			wantErr: false,
		},
	}

	tool := NewWriteFileTool(nil, true)

	// First create a file for append test
	os.WriteFile(filepath.Join(tmpDir, "append.txt"), []byte("Original"), 0o600)

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := tool.Execute(context.Background(), tt.input)

			if tt.wantErr {
				assert.Error(t, err)
				return
			}

			require.NoError(t, err)
			if tt.check != nil {
				tt.check(t, result)
			}
		})
	}
}

func TestListDirectoryTool(t *testing.T) {
	// Create test directory structure
	tmpDir := t.TempDir()

	// Create files and subdirs
	os.WriteFile(filepath.Join(tmpDir, "file1.txt"), []byte("content1"), 0o600)
	os.WriteFile(filepath.Join(tmpDir, "file2.md"), []byte("content2"), 0o600)
	os.Mkdir(filepath.Join(tmpDir, "subdir"), 0o755)
	os.WriteFile(filepath.Join(tmpDir, "subdir", "file3.txt"), []byte("content3"), 0o600)

	tests := []struct {
		name    string
		input   map[string]any
		wantErr bool
		check   func(t *testing.T, result any)
	}{
		{
			name: "list directory non-recursive",
			input: map[string]any{
				"path": tmpDir,
			},
			wantErr: false,
			check: func(t *testing.T, result any) {
				res, ok := result.(map[string]any)
				require.True(t, ok)

				entries, ok := res["entries"].([]map[string]any)
				require.True(t, ok)
				assert.Equal(t, 3, len(entries)) // 2 files + 1 dir
			},
		},
		{
			name: "list with pattern",
			input: map[string]any{
				"path":    tmpDir,
				"pattern": "*.txt",
			},
			wantErr: false,
			check: func(t *testing.T, result any) {
				res, ok := result.(map[string]any)
				require.True(t, ok)

				entries, ok := res["entries"].([]map[string]any)
				require.True(t, ok)
				assert.Equal(t, 1, len(entries)) // Only file1.txt
			},
		},
		{
			name: "list recursive",
			input: map[string]any{
				"path":      tmpDir,
				"recursive": true,
			},
			wantErr: false,
			check: func(t *testing.T, result any) {
				res, ok := result.(map[string]any)
				require.True(t, ok)

				entries, ok := res["entries"].([]map[string]any)
				require.True(t, ok)
				assert.Equal(t, 4, len(entries)) // All files and dirs
			},
		},
	}

	tool := NewListDirectoryTool(nil)

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := tool.Execute(context.Background(), tt.input)

			if tt.wantErr {
				assert.Error(t, err)
				return
			}

			require.NoError(t, err)
			if tt.check != nil {
				tt.check(t, result)
			}
		})
	}
}

// Test security restrictions
func TestFileToolSecurity(t *testing.T) {
	tmpDir := t.TempDir()
	allowedDir := filepath.Join(tmpDir, "allowed")
	os.Mkdir(allowedDir, 0o755)

	// Create tools with restrictions
	readTool := NewReadFileTool([]string{allowedDir}, 0)
	writeTool := NewWriteFileTool([]string{allowedDir}, false)

	t.Run("read outside allowed directory", func(t *testing.T) {
		_, err := readTool.Execute(context.Background(), map[string]any{
			"path": "/etc/passwd",
		})
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "access denied")
	})

	t.Run("write outside allowed directory", func(t *testing.T) {
		_, err := writeTool.Execute(context.Background(), map[string]any{
			"path":    "/tmp/evil.txt",
			"content": "bad content",
		})
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "access denied")
	})
}
