// Package tool provides type definitions for tool execution.
package tool

import (
	"context"
	"encoding/json"
	"time"
)

// Executor is the interface that tools implement for type-safe execution.
type Executor interface {
	// ExecuteRequest executes a tool with a concrete request type
	ExecuteRequest(ctx context.Context, req *Request) (*Response, error)
}

// Request represents a standardized tool execution request.
// This replaces the generic interface{} input with concrete types.
type Request struct {
	// Common fields used across multiple tools
	Action     string        `json:"action,omitempty"`     // Tool-specific action
	Query      string        `json:"query,omitempty"`      // Search query or question
	Path       string        `json:"path,omitempty"`       // File path
	Content    string        `json:"content,omitempty"`    // Content to write or process
	Expression string        `json:"expression,omitempty"` // Mathematical expression
	Timezone   string        `json:"timezone,omitempty"`   // Timezone for datetime operations
	Format     string        `json:"format,omitempty"`     // Date/time format
	Limit      int           `json:"limit,omitempty"`      // Result limit
	Offset     int           `json:"offset,omitempty"`     // Result offset
	Days       int           `json:"days,omitempty"`       // Days to add/subtract
	URL        string        `json:"url,omitempty"`        // URL for web operations
	UserAgent  string        `json:"user_agent,omitempty"` // User agent for web requests
	Headers    Headers       `json:"headers,omitempty"`    // HTTP headers
	Timeout    time.Duration `json:"timeout,omitempty"`    // Operation timeout
}

// Headers represents HTTP headers with proper types.
type Headers map[string]string

// Response represents a standardized tool execution response.
// This replaces the generic interface{} output with concrete types.
type Response struct {
	// Success case
	Success bool            `json:"success"`
	Result  json.RawMessage `json:"result,omitempty"`

	// Error case
	Error     string `json:"error,omitempty"`
	ErrorCode string `json:"error_code,omitempty"`

	// Metadata
	Duration  time.Duration `json:"duration,omitempty"`
	Timestamp time.Time     `json:"timestamp"`
}

// Result Types for Different Tools

// DateTimeResult represents the result of datetime operations.
type DateTimeResult struct {
	Time        time.Time `json:"time"`
	Formatted   string    `json:"formatted,omitempty"`
	Timezone    string    `json:"timezone,omitempty"`
	UnixSeconds int64     `json:"unix_seconds"`
	ISO8601     string    `json:"iso8601"`
}

// CalculationResult represents the result of mathematical calculations.
type CalculationResult struct {
	Expression string  `json:"expression"`
	Result     float64 `json:"result"`
	Formatted  string  `json:"formatted"`
}

// FileReadResult represents the result of file read operations.
type FileReadResult struct {
	Path        string    `json:"path"`
	Content     string    `json:"content"`
	Size        int64     `json:"size"`
	ModTime     time.Time `json:"mod_time"`
	LineCount   int       `json:"line_count,omitempty"`
	IsDirectory bool      `json:"is_directory"`
}

// FileWriteResult represents the result of file write operations.
type FileWriteResult struct {
	Path         string    `json:"path"`
	BytesWritten int       `json:"bytes_written"`
	Created      bool      `json:"created"`
	Timestamp    time.Time `json:"timestamp"`
}

// WebScrapeResult represents the result of web scraping operations.
type WebScrapeResult struct {
	URL        string            `json:"url"`
	Title      string            `json:"title,omitempty"`
	Content    string            `json:"content"`
	Links      []string          `json:"links,omitempty"`
	Images     []string          `json:"images,omitempty"`
	StatusCode int               `json:"status_code"`
	Headers    map[string]string `json:"headers,omitempty"`
	FetchTime  time.Duration     `json:"fetch_time"`
}

// SearchResult represents a single search result.
type SearchResult struct {
	Title   string  `json:"title"`
	URL     string  `json:"url"`
	Snippet string  `json:"snippet"`
	Score   float64 `json:"score,omitempty"`
}

// WebSearchResult represents the result of web search operations.
type WebSearchResult struct {
	Query        string         `json:"query"`
	Results      []SearchResult `json:"results"`
	TotalResults int            `json:"total_results"`
	SearchTime   time.Duration  `json:"search_time"`
}

// MemoryOperation represents a memory tool operation.
type MemoryOperation struct {
	Action string `json:"action"` // "store", "retrieve", "search", "clear"
	Key    string `json:"key,omitempty"`
	Value  string `json:"value,omitempty"`
	Query  string `json:"query,omitempty"`
	Limit  int    `json:"limit,omitempty"`
}

// MemoryResult represents the result of memory operations.
type MemoryResult struct {
	Action  string   `json:"action"`
	Success bool     `json:"success"`
	Data    []string `json:"data,omitempty"`
	Count   int      `json:"count,omitempty"`
	Message string   `json:"message,omitempty"`
}

// Deprecated aliases for backward compatibility
type (
	ToolExecutor = Executor
	ToolRequest  = Request
	ToolResponse = Response
)
