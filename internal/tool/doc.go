/*
Package tool provides a unified and extensible tool management system for the assistant.

This package allows the assistant to perform actions beyond generating text, such as searching the web, interacting with the file system, or connecting to external services.

Key Features:

- Unified Registry: All tools, whether built-in or dynamically loaded, are registered and managed through a central `Manager`.

- Dynamic Execution: The assistant can dynamically decide which tool to use based on the user's query. The manager handles the execution and returns the result in a standardized format.

- Built-in Tools: A set of essential tools for common tasks (file I/O, web fetching) are provided out of the box.

- MCP Integration: The system is designed to integrate seamlessly with the Model Context Protocol (MCP), allowing it to discover and use external tools without requiring code changes.

- Natural Language Access: The tool schemas are provided to the LLM, allowing it to understand how to use the tools and formulate the necessary arguments from a natural language query.
*/
package tool
