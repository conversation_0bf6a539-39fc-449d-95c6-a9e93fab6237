// Package tool provides tool registration utilities.
package tool

import (
	"fmt"
	"time"

	"github.com/koopa0/assistant-go/internal/platform/config"
	"github.com/koopa0/assistant-go/internal/platform/logger"
	"github.com/koopa0/assistant-go/internal/tool/file"
	tooltime "github.com/koopa0/assistant-go/internal/tool/time"
	"github.com/koopa0/assistant-go/internal/tool/web"
)

// RegisterBuiltinTools registers all built-in tools with the manager.
func RegisterBuiltinTools(manager *Manager, cfg *config.Config, log logger.Logger) error {
	// Register time tools
	if err := manager.Register(tooltime.NewCurrentTimeTool()); err != nil {
		return fmt.Errorf("register current_time tool: %w", err)
	}
	if err := manager.Register(tooltime.NewTimeDiffTool()); err != nil {
		return fmt.Errorf("register time_diff tool: %w", err)
	}

	// Register file tools
	allowedDirs := []string{".", "./examples", "./docs", "./test"}
	maxFileSize := int64(10 * 1024 * 1024) // 10MB

	if err := manager.Register(file.NewReadFileTool(allowedDirs, maxFileSize)); err != nil {
		return fmt.Errorf("register read_file tool: %w", err)
	}

	if err := manager.Register(file.NewListDirectoryTool(allowedDirs)); err != nil {
		return fmt.Errorf("register list_directory tool: %w", err)
	}

	// Register write tool (always enabled for now)
	if err := manager.Register(file.NewWriteFileTool(allowedDirs, true)); err != nil {
		return fmt.Errorf("register write_file tool: %w", err)
	}

	// Memory tools are registered elsewhere since they need database access

	// Register web tools
	webCfg := web.Config{
		SearXNGURL:  cfg.Tools.SearXNGURL,
		HTTPTimeout: 30 * time.Second,
		UserAgent:   "Assistant-Go/1.0",
	}

	webTools := web.GetWebTools(webCfg)
	for _, tool := range webTools {
		if err := manager.Register(tool); err != nil {
			return fmt.Errorf("register web tool %s: %w", tool.Name(), err)
		}
	}

	// Log tool registration
	if cfg.Tools.SearXNGURL != "" {
		log.Debug("SearXNG integration enabled", "url", cfg.Tools.SearXNGURL)
	}
	log.Debug("Built-in tools registered", "count", len(manager.List()))
	return nil
}
