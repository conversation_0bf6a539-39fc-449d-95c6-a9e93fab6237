package tool

import (
	"context"
	"encoding/json"
	"errors"
	"strings"
	"sync"
	"testing"

	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// Mock tool for testing
type testTool struct {
	name        string
	description string
	schema      json.RawMessage
	executeFunc func(ctx context.Context, input any) (any, error)
}

func (t *testTool) Name() string                 { return t.name }
func (t *testTool) Description() string          { return t.description }
func (t *testTool) InputSchema() json.RawMessage { return t.schema }
func (t *testTool) Execute(ctx context.Context, input any) (any, error) {
	if t.executeFunc != nil {
		return t.executeFunc(ctx, input)
	}
	return "default result", nil
}

func TestManager_Register(t *testing.T) {
	tests := []struct {
		name    string
		tool    Tool
		wantErr bool
		errMsg  string
	}{
		{
			name: "valid tool",
			tool: &testTool{
				name:        "test_tool",
				description: "Test tool",
			},
			wantErr: false,
		},
		{
			name:    "nil tool",
			tool:    nil,
			wantErr: true,
			errMsg:  "cannot register nil tool",
		},
		{
			name: "empty name",
			tool: &testTool{
				name:        "",
				description: "Tool with no name",
			},
			wantErr: true,
			errMsg:  "tool has empty name",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := NewManager(Config{})
			err := m.Register(tt.tool)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
			} else {
				assert.NoError(t, err)
				// Verify tool was registered
				if tt.tool != nil {
					tool, exists := m.Get(tt.tool.Name())
					assert.True(t, exists)
					assert.Equal(t, tt.tool, tool)
				}
			}
		})
	}
}

func TestManager_Unregister(t *testing.T) {
	m := NewManager(Config{})

	// Register a tool first
	tool := &testTool{name: "test_tool", description: "Test"}
	require.NoError(t, m.Register(tool))

	// Test successful unregister
	err := m.Unregister("test_tool")
	assert.NoError(t, err)

	// Verify tool was removed
	_, exists := m.Get("test_tool")
	assert.False(t, exists)

	// Test unregistering non-existent tool
	err = m.Unregister("non_existent")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not found")
}

func TestManager_RegisterTools(t *testing.T) {
	tests := []struct {
		name    string
		tools   []Tool
		prefix  string
		wantErr bool
		check   func(t *testing.T, m *Manager)
	}{
		{
			name: "register multiple tools",
			tools: []Tool{
				&testTool{name: "tool1", description: "Tool 1"},
				&testTool{name: "tool2", description: "Tool 2"},
			},
			prefix:  "",
			wantErr: false,
			check: func(t *testing.T, m *Manager) {
				assert.True(t, m.HasTool("tool1"))
				assert.True(t, m.HasTool("tool2"))
			},
		},
		{
			name: "register with prefix",
			tools: []Tool{
				&testTool{name: "tool1", description: "Tool 1"},
				&testTool{name: "tool2", description: "Tool 2"},
			},
			prefix:  "mcp",
			wantErr: false,
			check: func(t *testing.T, m *Manager) {
				assert.True(t, m.HasTool("mcp.tool1"))
				assert.True(t, m.HasTool("mcp.tool2"))
				assert.False(t, m.HasTool("tool1"))
			},
		},
		{
			name: "skip nil tools",
			tools: []Tool{
				&testTool{name: "tool1", description: "Tool 1"},
				nil,
				&testTool{name: "tool2", description: "Tool 2"},
			},
			prefix:  "",
			wantErr: false,
			check: func(t *testing.T, m *Manager) {
				assert.True(t, m.HasTool("tool1"))
				assert.True(t, m.HasTool("tool2"))
			},
		},
		{
			name: "error on invalid tool",
			tools: []Tool{
				&testTool{name: "tool1", description: "Tool 1"},
				&testTool{name: "", description: "Invalid tool"},
			},
			prefix:  "",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := NewManager(Config{})
			err := m.RegisterTools(tt.tools, tt.prefix)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				if tt.check != nil {
					tt.check(t, m)
				}
			}
		})
	}
}

func TestManager_Execute(t *testing.T) {
	tests := []struct {
		name      string
		setupTool func() *testTool
		call      ToolCall
		want      ToolResult
	}{
		{
			name: "successful execution",
			setupTool: func() *testTool {
				return &testTool{
					name: "calculator",
					executeFunc: func(ctx context.Context, input any) (any, error) {
						// Verify input parsing
						m, ok := input.(map[string]any)
						require.True(t, ok)
						a := m["a"].(float64)
						b := m["b"].(float64)
						return map[string]any{"result": a + b}, nil
					},
				}
			},
			call: ToolCall{
				ID:   "call_123",
				Name: "calculator",
				Args: ai.Arguments{
					"a": json.RawMessage(`5`),
					"b": json.RawMessage(`3`),
				},
			},
			want: ToolResult{
				ID:      "call_123",
				Content: `{"result":8}`,
				Error:   nil,
			},
		},
		{
			name:      "tool not found",
			setupTool: func() *testTool { return nil },
			call: ToolCall{
				ID:   "call_404",
				Name: "non_existent",
			},
			want: ToolResult{
				ID:      "call_404",
				Content: "tool 'non_existent' not found",
				Error:   errors.New("tool 'non_existent' not found"),
			},
		},
		{
			name: "execution error",
			setupTool: func() *testTool {
				return &testTool{
					name: "failing_tool",
					executeFunc: func(ctx context.Context, input any) (any, error) {
						return nil, errors.New("execution failed")
					},
				}
			},
			call: ToolCall{
				ID:   "call_err",
				Name: "failing_tool",
			},
			want: ToolResult{
				ID:      "call_err",
				Content: "error: execution failed",
				Error:   errors.New("execution failed"),
			},
		},
		{
			name: "string result",
			setupTool: func() *testTool {
				return &testTool{
					name: "string_tool",
					executeFunc: func(ctx context.Context, input any) (any, error) {
						return "simple string result", nil
					},
				}
			},
			call: ToolCall{
				ID:   "call_str",
				Name: "string_tool",
			},
			want: ToolResult{
				ID:      "call_str",
				Content: "simple string result",
				Error:   nil,
			},
		},
		{
			name: "byte slice result",
			setupTool: func() *testTool {
				return &testTool{
					name: "bytes_tool",
					executeFunc: func(ctx context.Context, input any) (any, error) {
						return []byte("byte result"), nil
					},
				}
			},
			call: ToolCall{
				ID:   "call_bytes",
				Name: "bytes_tool",
			},
			want: ToolResult{
				ID:      "call_bytes",
				Content: "byte result",
				Error:   nil,
			},
		},
		{
			name: "empty arguments",
			setupTool: func() *testTool {
				return &testTool{
					name: "no_args_tool",
					executeFunc: func(ctx context.Context, input any) (any, error) {
						assert.Nil(t, input)
						return "no args", nil
					},
				}
			},
			call: ToolCall{
				ID:   "call_empty",
				Name: "no_args_tool",
				Args: nil,
			},
			want: ToolResult{
				ID:      "call_empty",
				Content: "no args",
				Error:   nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := NewManager(Config{})

			if tool := tt.setupTool(); tool != nil {
				require.NoError(t, m.Register(tool))
			}

			result := m.Execute(context.Background(), tt.call)

			assert.Equal(t, tt.want.ID, result.ID)
			assert.Equal(t, tt.want.Content, result.Content)
			if tt.want.Error != nil {
				assert.Error(t, result.Error)
				assert.Equal(t, tt.want.Error.Error(), result.Error.Error())
			} else {
				assert.NoError(t, result.Error)
			}
		})
	}
}

func TestManager_GetToolParams(t *testing.T) {
	m := NewManager(Config{})

	// Register tools with different schemas
	schema1 := json.RawMessage(`{"type":"object","properties":{"query":{"type":"string"}},"required":["query"]}`)
	schema2 := json.RawMessage(`{"type":"object","properties":{"path":{"type":"string"}}}`)

	tool1 := &testTool{
		name:        "search",
		description: "Search tool",
		schema:      schema1,
	}
	tool2 := &testTool{
		name:        "file",
		description: "File tool",
		schema:      schema2,
	}

	require.NoError(t, m.Register(tool1))
	require.NoError(t, m.Register(tool2))

	params := m.GetToolParams()
	assert.Len(t, params, 2)

	// Verify tool params
	for _, param := range params {
		switch param.Name {
		case "search":
			assert.Equal(t, "Search tool", param.Description)
			assert.Equal(t, "object", param.InputSchema.Type)
			assert.Contains(t, param.InputSchema.Properties, "query")
			assert.Contains(t, param.InputSchema.Required, "query")
		case "file":
			assert.Equal(t, "File tool", param.Description)
			assert.Equal(t, "object", param.InputSchema.Type)
			assert.Contains(t, param.InputSchema.Properties, "path")
		default:
			t.Errorf("Unexpected tool: %s", param.Name)
		}
	}
}

func TestManager_ConcurrentAccess(t *testing.T) {
	m := NewManager(Config{})

	// Test concurrent registration and access
	var wg sync.WaitGroup
	numRoutines := 10
	numTools := 5

	wg.Add(numRoutines)
	for i := 0; i < numRoutines; i++ {
		go func(routineID int) {
			defer wg.Done()

			// Register tools
			for j := 0; j < numTools; j++ {
				tool := &testTool{
					name:        strings.Join([]string{"tool", string(rune(routineID)), string(rune(j))}, "_"),
					description: "Concurrent test tool",
				}
				err := m.Register(tool)
				assert.NoError(t, err)
			}

			// List tools
			tools := m.List()
			assert.NotEmpty(t, tools)

			// Execute a tool
			result := m.Execute(context.Background(), ToolCall{
				ID:   "concurrent_call",
				Name: "tool_0_0",
			})
			assert.NotNil(t, result)
		}(i)
	}

	wg.Wait()

	// Verify some tools were registered
	tools := m.List()
	assert.NotEmpty(t, tools)
}

func TestPrefixedTool(t *testing.T) {
	baseTool := &testTool{
		name:        "base",
		description: "Base tool",
		schema:      json.RawMessage(`{}`),
	}

	prefixed := &prefixedTool{
		Tool:   baseTool,
		prefix: "mcp",
	}

	assert.Equal(t, "mcp.base", prefixed.Name())
	assert.Equal(t, "Base tool", prefixed.Description())
	assert.Equal(t, json.RawMessage(`{}`), prefixed.InputSchema())
}

func TestManager_PrepareInput(t *testing.T) {
	m := NewManager(Config{})

	tests := []struct {
		name     string
		args     ai.Arguments
		expected any
	}{
		{
			name: "complex arguments",
			args: ai.Arguments{
				"string":  json.RawMessage(`"hello"`),
				"number":  json.RawMessage(`42`),
				"boolean": json.RawMessage(`true`),
				"array":   json.RawMessage(`[1,2,3]`),
				"object":  json.RawMessage(`{"key":"value"}`),
			},
			expected: map[string]any{
				"string":  "hello",
				"number":  float64(42),
				"boolean": true,
				"array":   []any{float64(1), float64(2), float64(3)},
				"object":  map[string]any{"key": "value"},
			},
		},
		{
			name:     "empty arguments",
			args:     ai.Arguments{},
			expected: nil,
		},
		{
			name: "invalid JSON",
			args: ai.Arguments{
				"invalid": json.RawMessage(`{invalid`),
			},
			expected: map[string]any{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			call := ToolCall{Args: tt.args}
			result := m.prepareInput(call)

			if tt.expected == nil {
				assert.Nil(t, result)
			} else {
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestManager_FormatResult(t *testing.T) {
	m := NewManager(Config{})

	tests := []struct {
		name     string
		result   any
		expected string
	}{
		{
			name:     "string result",
			result:   "hello world",
			expected: "hello world",
		},
		{
			name:     "byte slice result",
			result:   []byte("byte data"),
			expected: "byte data",
		},
		{
			name:     "error result",
			result:   errors.New("something went wrong"),
			expected: "error: something went wrong",
		},
		{
			name:     "map result",
			result:   map[string]any{"key": "value", "number": 42},
			expected: `{"key":"value","number":42}`,
		},
		{
			name:     "slice result",
			result:   []int{1, 2, 3},
			expected: "[1,2,3]",
		},
		{
			name:     "nil result",
			result:   nil,
			expected: "null",
		},
		{
			name:     "complex struct",
			result:   struct{ Name string }{"test"},
			expected: `{"Name":"test"}`,
		},
		{
			name:     "unmarshalable result",
			result:   make(chan int), // channels can't be marshaled
			expected: "failed to format result:",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := m.formatResult(tt.result)
			if tt.name == "unmarshalable result" {
				assert.Contains(t, result, tt.expected)
			} else {
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

// Fuzzing tests
func FuzzManager_Execute(f *testing.F) {
	// Add seed corpus
	f.Add("test_tool", `{"key":"value"}`)
	f.Add("", `{}`)
	f.Add("tool_name", `null`)
	f.Add("another_tool", `{"number":123,"string":"test","bool":true}`)
	f.Add(strings.Repeat("x", 100), `[]`)

	f.Fuzz(func(t *testing.T, toolName string, argsJSON string) {
		m := NewManager(Config{})

		// Register a test tool
		tool := &testTool{
			name: "fuzz_tool",
			executeFunc: func(ctx context.Context, input any) (any, error) {
				return input, nil // Echo input
			},
		}
		m.Register(tool)

		// Create tool call
		var args ai.Arguments
		if argsJSON != "" {
			args = ai.Arguments{"input": json.RawMessage(argsJSON)}
		}

		call := ToolCall{
			ID:   "fuzz_call",
			Name: toolName,
			Args: args,
		}

		// Should not panic
		result := m.Execute(context.Background(), call)
		assert.NotNil(t, result)
		assert.Equal(t, "fuzz_call", result.ID)
	})
}

// Benchmark tests
func BenchmarkManager_Register(b *testing.B) {
	m := NewManager(Config{})

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		tool := &testTool{
			name:        "bench_tool_" + string(rune(i%100)),
			description: "Benchmark tool",
		}
		m.Register(tool)
	}
}

func BenchmarkManager_Execute(b *testing.B) {
	m := NewManager(Config{})

	// Register a simple tool
	tool := &testTool{
		name: "bench_tool",
		executeFunc: func(ctx context.Context, input any) (any, error) {
			return "result", nil
		},
	}
	m.Register(tool)

	call := ToolCall{
		ID:   "bench_call",
		Name: "bench_tool",
		Args: ai.Arguments{"input": json.RawMessage(`"test"`)},
	}

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		m.Execute(ctx, call)
	}
}

func BenchmarkManager_List(b *testing.B) {
	m := NewManager(Config{})

	// Register 100 tools
	for i := 0; i < 100; i++ {
		tool := &testTool{
			name:        "tool_" + string(rune(i)),
			description: "Test tool",
		}
		m.Register(tool)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = m.List()
	}
}
