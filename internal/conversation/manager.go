// Package conversation provides enhanced conversation management.
package conversation

import (
	"context"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"

	"github.com/koopa0/assistant-go/internal/platform/logger"
)

// Manager provides enhanced conversation management with local caching.
// Inspired by <PERSON><PERSON>' approach to conversation handling.
type Manager struct {
	store  Store
	logger logger.Logger

	// Local cache configuration
	cacheDir string

	// Auto-save configuration
	autoSave  bool
	saveDelay time.Duration

	// Current conversation state
	mu           sync.RWMutex
	current      *Conversation
	pendingMsgs  []*Message
	lastActivity time.Time
	saveTimer    *time.Timer
}

// ManagerOption configures the conversation manager.
type ManagerOption func(*Manager)

// WithCacheDir sets the local cache directory.
func WithCacheDir(dir string) ManagerOption {
	return func(m *Manager) {
		m.cacheDir = dir
	}
}

// WithAutoSave enables automatic conversation saving.
func WithAutoSave(enabled bool, delay time.Duration) ManagerOption {
	return func(m *Manager) {
		m.autoSave = enabled
		if delay > 0 {
			m.saveDelay = delay
		}
	}
}

// WithManagerLogger sets the logger for the manager.
func WithManagerLogger(log logger.Logger) ManagerOption {
	return func(m *Manager) {
		m.logger = log
	}
}

// NewManager creates a new conversation manager with enhanced features.
func NewManager(store Store, opts ...ManagerOption) (*Manager, error) {
	m := &Manager{
		store:     store,
		logger:    logger.CharmLoggerForContext("conversation"),
		saveDelay: 2 * time.Second, // Default 2 second delay
		autoSave:  true,
	}

	// Apply options
	for _, opt := range opts {
		opt(m)
	}

	// Set default cache directory if not specified
	if m.cacheDir == "" {
		homeDir, err := os.UserHomeDir()
		if err != nil {
			return nil, fmt.Errorf("get home directory: %w", err)
		}
		m.cacheDir = filepath.Join(homeDir, ".assistant", "conversations")
	}

	// Ensure cache directory exists
	if err := os.MkdirAll(m.cacheDir, 0750); err != nil {
		return nil, fmt.Errorf("create cache directory: %w", err)
	}

	m.logger.Debug("conversation manager initialized",
		"cache_dir", m.cacheDir,
		"auto_save", m.autoSave,
		"save_delay", m.saveDelay)

	return m, nil
}

// StartNew starts a new conversation.
func (m *Manager) StartNew(ctx context.Context) (*Conversation, error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	// Save current conversation if exists
	if m.current != nil && len(m.pendingMsgs) > 0 {
		if err := m.savePendingMessages(ctx); err != nil {
			m.logger.Warn("failed to save pending messages", "error", err)
		}
	}

	// Create new conversation
	conv, err := m.store.Create(ctx)
	if err != nil {
		return nil, fmt.Errorf("create conversation: %w", err)
	}

	m.current = conv
	m.pendingMsgs = nil
	m.lastActivity = time.Now()

	m.logger.Info("started new conversation", "id", conv.ID)

	return conv, nil
}

// Current returns the current conversation, creating one if needed.
func (m *Manager) Current(ctx context.Context) (*Conversation, error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.current == nil {
		conv, err := m.store.Create(ctx)
		if err != nil {
			return nil, fmt.Errorf("create conversation: %w", err)
		}
		m.current = conv
		m.lastActivity = time.Now()
	}

	return m.current, nil
}

// Continue loads and continues a previous conversation.
func (m *Manager) Continue(ctx context.Context, id uuid.UUID) (*Conversation, error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	// Save current conversation if exists
	if m.current != nil && len(m.pendingMsgs) > 0 {
		if err := m.savePendingMessages(ctx); err != nil {
			m.logger.Warn("failed to save pending messages", "error", err)
		}
	}

	// Load conversation
	conv, err := m.store.Get(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("get conversation: %w", err)
	}

	m.current = conv
	m.pendingMsgs = nil
	m.lastActivity = time.Now()

	m.logger.Info("continuing conversation", "id", conv.ID, "title", conv.Title)

	return conv, nil
}

// ContinueLast continues the most recent conversation.
func (m *Manager) ContinueLast(ctx context.Context) (*Conversation, error) {
	conversations, err := m.store.List(ctx, 1)
	if err != nil {
		return nil, fmt.Errorf("list conversations: %w", err)
	}

	if len(conversations) == 0 {
		// No previous conversations, start new
		return m.StartNew(ctx)
	}

	return m.Continue(ctx, conversations[0].ID)
}

// AddMessage adds a message to the current conversation.
func (m *Manager) AddMessage(ctx context.Context, msg Message) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.current == nil {
		return fmt.Errorf("no active conversation")
	}

	// Set conversation ID
	msg.ConversationID = m.current.ID

	// Add to pending messages
	m.pendingMsgs = append(m.pendingMsgs, &msg)
	m.lastActivity = time.Now()

	// Update title if this is the first user message
	if msg.Role == "user" && m.current.Title == fmt.Sprintf("Conversation %s", m.current.CreatedAt.Format("2006-01-02 15:04")) {
		m.updateTitle(msg.Content)
	}

	// Schedule auto-save
	if m.autoSave {
		m.scheduleAutoSave(ctx)
	}

	return nil
}

// GetMessages retrieves messages for the current conversation.
func (m *Manager) GetMessages(ctx context.Context, limit int) ([]*Message, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if m.current == nil {
		return nil, fmt.Errorf("no active conversation")
	}

	// Get messages from store
	messages, err := m.store.GetMessages(ctx, m.current.ID, limit)
	if err != nil {
		return nil, err
	}

	// Append pending messages
	messages = append(messages, m.pendingMsgs...)

	return messages, nil
}

// Save immediately saves pending messages.
func (m *Manager) Save(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	return m.savePendingMessages(ctx)
}

// List returns recent conversations with enhanced metadata.
func (m *Manager) List(ctx context.Context, limit int) ([]*Summary, error) {
	conversations, err := m.store.List(ctx, limit)
	if err != nil {
		return nil, err
	}

	summaries := make([]*Summary, len(conversations))
	for i, conv := range conversations {
		// Generate fingerprint
		fingerprint := m.generateFingerprint(conv)

		summaries[i] = &Summary{
			Conversation: conv,
			Fingerprint:  fingerprint,
			MessageCount: conv.Metadata.MessageCount,
		}
	}

	return summaries, nil
}

// ListConversations returns raw conversations for UI display.
func (m *Manager) ListConversations(ctx context.Context, limit int) ([]*Conversation, error) {
	return m.store.List(ctx, limit)
}

// updateTitle generates a better title based on content.
func (m *Manager) updateTitle(firstMessage string) {
	// Generate title from first message (like Mods)
	title := generateTitle(firstMessage)
	m.current.Title = title

	m.logger.Debug("updated conversation title", "title", title)
}

// scheduleAutoSave schedules an automatic save operation.
func (m *Manager) scheduleAutoSave(ctx context.Context) {
	// Cancel existing timer
	if m.saveTimer != nil {
		m.saveTimer.Stop()
	}

	// Schedule new save
	m.saveTimer = time.AfterFunc(m.saveDelay, func() {
		m.mu.Lock()
		defer m.mu.Unlock()

		if err := m.savePendingMessages(ctx); err != nil {
			m.logger.Error("auto-save failed", "error", err)
		} else {
			m.logger.Debug("auto-saved conversation", "id", m.current.ID)
		}
	})
}

// savePendingMessages saves all pending messages to the store.
func (m *Manager) savePendingMessages(ctx context.Context) error {
	if len(m.pendingMsgs) == 0 {
		return nil
	}

	// Save each message
	for _, msg := range m.pendingMsgs {
		if err := m.store.AddMessage(ctx, m.current.ID, *msg); err != nil {
			return fmt.Errorf("add message: %w", err)
		}
	}

	// Update metadata
	m.current.Metadata.MessageCount += len(m.pendingMsgs)
	m.current.Metadata.LastActivity = m.lastActivity

	// Clear pending messages
	m.pendingMsgs = nil

	// Also save to local cache
	if err := m.saveToCache(); err != nil {
		m.logger.Warn("failed to save to cache", "error", err)
	}

	return nil
}

// saveToCache saves the conversation to local cache.
func (m *Manager) saveToCache() error {
	if m.current == nil {
		return nil
	}

	// Create cache file path
	filename := fmt.Sprintf("%s.json", m.current.ID)
	cachePath := filepath.Join(m.cacheDir, filename)

	// Marshal conversation
	data, err := json.MarshalIndent(m.current, "", "  ")
	if err != nil {
		return fmt.Errorf("marshal conversation: %w", err)
	}

	// Write to file
	if err := os.WriteFile(cachePath, data, 0600); err != nil {
		return fmt.Errorf("write cache file: %w", err)
	}

	return nil
}

// generateFingerprint creates a SHA-1 fingerprint for a conversation.
func (m *Manager) generateFingerprint(conv *Conversation) string {
	h := sha256.New()
	h.Write([]byte(conv.ID.String()))
	h.Write([]byte(conv.Title))
	h.Write([]byte(conv.CreatedAt.Format(time.RFC3339)))
	return fmt.Sprintf("%x", h.Sum(nil))[:8]
}

// generateTitle creates a title from the first message content.
func generateTitle(content string) string {
	// Remove newlines and excessive spaces
	title := strings.ReplaceAll(content, "\n", " ")
	title = strings.Join(strings.Fields(title), " ")

	// Truncate to reasonable length
	const maxLength = 50
	if len(title) > maxLength {
		// Find last complete word before limit
		if idx := strings.LastIndex(title[:maxLength], " "); idx > 0 {
			title = title[:idx] + "..."
		} else {
			title = title[:maxLength] + "..."
		}
	}

	return title
}

// Summary provides enhanced conversation information.
type Summary struct {
	*Conversation
	Fingerprint  string `json:"fingerprint"`
	MessageCount int    `json:"message_count"`
}

// Close gracefully shuts down the manager.
func (m *Manager) Close(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	// Cancel auto-save timer
	if m.saveTimer != nil {
		m.saveTimer.Stop()
	}

	// Save pending messages
	if m.current != nil && len(m.pendingMsgs) > 0 {
		return m.savePendingMessages(ctx)
	}

	return nil
}

// Store returns the underlying store (for advanced operations)
func (m *Manager) Store() interface{} {
	return m.store
}
