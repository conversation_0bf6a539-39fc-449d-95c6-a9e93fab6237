// Package conversation provides conversation management functionality.
// It follows Go standard library patterns for clean, testable design.
package conversation

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/jackc/pgx/v5/pgxpool"

	"github.com/koopa0/assistant-go/internal/platform/convert"
	"github.com/koopa0/assistant-go/internal/platform/logger"
	"github.com/koopa0/assistant-go/internal/storage/database/sqlc"
)

// Store defines the interface for conversation persistence.
// This allows for easy testing and alternative implementations.
type Store interface {
	// Create creates a new conversation
	Create(ctx context.Context) (*Conversation, error)
	// Get retrieves a conversation by ID
	Get(ctx context.Context, id uuid.UUID) (*Conversation, error)
	// List retrieves recent conversations
	List(ctx context.Context, limit int) ([]*Conversation, error)
	// Search performs full-text search on conversations
	Search(ctx context.Context, query string, limit int) ([]*SearchResult, error)
	// AddMessage adds a message to a conversation
	AddMessage(ctx context.Context, conversationID uuid.UUID, msg Message) error
	// GetMessages retrieves messages from a conversation
	GetMessages(ctx context.Context, conversationID uuid.UUID, limit int) ([]*Message, error)
}

// store implements the Store interface with PostgreSQL backend.
type store struct {
	db      *pgxpool.Pool
	queries sqlc.Querier
	logger  logger.Logger

	// Configuration
	defaultLimit    int
	maxMessageLimit int
}

// StoreOption configures a Store instance.
type StoreOption func(*store)

// WithLogger sets a custom logger for the store.
func WithLogger(log logger.Logger) StoreOption {
	return func(s *store) {
		s.logger = log
	}
}

// WithDefaultLimit sets the default limit for queries.
func WithDefaultLimit(limit int) StoreOption {
	return func(s *store) {
		if limit > 0 && limit <= 100 {
			s.defaultLimit = limit
		}
	}
}

// WithMaxMessageLimit sets the maximum message limit.
func WithMaxMessageLimit(limit int) StoreOption {
	return func(s *store) {
		if limit > 0 {
			s.maxMessageLimit = limit
		}
	}
}

// NewStore creates a new conversation store with the given database connection.
// It follows the pattern of sql.Open, pgx.Connect, etc.
func NewStore(db *pgxpool.Pool, opts ...StoreOption) Store {
	s := &store{
		db:              db,
		queries:         sqlc.New(db),
		logger:          logger.NewNoOpLogger(), // Default to no-op logger
		defaultLimit:    20,
		maxMessageLimit: 1000,
	}

	// Apply options
	for _, opt := range opts {
		opt(s)
	}

	return s
}

// Create creates a new conversation.
func (s *store) Create(ctx context.Context) (*Conversation, error) {
	title := fmt.Sprintf("Conversation %s", time.Now().Format("2006-01-02 15:04"))

	s.logger.Debug("creating conversation", "title", title)

	convDB, err := s.queries.CreateConversation(ctx, sqlc.CreateConversationParams{
		Title:    title,
		Summary:  pgtype.Text{},
		Status:   sqlc.ConversationStatusActive,
		Metadata: json.RawMessage("{}"),
	})
	if err != nil {
		s.logger.Error("failed to create conversation", "error", err)
		return nil, fmt.Errorf("create conversation: %w", err)
	}

	// Return the created conversation
	return s.conversationFromCreateRow(convDB), nil
}

// Get retrieves a conversation by ID.
func (s *store) Get(ctx context.Context, id uuid.UUID) (*Conversation, error) {
	// Validate input
	if id == uuid.Nil {
		return nil, fmt.Errorf("conversation ID cannot be nil")
	}

	convID := pgtype.UUID{
		Bytes: id,
		Valid: true,
	}

	s.logger.Debug("getting conversation", "id", id)

	convDB, err := s.queries.GetConversation(ctx, convID)
	if err != nil {
		s.logger.Error("failed to get conversation", "id", id, "error", err)
		return nil, fmt.Errorf("get conversation %s: %w", id, err)
	}

	return s.conversationFromGetRow(convDB), nil
}

// List retrieves recent conversations with pagination support.
func (s *store) List(ctx context.Context, limit int) ([]*Conversation, error) {
	// Apply default and bounds checking
	if limit <= 0 {
		limit = s.defaultLimit
	}
	if limit > 100 {
		limit = 100 // Hard cap for safety
	}

	s.logger.Debug("listing conversations", "limit", limit)

	rows, err := s.queries.ListConversations(ctx, sqlc.ListConversationsParams{
		Status:      sqlc.ConversationStatusActive,
		OrderBy:     "last_message",
		OffsetCount: 0,
		LimitCount:  convert.SafeIntToInt32(limit),
	})
	if err != nil {
		s.logger.Error("failed to list conversations", "error", err)
		return nil, fmt.Errorf("list conversations: %w", err)
	}

	conversations := make([]*Conversation, len(rows))
	for i, row := range rows {
		conversations[i] = s.conversationFromListRow(row)
	}

	s.logger.Debug("listed conversations", "count", len(conversations))

	return conversations, nil
}

// AddMessage adds a message to a conversation.
func (s *store) AddMessage(ctx context.Context, conversationID uuid.UUID, msg Message) error {
	// Validate input
	if conversationID == uuid.Nil {
		return fmt.Errorf("conversation ID cannot be nil")
	}

	if msg.Role == "" {
		return fmt.Errorf("message role cannot be empty")
	}

	if msg.Content == "" {
		return fmt.Errorf("message content cannot be empty")
	}

	convID := pgtype.UUID{
		Bytes: conversationID,
		Valid: true,
	}

	metadataBytes, err := json.Marshal(msg.Metadata)
	if err != nil {
		s.logger.Error("failed to marshal message metadata", "error", err)
		return fmt.Errorf("marshal message metadata: %w", err)
	}
	if len(metadataBytes) == 0 {
		metadataBytes = []byte("{}")
	}

	s.logger.Debug("adding message",
		"conversation_id", conversationID,
		"role", msg.Role,
		"content_length", len(msg.Content),
	)

	_, err = s.queries.CreateMessage(ctx, sqlc.CreateMessageParams{
		ConversationID: convID,
		Role:           sqlc.MessageRole(msg.Role),
		Content:        msg.Content,
		Metadata:       json.RawMessage(metadataBytes),
		TokensUsed:     msg.TokensUsed,
	})
	if err != nil {
		s.logger.Error("failed to add message", "conversation_id", conversationID, "error", err)
		return fmt.Errorf("add message to conversation %s: %w", conversationID, err)
	}

	return nil
}

// GetMessages retrieves messages from a conversation.
func (s *store) GetMessages(ctx context.Context, conversationID uuid.UUID, limit int) ([]*Message, error) {
	// Validate input
	if conversationID == uuid.Nil {
		return nil, fmt.Errorf("conversation ID cannot be nil")
	}

	// Apply limit bounds
	if limit <= 0 || limit > s.maxMessageLimit {
		limit = s.maxMessageLimit
	}

	convID := pgtype.UUID{
		Bytes: conversationID,
		Valid: true,
	}

	s.logger.Debug("getting messages", "conversation_id", conversationID, "limit", limit)

	rows, err := s.queries.ListMessages(ctx, sqlc.ListMessagesParams{
		ConversationID: convID,
		LimitCount:     convert.SafeIntToInt32(limit),
		OffsetCount:    0,
	})
	if err != nil {
		s.logger.Error("failed to get messages", "conversation_id", conversationID, "error", err)
		return nil, fmt.Errorf("get messages for conversation %s: %w", conversationID, err)
	}

	messages := make([]*Message, len(rows))
	for i, row := range rows {
		messages[i] = s.messageFromDB(row)
	}

	s.logger.Debug("retrieved messages", "conversation_id", conversationID, "count", len(messages))

	return messages, nil
}

// Search performs full-text search on conversations
func (s *store) Search(ctx context.Context, query string, limit int) ([]*SearchResult, error) {
	// Validate input
	query = strings.TrimSpace(query)
	if query == "" {
		return nil, fmt.Errorf("search query cannot be empty")
	}

	// Apply limit bounds
	if limit <= 0 || limit > 100 {
		limit = 20
	}

	s.logger.Debug("searching conversations", "query", query, "limit", limit)

	// Use the SearchConversationsWithHighlight query which provides highlighting
	rows, err := s.queries.SearchConversationsWithHighlight(ctx, sqlc.SearchConversationsWithHighlightParams{
		SearchQuery: query,
		Status:      sqlc.ConversationStatusActive,
		LimitCount:  convert.SafeIntToInt32(limit),
	})
	if err != nil {
		s.logger.Error("failed to search conversations", "error", err)
		return nil, fmt.Errorf("search conversations: %w", err)
	}

	results := make([]*SearchResult, len(rows))
	for i, row := range rows {
		// Convert database row to domain model
		conv := &Conversation{
			ID:        row.ID.Bytes,
			Title:     row.Title,
			Summary:   row.Summary.String,
			CreatedAt: row.CreatedAt,
			UpdatedAt: row.UpdatedAt,
			Metadata:  Metadata{},
		}

		if row.LastMessageAt.Valid {
			conv.LastMessageAt = &row.LastMessageAt.Time
		}

		// Parse metadata
		if len(row.Metadata) > 0 {
			if err := json.Unmarshal(row.Metadata, &conv.Metadata); err != nil {
				s.logger.Warn("failed to unmarshal conversation metadata", "id", row.ID.Bytes, "error", err)
			}
		}

		results[i] = &SearchResult{
			Conversation:       conv,
			HighlightedTitle:   string(row.HighlightedTitle),
			HighlightedSummary: string(row.HighlightedSummary),
			Rank:               row.Rank,
		}
	}

	s.logger.Debug("search completed", "query", query, "results", len(results))

	return results, nil
}

// conversationFromGetRow converts sqlc.Conversation to domain model.
func (s *store) conversationFromGetRow(row *sqlc.Conversation) *Conversation {
	var metadata Metadata
	if len(row.Metadata) > 0 {
		if err := json.Unmarshal(row.Metadata, &metadata); err != nil {
			s.logger.Warn("failed to unmarshal conversation metadata", "id", row.ID.Bytes, "error", err)
		}
	}

	// Ensure we have a valid UUID
	id := uuid.Nil
	if row.ID.Valid {
		id = row.ID.Bytes
	}

	conv := &Conversation{
		ID:        id,
		Title:     row.Title,
		Summary:   row.Summary.String,
		Metadata:  metadata,
		CreatedAt: row.CreatedAt,
		UpdatedAt: row.UpdatedAt,
	}

	if row.LastMessageAt.Valid {
		conv.LastMessageAt = &row.LastMessageAt.Time
	}

	return conv
}

// conversationFromListRow converts sqlc.Conversation to domain model.
func (s *store) conversationFromListRow(row *sqlc.Conversation) *Conversation {
	var metadata Metadata
	if len(row.Metadata) > 0 {
		if err := json.Unmarshal(row.Metadata, &metadata); err != nil {
			s.logger.Warn("failed to unmarshal conversation metadata", "id", row.ID.Bytes, "error", err)
		}
	}

	// Ensure we have a valid UUID
	id := uuid.Nil
	if row.ID.Valid {
		id = row.ID.Bytes
	}

	conv := &Conversation{
		ID:        id,
		Title:     row.Title,
		Summary:   row.Summary.String,
		Metadata:  metadata,
		CreatedAt: row.CreatedAt,
		UpdatedAt: row.UpdatedAt,
	}

	if row.LastMessageAt.Valid {
		conv.LastMessageAt = &row.LastMessageAt.Time
	}

	return conv
}

// conversationFromCreateRow converts sqlc.Conversation to domain model.
func (s *store) conversationFromCreateRow(row *sqlc.Conversation) *Conversation {
	var metadata Metadata
	if len(row.Metadata) > 0 {
		if err := json.Unmarshal(row.Metadata, &metadata); err != nil {
			s.logger.Warn("failed to unmarshal conversation metadata", "id", row.ID.Bytes, "error", err)
		}
	}

	// Ensure we have a valid UUID
	id := uuid.Nil
	if row.ID.Valid {
		id = row.ID.Bytes
	}

	conv := &Conversation{
		ID:        id,
		Title:     row.Title,
		Summary:   row.Summary.String,
		Metadata:  metadata,
		CreatedAt: row.CreatedAt,
		UpdatedAt: row.UpdatedAt,
	}

	if row.LastMessageAt.Valid {
		conv.LastMessageAt = &row.LastMessageAt.Time
	}

	s.logger.Debug("conversation created", "id", conv.ID, "title", conv.Title)

	return conv
}

// messageFromDB converts database message to domain model.
func (s *store) messageFromDB(row *sqlc.Message) *Message {
	var metadata MessageMetadata
	if len(row.Metadata) > 0 {
		if err := json.Unmarshal(row.Metadata, &metadata); err != nil {
			s.logger.Warn("failed to unmarshal message metadata", "id", row.ID.Bytes, "error", err)
		}
	}

	// Ensure we have valid UUIDs
	id := uuid.Nil
	if row.ID.Valid {
		id = row.ID.Bytes
	}

	convID := uuid.Nil
	if row.ConversationID.Valid {
		convID = row.ConversationID.Bytes
	}

	return &Message{
		ID:             id,
		ConversationID: convID,
		Role:           string(row.Role),
		Content:        row.Content,
		Metadata:       metadata,
		TokensUsed:     row.TokensUsed,
		CreatedAt:      row.CreatedAt,
	}
}
