package conversation

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

// Conversation represents a conversation session.
// It follows the pattern of standard library types - simple, focused structs.
type Conversation struct {
	ID            uuid.UUID
	Title         string
	Summary       string
	Metadata      Metadata
	LastMessageAt *time.Time
	CreatedAt     time.Time
	UpdatedAt     time.Time
}

// Metadata stores additional conversation properties.
// Using a dedicated type provides type safety and documentation.
type Metadata struct {
	PreferredModel    string            `json:"preferred_model,omitempty"`
	PreferredProvider string            `json:"preferred_provider,omitempty"`
	Topic             string            `json:"topic,omitempty"`
	Tags              []string          `json:"tags,omitempty"`
	Custom            map[string]string `json:"custom,omitempty"`

	// Enhanced metadata for better management
	MessageCount  int       `json:"message_count,omitempty"`
	LastActivity  time.Time `json:"last_activity,omitempty"`
	AutoGenerated bool      `json:"auto_generated,omitempty"` // If title was auto-generated
}

// Message represents a single message within a conversation.
// Each message is immutable once created.
type Message struct {
	ID             uuid.UUID
	ConversationID uuid.UUID
	Role           string // "user", "assistant", "system"
	Content        string
	Metadata       MessageMetadata
	TokensUsed     int32
	CreatedAt      time.Time
}

// MessageMetadata stores additional message properties.
// This separation allows the core Message to remain stable while metadata evolves.
type MessageMetadata struct {
	Model        string            `json:"model,omitempty"`
	Provider     string            `json:"provider,omitempty"`
	Temperature  float32           `json:"temperature,omitempty"`
	ToolCalls    []ToolCall        `json:"tool_calls,omitempty"`
	Custom       map[string]string `json:"custom,omitempty"`
	ProcessingMS int64             `json:"processing_ms,omitempty"`
}

// ToolCall represents a tool invocation within a message.
// Using json.RawMessage defers parsing until needed.
type ToolCall struct {
	ID     string          `json:"id"`
	Name   string          `json:"name"`
	Input  json.RawMessage `json:"input"`
	Output json.RawMessage `json:"output,omitempty"`
}

// Role constants define valid message roles.
const (
	RoleUser      = "user"
	RoleAssistant = "assistant"
	RoleSystem    = "system"
)

// IsValidRole checks if a role string is valid.
func IsValidRole(role string) bool {
	switch role {
	case RoleUser, RoleAssistant, RoleSystem:
		return true
	default:
		return false
	}
}

// SearchResult represents a search result with highlights
type SearchResult struct {
	Conversation       *Conversation
	HighlightedTitle   string
	HighlightedSummary string
	Rank               float32
	MatchedMessages    []*MessageMatch
}

// MessageMatch represents a matched message in search
type MessageMatch struct {
	Message           *Message
	HighlightedText   string
	ConversationTitle string
}
