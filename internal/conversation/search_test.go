package conversation_test

import (
	"context"
	"fmt"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/koopa0/assistant-go/internal/conversation"
	"github.com/koopa0/assistant-go/internal/platform/logger"
	"github.com/koopa0/assistant-go/internal/testing/testdb"
)

// TestConversationSearch tests the conversation search functionality
func TestConversationSearch(t *testing.T) {
	// Skip if no database available
	db := testdb.GetTestDB(t)
	if db == nil {
		t.Skip("Database not available for testing")
	}

	// Create store
	store := conversation.NewStore(db, conversation.WithLogger(logger.NewConsoleLogger()))
	ctx := context.Background()

	// Test data
	testConversations := []struct {
		title   string
		summary string
		message string
	}{
		{
			title:   "Go Programming Discussion",
			summary: "Discussing Go best practices and patterns",
			message: "What are the best practices for error handling in Go?",
		},
		{
			title:   "Python vs Go Performance",
			summary: "Comparing performance characteristics",
			message: "How does Python compare to Go in terms of performance?",
		},
		{
			title:   "Learning Rust",
			summary: "Starting journey with Rust programming",
			message: "I want to learn Rust, where should I start?",
		},
		{
			title:   "Database Design Patterns",
			summary: "Discussing PostgreSQL and schema design",
			message: "What's the best way to design a schema for user authentication?",
		},
	}

	// Create test conversations
	for _, tc := range testConversations {
		conv, err := store.Create(ctx)
		require.NoError(t, err)

		// Update with test data
		conv.Title = tc.title
		conv.Summary = tc.summary

		// Add a message
		err = store.AddMessage(ctx, conv.ID, conversation.Message{
			Role:    conversation.RoleUser,
			Content: tc.message,
		})
		require.NoError(t, err)
	}

	// Test cases
	tests := []struct {
		name          string
		query         string
		expectedCount int
		validateFunc  func(t *testing.T, results []*conversation.SearchResult)
	}{
		{
			name:          "search by programming language - Go",
			query:         "Go",
			expectedCount: 2, // "Go Programming" and "Python vs Go"
			validateFunc: func(t *testing.T, results []*conversation.SearchResult) {
				// Check highlighting
				for _, r := range results {
					if r.Conversation.Title == "Go Programming Discussion" {
						assert.Contains(t, r.HighlightedTitle, "<<Go>>")
					}
				}
			},
		},
		{
			name:          "search by topic - programming",
			query:         "programming",
			expectedCount: 2,
			validateFunc: func(t *testing.T, results []*conversation.SearchResult) {
				titles := make([]string, 0, len(results))
				for _, r := range results {
					titles = append(titles, r.Conversation.Title)
				}
				assert.Contains(t, titles, "Go Programming Discussion")
				assert.Contains(t, titles, "Learning Rust")
			},
		},
		{
			name:          "search in summary - patterns",
			query:         "patterns",
			expectedCount: 2,
			validateFunc: func(t *testing.T, results []*conversation.SearchResult) {
				for _, r := range results {
					if r.Conversation.Title == "Go Programming Discussion" {
						assert.Contains(t, r.HighlightedSummary, "<<patterns>>")
					}
				}
			},
		},
		{
			name:          "search with no results",
			query:         "JavaScript",
			expectedCount: 0,
			validateFunc: func(t *testing.T, results []*conversation.SearchResult) {
				assert.Empty(t, results)
			},
		},
		{
			name:          "search with limit",
			query:         "Programming",
			expectedCount: 2,
			validateFunc: func(t *testing.T, results []*conversation.SearchResult) {
				assert.Len(t, results, 2)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			results, err := store.Search(ctx, tt.query, 10)
			require.NoError(t, err)

			assert.Len(t, results, tt.expectedCount, "Expected %d results for query '%s'", tt.expectedCount, tt.query)

			if tt.validateFunc != nil {
				tt.validateFunc(t, results)
			}
		})
	}
}

// TestConversationContinue tests continuing an existing conversation
func TestConversationContinue(t *testing.T) {
	// Skip if no database available
	db := testdb.GetTestDB(t)
	if db == nil {
		t.Skip("Database not available for testing")
	}

	store := conversation.NewStore(db, conversation.WithLogger(logger.NewConsoleLogger()))
	ctx := context.Background()

	// Create initial conversation
	conv, err := store.Create(ctx)
	require.NoError(t, err)
	require.NotEqual(t, uuid.Nil, conv.ID)

	// Add initial messages
	messages := []conversation.Message{
		{
			Role:    conversation.RoleUser,
			Content: "Hello, I need help with Go programming",
		},
		{
			Role:    conversation.RoleAssistant,
			Content: "I'd be happy to help you with Go programming! What would you like to know?",
		},
		{
			Role:    conversation.RoleUser,
			Content: "How do I handle errors properly?",
		},
		{
			Role:    conversation.RoleAssistant,
			Content: "In Go, error handling follows the explicit pattern...",
		},
	}

	for _, msg := range messages {
		err = store.AddMessage(ctx, conv.ID, msg)
		require.NoError(t, err)
	}

	// Test retrieving the conversation
	retrievedConv, err := store.Get(ctx, conv.ID)
	require.NoError(t, err)
	assert.Equal(t, conv.ID, retrievedConv.ID)

	// Test retrieving messages
	retrievedMessages, err := store.GetMessages(ctx, conv.ID, 10)
	require.NoError(t, err)
	assert.Len(t, retrievedMessages, len(messages))

	// Verify message order (should be oldest first)
	for i, msg := range retrievedMessages {
		assert.Equal(t, messages[i].Role, msg.Role)
		assert.Equal(t, messages[i].Content, msg.Content)
	}

	// Test continuing the conversation
	newMessage := conversation.Message{
		Role:    conversation.RoleUser,
		Content: "What about error wrapping?",
	}
	err = store.AddMessage(ctx, conv.ID, newMessage)
	require.NoError(t, err)

	// Verify the new message was added
	allMessages, err := store.GetMessages(ctx, conv.ID, 10)
	require.NoError(t, err)
	assert.Len(t, allMessages, len(messages)+1)
	assert.Equal(t, newMessage.Content, allMessages[len(allMessages)-1].Content)
}

// TestSearchAndContinue tests the full flow of searching and continuing a conversation
func TestSearchAndContinue(t *testing.T) {
	// Skip if no database available
	db := testdb.GetTestDB(t)
	if db == nil {
		t.Skip("Database not available for testing")
	}

	store := conversation.NewStore(db, conversation.WithLogger(logger.NewConsoleLogger()))
	ctx := context.Background()

	// Create a conversation about error handling
	conv, err := store.Create(ctx)
	require.NoError(t, err)
	conv.Title = "Go Error Handling Best Practices"
	conv.Summary = "Discussion about proper error handling in Go"

	// Add messages
	messages := []conversation.Message{
		{
			Role:    conversation.RoleUser,
			Content: "What's the best way to handle errors in Go?",
		},
		{
			Role:    conversation.RoleAssistant,
			Content: "Go uses explicit error handling with the error interface...",
		},
	}

	for _, msg := range messages {
		err = store.AddMessage(ctx, conv.ID, msg)
		require.NoError(t, err)
	}

	// Search for the conversation
	searchResults, err := store.Search(ctx, "error handling", 10)
	require.NoError(t, err)
	require.NotEmpty(t, searchResults)

	// Find our conversation in results
	var foundConv *conversation.Conversation
	for _, result := range searchResults {
		if result.Conversation.ID == conv.ID {
			foundConv = result.Conversation
			break
		}
	}
	require.NotNil(t, foundConv, "Should find the created conversation")

	// Continue the conversation
	err = store.AddMessage(ctx, foundConv.ID, conversation.Message{
		Role:    conversation.RoleUser,
		Content: "Can you show me an example of error wrapping?",
	})
	require.NoError(t, err)

	// Verify we can retrieve all messages
	allMessages, err := store.GetMessages(ctx, foundConv.ID, 10)
	require.NoError(t, err)
	assert.Len(t, allMessages, 3)
}

// TestSearchPagination tests search result pagination
func TestSearchPagination(t *testing.T) {
	// Skip if no database available
	db := testdb.GetTestDB(t)
	if db == nil {
		t.Skip("Database not available for testing")
	}

	store := conversation.NewStore(db, conversation.WithLogger(logger.NewConsoleLogger()))
	ctx := context.Background()

	// Create many conversations
	for i := 0; i < 30; i++ {
		conv, err := store.Create(ctx)
		require.NoError(t, err)
		conv.Title = fmt.Sprintf("Programming Topic %d", i)
		conv.Summary = "Discussion about programming"

		err = store.AddMessage(ctx, conv.ID, conversation.Message{
			Role:    conversation.RoleUser,
			Content: fmt.Sprintf("Question about programming topic %d", i),
		})
		require.NoError(t, err)
	}

	// Test different limit values
	testCases := []struct {
		limit    int
		expected int
	}{
		{limit: 5, expected: 5},
		{limit: 10, expected: 10},
		{limit: 20, expected: 20},
		{limit: 0, expected: 20},   // Default limit
		{limit: 200, expected: 20}, // Max limit enforced
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("limit_%d", tc.limit), func(t *testing.T) {
			results, err := store.Search(ctx, "programming", tc.limit)
			require.NoError(t, err)
			assert.Len(t, results, tc.expected)
		})
	}
}

// TestSearchCaseSensitivity tests case-insensitive search
func TestSearchCaseSensitivity(t *testing.T) {
	// Skip if no database available
	db := testdb.GetTestDB(t)
	if db == nil {
		t.Skip("Database not available for testing")
	}

	store := conversation.NewStore(db, conversation.WithLogger(logger.NewConsoleLogger()))
	ctx := context.Background()

	// Create conversation with mixed case
	conv, err := store.Create(ctx)
	require.NoError(t, err)
	conv.Title = "GoLang PROGRAMMING Best Practices"
	conv.Summary = "Discussion about GOLANG programming"

	// Test various case combinations
	queries := []string{
		"golang",
		"GOLANG",
		"GoLang",
		"gOlAnG",
		"programming",
		"PROGRAMMING",
		"Programming",
	}

	for _, query := range queries {
		t.Run(query, func(t *testing.T) {
			results, err := store.Search(ctx, query, 10)
			require.NoError(t, err)
			assert.NotEmpty(t, results, "Should find result for query: %s", query)
		})
	}
}

// TestEmptySearch tests edge cases for search
func TestEmptySearch(t *testing.T) {
	// Skip if no database available
	db := testdb.GetTestDB(t)
	if db == nil {
		t.Skip("Database not available for testing")
	}

	store := conversation.NewStore(db, conversation.WithLogger(logger.NewConsoleLogger()))
	ctx := context.Background()

	// Test empty query
	_, err := store.Search(ctx, "", 10)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "search query cannot be empty")

	// Test whitespace only query
	_, err = store.Search(ctx, "   ", 10)
	assert.Error(t, err)
}

// TestConversationNotFound tests getting non-existent conversation
func TestConversationNotFound(t *testing.T) {
	// Skip if no database available
	db := testdb.GetTestDB(t)
	if db == nil {
		t.Skip("Database not available for testing")
	}

	store := conversation.NewStore(db, conversation.WithLogger(logger.NewConsoleLogger()))
	ctx := context.Background()

	// Try to get non-existent conversation
	randomID := uuid.New()
	_, err := store.Get(ctx, randomID)
	assert.Error(t, err)

	// Try to get messages for non-existent conversation
	messages, err := store.GetMessages(ctx, randomID, 10)
	// This might not error if it returns empty list
	// Depends on implementation
	if err == nil {
		assert.Empty(t, messages)
	}
}
