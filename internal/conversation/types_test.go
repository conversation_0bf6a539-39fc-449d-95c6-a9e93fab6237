package conversation

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestConversationStruct(t *testing.T) {
	now := time.Now()
	id := uuid.New()
	lastMessageTime := now.Add(-time.Hour)

	conv := Conversation{
		ID:            id,
		Title:         "Test Conversation",
		Summary:       "A test conversation summary",
		Metadata:      Metadata{},
		LastMessageAt: &lastMessageTime,
		CreatedAt:     now,
		UpdatedAt:     now,
	}

	assert.Equal(t, id, conv.ID)
	assert.Equal(t, "Test Conversation", conv.Title)
	assert.Equal(t, "A test conversation summary", conv.Summary)
	assert.NotNil(t, conv.LastMessageAt)
	assert.Equal(t, lastMessageTime, *conv.LastMessageAt)
	assert.Equal(t, now, conv.CreatedAt)
	assert.Equal(t, now, conv.UpdatedAt)
}

func TestMetadata(t *testing.T) {
	t.Run("empty metadata", func(t *testing.T) {
		var meta Metadata
		data, err := json.Marshal(meta)
		require.NoError(t, err)

		var decoded Metadata
		err = json.Unmarshal(data, &decoded)
		require.NoError(t, err)
		assert.Equal(t, meta, decoded)
	})

	t.Run("full metadata", func(t *testing.T) {
		meta := Metadata{
			PreferredModel:    "gpt-4",
			PreferredProvider: "openai",
			Topic:             "golang",
			Tags:              []string{"programming", "go", "testing"},
			Custom: map[string]string{
				"project": "assistant-go",
				"version": "1.0.0",
			},
		}

		data, err := json.Marshal(meta)
		require.NoError(t, err)

		var decoded Metadata
		err = json.Unmarshal(data, &decoded)
		require.NoError(t, err)
		assert.Equal(t, meta, decoded)
	})

	t.Run("omitempty fields", func(t *testing.T) {
		meta := Metadata{
			Topic: "only-topic",
		}

		data, err := json.Marshal(meta)
		require.NoError(t, err)

		// Check that empty fields are omitted
		var raw map[string]any
		err = json.Unmarshal(data, &raw)
		require.NoError(t, err)

		assert.Equal(t, "only-topic", raw["topic"])
		assert.NotContains(t, raw, "preferred_model")
		assert.NotContains(t, raw, "preferred_provider")
		assert.NotContains(t, raw, "tags")
		assert.NotContains(t, raw, "custom")
	})
}

func TestMessageStruct(t *testing.T) {
	now := time.Now()
	msgID := uuid.New()
	convID := uuid.New()

	msg := Message{
		ID:             msgID,
		ConversationID: convID,
		Role:           RoleUser,
		Content:        "Hello, world!",
		Metadata:       MessageMetadata{},
		TokensUsed:     10,
		CreatedAt:      now,
	}

	assert.Equal(t, msgID, msg.ID)
	assert.Equal(t, convID, msg.ConversationID)
	assert.Equal(t, RoleUser, msg.Role)
	assert.Equal(t, "Hello, world!", msg.Content)
	assert.Equal(t, int32(10), msg.TokensUsed)
	assert.Equal(t, now, msg.CreatedAt)
}

func TestMessageMetadata(t *testing.T) {
	t.Run("empty metadata", func(t *testing.T) {
		var meta MessageMetadata
		data, err := json.Marshal(meta)
		require.NoError(t, err)

		var decoded MessageMetadata
		err = json.Unmarshal(data, &decoded)
		require.NoError(t, err)
		assert.Equal(t, meta, decoded)
	})

	t.Run("full metadata", func(t *testing.T) {
		toolInput := json.RawMessage(`{"query": "test"}`)
		toolOutput := json.RawMessage(`{"result": "success"}`)

		meta := MessageMetadata{
			Model:       "gpt-4",
			Provider:    "openai",
			Temperature: 0.7,
			ToolCalls: []ToolCall{
				{
					ID:     "call-123",
					Name:   "search",
					Input:  toolInput,
					Output: toolOutput,
				},
			},
			Custom: map[string]string{
				"request_id": "req-456",
			},
			ProcessingMS: 1500,
		}

		data, err := json.Marshal(meta)
		require.NoError(t, err)

		var decoded MessageMetadata
		err = json.Unmarshal(data, &decoded)
		require.NoError(t, err)
		assert.Equal(t, meta.Model, decoded.Model)
		assert.Equal(t, meta.Provider, decoded.Provider)
		assert.Equal(t, meta.Temperature, decoded.Temperature)
		assert.Len(t, decoded.ToolCalls, 1)
		assert.Equal(t, "call-123", decoded.ToolCalls[0].ID)
		assert.Equal(t, "search", decoded.ToolCalls[0].Name)
		assert.Equal(t, meta.ProcessingMS, decoded.ProcessingMS)
	})

	t.Run("tool calls with raw json", func(t *testing.T) {
		// Test that json.RawMessage is preserved correctly
		complexInput := json.RawMessage(`{"nested": {"field": "value"}, "array": [1,2,3]}`)

		toolCall := ToolCall{
			ID:    "test-id",
			Name:  "complex-tool",
			Input: complexInput,
		}

		data, err := json.Marshal(toolCall)
		require.NoError(t, err)

		var decoded ToolCall
		err = json.Unmarshal(data, &decoded)
		require.NoError(t, err)

		// Verify the raw JSON is preserved
		assert.JSONEq(t, string(complexInput), string(decoded.Input))
	})
}

func TestIsValidRole(t *testing.T) {
	tests := []struct {
		name     string
		role     string
		expected bool
	}{
		{
			name:     "valid user role",
			role:     RoleUser,
			expected: true,
		},
		{
			name:     "valid assistant role",
			role:     RoleAssistant,
			expected: true,
		},
		{
			name:     "valid system role",
			role:     RoleSystem,
			expected: true,
		},
		{
			name:     "invalid role",
			role:     "invalid",
			expected: false,
		},
		{
			name:     "empty role",
			role:     "",
			expected: false,
		},
		{
			name:     "uppercase role",
			role:     "USER",
			expected: false,
		},
		{
			name:     "role with spaces",
			role:     "user ",
			expected: false,
		},
		{
			name:     "numeric role",
			role:     "123",
			expected: false,
		},
		{
			name:     "special characters",
			role:     "user!",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsValidRole(tt.role)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestRoleConstants(t *testing.T) {
	// Ensure role constants have expected values
	assert.Equal(t, "user", RoleUser)
	assert.Equal(t, "assistant", RoleAssistant)
	assert.Equal(t, "system", RoleSystem)
}

// Benchmark tests
func BenchmarkIsValidRole(b *testing.B) {
	roles := []string{RoleUser, RoleAssistant, RoleSystem, "invalid", ""}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = IsValidRole(roles[i%len(roles)])
	}
}

func BenchmarkMetadataJSON(b *testing.B) {
	meta := Metadata{
		PreferredModel:    "gpt-4",
		PreferredProvider: "openai",
		Topic:             "golang",
		Tags:              []string{"programming", "go", "testing"},
		Custom: map[string]string{
			"project": "assistant-go",
			"version": "1.0.0",
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		data, _ := json.Marshal(meta)
		var decoded Metadata
		_ = json.Unmarshal(data, &decoded)
	}
}

// Fuzzing tests
func FuzzIsValidRole(f *testing.F) {
	// Add seed corpus
	f.Add("user")
	f.Add("assistant")
	f.Add("system")
	f.Add("")
	f.Add("invalid")
	f.Add("USER")
	f.Add("user ")
	f.Add(" user")

	f.Fuzz(func(t *testing.T, role string) {
		result := IsValidRole(role)

		// Verify the result is consistent with expectations
		switch role {
		case RoleUser, RoleAssistant, RoleSystem:
			assert.True(t, result, "Expected valid role for: %s", role)
		default:
			assert.False(t, result, "Expected invalid role for: %s", role)
		}
	})
}
