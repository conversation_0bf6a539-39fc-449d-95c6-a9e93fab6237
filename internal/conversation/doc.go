// Package conversation provides conversation management functionality.
//
// This package follows Go standard library design principles:
//   - No global state - all functionality requires explicit dependencies
//   - Interface-based design for testability
//   - Clear separation between types and behavior
//   - Consistent error handling with context
//
// Basic usage:
//
//	db, err := pgxpool.New(ctx, databaseURL)
//	if err != nil {
//	    return err
//	}
//
//	store := conversation.NewStore(db, conversation.WithLogger(logger))
//
//	// Create a conversation
//	conv, err := store.Create(ctx, userID)
//	if err != nil {
//	    return err
//	}
//
//	// Add messages
//	err = store.AddMessage(ctx, conv.ID, conversation.Message{
//	    Role:    conversation.RoleUser,
//	    Content: "Hello, assistant!",
//	})
//
// The package is designed to work with PostgreSQL through sqlc-generated
// queries, providing type safety and performance.
package conversation
