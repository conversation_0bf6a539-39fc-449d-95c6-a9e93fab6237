// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: conversation_search.sql

package sqlc

import (
	"context"
	"database/sql"
	"encoding/json"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

const SearchConversationMessages = `-- name: SearchConversationMessages :many
SELECT 
    m.id, m.conversation_id, m.role, m.content, m.tokens_used, m.metadata, m.created_at,
    c.title as conversation_title,
    ts_rank(m.search_vector, plainto_tsquery('english', $1::text)) AS rank
FROM messages m
JOIN conversations c ON m.conversation_id = c.id
WHERE 
    m.search_vector @@ plainto_tsquery('english', $1::text)
    AND ($2::uuid IS NULL OR m.conversation_id = $2)
ORDER BY 
    rank DESC,
    m.created_at DESC
LIMIT $4
OFFSET $3
`

type SearchConversationMessagesParams struct {
	SearchQuery    string      `json:"search_query"`
	ConversationID pgtype.UUID `json:"conversation_id"`
	OffsetCount    int32       `json:"offset_count"`
	LimitCount     int32       `json:"limit_count"`
}

type SearchConversationMessagesRow struct {
	ID                pgtype.UUID     `json:"id"`
	ConversationID    pgtype.UUID     `json:"conversation_id"`
	Role              MessageRole     `json:"role"`
	Content           string          `json:"content"`
	TokensUsed        int32           `json:"tokens_used"`
	Metadata          json.RawMessage `json:"metadata"`
	CreatedAt         time.Time       `json:"created_at"`
	ConversationTitle string          `json:"conversation_title"`
	Rank              float32         `json:"rank"`
}

// Search messages within conversations
func (q *Queries) SearchConversationMessages(ctx context.Context, arg SearchConversationMessagesParams) ([]*SearchConversationMessagesRow, error) {
	rows, err := q.db.Query(ctx, SearchConversationMessages,
		arg.SearchQuery,
		arg.ConversationID,
		arg.OffsetCount,
		arg.LimitCount,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*SearchConversationMessagesRow{}
	for rows.Next() {
		var i SearchConversationMessagesRow
		if err := rows.Scan(
			&i.ID,
			&i.ConversationID,
			&i.Role,
			&i.Content,
			&i.TokensUsed,
			&i.Metadata,
			&i.CreatedAt,
			&i.ConversationTitle,
			&i.Rank,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const SearchConversations = `-- name: SearchConversations :many
SELECT 
    c.id, c.title, c.summary, c.status, c.metadata, c.created_at, c.updated_at, c.last_message_at, c.message_count, c.total_tokens,
    ts_rank(c.search_vector, plainto_tsquery('english', $1::text)) AS rank
FROM conversations c
WHERE 
    c.search_vector @@ plainto_tsquery('english', $1::text)
    AND c.status = $2
ORDER BY 
    rank DESC,
    c.last_message_at DESC NULLS LAST
LIMIT $4
OFFSET $3
`

type SearchConversationsParams struct {
	SearchQuery string             `json:"search_query"`
	Status      ConversationStatus `json:"status"`
	OffsetCount int32              `json:"offset_count"`
	LimitCount  int32              `json:"limit_count"`
}

type SearchConversationsRow struct {
	ID            pgtype.UUID        `json:"id"`
	Title         string             `json:"title"`
	Summary       pgtype.Text        `json:"summary"`
	Status        ConversationStatus `json:"status"`
	Metadata      json.RawMessage    `json:"metadata"`
	CreatedAt     time.Time          `json:"created_at"`
	UpdatedAt     time.Time          `json:"updated_at"`
	LastMessageAt sql.NullTime       `json:"last_message_at"`
	MessageCount  int32              `json:"message_count"`
	TotalTokens   int32              `json:"total_tokens"`
	Rank          float32            `json:"rank"`
}

// Search conversations using full-text search
func (q *Queries) SearchConversations(ctx context.Context, arg SearchConversationsParams) ([]*SearchConversationsRow, error) {
	rows, err := q.db.Query(ctx, SearchConversations,
		arg.SearchQuery,
		arg.Status,
		arg.OffsetCount,
		arg.LimitCount,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*SearchConversationsRow{}
	for rows.Next() {
		var i SearchConversationsRow
		if err := rows.Scan(
			&i.ID,
			&i.Title,
			&i.Summary,
			&i.Status,
			&i.Metadata,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.LastMessageAt,
			&i.MessageCount,
			&i.TotalTokens,
			&i.Rank,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const SearchConversationsWithHighlight = `-- name: SearchConversationsWithHighlight :many
SELECT 
    c.id, c.title, c.summary, c.status, c.metadata, c.created_at, c.updated_at, c.last_message_at, c.message_count, c.total_tokens,
    ts_headline('english', c.title, plainto_tsquery('english', $1::text), 
        'HighlightAll=true, StartSel=<<, StopSel=>>') as highlighted_title,
    ts_headline('english', COALESCE(c.summary, ''), plainto_tsquery('english', $1::text), 
        'HighlightAll=true, StartSel=<<, StopSel=>>') as highlighted_summary,
    ts_rank(c.search_vector, plainto_tsquery('english', $1::text)) AS rank
FROM conversations c
WHERE 
    c.search_vector @@ plainto_tsquery('english', $1::text)
    AND c.status = $2
ORDER BY 
    rank DESC,
    c.last_message_at DESC NULLS LAST
LIMIT $3
`

type SearchConversationsWithHighlightParams struct {
	SearchQuery string             `json:"search_query"`
	Status      ConversationStatus `json:"status"`
	LimitCount  int32              `json:"limit_count"`
}

type SearchConversationsWithHighlightRow struct {
	ID                 pgtype.UUID        `json:"id"`
	Title              string             `json:"title"`
	Summary            pgtype.Text        `json:"summary"`
	Status             ConversationStatus `json:"status"`
	Metadata           json.RawMessage    `json:"metadata"`
	CreatedAt          time.Time          `json:"created_at"`
	UpdatedAt          time.Time          `json:"updated_at"`
	LastMessageAt      sql.NullTime       `json:"last_message_at"`
	MessageCount       int32              `json:"message_count"`
	TotalTokens        int32              `json:"total_tokens"`
	HighlightedTitle   []byte             `json:"highlighted_title"`
	HighlightedSummary []byte             `json:"highlighted_summary"`
	Rank               float32            `json:"rank"`
}

// Search with highlighted results
func (q *Queries) SearchConversationsWithHighlight(ctx context.Context, arg SearchConversationsWithHighlightParams) ([]*SearchConversationsWithHighlightRow, error) {
	rows, err := q.db.Query(ctx, SearchConversationsWithHighlight, arg.SearchQuery, arg.Status, arg.LimitCount)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*SearchConversationsWithHighlightRow{}
	for rows.Next() {
		var i SearchConversationsWithHighlightRow
		if err := rows.Scan(
			&i.ID,
			&i.Title,
			&i.Summary,
			&i.Status,
			&i.Metadata,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.LastMessageAt,
			&i.MessageCount,
			&i.TotalTokens,
			&i.HighlightedTitle,
			&i.HighlightedSummary,
			&i.Rank,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
