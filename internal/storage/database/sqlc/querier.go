// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package sqlc

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

type Querier interface {
	AddTaskLabel(ctx context.Context, arg AddTaskLabelParams) (*Task, error)
	ArchiveConversation(ctx context.Context, id pgtype.UUID) (*Conversation, error)
	BulkSetSystemSettings(ctx context.Context, arg BulkSetSystemSettingsParams) error
	CancelPendingToolExecutions(ctx context.Context, conversationID pgtype.UUID) error
	CleanupJobs(ctx context.Context) error
	CompleteTask(ctx context.Context, id pgtype.UUID) (*Task, error)
	CompleteToolExecution(ctx context.Context, arg CompleteToolExecutionParams) (*ToolExecution, error)
	CountByType(ctx context.Context) ([]*CountByTypeRow, error)
	CountEmbeddingsBySource(ctx context.Context) ([]*CountEmbeddingsBySourceRow, error)
	CountMemoryBackups(ctx context.Context) (int32, error)
	CountMessagesByRole(ctx context.Context, conversationID pgtype.UUID) ([]*CountMessagesByRoleRow, error)
	CountSystemSettings(ctx context.Context) (int32, error)
	CreateConversation(ctx context.Context, arg CreateConversationParams) (*Conversation, error)
	CreateEmbedding(ctx context.Context, arg CreateEmbeddingParams) (*Embedding, error)
	CreateHistory(ctx context.Context, arg CreateHistoryParams) error
	// Queue operations
	CreateJob(ctx context.Context, arg CreateJobParams) (*Queue, error)
	CreateMemory(ctx context.Context, arg CreateMemoryParams) (*Memory, error)
	CreateMemoryBackup(ctx context.Context, arg CreateMemoryBackupParams) (*MemoryBackup, error)
	CreateMergedMemory(ctx context.Context, arg CreateMergedMemoryParams) (*Memory, error)
	CreateMessage(ctx context.Context, arg CreateMessageParams) (*Message, error)
	CreateRelation(ctx context.Context, arg CreateRelationParams) (*Relation, error)
	CreateRelationsBatch(ctx context.Context, arg []CreateRelationsBatchParams) (int64, error)
	CreateTask(ctx context.Context, arg CreateTaskParams) (*Task, error)
	CreateToolExecution(ctx context.Context, arg CreateToolExecutionParams) (*ToolExecution, error)
	DeactivateMany(ctx context.Context, dollar_1 []pgtype.UUID) error
	DeactivateMemory(ctx context.Context, id pgtype.UUID) error
	DeleteConversation(ctx context.Context, id pgtype.UUID) error
	DeleteConversationMessages(ctx context.Context, conversationID pgtype.UUID) error
	DeleteEmbedding(ctx context.Context, id pgtype.UUID) error
	DeleteEmbeddingBySource(ctx context.Context, arg DeleteEmbeddingBySourceParams) error
	DeleteMemoryBackup(ctx context.Context, id pgtype.UUID) error
	DeleteMessage(ctx context.Context, id pgtype.UUID) error
	DeleteOldEmbeddings(ctx context.Context, arg DeleteOldEmbeddingsParams) error
	DeleteOldMemoryBackups(ctx context.Context, arg DeleteOldMemoryBackupsParams) error
	DeleteOldToolExecutions(ctx context.Context, daysOld interface{}) error
	DeleteSystemSetting(ctx context.Context, key string) error
	DeleteTask(ctx context.Context, id pgtype.UUID) error
	ExistsSystemSetting(ctx context.Context, key string) (bool, error)
	FailToolExecution(ctx context.Context, arg FailToolExecutionParams) (*ToolExecution, error)
	GetAllHistory(ctx context.Context, limit int32) ([]*History, error)
	GetBackupStats(ctx context.Context) (*GetBackupStatsRow, error)
	GetBackupsWithinTimeRange(ctx context.Context, arg GetBackupsWithinTimeRangeParams) ([]*MemoryBackup, error)
	GetConversation(ctx context.Context, id pgtype.UUID) (*Conversation, error)
	GetConversationStats(ctx context.Context) (*GetConversationStatsRow, error)
	GetConversationTokenUsage(ctx context.Context, conversationID pgtype.UUID) (*GetConversationTokenUsageRow, error)
	GetEmbedding(ctx context.Context, id pgtype.UUID) (*Embedding, error)
	GetEmbeddingBySource(ctx context.Context, arg GetEmbeddingBySourceParams) (*Embedding, error)
	GetEmbeddingStats(ctx context.Context) (*GetEmbeddingStatsRow, error)
	GetHistory(ctx context.Context, arg GetHistoryParams) ([]*History, error)
	GetHistoryByAction(ctx context.Context, arg GetHistoryByActionParams) ([]*History, error)
	GetLastMessages(ctx context.Context, arg GetLastMessagesParams) ([]*Message, error)
	GetLatestMemoryBackup(ctx context.Context) (*MemoryBackup, error)
	GetMemory(ctx context.Context, id pgtype.UUID) (*Memory, error)
	GetMemoryBackup(ctx context.Context, id pgtype.UUID) (*MemoryBackup, error)
	GetMemoryBySemantic(ctx context.Context, semanticID string) (*Memory, error)
	GetMessage(ctx context.Context, id pgtype.UUID) (*Message, error)
	GetMultipleSettings(ctx context.Context, keys []string) ([]*SystemSetting, error)
	GetPendingJobs(ctx context.Context, limit int32) ([]*Queue, error)
	GetRecentConversationsWithMessageCount(ctx context.Context, limitCount int32) ([]*GetRecentConversationsWithMessageCountRow, error)
	GetRelated(ctx context.Context, arg GetRelatedParams) ([]*GetRelatedRow, error)
	GetRelationsByMemory(ctx context.Context, fromID pgtype.UUID) ([]*Relation, error)
	GetSettingHistory(ctx context.Context, key string) ([]*GetSettingHistoryRow, error)
	GetStats(ctx context.Context) (*GetStatsRow, error)
	GetSystemSetting(ctx context.Context, key string) (*SystemSetting, error)
	GetSystemSettingKeys(ctx context.Context) ([]string, error)
	GetTask(ctx context.Context, id pgtype.UUID) (*Task, error)
	GetTaskStats(ctx context.Context) (*GetTaskStatsRow, error)
	GetTasksByPriority(ctx context.Context) ([]*GetTasksByPriorityRow, error)
	GetToolExecution(ctx context.Context, id pgtype.UUID) (*ToolExecution, error)
	GetToolExecutionStats(ctx context.Context, arg GetToolExecutionStatsParams) (*GetToolExecutionStatsRow, error)
	GetToolUsageByName(ctx context.Context, since pgtype.Timestamptz) ([]*GetToolUsageByNameRow, error)
	IncrementAccess(ctx context.Context, id pgtype.UUID) error
	ListActiveConversations(ctx context.Context, limitCount int32) ([]*Conversation, error)
	ListByKeyword(ctx context.Context, arg ListByKeywordParams) ([]*Memory, error)
	ListByType(ctx context.Context, arg ListByTypeParams) ([]*Memory, error)
	ListConversations(ctx context.Context, arg ListConversationsParams) ([]*Conversation, error)
	ListEmbeddingsBySource(ctx context.Context, arg ListEmbeddingsBySourceParams) ([]*Embedding, error)
	ListInTimeRange(ctx context.Context, arg ListInTimeRangeParams) ([]*Memory, error)
	ListMemoryBackups(ctx context.Context, arg ListMemoryBackupsParams) ([]*MemoryBackup, error)
	ListMessages(ctx context.Context, arg ListMessagesParams) ([]*Message, error)
	ListOverdueTasks(ctx context.Context) ([]*Task, error)
	ListPendingTasks(ctx context.Context, limitCount int32) ([]*Task, error)
	ListPendingToolExecutions(ctx context.Context, limitCount int32) ([]*ToolExecution, error)
	ListRecent(ctx context.Context, limit int32) ([]*Memory, error)
	ListRecentMessages(ctx context.Context, arg ListRecentMessagesParams) ([]*Message, error)
	ListRecurring(ctx context.Context) ([]*Memory, error)
	ListSystemSettings(ctx context.Context) ([]*SystemSetting, error)
	ListSystemSettingsByPrefix(ctx context.Context, prefix pgtype.Text) ([]*SystemSetting, error)
	ListTasks(ctx context.Context, arg ListTasksParams) ([]*Task, error)
	ListTasksByLabel(ctx context.Context, arg ListTasksByLabelParams) ([]*Task, error)
	ListTasksDueToday(ctx context.Context) ([]*Task, error)
	ListToolExecutions(ctx context.Context, arg ListToolExecutionsParams) ([]*ToolExecution, error)
	ListToolExecutionsByConversation(ctx context.Context, arg ListToolExecutionsByConversationParams) ([]*ToolExecution, error)
	RemoveTaskLabel(ctx context.Context, arg RemoveTaskLabelParams) (*Task, error)
	SearchByAttributes(ctx context.Context, arg SearchByAttributesParams) ([]*Memory, error)
	// Search messages within conversations
	SearchConversationMessages(ctx context.Context, arg SearchConversationMessagesParams) ([]*SearchConversationMessagesRow, error)
	// Search conversations using full-text search
	SearchConversations(ctx context.Context, arg SearchConversationsParams) ([]*SearchConversationsRow, error)
	// Search with highlighted results
	SearchConversationsWithHighlight(ctx context.Context, arg SearchConversationsWithHighlightParams) ([]*SearchConversationsWithHighlightRow, error)
	// Hybrid search combining vector similarity and text search (NO LIMIT - returns all matches)
	SearchMemories(ctx context.Context, arg SearchMemoriesParams) ([]*SearchMemoriesRow, error)
	SearchMessages(ctx context.Context, arg SearchMessagesParams) ([]*Message, error)
	SearchSimilarEmbeddings(ctx context.Context, arg SearchSimilarEmbeddingsParams) ([]*SearchSimilarEmbeddingsRow, error)
	// Optimized vector similarity search that leverages HNSW/IVFFlat indexes
	// This query is specifically designed for efficient nearest neighbor search
	SearchSimilarWithLimit(ctx context.Context, arg SearchSimilarWithLimitParams) ([]*SearchSimilarWithLimitRow, error)
	SearchTasks(ctx context.Context, arg SearchTasksParams) ([]*Task, error)
	SetEmbedding(ctx context.Context, arg SetEmbeddingParams) error
	SetSystemSetting(ctx context.Context, arg SetSystemSettingParams) (*SystemSetting, error)
	StartToolExecution(ctx context.Context, id pgtype.UUID) (*ToolExecution, error)
	UpdateConversation(ctx context.Context, arg UpdateConversationParams) (*Conversation, error)
	UpdateEmbedding(ctx context.Context, arg UpdateEmbeddingParams) (*Embedding, error)
	UpdateJob(ctx context.Context, arg UpdateJobParams) error
	UpdateMemory(ctx context.Context, arg UpdateMemoryParams) error
	UpdateRelation(ctx context.Context, arg UpdateRelationParams) error
	UpdateSystemSetting(ctx context.Context, arg UpdateSystemSettingParams) (*SystemSetting, error)
	UpdateTask(ctx context.Context, arg UpdateTaskParams) (*Task, error)
	UpdateTaskLabels(ctx context.Context, arg UpdateTaskLabelsParams) (*Task, error)
	UpdateToolExecution(ctx context.Context, arg UpdateToolExecutionParams) (*ToolExecution, error)
}

var _ Querier = (*Queries)(nil)
