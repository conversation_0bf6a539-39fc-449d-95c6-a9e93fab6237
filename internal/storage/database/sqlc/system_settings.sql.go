// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: system_settings.sql

package sqlc

import (
	"context"
	"encoding/json"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

const BulkSetSystemSettings = `-- name: BulkSetSystemSettings :exec
INSERT INTO system_settings (key, value, description)
SELECT 
    unnest($1::text[]),
    unnest($2::jsonb[]),
    unnest($3::text[])
ON CONFLICT (key) DO UPDATE
SET 
    value = EXCLUDED.value,
    description = COALESCE(EXCLUDED.description, system_settings.description)
`

type BulkSetSystemSettingsParams struct {
	Keys         []string `json:"keys"`
	Values       [][]byte `json:"values"`
	Descriptions []string `json:"descriptions"`
}

func (q *Queries) BulkSetSystemSettings(ctx context.Context, arg BulkSetSystemSettingsParams) error {
	_, err := q.db.Exec(ctx, BulkSetSystemSettings, arg.Keys, arg.Values, arg.Descriptions)
	return err
}

const CountSystemSettings = `-- name: CountSystemSettings :one
SELECT COUNT(*)::int as total_settings
FROM system_settings
`

func (q *Queries) CountSystemSettings(ctx context.Context) (int32, error) {
	row := q.db.QueryRow(ctx, CountSystemSettings)
	var total_settings int32
	err := row.Scan(&total_settings)
	return total_settings, err
}

const DeleteSystemSetting = `-- name: DeleteSystemSetting :exec
DELETE FROM system_settings
WHERE key = $1
`

func (q *Queries) DeleteSystemSetting(ctx context.Context, key string) error {
	_, err := q.db.Exec(ctx, DeleteSystemSetting, key)
	return err
}

const ExistsSystemSetting = `-- name: ExistsSystemSetting :one
SELECT EXISTS (
    SELECT 1 FROM system_settings
    WHERE key = $1
)
`

func (q *Queries) ExistsSystemSetting(ctx context.Context, key string) (bool, error) {
	row := q.db.QueryRow(ctx, ExistsSystemSetting, key)
	var exists bool
	err := row.Scan(&exists)
	return exists, err
}

const GetMultipleSettings = `-- name: GetMultipleSettings :many
SELECT key, value, description, created_at, updated_at FROM system_settings
WHERE key = ANY($1::text[])
ORDER BY key ASC
`

func (q *Queries) GetMultipleSettings(ctx context.Context, keys []string) ([]*SystemSetting, error) {
	rows, err := q.db.Query(ctx, GetMultipleSettings, keys)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*SystemSetting{}
	for rows.Next() {
		var i SystemSetting
		if err := rows.Scan(
			&i.Key,
			&i.Value,
			&i.Description,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetSettingHistory = `-- name: GetSettingHistory :many
SELECT 
    key,
    value,
    created_at,
    updated_at
FROM system_settings
WHERE key = $1
ORDER BY updated_at DESC
`

type GetSettingHistoryRow struct {
	Key       string          `json:"key"`
	Value     json.RawMessage `json:"value"`
	CreatedAt time.Time       `json:"created_at"`
	UpdatedAt time.Time       `json:"updated_at"`
}

func (q *Queries) GetSettingHistory(ctx context.Context, key string) ([]*GetSettingHistoryRow, error) {
	rows, err := q.db.Query(ctx, GetSettingHistory, key)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*GetSettingHistoryRow{}
	for rows.Next() {
		var i GetSettingHistoryRow
		if err := rows.Scan(
			&i.Key,
			&i.Value,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetSystemSetting = `-- name: GetSystemSetting :one
SELECT key, value, description, created_at, updated_at FROM system_settings
WHERE key = $1
LIMIT 1
`

func (q *Queries) GetSystemSetting(ctx context.Context, key string) (*SystemSetting, error) {
	row := q.db.QueryRow(ctx, GetSystemSetting, key)
	var i SystemSetting
	err := row.Scan(
		&i.Key,
		&i.Value,
		&i.Description,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const GetSystemSettingKeys = `-- name: GetSystemSettingKeys :many
SELECT key
FROM system_settings
ORDER BY key ASC
`

func (q *Queries) GetSystemSettingKeys(ctx context.Context) ([]string, error) {
	rows, err := q.db.Query(ctx, GetSystemSettingKeys)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []string{}
	for rows.Next() {
		var key string
		if err := rows.Scan(&key); err != nil {
			return nil, err
		}
		items = append(items, key)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const ListSystemSettings = `-- name: ListSystemSettings :many
SELECT key, value, description, created_at, updated_at FROM system_settings
ORDER BY key ASC
`

func (q *Queries) ListSystemSettings(ctx context.Context) ([]*SystemSetting, error) {
	rows, err := q.db.Query(ctx, ListSystemSettings)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*SystemSetting{}
	for rows.Next() {
		var i SystemSetting
		if err := rows.Scan(
			&i.Key,
			&i.Value,
			&i.Description,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const ListSystemSettingsByPrefix = `-- name: ListSystemSettingsByPrefix :many
SELECT key, value, description, created_at, updated_at FROM system_settings
WHERE key LIKE $1 || '%'
ORDER BY key ASC
`

func (q *Queries) ListSystemSettingsByPrefix(ctx context.Context, prefix pgtype.Text) ([]*SystemSetting, error) {
	rows, err := q.db.Query(ctx, ListSystemSettingsByPrefix, prefix)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*SystemSetting{}
	for rows.Next() {
		var i SystemSetting
		if err := rows.Scan(
			&i.Key,
			&i.Value,
			&i.Description,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const SetSystemSetting = `-- name: SetSystemSetting :one
INSERT INTO system_settings (
    key,
    value,
    description
) VALUES (
    $1,
    $2,
    $3
)
ON CONFLICT (key) DO UPDATE
SET 
    value = EXCLUDED.value,
    description = COALESCE(EXCLUDED.description, system_settings.description)
RETURNING key, value, description, created_at, updated_at
`

type SetSystemSettingParams struct {
	Key         string          `json:"key"`
	Value       json.RawMessage `json:"value"`
	Description pgtype.Text     `json:"description"`
}

func (q *Queries) SetSystemSetting(ctx context.Context, arg SetSystemSettingParams) (*SystemSetting, error) {
	row := q.db.QueryRow(ctx, SetSystemSetting, arg.Key, arg.Value, arg.Description)
	var i SystemSetting
	err := row.Scan(
		&i.Key,
		&i.Value,
		&i.Description,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const UpdateSystemSetting = `-- name: UpdateSystemSetting :one
UPDATE system_settings
SET value = $1
WHERE key = $2
RETURNING key, value, description, created_at, updated_at
`

type UpdateSystemSettingParams struct {
	Value json.RawMessage `json:"value"`
	Key   string          `json:"key"`
}

func (q *Queries) UpdateSystemSetting(ctx context.Context, arg UpdateSystemSettingParams) (*SystemSetting, error) {
	row := q.db.QueryRow(ctx, UpdateSystemSetting, arg.Value, arg.Key)
	var i SystemSetting
	err := row.Scan(
		&i.Key,
		&i.Value,
		&i.Description,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}
