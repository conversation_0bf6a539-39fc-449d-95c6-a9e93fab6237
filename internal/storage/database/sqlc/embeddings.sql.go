// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: embeddings.sql

package sqlc

import (
	"context"
	"encoding/json"

	"github.com/jackc/pgx/v5/pgtype"
	pgvector "github.com/pgvector/pgvector-go"
)

const CountEmbeddingsBySource = `-- name: CountEmbeddingsBySource :many
SELECT
    source_table,
    COUNT(*)::int as count
FROM embeddings
GROUP BY source_table
`

type CountEmbeddingsBySourceRow struct {
	SourceTable string `json:"source_table"`
	Count       int32  `json:"count"`
}

func (q *Queries) CountEmbeddingsBySource(ctx context.Context) ([]*CountEmbeddingsBySourceRow, error) {
	rows, err := q.db.Query(ctx, CountEmbeddingsBySource)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*CountEmbeddingsBySourceRow{}
	for rows.Next() {
		var i CountEmbeddingsBySourceRow
		if err := rows.Scan(&i.SourceTable, &i.Count); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const CreateEmbedding = `-- name: CreateEmbedding :one
INSERT INTO embeddings (
    source_table,
    source_id,
    content,
    embedding,
    model,
    metadata
) VALUES (
    $1,
    $2,
    $3,
    $4,
    COALESCE($5, 'gemini-embedding-001'),
    COALESCE($6::jsonb, '{}')
)
ON CONFLICT (source_table, source_id) DO UPDATE
SET
    content = EXCLUDED.content,
    embedding = EXCLUDED.embedding,
    model = EXCLUDED.model,
    metadata = EXCLUDED.metadata,
    created_at = CURRENT_TIMESTAMP
RETURNING id, source_table, source_id, content, embedding, model, metadata, created_at
`

type CreateEmbeddingParams struct {
	SourceTable string          `json:"source_table"`
	SourceID    pgtype.UUID     `json:"source_id"`
	Content     string          `json:"content"`
	Embedding   pgvector.Vector `json:"embedding"`
	Model       interface{}     `json:"model"`
	Metadata    []byte          `json:"metadata"`
}

func (q *Queries) CreateEmbedding(ctx context.Context, arg CreateEmbeddingParams) (*Embedding, error) {
	row := q.db.QueryRow(ctx, CreateEmbedding,
		arg.SourceTable,
		arg.SourceID,
		arg.Content,
		arg.Embedding,
		arg.Model,
		arg.Metadata,
	)
	var i Embedding
	err := row.Scan(
		&i.ID,
		&i.SourceTable,
		&i.SourceID,
		&i.Content,
		&i.Embedding,
		&i.Model,
		&i.Metadata,
		&i.CreatedAt,
	)
	return &i, err
}

const DeleteEmbedding = `-- name: DeleteEmbedding :exec
DELETE FROM embeddings
WHERE id = $1
`

func (q *Queries) DeleteEmbedding(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, DeleteEmbedding, id)
	return err
}

const DeleteEmbeddingBySource = `-- name: DeleteEmbeddingBySource :exec
DELETE FROM embeddings
WHERE source_table = $1 AND source_id = $2
`

type DeleteEmbeddingBySourceParams struct {
	SourceTable string      `json:"source_table"`
	SourceID    pgtype.UUID `json:"source_id"`
}

func (q *Queries) DeleteEmbeddingBySource(ctx context.Context, arg DeleteEmbeddingBySourceParams) error {
	_, err := q.db.Exec(ctx, DeleteEmbeddingBySource, arg.SourceTable, arg.SourceID)
	return err
}

const DeleteOldEmbeddings = `-- name: DeleteOldEmbeddings :exec
DELETE FROM embeddings
WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '1 day' * $1
AND source_table = COALESCE($2::text, source_table)
`

type DeleteOldEmbeddingsParams struct {
	DaysOld     interface{} `json:"days_old"`
	SourceTable string      `json:"source_table"`
}

func (q *Queries) DeleteOldEmbeddings(ctx context.Context, arg DeleteOldEmbeddingsParams) error {
	_, err := q.db.Exec(ctx, DeleteOldEmbeddings, arg.DaysOld, arg.SourceTable)
	return err
}

const GetEmbedding = `-- name: GetEmbedding :one
SELECT id, source_table, source_id, content, embedding, model, metadata, created_at FROM embeddings
WHERE id = $1
LIMIT 1
`

func (q *Queries) GetEmbedding(ctx context.Context, id pgtype.UUID) (*Embedding, error) {
	row := q.db.QueryRow(ctx, GetEmbedding, id)
	var i Embedding
	err := row.Scan(
		&i.ID,
		&i.SourceTable,
		&i.SourceID,
		&i.Content,
		&i.Embedding,
		&i.Model,
		&i.Metadata,
		&i.CreatedAt,
	)
	return &i, err
}

const GetEmbeddingBySource = `-- name: GetEmbeddingBySource :one
SELECT id, source_table, source_id, content, embedding, model, metadata, created_at FROM embeddings
WHERE source_table = $1 AND source_id = $2
LIMIT 1
`

type GetEmbeddingBySourceParams struct {
	SourceTable string      `json:"source_table"`
	SourceID    pgtype.UUID `json:"source_id"`
}

func (q *Queries) GetEmbeddingBySource(ctx context.Context, arg GetEmbeddingBySourceParams) (*Embedding, error) {
	row := q.db.QueryRow(ctx, GetEmbeddingBySource, arg.SourceTable, arg.SourceID)
	var i Embedding
	err := row.Scan(
		&i.ID,
		&i.SourceTable,
		&i.SourceID,
		&i.Content,
		&i.Embedding,
		&i.Model,
		&i.Metadata,
		&i.CreatedAt,
	)
	return &i, err
}

const GetEmbeddingStats = `-- name: GetEmbeddingStats :one
SELECT
    COUNT(*)::int as total_embeddings,
    COUNT(DISTINCT source_table)::int as source_types,
    MIN(created_at) as oldest_embedding,
    MAX(created_at) as newest_embedding
FROM embeddings
`

type GetEmbeddingStatsRow struct {
	TotalEmbeddings int32       `json:"total_embeddings"`
	SourceTypes     int32       `json:"source_types"`
	OldestEmbedding interface{} `json:"oldest_embedding"`
	NewestEmbedding interface{} `json:"newest_embedding"`
}

func (q *Queries) GetEmbeddingStats(ctx context.Context) (*GetEmbeddingStatsRow, error) {
	row := q.db.QueryRow(ctx, GetEmbeddingStats)
	var i GetEmbeddingStatsRow
	err := row.Scan(
		&i.TotalEmbeddings,
		&i.SourceTypes,
		&i.OldestEmbedding,
		&i.NewestEmbedding,
	)
	return &i, err
}

const ListEmbeddingsBySource = `-- name: ListEmbeddingsBySource :many
SELECT id, source_table, source_id, content, embedding, model, metadata, created_at FROM embeddings
WHERE source_table = $1
ORDER BY created_at DESC
LIMIT COALESCE($3::int, 100)
OFFSET COALESCE($2::int, 0)
`

type ListEmbeddingsBySourceParams struct {
	SourceTable string `json:"source_table"`
	OffsetCount int32  `json:"offset_count"`
	LimitCount  int32  `json:"limit_count"`
}

func (q *Queries) ListEmbeddingsBySource(ctx context.Context, arg ListEmbeddingsBySourceParams) ([]*Embedding, error) {
	rows, err := q.db.Query(ctx, ListEmbeddingsBySource, arg.SourceTable, arg.OffsetCount, arg.LimitCount)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Embedding{}
	for rows.Next() {
		var i Embedding
		if err := rows.Scan(
			&i.ID,
			&i.SourceTable,
			&i.SourceID,
			&i.Content,
			&i.Embedding,
			&i.Model,
			&i.Metadata,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const SearchSimilarEmbeddings = `-- name: SearchSimilarEmbeddings :many
SELECT
    id,
    source_table,
    source_id,
    content,
    1 - (embedding <=> $1) as similarity,
    metadata
FROM embeddings
WHERE
    1 - (embedding <=> $1) > $2
    AND source_table = COALESCE($3::text, source_table)
ORDER BY embedding <=> $1
LIMIT $4
`

type SearchSimilarEmbeddingsParams struct {
	QueryEmbedding      pgvector.Vector `json:"query_embedding"`
	SimilarityThreshold pgvector.Vector `json:"similarity_threshold"`
	SourceTable         string          `json:"source_table"`
	LimitCount          int32           `json:"limit_count"`
}

type SearchSimilarEmbeddingsRow struct {
	ID          pgtype.UUID     `json:"id"`
	SourceTable string          `json:"source_table"`
	SourceID    pgtype.UUID     `json:"source_id"`
	Content     string          `json:"content"`
	Similarity  int32           `json:"similarity"`
	Metadata    json.RawMessage `json:"metadata"`
}

func (q *Queries) SearchSimilarEmbeddings(ctx context.Context, arg SearchSimilarEmbeddingsParams) ([]*SearchSimilarEmbeddingsRow, error) {
	rows, err := q.db.Query(ctx, SearchSimilarEmbeddings,
		arg.QueryEmbedding,
		arg.SimilarityThreshold,
		arg.SourceTable,
		arg.LimitCount,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*SearchSimilarEmbeddingsRow{}
	for rows.Next() {
		var i SearchSimilarEmbeddingsRow
		if err := rows.Scan(
			&i.ID,
			&i.SourceTable,
			&i.SourceID,
			&i.Content,
			&i.Similarity,
			&i.Metadata,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const UpdateEmbedding = `-- name: UpdateEmbedding :one
UPDATE embeddings
SET
    content = $1,
    embedding = $2,
    metadata = COALESCE($3::jsonb, metadata)
WHERE id = $4
RETURNING id, source_table, source_id, content, embedding, model, metadata, created_at
`

type UpdateEmbeddingParams struct {
	Content   string          `json:"content"`
	Embedding pgvector.Vector `json:"embedding"`
	Metadata  []byte          `json:"metadata"`
	ID        pgtype.UUID     `json:"id"`
}

func (q *Queries) UpdateEmbedding(ctx context.Context, arg UpdateEmbeddingParams) (*Embedding, error) {
	row := q.db.QueryRow(ctx, UpdateEmbedding,
		arg.Content,
		arg.Embedding,
		arg.Metadata,
		arg.ID,
	)
	var i Embedding
	err := row.Scan(
		&i.ID,
		&i.SourceTable,
		&i.SourceID,
		&i.Content,
		&i.Embedding,
		&i.Model,
		&i.Metadata,
		&i.CreatedAt,
	)
	return &i, err
}
