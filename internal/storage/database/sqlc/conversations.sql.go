// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: conversations.sql

package sqlc

import (
	"context"
	"database/sql"
	"encoding/json"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

const ArchiveConversation = `-- name: ArchiveConversation :one
UPDATE conversations
SET status = 'archived'
WHERE id = $1
RETURNING id, title, summary, status, metadata, created_at, updated_at, last_message_at, message_count, total_tokens
`

func (q *Queries) ArchiveConversation(ctx context.Context, id pgtype.UUID) (*Conversation, error) {
	row := q.db.QueryRow(ctx, ArchiveConversation, id)
	var i Conversation
	err := row.Scan(
		&i.ID,
		&i.Title,
		&i.Summary,
		&i.Status,
		&i.Metadata,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.LastMessageAt,
		&i.MessageCount,
		&i.TotalTokens,
	)
	return &i, err
}

const CreateConversation = `-- name: CreateConversation :one
INSERT INTO conversations (
    title,
    summary,
    status,
    metadata
) VALUES (
    COALESCE($1::text, '新對話'),
    $2,
    COALESCE($3::conversation_status, 'active'),
    COALESCE($4::jsonb, '{}')
)
RETURNING id, title, summary, status, metadata, created_at, updated_at, last_message_at, message_count, total_tokens
`

type CreateConversationParams struct {
	Title    string             `json:"title"`
	Summary  pgtype.Text        `json:"summary"`
	Status   ConversationStatus `json:"status"`
	Metadata []byte             `json:"metadata"`
}

func (q *Queries) CreateConversation(ctx context.Context, arg CreateConversationParams) (*Conversation, error) {
	row := q.db.QueryRow(ctx, CreateConversation,
		arg.Title,
		arg.Summary,
		arg.Status,
		arg.Metadata,
	)
	var i Conversation
	err := row.Scan(
		&i.ID,
		&i.Title,
		&i.Summary,
		&i.Status,
		&i.Metadata,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.LastMessageAt,
		&i.MessageCount,
		&i.TotalTokens,
	)
	return &i, err
}

const DeleteConversation = `-- name: DeleteConversation :exec
DELETE FROM conversations
WHERE id = $1
`

func (q *Queries) DeleteConversation(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, DeleteConversation, id)
	return err
}

const GetConversation = `-- name: GetConversation :one
SELECT id, title, summary, status, metadata, created_at, updated_at, last_message_at, message_count, total_tokens
FROM conversations
WHERE id = $1
LIMIT 1
`

func (q *Queries) GetConversation(ctx context.Context, id pgtype.UUID) (*Conversation, error) {
	row := q.db.QueryRow(ctx, GetConversation, id)
	var i Conversation
	err := row.Scan(
		&i.ID,
		&i.Title,
		&i.Summary,
		&i.Status,
		&i.Metadata,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.LastMessageAt,
		&i.MessageCount,
		&i.TotalTokens,
	)
	return &i, err
}

const GetConversationStats = `-- name: GetConversationStats :one
SELECT
    COUNT(DISTINCT c.id) as total_conversations,
    COUNT(DISTINCT CASE WHEN c.status = 'active' THEN c.id END) as active_conversations,
    COUNT(m.id) as total_messages,
    SUM(m.tokens_used) as total_tokens
FROM conversations c
LEFT JOIN messages m ON c.id = m.conversation_id
`

type GetConversationStatsRow struct {
	TotalConversations  int64 `json:"total_conversations"`
	ActiveConversations int64 `json:"active_conversations"`
	TotalMessages       int64 `json:"total_messages"`
	TotalTokens         int64 `json:"total_tokens"`
}

func (q *Queries) GetConversationStats(ctx context.Context) (*GetConversationStatsRow, error) {
	row := q.db.QueryRow(ctx, GetConversationStats)
	var i GetConversationStatsRow
	err := row.Scan(
		&i.TotalConversations,
		&i.ActiveConversations,
		&i.TotalMessages,
		&i.TotalTokens,
	)
	return &i, err
}

const GetRecentConversationsWithMessageCount = `-- name: GetRecentConversationsWithMessageCount :many
SELECT 
    c.id, c.title, c.summary, c.status, c.metadata, c.created_at, c.updated_at, c.last_message_at, c.message_count, c.total_tokens,
    COUNT(m.id)::int as message_count,
    MAX(m.created_at) as last_message_time
FROM conversations c
LEFT JOIN messages m ON c.id = m.conversation_id
WHERE c.status = 'active'
GROUP BY c.id
ORDER BY COALESCE(MAX(m.created_at), c.created_at) DESC
LIMIT $1
`

type GetRecentConversationsWithMessageCountRow struct {
	ID              pgtype.UUID        `json:"id"`
	Title           string             `json:"title"`
	Summary         pgtype.Text        `json:"summary"`
	Status          ConversationStatus `json:"status"`
	Metadata        json.RawMessage    `json:"metadata"`
	CreatedAt       time.Time          `json:"created_at"`
	UpdatedAt       time.Time          `json:"updated_at"`
	LastMessageAt   sql.NullTime       `json:"last_message_at"`
	MessageCount    int32              `json:"message_count"`
	TotalTokens     int32              `json:"total_tokens"`
	MessageCount_2  int32              `json:"message_count_2"`
	LastMessageTime interface{}        `json:"last_message_time"`
}

func (q *Queries) GetRecentConversationsWithMessageCount(ctx context.Context, limitCount int32) ([]*GetRecentConversationsWithMessageCountRow, error) {
	rows, err := q.db.Query(ctx, GetRecentConversationsWithMessageCount, limitCount)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*GetRecentConversationsWithMessageCountRow{}
	for rows.Next() {
		var i GetRecentConversationsWithMessageCountRow
		if err := rows.Scan(
			&i.ID,
			&i.Title,
			&i.Summary,
			&i.Status,
			&i.Metadata,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.LastMessageAt,
			&i.MessageCount,
			&i.TotalTokens,
			&i.MessageCount_2,
			&i.LastMessageTime,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const ListActiveConversations = `-- name: ListActiveConversations :many
SELECT id, title, summary, status, metadata, created_at, updated_at, last_message_at, message_count, total_tokens
FROM conversations
WHERE status = 'active'
ORDER BY COALESCE(last_message_at, created_at) DESC
LIMIT $1
`

func (q *Queries) ListActiveConversations(ctx context.Context, limitCount int32) ([]*Conversation, error) {
	rows, err := q.db.Query(ctx, ListActiveConversations, limitCount)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Conversation{}
	for rows.Next() {
		var i Conversation
		if err := rows.Scan(
			&i.ID,
			&i.Title,
			&i.Summary,
			&i.Status,
			&i.Metadata,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.LastMessageAt,
			&i.MessageCount,
			&i.TotalTokens,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const ListConversations = `-- name: ListConversations :many
SELECT id, title, summary, status, metadata, created_at, updated_at, last_message_at, message_count, total_tokens
FROM conversations
WHERE status = COALESCE($1::conversation_status, status)
ORDER BY
    CASE WHEN $2 = 'last_message' THEN last_message_at END DESC NULLS LAST,
    CASE WHEN $2 = 'created' THEN created_at END DESC,
    updated_at DESC
LIMIT COALESCE($4::int, 20)
OFFSET COALESCE($3::int, 0)
`

type ListConversationsParams struct {
	Status      ConversationStatus `json:"status"`
	OrderBy     interface{}        `json:"order_by"`
	OffsetCount int32              `json:"offset_count"`
	LimitCount  int32              `json:"limit_count"`
}

func (q *Queries) ListConversations(ctx context.Context, arg ListConversationsParams) ([]*Conversation, error) {
	rows, err := q.db.Query(ctx, ListConversations,
		arg.Status,
		arg.OrderBy,
		arg.OffsetCount,
		arg.LimitCount,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Conversation{}
	for rows.Next() {
		var i Conversation
		if err := rows.Scan(
			&i.ID,
			&i.Title,
			&i.Summary,
			&i.Status,
			&i.Metadata,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.LastMessageAt,
			&i.MessageCount,
			&i.TotalTokens,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const UpdateConversation = `-- name: UpdateConversation :one
UPDATE conversations
SET
    title = COALESCE($1::text, title),
    summary = COALESCE($2::text, summary),
    status = COALESCE($3::conversation_status, status),
    metadata = COALESCE($4::jsonb, metadata)
WHERE id = $5
RETURNING id, title, summary, status, metadata, created_at, updated_at, last_message_at, message_count, total_tokens
`

type UpdateConversationParams struct {
	Title    string             `json:"title"`
	Summary  string             `json:"summary"`
	Status   ConversationStatus `json:"status"`
	Metadata []byte             `json:"metadata"`
	ID       pgtype.UUID        `json:"id"`
}

func (q *Queries) UpdateConversation(ctx context.Context, arg UpdateConversationParams) (*Conversation, error) {
	row := q.db.QueryRow(ctx, UpdateConversation,
		arg.Title,
		arg.Summary,
		arg.Status,
		arg.Metadata,
		arg.ID,
	)
	var i Conversation
	err := row.Scan(
		&i.ID,
		&i.Title,
		&i.Summary,
		&i.Status,
		&i.Metadata,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.LastMessageAt,
		&i.MessageCount,
		&i.TotalTokens,
	)
	return &i, err
}
