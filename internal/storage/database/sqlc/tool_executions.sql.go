// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: tool_executions.sql

package sqlc

import (
	"context"
	"encoding/json"

	"github.com/jackc/pgx/v5/pgtype"
)

const CancelPendingToolExecutions = `-- name: CancelPendingToolExecutions :exec
UPDATE tool_executions
SET 
    status = 'cancelled',
    completed_at = CURRENT_TIMESTAMP
WHERE 
    conversation_id = $1
    AND status IN ('pending', 'running')
`

func (q *Queries) CancelPendingToolExecutions(ctx context.Context, conversationID pgtype.UUID) error {
	_, err := q.db.Exec(ctx, CancelPendingToolExecutions, conversationID)
	return err
}

const CompleteToolExecution = `-- name: CompleteToolExecution :one
UPDATE tool_executions
SET
    output = $1,
    status = 'success',
    duration_ms = EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - created_at)) * 1000,
    completed_at = CURRENT_TIMESTAMP
WHERE id = $2
RETURNING id, conversation_id, tool_name, input, output, status, error_message, duration_ms, created_at, completed_at
`

type CompleteToolExecutionParams struct {
	Output json.RawMessage `json:"output"`
	ID     pgtype.UUID     `json:"id"`
}

func (q *Queries) CompleteToolExecution(ctx context.Context, arg CompleteToolExecutionParams) (*ToolExecution, error) {
	row := q.db.QueryRow(ctx, CompleteToolExecution, arg.Output, arg.ID)
	var i ToolExecution
	err := row.Scan(
		&i.ID,
		&i.ConversationID,
		&i.ToolName,
		&i.Input,
		&i.Output,
		&i.Status,
		&i.ErrorMessage,
		&i.DurationMs,
		&i.CreatedAt,
		&i.CompletedAt,
	)
	return &i, err
}

const CreateToolExecution = `-- name: CreateToolExecution :one
INSERT INTO tool_executions (
    conversation_id,
    tool_name,
    input,
    status
) VALUES (
    $1,
    $2,
    COALESCE($3::jsonb, '{}'),
    COALESCE($4::tool_status, 'pending')
)
RETURNING id, conversation_id, tool_name, input, output, status, error_message, duration_ms, created_at, completed_at
`

type CreateToolExecutionParams struct {
	ConversationID pgtype.UUID `json:"conversation_id"`
	ToolName       string      `json:"tool_name"`
	Input          []byte      `json:"input"`
	Status         ToolStatus  `json:"status"`
}

func (q *Queries) CreateToolExecution(ctx context.Context, arg CreateToolExecutionParams) (*ToolExecution, error) {
	row := q.db.QueryRow(ctx, CreateToolExecution,
		arg.ConversationID,
		arg.ToolName,
		arg.Input,
		arg.Status,
	)
	var i ToolExecution
	err := row.Scan(
		&i.ID,
		&i.ConversationID,
		&i.ToolName,
		&i.Input,
		&i.Output,
		&i.Status,
		&i.ErrorMessage,
		&i.DurationMs,
		&i.CreatedAt,
		&i.CompletedAt,
	)
	return &i, err
}

const DeleteOldToolExecutions = `-- name: DeleteOldToolExecutions :exec
DELETE FROM tool_executions
WHERE 
    created_at < CURRENT_TIMESTAMP - INTERVAL '1 day' * $1
    AND status IN ('success', 'failed', 'timeout')
`

func (q *Queries) DeleteOldToolExecutions(ctx context.Context, daysOld interface{}) error {
	_, err := q.db.Exec(ctx, DeleteOldToolExecutions, daysOld)
	return err
}

const FailToolExecution = `-- name: FailToolExecution :one
UPDATE tool_executions
SET
    status = 'failed',
    error_message = $1,
    duration_ms = EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - created_at)) * 1000,
    completed_at = CURRENT_TIMESTAMP
WHERE id = $2
RETURNING id, conversation_id, tool_name, input, output, status, error_message, duration_ms, created_at, completed_at
`

type FailToolExecutionParams struct {
	ErrorMessage pgtype.Text `json:"error_message"`
	ID           pgtype.UUID `json:"id"`
}

func (q *Queries) FailToolExecution(ctx context.Context, arg FailToolExecutionParams) (*ToolExecution, error) {
	row := q.db.QueryRow(ctx, FailToolExecution, arg.ErrorMessage, arg.ID)
	var i ToolExecution
	err := row.Scan(
		&i.ID,
		&i.ConversationID,
		&i.ToolName,
		&i.Input,
		&i.Output,
		&i.Status,
		&i.ErrorMessage,
		&i.DurationMs,
		&i.CreatedAt,
		&i.CompletedAt,
	)
	return &i, err
}

const GetToolExecution = `-- name: GetToolExecution :one
SELECT id, conversation_id, tool_name, input, output, status, error_message, duration_ms, created_at, completed_at FROM tool_executions
WHERE id = $1
LIMIT 1
`

func (q *Queries) GetToolExecution(ctx context.Context, id pgtype.UUID) (*ToolExecution, error) {
	row := q.db.QueryRow(ctx, GetToolExecution, id)
	var i ToolExecution
	err := row.Scan(
		&i.ID,
		&i.ConversationID,
		&i.ToolName,
		&i.Input,
		&i.Output,
		&i.Status,
		&i.ErrorMessage,
		&i.DurationMs,
		&i.CreatedAt,
		&i.CompletedAt,
	)
	return &i, err
}

const GetToolExecutionStats = `-- name: GetToolExecutionStats :one
SELECT
    COUNT(*)::int as total_executions,
    COUNT(CASE WHEN status = 'success' THEN 1 END)::int as success_count,
    COUNT(CASE WHEN status = 'failed' THEN 1 END)::int as failed_count,
    COUNT(CASE WHEN status = 'timeout' THEN 1 END)::int as timeout_count,
    AVG(CASE WHEN duration_ms IS NOT NULL THEN duration_ms END)::float as avg_duration_ms,
    MAX(duration_ms)::int as max_duration_ms
FROM tool_executions
WHERE 
    created_at >= COALESCE($1::timestamptz, created_at)
    AND tool_name = COALESCE($2::text, tool_name)
`

type GetToolExecutionStatsParams struct {
	Since    pgtype.Timestamptz `json:"since"`
	ToolName string             `json:"tool_name"`
}

type GetToolExecutionStatsRow struct {
	TotalExecutions int32   `json:"total_executions"`
	SuccessCount    int32   `json:"success_count"`
	FailedCount     int32   `json:"failed_count"`
	TimeoutCount    int32   `json:"timeout_count"`
	AvgDurationMs   float64 `json:"avg_duration_ms"`
	MaxDurationMs   int32   `json:"max_duration_ms"`
}

func (q *Queries) GetToolExecutionStats(ctx context.Context, arg GetToolExecutionStatsParams) (*GetToolExecutionStatsRow, error) {
	row := q.db.QueryRow(ctx, GetToolExecutionStats, arg.Since, arg.ToolName)
	var i GetToolExecutionStatsRow
	err := row.Scan(
		&i.TotalExecutions,
		&i.SuccessCount,
		&i.FailedCount,
		&i.TimeoutCount,
		&i.AvgDurationMs,
		&i.MaxDurationMs,
	)
	return &i, err
}

const GetToolUsageByName = `-- name: GetToolUsageByName :many
SELECT
    tool_name,
    COUNT(*)::int as execution_count,
    COUNT(CASE WHEN status = 'success' THEN 1 END)::int as success_count,
    AVG(CASE WHEN duration_ms IS NOT NULL THEN duration_ms END)::float as avg_duration_ms
FROM tool_executions
WHERE created_at >= COALESCE($1::timestamptz, created_at)
GROUP BY tool_name
ORDER BY execution_count DESC
`

type GetToolUsageByNameRow struct {
	ToolName       string  `json:"tool_name"`
	ExecutionCount int32   `json:"execution_count"`
	SuccessCount   int32   `json:"success_count"`
	AvgDurationMs  float64 `json:"avg_duration_ms"`
}

func (q *Queries) GetToolUsageByName(ctx context.Context, since pgtype.Timestamptz) ([]*GetToolUsageByNameRow, error) {
	rows, err := q.db.Query(ctx, GetToolUsageByName, since)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*GetToolUsageByNameRow{}
	for rows.Next() {
		var i GetToolUsageByNameRow
		if err := rows.Scan(
			&i.ToolName,
			&i.ExecutionCount,
			&i.SuccessCount,
			&i.AvgDurationMs,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const ListPendingToolExecutions = `-- name: ListPendingToolExecutions :many
SELECT id, conversation_id, tool_name, input, output, status, error_message, duration_ms, created_at, completed_at FROM tool_executions
WHERE status IN ('pending', 'running')
ORDER BY created_at ASC
LIMIT $1
`

func (q *Queries) ListPendingToolExecutions(ctx context.Context, limitCount int32) ([]*ToolExecution, error) {
	rows, err := q.db.Query(ctx, ListPendingToolExecutions, limitCount)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ToolExecution{}
	for rows.Next() {
		var i ToolExecution
		if err := rows.Scan(
			&i.ID,
			&i.ConversationID,
			&i.ToolName,
			&i.Input,
			&i.Output,
			&i.Status,
			&i.ErrorMessage,
			&i.DurationMs,
			&i.CreatedAt,
			&i.CompletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const ListToolExecutions = `-- name: ListToolExecutions :many
SELECT id, conversation_id, tool_name, input, output, status, error_message, duration_ms, created_at, completed_at FROM tool_executions
WHERE 
    tool_name = COALESCE($1::text, tool_name)
    AND status = COALESCE($2::tool_status, status)
ORDER BY created_at DESC
LIMIT COALESCE($4::int, 100)
OFFSET COALESCE($3::int, 0)
`

type ListToolExecutionsParams struct {
	ToolName    string     `json:"tool_name"`
	Status      ToolStatus `json:"status"`
	OffsetCount int32      `json:"offset_count"`
	LimitCount  int32      `json:"limit_count"`
}

func (q *Queries) ListToolExecutions(ctx context.Context, arg ListToolExecutionsParams) ([]*ToolExecution, error) {
	rows, err := q.db.Query(ctx, ListToolExecutions,
		arg.ToolName,
		arg.Status,
		arg.OffsetCount,
		arg.LimitCount,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ToolExecution{}
	for rows.Next() {
		var i ToolExecution
		if err := rows.Scan(
			&i.ID,
			&i.ConversationID,
			&i.ToolName,
			&i.Input,
			&i.Output,
			&i.Status,
			&i.ErrorMessage,
			&i.DurationMs,
			&i.CreatedAt,
			&i.CompletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const ListToolExecutionsByConversation = `-- name: ListToolExecutionsByConversation :many
SELECT id, conversation_id, tool_name, input, output, status, error_message, duration_ms, created_at, completed_at FROM tool_executions
WHERE conversation_id = $1
ORDER BY created_at DESC
LIMIT $2
`

type ListToolExecutionsByConversationParams struct {
	ConversationID pgtype.UUID `json:"conversation_id"`
	LimitCount     int32       `json:"limit_count"`
}

func (q *Queries) ListToolExecutionsByConversation(ctx context.Context, arg ListToolExecutionsByConversationParams) ([]*ToolExecution, error) {
	rows, err := q.db.Query(ctx, ListToolExecutionsByConversation, arg.ConversationID, arg.LimitCount)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*ToolExecution{}
	for rows.Next() {
		var i ToolExecution
		if err := rows.Scan(
			&i.ID,
			&i.ConversationID,
			&i.ToolName,
			&i.Input,
			&i.Output,
			&i.Status,
			&i.ErrorMessage,
			&i.DurationMs,
			&i.CreatedAt,
			&i.CompletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const StartToolExecution = `-- name: StartToolExecution :one
UPDATE tool_executions
SET status = 'running'
WHERE id = $1 AND status = 'pending'
RETURNING id, conversation_id, tool_name, input, output, status, error_message, duration_ms, created_at, completed_at
`

func (q *Queries) StartToolExecution(ctx context.Context, id pgtype.UUID) (*ToolExecution, error) {
	row := q.db.QueryRow(ctx, StartToolExecution, id)
	var i ToolExecution
	err := row.Scan(
		&i.ID,
		&i.ConversationID,
		&i.ToolName,
		&i.Input,
		&i.Output,
		&i.Status,
		&i.ErrorMessage,
		&i.DurationMs,
		&i.CreatedAt,
		&i.CompletedAt,
	)
	return &i, err
}

const UpdateToolExecution = `-- name: UpdateToolExecution :one
UPDATE tool_executions
SET
    output = $1,
    status = $2::tool_status,
    error_message = $3,
    duration_ms = $4,
    completed_at = CASE 
        WHEN $2::tool_status IN ('success', 'failed', 'timeout') THEN CURRENT_TIMESTAMP
        ELSE completed_at
    END
WHERE id = $5
RETURNING id, conversation_id, tool_name, input, output, status, error_message, duration_ms, created_at, completed_at
`

type UpdateToolExecutionParams struct {
	Output       json.RawMessage `json:"output"`
	Status       ToolStatus      `json:"status"`
	ErrorMessage pgtype.Text     `json:"error_message"`
	DurationMs   pgtype.Int4     `json:"duration_ms"`
	ID           pgtype.UUID     `json:"id"`
}

func (q *Queries) UpdateToolExecution(ctx context.Context, arg UpdateToolExecutionParams) (*ToolExecution, error) {
	row := q.db.QueryRow(ctx, UpdateToolExecution,
		arg.Output,
		arg.Status,
		arg.ErrorMessage,
		arg.DurationMs,
		arg.ID,
	)
	var i ToolExecution
	err := row.Scan(
		&i.ID,
		&i.ConversationID,
		&i.ToolName,
		&i.Input,
		&i.Output,
		&i.Status,
		&i.ErrorMessage,
		&i.DurationMs,
		&i.CreatedAt,
		&i.CompletedAt,
	)
	return &i, err
}
