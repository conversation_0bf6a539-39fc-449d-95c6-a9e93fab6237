// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: copyfrom.go

package sqlc

import (
	"context"
)

// iteratorForCreateRelationsBatch implements pgx.CopyFromSource.
type iteratorForCreateRelationsBatch struct {
	rows                 []CreateRelationsBatchParams
	skippedFirstNextCall bool
}

func (r *iteratorForCreateRelationsBatch) Next() bool {
	if len(r.rows) == 0 {
		return false
	}
	if !r.skippedFirstNextCall {
		r.skippedFirstNextCall = true
		return true
	}
	r.rows = r.rows[1:]
	return len(r.rows) > 0
}

func (r iteratorForCreateRelationsBatch) Values() ([]interface{}, error) {
	return []interface{}{
		r.rows[0].FromID,
		r.rows[0].ToID,
		r.rows[0].Type,
		r.rows[0].Strength,
		r.rows[0].Metadata,
	}, nil
}

func (r iteratorForCreateRelationsBatch) Err() error {
	return nil
}

func (q *Queries) CreateRelationsBatch(ctx context.Context, arg []CreateRelationsBatchParams) (int64, error) {
	return q.db.CopyFrom(ctx, []string{"relations"}, []string{"from_id", "to_id", "type", "strength", "metadata"}, &iteratorForCreateRelationsBatch{rows: arg})
}
