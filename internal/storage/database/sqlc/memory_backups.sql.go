// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: memory_backups.sql

package sqlc

import (
	"context"
	"encoding/json"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

const CountMemoryBackups = `-- name: CountMemoryBackups :one
SELECT COUNT(*)::int as total_backups
FROM memory_backups
`

func (q *Queries) CountMemoryBackups(ctx context.Context) (int32, error) {
	row := q.db.QueryRow(ctx, CountMemoryBackups)
	var total_backups int32
	err := row.Scan(&total_backups)
	return total_backups, err
}

const CreateMemoryBackup = `-- name: CreateMemoryBackup :one
INSERT INTO memory_backups (
    backup_data,
    node_count,
    edge_count,
    version
) VALUES (
    $1,
    $2,
    $3,
    COALESCE($4, '1.0')
)
RETURNING id, backup_data, node_count, edge_count, version, created_at
`

type CreateMemoryBackupParams struct {
	BackupData json.RawMessage `json:"backup_data"`
	NodeCount  int32           `json:"node_count"`
	EdgeCount  int32           `json:"edge_count"`
	Version    interface{}     `json:"version"`
}

func (q *Queries) CreateMemoryBackup(ctx context.Context, arg CreateMemoryBackupParams) (*MemoryBackup, error) {
	row := q.db.QueryRow(ctx, CreateMemoryBackup,
		arg.BackupData,
		arg.NodeCount,
		arg.EdgeCount,
		arg.Version,
	)
	var i MemoryBackup
	err := row.Scan(
		&i.ID,
		&i.BackupData,
		&i.NodeCount,
		&i.EdgeCount,
		&i.Version,
		&i.CreatedAt,
	)
	return &i, err
}

const DeleteMemoryBackup = `-- name: DeleteMemoryBackup :exec
DELETE FROM memory_backups
WHERE id = $1
`

func (q *Queries) DeleteMemoryBackup(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, DeleteMemoryBackup, id)
	return err
}

const DeleteOldMemoryBackups = `-- name: DeleteOldMemoryBackups :exec
DELETE FROM memory_backups
WHERE id NOT IN (
    SELECT id 
    FROM memory_backups 
    ORDER BY created_at DESC 
    LIMIT $1
)
AND created_at < CURRENT_TIMESTAMP - INTERVAL '1 day' * $2
`

type DeleteOldMemoryBackupsParams struct {
	KeepCount int32       `json:"keep_count"`
	KeepDays  interface{} `json:"keep_days"`
}

func (q *Queries) DeleteOldMemoryBackups(ctx context.Context, arg DeleteOldMemoryBackupsParams) error {
	_, err := q.db.Exec(ctx, DeleteOldMemoryBackups, arg.KeepCount, arg.KeepDays)
	return err
}

const GetBackupStats = `-- name: GetBackupStats :one
SELECT
    COUNT(*)::int as total_backups,
    SUM(node_count)::int as total_nodes_backed_up,
    SUM(edge_count)::int as total_edges_backed_up,
    MIN(created_at) as oldest_backup,
    MAX(created_at) as newest_backup
FROM memory_backups
`

type GetBackupStatsRow struct {
	TotalBackups       int32       `json:"total_backups"`
	TotalNodesBackedUp int32       `json:"total_nodes_backed_up"`
	TotalEdgesBackedUp int32       `json:"total_edges_backed_up"`
	OldestBackup       interface{} `json:"oldest_backup"`
	NewestBackup       interface{} `json:"newest_backup"`
}

func (q *Queries) GetBackupStats(ctx context.Context) (*GetBackupStatsRow, error) {
	row := q.db.QueryRow(ctx, GetBackupStats)
	var i GetBackupStatsRow
	err := row.Scan(
		&i.TotalBackups,
		&i.TotalNodesBackedUp,
		&i.TotalEdgesBackedUp,
		&i.OldestBackup,
		&i.NewestBackup,
	)
	return &i, err
}

const GetBackupsWithinTimeRange = `-- name: GetBackupsWithinTimeRange :many
SELECT id, backup_data, node_count, edge_count, version, created_at FROM memory_backups
WHERE 
    created_at >= $1
    AND created_at <= $2
ORDER BY created_at DESC
`

type GetBackupsWithinTimeRangeParams struct {
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
}

func (q *Queries) GetBackupsWithinTimeRange(ctx context.Context, arg GetBackupsWithinTimeRangeParams) ([]*MemoryBackup, error) {
	rows, err := q.db.Query(ctx, GetBackupsWithinTimeRange, arg.StartTime, arg.EndTime)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*MemoryBackup{}
	for rows.Next() {
		var i MemoryBackup
		if err := rows.Scan(
			&i.ID,
			&i.BackupData,
			&i.NodeCount,
			&i.EdgeCount,
			&i.Version,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetLatestMemoryBackup = `-- name: GetLatestMemoryBackup :one
SELECT id, backup_data, node_count, edge_count, version, created_at FROM memory_backups
ORDER BY created_at DESC
LIMIT 1
`

func (q *Queries) GetLatestMemoryBackup(ctx context.Context) (*MemoryBackup, error) {
	row := q.db.QueryRow(ctx, GetLatestMemoryBackup)
	var i MemoryBackup
	err := row.Scan(
		&i.ID,
		&i.BackupData,
		&i.NodeCount,
		&i.EdgeCount,
		&i.Version,
		&i.CreatedAt,
	)
	return &i, err
}

const GetMemoryBackup = `-- name: GetMemoryBackup :one
SELECT id, backup_data, node_count, edge_count, version, created_at FROM memory_backups
WHERE id = $1
LIMIT 1
`

func (q *Queries) GetMemoryBackup(ctx context.Context, id pgtype.UUID) (*MemoryBackup, error) {
	row := q.db.QueryRow(ctx, GetMemoryBackup, id)
	var i MemoryBackup
	err := row.Scan(
		&i.ID,
		&i.BackupData,
		&i.NodeCount,
		&i.EdgeCount,
		&i.Version,
		&i.CreatedAt,
	)
	return &i, err
}

const ListMemoryBackups = `-- name: ListMemoryBackups :many
SELECT id, backup_data, node_count, edge_count, version, created_at FROM memory_backups
ORDER BY created_at DESC
LIMIT COALESCE($2::int, 20)
OFFSET COALESCE($1::int, 0)
`

type ListMemoryBackupsParams struct {
	OffsetCount int32 `json:"offset_count"`
	LimitCount  int32 `json:"limit_count"`
}

func (q *Queries) ListMemoryBackups(ctx context.Context, arg ListMemoryBackupsParams) ([]*MemoryBackup, error) {
	rows, err := q.db.Query(ctx, ListMemoryBackups, arg.OffsetCount, arg.LimitCount)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*MemoryBackup{}
	for rows.Next() {
		var i MemoryBackup
		if err := rows.Scan(
			&i.ID,
			&i.BackupData,
			&i.NodeCount,
			&i.EdgeCount,
			&i.Version,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
