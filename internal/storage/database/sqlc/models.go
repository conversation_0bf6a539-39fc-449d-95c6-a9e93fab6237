// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package sqlc

import (
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"
	pgvector "github.com/pgvector/pgvector-go"
)

type ActionType string

const (
	ActionTypeADD    ActionType = "ADD"
	ActionTypeUPDATE ActionType = "UPDATE"
	ActionTypeDELETE ActionType = "DELETE"
	ActionTypeSKIP   ActionType = "SKIP"
	ActionTypeMERGE  ActionType = "MERGE"
)

func (e *ActionType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = ActionType(s)
	case string:
		*e = ActionType(s)
	default:
		return fmt.Errorf("unsupported scan type for ActionType: %T", src)
	}
	return nil
}

type NullActionType struct {
	ActionType ActionType `json:"action_type"`
	Valid      bool       `json:"valid"` // Valid is true if ActionType is not NULL
}

// <PERSON>an implements the Scanner interface.
func (ns *NullActionType) Scan(value interface{}) error {
	if value == nil {
		ns.ActionType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.ActionType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullActionType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.ActionType), nil
}

func (e ActionType) Valid() bool {
	switch e {
	case ActionTypeADD,
		ActionTypeUPDATE,
		ActionTypeDELETE,
		ActionTypeSKIP,
		ActionTypeMERGE:
		return true
	}
	return false
}

func AllActionTypeValues() []ActionType {
	return []ActionType{
		ActionTypeADD,
		ActionTypeUPDATE,
		ActionTypeDELETE,
		ActionTypeSKIP,
		ActionTypeMERGE,
	}
}

type ConversationStatus string

const (
	ConversationStatusActive   ConversationStatus = "active"
	ConversationStatusArchived ConversationStatus = "archived"
	ConversationStatusDeleted  ConversationStatus = "deleted"
)

func (e *ConversationStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = ConversationStatus(s)
	case string:
		*e = ConversationStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for ConversationStatus: %T", src)
	}
	return nil
}

type NullConversationStatus struct {
	ConversationStatus ConversationStatus `json:"conversation_status"`
	Valid              bool               `json:"valid"` // Valid is true if ConversationStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullConversationStatus) Scan(value interface{}) error {
	if value == nil {
		ns.ConversationStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.ConversationStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullConversationStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.ConversationStatus), nil
}

func (e ConversationStatus) Valid() bool {
	switch e {
	case ConversationStatusActive,
		ConversationStatusArchived,
		ConversationStatusDeleted:
		return true
	}
	return false
}

func AllConversationStatusValues() []ConversationStatus {
	return []ConversationStatus{
		ConversationStatusActive,
		ConversationStatusArchived,
		ConversationStatusDeleted,
	}
}

type EntityRole string

const (
	EntityRoleSubject  EntityRole = "subject"
	EntityRoleObject   EntityRole = "object"
	EntityRoleLocation EntityRole = "location"
	EntityRoleTime     EntityRole = "time"
	EntityRoleWith     EntityRole = "with"
)

func (e *EntityRole) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = EntityRole(s)
	case string:
		*e = EntityRole(s)
	default:
		return fmt.Errorf("unsupported scan type for EntityRole: %T", src)
	}
	return nil
}

type NullEntityRole struct {
	EntityRole EntityRole `json:"entity_role"`
	Valid      bool       `json:"valid"` // Valid is true if EntityRole is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullEntityRole) Scan(value interface{}) error {
	if value == nil {
		ns.EntityRole, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.EntityRole.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullEntityRole) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.EntityRole), nil
}

func (e EntityRole) Valid() bool {
	switch e {
	case EntityRoleSubject,
		EntityRoleObject,
		EntityRoleLocation,
		EntityRoleTime,
		EntityRoleWith:
		return true
	}
	return false
}

func AllEntityRoleValues() []EntityRole {
	return []EntityRole{
		EntityRoleSubject,
		EntityRoleObject,
		EntityRoleLocation,
		EntityRoleTime,
		EntityRoleWith,
	}
}

type EntityType string

const (
	EntityTypePerson   EntityType = "person"
	EntityTypePlace    EntityType = "place"
	EntityTypeActivity EntityType = "activity"
	EntityTypeTime     EntityType = "time"
	EntityTypeThing    EntityType = "thing"
	EntityTypeConcept  EntityType = "concept"
)

func (e *EntityType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = EntityType(s)
	case string:
		*e = EntityType(s)
	default:
		return fmt.Errorf("unsupported scan type for EntityType: %T", src)
	}
	return nil
}

type NullEntityType struct {
	EntityType EntityType `json:"entity_type"`
	Valid      bool       `json:"valid"` // Valid is true if EntityType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullEntityType) Scan(value interface{}) error {
	if value == nil {
		ns.EntityType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.EntityType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullEntityType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.EntityType), nil
}

func (e EntityType) Valid() bool {
	switch e {
	case EntityTypePerson,
		EntityTypePlace,
		EntityTypeActivity,
		EntityTypeTime,
		EntityTypeThing,
		EntityTypeConcept:
		return true
	}
	return false
}

func AllEntityTypeValues() []EntityType {
	return []EntityType{
		EntityTypePerson,
		EntityTypePlace,
		EntityTypeActivity,
		EntityTypeTime,
		EntityTypeThing,
		EntityTypeConcept,
	}
}

type MemoryStatus string

const (
	MemoryStatusActive   MemoryStatus = "active"
	MemoryStatusArchived MemoryStatus = "archived"
	MemoryStatusDeleted  MemoryStatus = "deleted"
)

func (e *MemoryStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = MemoryStatus(s)
	case string:
		*e = MemoryStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for MemoryStatus: %T", src)
	}
	return nil
}

type NullMemoryStatus struct {
	MemoryStatus MemoryStatus `json:"memory_status"`
	Valid        bool         `json:"valid"` // Valid is true if MemoryStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullMemoryStatus) Scan(value interface{}) error {
	if value == nil {
		ns.MemoryStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.MemoryStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullMemoryStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.MemoryStatus), nil
}

func (e MemoryStatus) Valid() bool {
	switch e {
	case MemoryStatusActive,
		MemoryStatusArchived,
		MemoryStatusDeleted:
		return true
	}
	return false
}

func AllMemoryStatusValues() []MemoryStatus {
	return []MemoryStatus{
		MemoryStatusActive,
		MemoryStatusArchived,
		MemoryStatusDeleted,
	}
}

type MemoryType string

const (
	MemoryTypeFact         MemoryType = "fact"
	MemoryTypePreference   MemoryType = "preference"
	MemoryTypeSchedule     MemoryType = "schedule"
	MemoryTypeRelationship MemoryType = "relationship"
	MemoryTypeGoal         MemoryType = "goal"
	MemoryTypeSkill        MemoryType = "skill"
)

func (e *MemoryType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = MemoryType(s)
	case string:
		*e = MemoryType(s)
	default:
		return fmt.Errorf("unsupported scan type for MemoryType: %T", src)
	}
	return nil
}

type NullMemoryType struct {
	MemoryType MemoryType `json:"memory_type"`
	Valid      bool       `json:"valid"` // Valid is true if MemoryType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullMemoryType) Scan(value interface{}) error {
	if value == nil {
		ns.MemoryType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.MemoryType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullMemoryType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.MemoryType), nil
}

func (e MemoryType) Valid() bool {
	switch e {
	case MemoryTypeFact,
		MemoryTypePreference,
		MemoryTypeSchedule,
		MemoryTypeRelationship,
		MemoryTypeGoal,
		MemoryTypeSkill:
		return true
	}
	return false
}

func AllMemoryTypeValues() []MemoryType {
	return []MemoryType{
		MemoryTypeFact,
		MemoryTypePreference,
		MemoryTypeSchedule,
		MemoryTypeRelationship,
		MemoryTypeGoal,
		MemoryTypeSkill,
	}
}

type MessageRole string

const (
	MessageRoleUser      MessageRole = "user"
	MessageRoleAssistant MessageRole = "assistant"
	MessageRoleSystem    MessageRole = "system"
	MessageRoleTool      MessageRole = "tool"
	MessageRoleError     MessageRole = "error"
)

func (e *MessageRole) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = MessageRole(s)
	case string:
		*e = MessageRole(s)
	default:
		return fmt.Errorf("unsupported scan type for MessageRole: %T", src)
	}
	return nil
}

type NullMessageRole struct {
	MessageRole MessageRole `json:"message_role"`
	Valid       bool        `json:"valid"` // Valid is true if MessageRole is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullMessageRole) Scan(value interface{}) error {
	if value == nil {
		ns.MessageRole, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.MessageRole.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullMessageRole) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.MessageRole), nil
}

func (e MessageRole) Valid() bool {
	switch e {
	case MessageRoleUser,
		MessageRoleAssistant,
		MessageRoleSystem,
		MessageRoleTool,
		MessageRoleError:
		return true
	}
	return false
}

func AllMessageRoleValues() []MessageRole {
	return []MessageRole{
		MessageRoleUser,
		MessageRoleAssistant,
		MessageRoleSystem,
		MessageRoleTool,
		MessageRoleError,
	}
}

type RecurrencePattern string

const (
	RecurrencePatternDaily   RecurrencePattern = "daily"
	RecurrencePatternWeekly  RecurrencePattern = "weekly"
	RecurrencePatternMonthly RecurrencePattern = "monthly"
	RecurrencePatternYearly  RecurrencePattern = "yearly"
)

func (e *RecurrencePattern) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = RecurrencePattern(s)
	case string:
		*e = RecurrencePattern(s)
	default:
		return fmt.Errorf("unsupported scan type for RecurrencePattern: %T", src)
	}
	return nil
}

type NullRecurrencePattern struct {
	RecurrencePattern RecurrencePattern `json:"recurrence_pattern"`
	Valid             bool              `json:"valid"` // Valid is true if RecurrencePattern is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullRecurrencePattern) Scan(value interface{}) error {
	if value == nil {
		ns.RecurrencePattern, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.RecurrencePattern.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullRecurrencePattern) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.RecurrencePattern), nil
}

func (e RecurrencePattern) Valid() bool {
	switch e {
	case RecurrencePatternDaily,
		RecurrencePatternWeekly,
		RecurrencePatternMonthly,
		RecurrencePatternYearly:
		return true
	}
	return false
}

func AllRecurrencePatternValues() []RecurrencePattern {
	return []RecurrencePattern{
		RecurrencePatternDaily,
		RecurrencePatternWeekly,
		RecurrencePatternMonthly,
		RecurrencePatternYearly,
	}
}

type RelationType string

const (
	RelationTypeUpdates     RelationType = "updates"
	RelationTypeContradicts RelationType = "contradicts"
	RelationTypeRelatesTo   RelationType = "relates_to"
	RelationTypeFollows     RelationType = "follows"
	RelationTypePrecedes    RelationType = "precedes"
	RelationTypePartOf      RelationType = "part_of"
)

func (e *RelationType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = RelationType(s)
	case string:
		*e = RelationType(s)
	default:
		return fmt.Errorf("unsupported scan type for RelationType: %T", src)
	}
	return nil
}

type NullRelationType struct {
	RelationType RelationType `json:"relation_type"`
	Valid        bool         `json:"valid"` // Valid is true if RelationType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullRelationType) Scan(value interface{}) error {
	if value == nil {
		ns.RelationType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.RelationType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullRelationType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.RelationType), nil
}

func (e RelationType) Valid() bool {
	switch e {
	case RelationTypeUpdates,
		RelationTypeContradicts,
		RelationTypeRelatesTo,
		RelationTypeFollows,
		RelationTypePrecedes,
		RelationTypePartOf:
		return true
	}
	return false
}

func AllRelationTypeValues() []RelationType {
	return []RelationType{
		RelationTypeUpdates,
		RelationTypeContradicts,
		RelationTypeRelatesTo,
		RelationTypeFollows,
		RelationTypePrecedes,
		RelationTypePartOf,
	}
}

type TaskPriority string

const (
	TaskPriorityLow      TaskPriority = "low"
	TaskPriorityMedium   TaskPriority = "medium"
	TaskPriorityHigh     TaskPriority = "high"
	TaskPriorityUrgent   TaskPriority = "urgent"
	TaskPriorityCritical TaskPriority = "critical"
)

func (e *TaskPriority) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = TaskPriority(s)
	case string:
		*e = TaskPriority(s)
	default:
		return fmt.Errorf("unsupported scan type for TaskPriority: %T", src)
	}
	return nil
}

type NullTaskPriority struct {
	TaskPriority TaskPriority `json:"task_priority"`
	Valid        bool         `json:"valid"` // Valid is true if TaskPriority is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullTaskPriority) Scan(value interface{}) error {
	if value == nil {
		ns.TaskPriority, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.TaskPriority.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullTaskPriority) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.TaskPriority), nil
}

func (e TaskPriority) Valid() bool {
	switch e {
	case TaskPriorityLow,
		TaskPriorityMedium,
		TaskPriorityHigh,
		TaskPriorityUrgent,
		TaskPriorityCritical:
		return true
	}
	return false
}

func AllTaskPriorityValues() []TaskPriority {
	return []TaskPriority{
		TaskPriorityLow,
		TaskPriorityMedium,
		TaskPriorityHigh,
		TaskPriorityUrgent,
		TaskPriorityCritical,
	}
}

type TaskStatus string

const (
	TaskStatusTodo       TaskStatus = "todo"
	TaskStatusInProgress TaskStatus = "in_progress"
	TaskStatusDone       TaskStatus = "done"
	TaskStatusCancelled  TaskStatus = "cancelled"
	TaskStatusDeferred   TaskStatus = "deferred"
)

func (e *TaskStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = TaskStatus(s)
	case string:
		*e = TaskStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for TaskStatus: %T", src)
	}
	return nil
}

type NullTaskStatus struct {
	TaskStatus TaskStatus `json:"task_status"`
	Valid      bool       `json:"valid"` // Valid is true if TaskStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullTaskStatus) Scan(value interface{}) error {
	if value == nil {
		ns.TaskStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.TaskStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullTaskStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.TaskStatus), nil
}

func (e TaskStatus) Valid() bool {
	switch e {
	case TaskStatusTodo,
		TaskStatusInProgress,
		TaskStatusDone,
		TaskStatusCancelled,
		TaskStatusDeferred:
		return true
	}
	return false
}

func AllTaskStatusValues() []TaskStatus {
	return []TaskStatus{
		TaskStatusTodo,
		TaskStatusInProgress,
		TaskStatusDone,
		TaskStatusCancelled,
		TaskStatusDeferred,
	}
}

type ToolStatus string

const (
	ToolStatusPending   ToolStatus = "pending"
	ToolStatusRunning   ToolStatus = "running"
	ToolStatusSuccess   ToolStatus = "success"
	ToolStatusFailed    ToolStatus = "failed"
	ToolStatusTimeout   ToolStatus = "timeout"
	ToolStatusCancelled ToolStatus = "cancelled"
)

func (e *ToolStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = ToolStatus(s)
	case string:
		*e = ToolStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for ToolStatus: %T", src)
	}
	return nil
}

type NullToolStatus struct {
	ToolStatus ToolStatus `json:"tool_status"`
	Valid      bool       `json:"valid"` // Valid is true if ToolStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullToolStatus) Scan(value interface{}) error {
	if value == nil {
		ns.ToolStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.ToolStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullToolStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.ToolStatus), nil
}

func (e ToolStatus) Valid() bool {
	switch e {
	case ToolStatusPending,
		ToolStatusRunning,
		ToolStatusSuccess,
		ToolStatusFailed,
		ToolStatusTimeout,
		ToolStatusCancelled:
		return true
	}
	return false
}

func AllToolStatusValues() []ToolStatus {
	return []ToolStatus{
		ToolStatusPending,
		ToolStatusRunning,
		ToolStatusSuccess,
		ToolStatusFailed,
		ToolStatusTimeout,
		ToolStatusCancelled,
	}
}

// Conversation session records, tracking user and AI interaction history
type Conversation struct {
	ID            pgtype.UUID        `json:"id"`
	Title         string             `json:"title"`
	Summary       pgtype.Text        `json:"summary"`
	Status        ConversationStatus `json:"status"`
	Metadata      json.RawMessage    `json:"metadata"`
	CreatedAt     time.Time          `json:"created_at"`
	UpdatedAt     time.Time          `json:"updated_at"`
	LastMessageAt sql.NullTime       `json:"last_message_at"`
	MessageCount  int32              `json:"message_count"`
	TotalTokens   int32              `json:"total_tokens"`
}

// Unified vector embeddings table, supporting semantic search and similarity matching
type Embedding struct {
	ID          pgtype.UUID     `json:"id"`
	SourceTable string          `json:"source_table"`
	SourceID    pgtype.UUID     `json:"source_id"`
	Content     string          `json:"content"`
	Embedding   pgvector.Vector `json:"embedding"`
	Model       string          `json:"model"`
	Metadata    json.RawMessage `json:"metadata"`
	CreatedAt   time.Time       `json:"created_at"`
}

// Change history for memories
type History struct {
	ID         pgtype.UUID     `json:"id"`
	MemoryID   pgtype.UUID     `json:"memory_id"`
	Action     ActionType      `json:"action"`
	Version    int32           `json:"version"`
	Content    string          `json:"content"`
	Entities   json.RawMessage `json:"entities"`
	Attributes json.RawMessage `json:"attributes"`
	Context    json.RawMessage `json:"context"`
	Reason     pgtype.Text     `json:"reason"`
	CreatedBy  pgtype.Text     `json:"created_by"`
	CreatedAt  time.Time       `json:"created_at"`
}

// Structured memory system with semantic IDs and embeddings
type Memory struct {
	ID pgtype.UUID `json:"id"`
	// Semantic ID for updates (e.g., schedule_thai_boxing_20250714)
	SemanticID string      `json:"semantic_id"`
	Type       MemoryType  `json:"type"`
	Content    string      `json:"content"`
	Summary    pgtype.Text `json:"summary"`
	// Entities with types and roles
	Entities json.RawMessage `json:"entities"`
	// Key-value attributes
	Attributes json.RawMessage `json:"attributes"`
	// Temporal and spatial context
	Context json.RawMessage `json:"context"`
	// Embedding vector for gemini-embedding-001 with OutputDimensionality=768
	Embedding   pgvector.Vector `json:"embedding"`
	Keywords    []string        `json:"keywords"`
	Confidence  pgtype.Float4   `json:"confidence"`
	Version     pgtype.Int4     `json:"version"`
	AccessCount pgtype.Int4     `json:"access_count"`
	CreatedAt   time.Time       `json:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at"`
	AccessedAt  sql.NullTime    `json:"accessed_at"`
	// Memory status: active, archived, or deleted
	Status     MemoryStatus       `json:"status"`
	ArchivedAt pgtype.Timestamptz `json:"archived_at"`
	MergedFrom []uuid.UUID        `json:"merged_from"`
}

// Knowledge graph backups, ensuring memory persistence
type MemoryBackup struct {
	ID         pgtype.UUID     `json:"id"`
	BackupData json.RawMessage `json:"backup_data"`
	NodeCount  int32           `json:"node_count"`
	EdgeCount  int32           `json:"edge_count"`
	Version    string          `json:"version"`
	CreatedAt  time.Time       `json:"created_at"`
}

// Messages within conversations, including user input and AI responses
type Message struct {
	ID             pgtype.UUID     `json:"id"`
	ConversationID pgtype.UUID     `json:"conversation_id"`
	Role           MessageRole     `json:"role"`
	Content        string          `json:"content"`
	TokensUsed     int32           `json:"tokens_used"`
	Metadata       json.RawMessage `json:"metadata"`
	CreatedAt      time.Time       `json:"created_at"`
}

// Async processing queue for memory operations
type Queue struct {
	ID             pgtype.UUID        `json:"id"`
	ConversationID pgtype.UUID        `json:"conversation_id"`
	Messages       []byte             `json:"messages"`
	Status         pgtype.Text        `json:"status"`
	Error          pgtype.Text        `json:"error"`
	CreatedAt      time.Time          `json:"created_at"`
	ProcessedAt    pgtype.Timestamptz `json:"processed_at"`
}

// Memory relationships
type Relation struct {
	ID        pgtype.UUID     `json:"id"`
	FromID    pgtype.UUID     `json:"from_id"`
	ToID      pgtype.UUID     `json:"to_id"`
	Type      RelationType    `json:"type"`
	Strength  pgtype.Float4   `json:"strength"`
	Metadata  json.RawMessage `json:"metadata"`
	CreatedAt time.Time       `json:"created_at"`
}

// System settings key-value pairs, storing application configuration
type SystemSetting struct {
	Key         string          `json:"key"`
	Value       json.RawMessage `json:"value"`
	Description pgtype.Text     `json:"description"`
	CreatedAt   time.Time       `json:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at"`
}

// Task management system, tracking to-do items
type Task struct {
	ID          pgtype.UUID     `json:"id"`
	Title       string          `json:"title"`
	Description pgtype.Text     `json:"description"`
	Status      TaskStatus      `json:"status"`
	Priority    TaskPriority    `json:"priority"`
	DueDate     sql.NullTime    `json:"due_date"`
	CompletedAt sql.NullTime    `json:"completed_at"`
	Labels      []string        `json:"labels"`
	Metadata    json.RawMessage `json:"metadata"`
	CreatedAt   time.Time       `json:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at"`
}

// Tool execution history, recording AI tool usage
type ToolExecution struct {
	ID             pgtype.UUID     `json:"id"`
	ConversationID pgtype.UUID     `json:"conversation_id"`
	ToolName       string          `json:"tool_name"`
	Input          json.RawMessage `json:"input"`
	Output         json.RawMessage `json:"output"`
	Status         ToolStatus      `json:"status"`
	ErrorMessage   pgtype.Text     `json:"error_message"`
	DurationMs     pgtype.Int4     `json:"duration_ms"`
	CreatedAt      time.Time       `json:"created_at"`
	CompletedAt    sql.NullTime    `json:"completed_at"`
}
