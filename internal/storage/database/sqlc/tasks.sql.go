// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: tasks.sql

package sqlc

import (
	"context"
	"database/sql"

	"github.com/jackc/pgx/v5/pgtype"
)

const AddTaskLabel = `-- name: AddTaskLabel :one
UPDATE tasks
SET labels = array_append(labels, $1)
WHERE id = $2 AND NOT ($1 = ANY(labels))
RETURNING id, title, description, status, priority, due_date, completed_at, labels, metadata, created_at, updated_at
`

type AddTaskLabelParams struct {
	Label interface{} `json:"label"`
	ID    pgtype.UUID `json:"id"`
}

func (q *Queries) AddTaskLabel(ctx context.Context, arg AddTaskLabelParams) (*Task, error) {
	row := q.db.QueryRow(ctx, AddTaskLabel, arg.Label, arg.ID)
	var i Task
	err := row.Scan(
		&i.ID,
		&i.Title,
		&i.Description,
		&i.Status,
		&i.Priority,
		&i.DueDate,
		&i.CompletedAt,
		&i.Labels,
		&i.Metadata,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const CompleteTask = `-- name: CompleteTask :one
UPDATE tasks
SET 
    status = 'done',
    completed_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING id, title, description, status, priority, due_date, completed_at, labels, metadata, created_at, updated_at
`

func (q *Queries) CompleteTask(ctx context.Context, id pgtype.UUID) (*Task, error) {
	row := q.db.QueryRow(ctx, CompleteTask, id)
	var i Task
	err := row.Scan(
		&i.ID,
		&i.Title,
		&i.Description,
		&i.Status,
		&i.Priority,
		&i.DueDate,
		&i.CompletedAt,
		&i.Labels,
		&i.Metadata,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const CreateTask = `-- name: CreateTask :one
INSERT INTO tasks (
    title,
    description,
    status,
    priority,
    due_date,
    labels,
    metadata
) VALUES (
    $1,
    $2,
    COALESCE($3::task_status, 'todo'),
    COALESCE($4::task_priority, 'medium'),
    $5,
    COALESCE($6, '{}'),
    COALESCE($7::jsonb, '{}')
)
RETURNING id, title, description, status, priority, due_date, completed_at, labels, metadata, created_at, updated_at
`

type CreateTaskParams struct {
	Title       string       `json:"title"`
	Description pgtype.Text  `json:"description"`
	Status      TaskStatus   `json:"status"`
	Priority    TaskPriority `json:"priority"`
	DueDate     sql.NullTime `json:"due_date"`
	Labels      interface{}  `json:"labels"`
	Metadata    []byte       `json:"metadata"`
}

func (q *Queries) CreateTask(ctx context.Context, arg CreateTaskParams) (*Task, error) {
	row := q.db.QueryRow(ctx, CreateTask,
		arg.Title,
		arg.Description,
		arg.Status,
		arg.Priority,
		arg.DueDate,
		arg.Labels,
		arg.Metadata,
	)
	var i Task
	err := row.Scan(
		&i.ID,
		&i.Title,
		&i.Description,
		&i.Status,
		&i.Priority,
		&i.DueDate,
		&i.CompletedAt,
		&i.Labels,
		&i.Metadata,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const DeleteTask = `-- name: DeleteTask :exec
DELETE FROM tasks
WHERE id = $1
`

func (q *Queries) DeleteTask(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, DeleteTask, id)
	return err
}

const GetTask = `-- name: GetTask :one
SELECT id, title, description, status, priority, due_date, completed_at, labels, metadata, created_at, updated_at FROM tasks
WHERE id = $1
LIMIT 1
`

func (q *Queries) GetTask(ctx context.Context, id pgtype.UUID) (*Task, error) {
	row := q.db.QueryRow(ctx, GetTask, id)
	var i Task
	err := row.Scan(
		&i.ID,
		&i.Title,
		&i.Description,
		&i.Status,
		&i.Priority,
		&i.DueDate,
		&i.CompletedAt,
		&i.Labels,
		&i.Metadata,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const GetTaskStats = `-- name: GetTaskStats :one
SELECT
    COUNT(*)::int as total_tasks,
    COUNT(CASE WHEN status = 'todo' THEN 1 END)::int as todo_count,
    COUNT(CASE WHEN status = 'in_progress' THEN 1 END)::int as in_progress_count,
    COUNT(CASE WHEN status = 'done' THEN 1 END)::int as done_count,
    COUNT(CASE WHEN status = 'cancelled' THEN 1 END)::int as cancelled_count,
    COUNT(CASE WHEN status != 'done' AND due_date < CURRENT_TIMESTAMP THEN 1 END)::int as overdue_count
FROM tasks
`

type GetTaskStatsRow struct {
	TotalTasks      int32 `json:"total_tasks"`
	TodoCount       int32 `json:"todo_count"`
	InProgressCount int32 `json:"in_progress_count"`
	DoneCount       int32 `json:"done_count"`
	CancelledCount  int32 `json:"cancelled_count"`
	OverdueCount    int32 `json:"overdue_count"`
}

func (q *Queries) GetTaskStats(ctx context.Context) (*GetTaskStatsRow, error) {
	row := q.db.QueryRow(ctx, GetTaskStats)
	var i GetTaskStatsRow
	err := row.Scan(
		&i.TotalTasks,
		&i.TodoCount,
		&i.InProgressCount,
		&i.DoneCount,
		&i.CancelledCount,
		&i.OverdueCount,
	)
	return &i, err
}

const GetTasksByPriority = `-- name: GetTasksByPriority :many
SELECT
    priority,
    COUNT(*)::int as count
FROM tasks
WHERE status != 'done'
GROUP BY priority
ORDER BY 
    CASE priority 
        WHEN 'critical' THEN 0
        WHEN 'urgent' THEN 1
        WHEN 'high' THEN 2
        WHEN 'medium' THEN 3
        WHEN 'low' THEN 4
    END
`

type GetTasksByPriorityRow struct {
	Priority TaskPriority `json:"priority"`
	Count    int32        `json:"count"`
}

func (q *Queries) GetTasksByPriority(ctx context.Context) ([]*GetTasksByPriorityRow, error) {
	rows, err := q.db.Query(ctx, GetTasksByPriority)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*GetTasksByPriorityRow{}
	for rows.Next() {
		var i GetTasksByPriorityRow
		if err := rows.Scan(&i.Priority, &i.Count); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const ListOverdueTasks = `-- name: ListOverdueTasks :many
SELECT id, title, description, status, priority, due_date, completed_at, labels, metadata, created_at, updated_at FROM tasks
WHERE 
    status != 'done'
    AND due_date < CURRENT_TIMESTAMP
ORDER BY due_date ASC
`

func (q *Queries) ListOverdueTasks(ctx context.Context) ([]*Task, error) {
	rows, err := q.db.Query(ctx, ListOverdueTasks)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Task{}
	for rows.Next() {
		var i Task
		if err := rows.Scan(
			&i.ID,
			&i.Title,
			&i.Description,
			&i.Status,
			&i.Priority,
			&i.DueDate,
			&i.CompletedAt,
			&i.Labels,
			&i.Metadata,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const ListPendingTasks = `-- name: ListPendingTasks :many
SELECT id, title, description, status, priority, due_date, completed_at, labels, metadata, created_at, updated_at FROM tasks
WHERE status IN ('todo', 'in_progress')
ORDER BY
    CASE priority 
        WHEN 'critical' THEN 0
        WHEN 'urgent' THEN 1
        WHEN 'high' THEN 2
        WHEN 'medium' THEN 3
        WHEN 'low' THEN 4
    END,
    due_date ASC NULLS LAST,
    created_at ASC
LIMIT $1
`

func (q *Queries) ListPendingTasks(ctx context.Context, limitCount int32) ([]*Task, error) {
	rows, err := q.db.Query(ctx, ListPendingTasks, limitCount)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Task{}
	for rows.Next() {
		var i Task
		if err := rows.Scan(
			&i.ID,
			&i.Title,
			&i.Description,
			&i.Status,
			&i.Priority,
			&i.DueDate,
			&i.CompletedAt,
			&i.Labels,
			&i.Metadata,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const ListTasks = `-- name: ListTasks :many
SELECT id, title, description, status, priority, due_date, completed_at, labels, metadata, created_at, updated_at FROM tasks
WHERE 
    status = COALESCE($1::task_status, status)
    AND priority = COALESCE($2::task_priority, priority)
    AND ($3::bool OR status != 'done')
ORDER BY
    CASE WHEN status != 'done' THEN 0 ELSE 1 END,
    CASE priority 
        WHEN 'critical' THEN 0
        WHEN 'urgent' THEN 1
        WHEN 'high' THEN 2
        WHEN 'medium' THEN 3
        WHEN 'low' THEN 4
    END,
    due_date ASC NULLS LAST,
    created_at DESC
LIMIT COALESCE($5::int, 50)
OFFSET COALESCE($4::int, 0)
`

type ListTasksParams struct {
	Status           TaskStatus   `json:"status"`
	Priority         TaskPriority `json:"priority"`
	IncludeCompleted bool         `json:"include_completed"`
	OffsetCount      int32        `json:"offset_count"`
	LimitCount       int32        `json:"limit_count"`
}

func (q *Queries) ListTasks(ctx context.Context, arg ListTasksParams) ([]*Task, error) {
	rows, err := q.db.Query(ctx, ListTasks,
		arg.Status,
		arg.Priority,
		arg.IncludeCompleted,
		arg.OffsetCount,
		arg.LimitCount,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Task{}
	for rows.Next() {
		var i Task
		if err := rows.Scan(
			&i.ID,
			&i.Title,
			&i.Description,
			&i.Status,
			&i.Priority,
			&i.DueDate,
			&i.CompletedAt,
			&i.Labels,
			&i.Metadata,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const ListTasksByLabel = `-- name: ListTasksByLabel :many
SELECT id, title, description, status, priority, due_date, completed_at, labels, metadata, created_at, updated_at FROM tasks
WHERE 
    $1 = ANY(labels)
    AND ($2::bool OR status != 'done')
ORDER BY created_at DESC
LIMIT $3
`

type ListTasksByLabelParams struct {
	Label            []string `json:"label"`
	IncludeCompleted bool     `json:"include_completed"`
	LimitCount       int32    `json:"limit_count"`
}

func (q *Queries) ListTasksByLabel(ctx context.Context, arg ListTasksByLabelParams) ([]*Task, error) {
	rows, err := q.db.Query(ctx, ListTasksByLabel, arg.Label, arg.IncludeCompleted, arg.LimitCount)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Task{}
	for rows.Next() {
		var i Task
		if err := rows.Scan(
			&i.ID,
			&i.Title,
			&i.Description,
			&i.Status,
			&i.Priority,
			&i.DueDate,
			&i.CompletedAt,
			&i.Labels,
			&i.Metadata,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const ListTasksDueToday = `-- name: ListTasksDueToday :many
SELECT id, title, description, status, priority, due_date, completed_at, labels, metadata, created_at, updated_at FROM tasks
WHERE 
    status != 'done'
    AND due_date >= CURRENT_DATE
    AND due_date < CURRENT_DATE + INTERVAL '1 day'
ORDER BY due_date ASC, priority DESC
`

func (q *Queries) ListTasksDueToday(ctx context.Context) ([]*Task, error) {
	rows, err := q.db.Query(ctx, ListTasksDueToday)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Task{}
	for rows.Next() {
		var i Task
		if err := rows.Scan(
			&i.ID,
			&i.Title,
			&i.Description,
			&i.Status,
			&i.Priority,
			&i.DueDate,
			&i.CompletedAt,
			&i.Labels,
			&i.Metadata,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const RemoveTaskLabel = `-- name: RemoveTaskLabel :one
UPDATE tasks
SET labels = array_remove(labels, $1)
WHERE id = $2
RETURNING id, title, description, status, priority, due_date, completed_at, labels, metadata, created_at, updated_at
`

type RemoveTaskLabelParams struct {
	Label interface{} `json:"label"`
	ID    pgtype.UUID `json:"id"`
}

func (q *Queries) RemoveTaskLabel(ctx context.Context, arg RemoveTaskLabelParams) (*Task, error) {
	row := q.db.QueryRow(ctx, RemoveTaskLabel, arg.Label, arg.ID)
	var i Task
	err := row.Scan(
		&i.ID,
		&i.Title,
		&i.Description,
		&i.Status,
		&i.Priority,
		&i.DueDate,
		&i.CompletedAt,
		&i.Labels,
		&i.Metadata,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const SearchTasks = `-- name: SearchTasks :many
SELECT id, title, description, status, priority, due_date, completed_at, labels, metadata, created_at, updated_at FROM tasks
WHERE 
    (title ILIKE '%' || $1 || '%' OR description ILIKE '%' || $1 || '%')
    AND ($2::bool OR status != 'done')
ORDER BY 
    CASE WHEN status != 'done' THEN 0 ELSE 1 END,
    updated_at DESC
LIMIT $3
`

type SearchTasksParams struct {
	Query            pgtype.Text `json:"query"`
	IncludeCompleted bool        `json:"include_completed"`
	LimitCount       int32       `json:"limit_count"`
}

func (q *Queries) SearchTasks(ctx context.Context, arg SearchTasksParams) ([]*Task, error) {
	rows, err := q.db.Query(ctx, SearchTasks, arg.Query, arg.IncludeCompleted, arg.LimitCount)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Task{}
	for rows.Next() {
		var i Task
		if err := rows.Scan(
			&i.ID,
			&i.Title,
			&i.Description,
			&i.Status,
			&i.Priority,
			&i.DueDate,
			&i.CompletedAt,
			&i.Labels,
			&i.Metadata,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const UpdateTask = `-- name: UpdateTask :one
UPDATE tasks
SET
    title = COALESCE($1::text, title),
    description = COALESCE($2::text, description),
    status = COALESCE($3::task_status, status),
    priority = COALESCE($4::task_priority, priority),
    due_date = COALESCE($5::timestamptz, due_date),
    completed_at = CASE 
        WHEN $3::task_status = 'done' AND completed_at IS NULL THEN CURRENT_TIMESTAMP
        WHEN $3::task_status != 'done' THEN NULL
        ELSE completed_at
    END,
    labels = COALESCE($6, labels),
    metadata = COALESCE($7::jsonb, metadata)
WHERE id = $8
RETURNING id, title, description, status, priority, due_date, completed_at, labels, metadata, created_at, updated_at
`

type UpdateTaskParams struct {
	Title       string             `json:"title"`
	Description string             `json:"description"`
	Status      TaskStatus         `json:"status"`
	Priority    TaskPriority       `json:"priority"`
	DueDate     pgtype.Timestamptz `json:"due_date"`
	Labels      []string           `json:"labels"`
	Metadata    []byte             `json:"metadata"`
	ID          pgtype.UUID        `json:"id"`
}

func (q *Queries) UpdateTask(ctx context.Context, arg UpdateTaskParams) (*Task, error) {
	row := q.db.QueryRow(ctx, UpdateTask,
		arg.Title,
		arg.Description,
		arg.Status,
		arg.Priority,
		arg.DueDate,
		arg.Labels,
		arg.Metadata,
		arg.ID,
	)
	var i Task
	err := row.Scan(
		&i.ID,
		&i.Title,
		&i.Description,
		&i.Status,
		&i.Priority,
		&i.DueDate,
		&i.CompletedAt,
		&i.Labels,
		&i.Metadata,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const UpdateTaskLabels = `-- name: UpdateTaskLabels :one
UPDATE tasks
SET labels = $1
WHERE id = $2
RETURNING id, title, description, status, priority, due_date, completed_at, labels, metadata, created_at, updated_at
`

type UpdateTaskLabelsParams struct {
	Labels []string    `json:"labels"`
	ID     pgtype.UUID `json:"id"`
}

func (q *Queries) UpdateTaskLabels(ctx context.Context, arg UpdateTaskLabelsParams) (*Task, error) {
	row := q.db.QueryRow(ctx, UpdateTaskLabels, arg.Labels, arg.ID)
	var i Task
	err := row.Scan(
		&i.ID,
		&i.Title,
		&i.Description,
		&i.Status,
		&i.Priority,
		&i.DueDate,
		&i.CompletedAt,
		&i.Labels,
		&i.Metadata,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}
