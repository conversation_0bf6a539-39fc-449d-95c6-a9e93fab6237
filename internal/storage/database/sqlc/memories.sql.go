// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: memories.sql

package sqlc

import (
	"context"
	"database/sql"
	"encoding/json"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"
	pgvector "github.com/pgvector/pgvector-go"
)

const CleanupJobs = `-- name: CleanupJobs :exec
DELETE FROM queue
WHERE status IN ('completed', 'failed')
    AND processed_at < NOW() - INTERVAL '7 days'
`

func (q *Queries) CleanupJobs(ctx context.Context) error {
	_, err := q.db.Exec(ctx, CleanupJobs)
	return err
}

const CountByType = `-- name: CountByType :many
SELECT type, COUNT(*)::int as count
FROM memories
WHERE status = 'active'
GROUP BY type
ORDER BY count DESC
`

type CountByTypeRow struct {
	Type  MemoryType `json:"type"`
	Count int32      `json:"count"`
}

func (q *Queries) CountByType(ctx context.Context) ([]*CountByTypeRow, error) {
	rows, err := q.db.Query(ctx, CountByType)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*CountByTypeRow{}
	for rows.Next() {
		var i CountByTypeRow
		if err := rows.Scan(&i.Type, &i.Count); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const CreateHistory = `-- name: CreateHistory :exec
INSERT INTO history (
    memory_id, action, version,
    content, entities, attributes, context,
    reason, created_by
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9
)
`

type CreateHistoryParams struct {
	MemoryID   pgtype.UUID     `json:"memory_id"`
	Action     ActionType      `json:"action"`
	Version    int32           `json:"version"`
	Content    string          `json:"content"`
	Entities   json.RawMessage `json:"entities"`
	Attributes json.RawMessage `json:"attributes"`
	Context    json.RawMessage `json:"context"`
	Reason     pgtype.Text     `json:"reason"`
	CreatedBy  pgtype.Text     `json:"created_by"`
}

func (q *Queries) CreateHistory(ctx context.Context, arg CreateHistoryParams) error {
	_, err := q.db.Exec(ctx, CreateHistory,
		arg.MemoryID,
		arg.Action,
		arg.Version,
		arg.Content,
		arg.Entities,
		arg.Attributes,
		arg.Context,
		arg.Reason,
		arg.CreatedBy,
	)
	return err
}

const CreateJob = `-- name: CreateJob :one

INSERT INTO queue (conversation_id, messages)
VALUES ($1, $2)
RETURNING id, conversation_id, messages, status, error, created_at, processed_at
`

type CreateJobParams struct {
	ConversationID pgtype.UUID `json:"conversation_id"`
	Messages       []byte      `json:"messages"`
}

// Queue operations
func (q *Queries) CreateJob(ctx context.Context, arg CreateJobParams) (*Queue, error) {
	row := q.db.QueryRow(ctx, CreateJob, arg.ConversationID, arg.Messages)
	var i Queue
	err := row.Scan(
		&i.ID,
		&i.ConversationID,
		&i.Messages,
		&i.Status,
		&i.Error,
		&i.CreatedAt,
		&i.ProcessedAt,
	)
	return &i, err
}

const CreateMemory = `-- name: CreateMemory :one
INSERT INTO memories (
    semantic_id, type, content, summary,
    entities, attributes, context,
    embedding, keywords, confidence
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10
)
RETURNING id, semantic_id, type, content, summary, entities, attributes, context, embedding, keywords, confidence, version, access_count, created_at, updated_at, accessed_at, status, archived_at, merged_from
`

type CreateMemoryParams struct {
	SemanticID string          `json:"semantic_id"`
	Type       MemoryType      `json:"type"`
	Content    string          `json:"content"`
	Summary    pgtype.Text     `json:"summary"`
	Entities   json.RawMessage `json:"entities"`
	Attributes json.RawMessage `json:"attributes"`
	Context    json.RawMessage `json:"context"`
	Embedding  pgvector.Vector `json:"embedding"`
	Keywords   []string        `json:"keywords"`
	Confidence pgtype.Float4   `json:"confidence"`
}

func (q *Queries) CreateMemory(ctx context.Context, arg CreateMemoryParams) (*Memory, error) {
	row := q.db.QueryRow(ctx, CreateMemory,
		arg.SemanticID,
		arg.Type,
		arg.Content,
		arg.Summary,
		arg.Entities,
		arg.Attributes,
		arg.Context,
		arg.Embedding,
		arg.Keywords,
		arg.Confidence,
	)
	var i Memory
	err := row.Scan(
		&i.ID,
		&i.SemanticID,
		&i.Type,
		&i.Content,
		&i.Summary,
		&i.Entities,
		&i.Attributes,
		&i.Context,
		&i.Embedding,
		&i.Keywords,
		&i.Confidence,
		&i.Version,
		&i.AccessCount,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.AccessedAt,
		&i.Status,
		&i.ArchivedAt,
		&i.MergedFrom,
	)
	return &i, err
}

const CreateMergedMemory = `-- name: CreateMergedMemory :one
INSERT INTO memories (
    semantic_id, type, content, summary,
    entities, attributes, context,
    embedding, keywords, confidence,
    merged_from
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11
)
RETURNING id, semantic_id, type, content, summary, entities, attributes, context, embedding, keywords, confidence, version, access_count, created_at, updated_at, accessed_at, status, archived_at, merged_from
`

type CreateMergedMemoryParams struct {
	SemanticID string          `json:"semantic_id"`
	Type       MemoryType      `json:"type"`
	Content    string          `json:"content"`
	Summary    pgtype.Text     `json:"summary"`
	Entities   json.RawMessage `json:"entities"`
	Attributes json.RawMessage `json:"attributes"`
	Context    json.RawMessage `json:"context"`
	Embedding  pgvector.Vector `json:"embedding"`
	Keywords   []string        `json:"keywords"`
	Confidence pgtype.Float4   `json:"confidence"`
	MergedFrom []uuid.UUID     `json:"merged_from"`
}

func (q *Queries) CreateMergedMemory(ctx context.Context, arg CreateMergedMemoryParams) (*Memory, error) {
	row := q.db.QueryRow(ctx, CreateMergedMemory,
		arg.SemanticID,
		arg.Type,
		arg.Content,
		arg.Summary,
		arg.Entities,
		arg.Attributes,
		arg.Context,
		arg.Embedding,
		arg.Keywords,
		arg.Confidence,
		arg.MergedFrom,
	)
	var i Memory
	err := row.Scan(
		&i.ID,
		&i.SemanticID,
		&i.Type,
		&i.Content,
		&i.Summary,
		&i.Entities,
		&i.Attributes,
		&i.Context,
		&i.Embedding,
		&i.Keywords,
		&i.Confidence,
		&i.Version,
		&i.AccessCount,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.AccessedAt,
		&i.Status,
		&i.ArchivedAt,
		&i.MergedFrom,
	)
	return &i, err
}

const CreateRelation = `-- name: CreateRelation :one
INSERT INTO relations (
    from_id, to_id, type, strength, metadata
) VALUES (
    $1, $2, $3, $4, $5
)
ON CONFLICT (from_id, to_id, type) 
DO UPDATE SET 
    strength = EXCLUDED.strength,
    metadata = EXCLUDED.metadata
RETURNING id, from_id, to_id, type, strength, metadata, created_at
`

type CreateRelationParams struct {
	FromID   pgtype.UUID     `json:"from_id"`
	ToID     pgtype.UUID     `json:"to_id"`
	Type     RelationType    `json:"type"`
	Strength pgtype.Float4   `json:"strength"`
	Metadata json.RawMessage `json:"metadata"`
}

func (q *Queries) CreateRelation(ctx context.Context, arg CreateRelationParams) (*Relation, error) {
	row := q.db.QueryRow(ctx, CreateRelation,
		arg.FromID,
		arg.ToID,
		arg.Type,
		arg.Strength,
		arg.Metadata,
	)
	var i Relation
	err := row.Scan(
		&i.ID,
		&i.FromID,
		&i.ToID,
		&i.Type,
		&i.Strength,
		&i.Metadata,
		&i.CreatedAt,
	)
	return &i, err
}

type CreateRelationsBatchParams struct {
	FromID   pgtype.UUID     `json:"from_id"`
	ToID     pgtype.UUID     `json:"to_id"`
	Type     RelationType    `json:"type"`
	Strength pgtype.Float4   `json:"strength"`
	Metadata json.RawMessage `json:"metadata"`
}

const DeactivateMany = `-- name: DeactivateMany :exec
UPDATE memories
SET status = 'archived'
WHERE id = ANY($1::uuid[])
`

func (q *Queries) DeactivateMany(ctx context.Context, dollar_1 []pgtype.UUID) error {
	_, err := q.db.Exec(ctx, DeactivateMany, dollar_1)
	return err
}

const DeactivateMemory = `-- name: DeactivateMemory :exec
UPDATE memories
SET status = 'archived'
WHERE id = $1
`

func (q *Queries) DeactivateMemory(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, DeactivateMemory, id)
	return err
}

const GetAllHistory = `-- name: GetAllHistory :many
SELECT id, memory_id, action, version, content, entities, attributes, context, reason, created_by, created_at FROM history
ORDER BY created_at DESC
LIMIT $1
`

func (q *Queries) GetAllHistory(ctx context.Context, limit int32) ([]*History, error) {
	rows, err := q.db.Query(ctx, GetAllHistory, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*History{}
	for rows.Next() {
		var i History
		if err := rows.Scan(
			&i.ID,
			&i.MemoryID,
			&i.Action,
			&i.Version,
			&i.Content,
			&i.Entities,
			&i.Attributes,
			&i.Context,
			&i.Reason,
			&i.CreatedBy,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetHistory = `-- name: GetHistory :many
SELECT id, memory_id, action, version, content, entities, attributes, context, reason, created_by, created_at FROM history
WHERE memory_id = $1
ORDER BY created_at DESC
LIMIT $2
`

type GetHistoryParams struct {
	MemoryID pgtype.UUID `json:"memory_id"`
	Limit    int32       `json:"limit"`
}

func (q *Queries) GetHistory(ctx context.Context, arg GetHistoryParams) ([]*History, error) {
	rows, err := q.db.Query(ctx, GetHistory, arg.MemoryID, arg.Limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*History{}
	for rows.Next() {
		var i History
		if err := rows.Scan(
			&i.ID,
			&i.MemoryID,
			&i.Action,
			&i.Version,
			&i.Content,
			&i.Entities,
			&i.Attributes,
			&i.Context,
			&i.Reason,
			&i.CreatedBy,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetHistoryByAction = `-- name: GetHistoryByAction :many
SELECT id, memory_id, action, version, content, entities, attributes, context, reason, created_by, created_at FROM history
WHERE action = $1::action_type
ORDER BY created_at DESC
LIMIT $2
`

type GetHistoryByActionParams struct {
	Column1 ActionType `json:"column_1"`
	Limit   int32      `json:"limit"`
}

func (q *Queries) GetHistoryByAction(ctx context.Context, arg GetHistoryByActionParams) ([]*History, error) {
	rows, err := q.db.Query(ctx, GetHistoryByAction, arg.Column1, arg.Limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*History{}
	for rows.Next() {
		var i History
		if err := rows.Scan(
			&i.ID,
			&i.MemoryID,
			&i.Action,
			&i.Version,
			&i.Content,
			&i.Entities,
			&i.Attributes,
			&i.Context,
			&i.Reason,
			&i.CreatedBy,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetMemory = `-- name: GetMemory :one
SELECT id, semantic_id, type, content, summary, entities, attributes, context, embedding, keywords, confidence, version, access_count, created_at, updated_at, accessed_at, status, archived_at, merged_from FROM memories
WHERE id = $1 AND status = 'active'
`

func (q *Queries) GetMemory(ctx context.Context, id pgtype.UUID) (*Memory, error) {
	row := q.db.QueryRow(ctx, GetMemory, id)
	var i Memory
	err := row.Scan(
		&i.ID,
		&i.SemanticID,
		&i.Type,
		&i.Content,
		&i.Summary,
		&i.Entities,
		&i.Attributes,
		&i.Context,
		&i.Embedding,
		&i.Keywords,
		&i.Confidence,
		&i.Version,
		&i.AccessCount,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.AccessedAt,
		&i.Status,
		&i.ArchivedAt,
		&i.MergedFrom,
	)
	return &i, err
}

const GetMemoryBySemantic = `-- name: GetMemoryBySemantic :one
SELECT id, semantic_id, type, content, summary, entities, attributes, context, embedding, keywords, confidence, version, access_count, created_at, updated_at, accessed_at, status, archived_at, merged_from FROM memories
WHERE semantic_id = $1 AND status = 'active'
`

func (q *Queries) GetMemoryBySemantic(ctx context.Context, semanticID string) (*Memory, error) {
	row := q.db.QueryRow(ctx, GetMemoryBySemantic, semanticID)
	var i Memory
	err := row.Scan(
		&i.ID,
		&i.SemanticID,
		&i.Type,
		&i.Content,
		&i.Summary,
		&i.Entities,
		&i.Attributes,
		&i.Context,
		&i.Embedding,
		&i.Keywords,
		&i.Confidence,
		&i.Version,
		&i.AccessCount,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.AccessedAt,
		&i.Status,
		&i.ArchivedAt,
		&i.MergedFrom,
	)
	return &i, err
}

const GetPendingJobs = `-- name: GetPendingJobs :many
SELECT id, conversation_id, messages, status, error, created_at, processed_at FROM queue
WHERE status = 'pending'
ORDER BY created_at
LIMIT $1
`

func (q *Queries) GetPendingJobs(ctx context.Context, limit int32) ([]*Queue, error) {
	rows, err := q.db.Query(ctx, GetPendingJobs, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Queue{}
	for rows.Next() {
		var i Queue
		if err := rows.Scan(
			&i.ID,
			&i.ConversationID,
			&i.Messages,
			&i.Status,
			&i.Error,
			&i.CreatedAt,
			&i.ProcessedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetRelated = `-- name: GetRelated :many
WITH RECURSIVE related AS (
    -- Direct relations
    SELECT 
        r.to_id as memory_id,
        r.type,
        r.strength,
        1 as depth
    FROM relations r
    WHERE r.from_id = $1::uuid
        AND ($2::relation_type IS NULL OR r.type = $2::relation_type)
    
    UNION
    
    -- Recursive relations up to specified depth
    SELECT 
        r.to_id as memory_id,
        r.type,
        r.strength * rel.strength as strength,
        rel.depth + 1 as depth
    FROM relations r
    INNER JOIN related rel ON r.from_id = rel.memory_id
    WHERE rel.depth < COALESCE($3::int, 1)
)
SELECT DISTINCT ON (m.id)
    m.id, m.semantic_id, m.type, m.content, m.summary, m.entities, m.attributes, m.context, m.embedding, m.keywords, m.confidence, m.version, m.access_count, m.created_at, m.updated_at, m.accessed_at, m.status, m.archived_at, m.merged_from,
    rel.type as relation_type,
    rel.strength as relation_strength,
    rel.depth as relation_depth
FROM related rel
INNER JOIN memories m ON m.id = rel.memory_id
WHERE m.status = 'active'
ORDER BY m.id, rel.strength DESC
`

type GetRelatedParams struct {
	Column1 pgtype.UUID  `json:"column_1"`
	Column2 RelationType `json:"column_2"`
	Column3 int32        `json:"column_3"`
}

type GetRelatedRow struct {
	ID               pgtype.UUID        `json:"id"`
	SemanticID       string             `json:"semantic_id"`
	Type             MemoryType         `json:"type"`
	Content          string             `json:"content"`
	Summary          pgtype.Text        `json:"summary"`
	Entities         json.RawMessage    `json:"entities"`
	Attributes       json.RawMessage    `json:"attributes"`
	Context          json.RawMessage    `json:"context"`
	Embedding        pgvector.Vector    `json:"embedding"`
	Keywords         []string           `json:"keywords"`
	Confidence       pgtype.Float4      `json:"confidence"`
	Version          pgtype.Int4        `json:"version"`
	AccessCount      pgtype.Int4        `json:"access_count"`
	CreatedAt        time.Time          `json:"created_at"`
	UpdatedAt        time.Time          `json:"updated_at"`
	AccessedAt       sql.NullTime       `json:"accessed_at"`
	Status           MemoryStatus       `json:"status"`
	ArchivedAt       pgtype.Timestamptz `json:"archived_at"`
	MergedFrom       []uuid.UUID        `json:"merged_from"`
	RelationType     RelationType       `json:"relation_type"`
	RelationStrength pgtype.Float4      `json:"relation_strength"`
	RelationDepth    int32              `json:"relation_depth"`
}

func (q *Queries) GetRelated(ctx context.Context, arg GetRelatedParams) ([]*GetRelatedRow, error) {
	rows, err := q.db.Query(ctx, GetRelated, arg.Column1, arg.Column2, arg.Column3)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*GetRelatedRow{}
	for rows.Next() {
		var i GetRelatedRow
		if err := rows.Scan(
			&i.ID,
			&i.SemanticID,
			&i.Type,
			&i.Content,
			&i.Summary,
			&i.Entities,
			&i.Attributes,
			&i.Context,
			&i.Embedding,
			&i.Keywords,
			&i.Confidence,
			&i.Version,
			&i.AccessCount,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.AccessedAt,
			&i.Status,
			&i.ArchivedAt,
			&i.MergedFrom,
			&i.RelationType,
			&i.RelationStrength,
			&i.RelationDepth,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetRelationsByMemory = `-- name: GetRelationsByMemory :many
SELECT id, from_id, to_id, type, strength, metadata, created_at FROM relations
WHERE from_id = $1 OR to_id = $1
ORDER BY strength DESC
`

func (q *Queries) GetRelationsByMemory(ctx context.Context, fromID pgtype.UUID) ([]*Relation, error) {
	rows, err := q.db.Query(ctx, GetRelationsByMemory, fromID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Relation{}
	for rows.Next() {
		var i Relation
		if err := rows.Scan(
			&i.ID,
			&i.FromID,
			&i.ToID,
			&i.Type,
			&i.Strength,
			&i.Metadata,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetStats = `-- name: GetStats :one
SELECT 
    COUNT(*)::int as total,
    COUNT(DISTINCT type)::int as types,
    AVG(confidence)::float4 as avg_confidence,
    MAX(access_count)::int as max_access,
    MIN(created_at) as oldest,
    MAX(created_at) as newest
FROM memories
WHERE status = 'active'
`

type GetStatsRow struct {
	Total         int32       `json:"total"`
	Types         int32       `json:"types"`
	AvgConfidence float32     `json:"avg_confidence"`
	MaxAccess     int32       `json:"max_access"`
	Oldest        interface{} `json:"oldest"`
	Newest        interface{} `json:"newest"`
}

func (q *Queries) GetStats(ctx context.Context) (*GetStatsRow, error) {
	row := q.db.QueryRow(ctx, GetStats)
	var i GetStatsRow
	err := row.Scan(
		&i.Total,
		&i.Types,
		&i.AvgConfidence,
		&i.MaxAccess,
		&i.Oldest,
		&i.Newest,
	)
	return &i, err
}

const IncrementAccess = `-- name: IncrementAccess :exec
UPDATE memories
SET access_count = access_count + 1, accessed_at = NOW()
WHERE id = $1
`

func (q *Queries) IncrementAccess(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, IncrementAccess, id)
	return err
}

const ListByKeyword = `-- name: ListByKeyword :many
SELECT id, semantic_id, type, content, summary, entities, attributes, context, embedding, keywords, confidence, version, access_count, created_at, updated_at, accessed_at, status, archived_at, merged_from FROM memories
WHERE status = 'active' AND $1 = ANY(keywords)
ORDER BY updated_at DESC
LIMIT $2
`

type ListByKeywordParams struct {
	Keywords []string `json:"keywords"`
	Limit    int32    `json:"limit"`
}

func (q *Queries) ListByKeyword(ctx context.Context, arg ListByKeywordParams) ([]*Memory, error) {
	rows, err := q.db.Query(ctx, ListByKeyword, arg.Keywords, arg.Limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Memory{}
	for rows.Next() {
		var i Memory
		if err := rows.Scan(
			&i.ID,
			&i.SemanticID,
			&i.Type,
			&i.Content,
			&i.Summary,
			&i.Entities,
			&i.Attributes,
			&i.Context,
			&i.Embedding,
			&i.Keywords,
			&i.Confidence,
			&i.Version,
			&i.AccessCount,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.AccessedAt,
			&i.Status,
			&i.ArchivedAt,
			&i.MergedFrom,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const ListByType = `-- name: ListByType :many
SELECT id, semantic_id, type, content, summary, entities, attributes, context, embedding, keywords, confidence, version, access_count, created_at, updated_at, accessed_at, status, archived_at, merged_from FROM memories
WHERE type = $1::memory_type AND status = 'active'
ORDER BY updated_at DESC
LIMIT $2
`

type ListByTypeParams struct {
	Column1 MemoryType `json:"column_1"`
	Limit   int32      `json:"limit"`
}

func (q *Queries) ListByType(ctx context.Context, arg ListByTypeParams) ([]*Memory, error) {
	rows, err := q.db.Query(ctx, ListByType, arg.Column1, arg.Limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Memory{}
	for rows.Next() {
		var i Memory
		if err := rows.Scan(
			&i.ID,
			&i.SemanticID,
			&i.Type,
			&i.Content,
			&i.Summary,
			&i.Entities,
			&i.Attributes,
			&i.Context,
			&i.Embedding,
			&i.Keywords,
			&i.Confidence,
			&i.Version,
			&i.AccessCount,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.AccessedAt,
			&i.Status,
			&i.ArchivedAt,
			&i.MergedFrom,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const ListInTimeRange = `-- name: ListInTimeRange :many
SELECT id, semantic_id, type, content, summary, entities, attributes, context, embedding, keywords, confidence, version, access_count, created_at, updated_at, accessed_at, status, archived_at, merged_from FROM memories
WHERE status = 'active'
    AND (
        (context->>'occurred_at')::timestamptz BETWEEN $1 AND $2
        OR (context->>'start_time')::timestamptz BETWEEN $1 AND $2
    )
ORDER BY COALESCE(
    (context->>'occurred_at')::timestamptz,
    (context->>'start_time')::timestamptz
)
`

type ListInTimeRangeParams struct {
	Context   json.RawMessage `json:"context"`
	Context_2 json.RawMessage `json:"context_2"`
}

func (q *Queries) ListInTimeRange(ctx context.Context, arg ListInTimeRangeParams) ([]*Memory, error) {
	rows, err := q.db.Query(ctx, ListInTimeRange, arg.Context, arg.Context_2)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Memory{}
	for rows.Next() {
		var i Memory
		if err := rows.Scan(
			&i.ID,
			&i.SemanticID,
			&i.Type,
			&i.Content,
			&i.Summary,
			&i.Entities,
			&i.Attributes,
			&i.Context,
			&i.Embedding,
			&i.Keywords,
			&i.Confidence,
			&i.Version,
			&i.AccessCount,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.AccessedAt,
			&i.Status,
			&i.ArchivedAt,
			&i.MergedFrom,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const ListRecent = `-- name: ListRecent :many
SELECT id, semantic_id, type, content, summary, entities, attributes, context, embedding, keywords, confidence, version, access_count, created_at, updated_at, accessed_at, status, archived_at, merged_from FROM memories
WHERE status = 'active'
ORDER BY updated_at DESC
LIMIT $1
`

func (q *Queries) ListRecent(ctx context.Context, limit int32) ([]*Memory, error) {
	rows, err := q.db.Query(ctx, ListRecent, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Memory{}
	for rows.Next() {
		var i Memory
		if err := rows.Scan(
			&i.ID,
			&i.SemanticID,
			&i.Type,
			&i.Content,
			&i.Summary,
			&i.Entities,
			&i.Attributes,
			&i.Context,
			&i.Embedding,
			&i.Keywords,
			&i.Confidence,
			&i.Version,
			&i.AccessCount,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.AccessedAt,
			&i.Status,
			&i.ArchivedAt,
			&i.MergedFrom,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const ListRecurring = `-- name: ListRecurring :many
SELECT id, semantic_id, type, content, summary, entities, attributes, context, embedding, keywords, confidence, version, access_count, created_at, updated_at, accessed_at, status, archived_at, merged_from FROM memories
WHERE status = 'active' AND context->>'recurrence' IS NOT NULL
ORDER BY updated_at DESC
`

func (q *Queries) ListRecurring(ctx context.Context) ([]*Memory, error) {
	rows, err := q.db.Query(ctx, ListRecurring)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Memory{}
	for rows.Next() {
		var i Memory
		if err := rows.Scan(
			&i.ID,
			&i.SemanticID,
			&i.Type,
			&i.Content,
			&i.Summary,
			&i.Entities,
			&i.Attributes,
			&i.Context,
			&i.Embedding,
			&i.Keywords,
			&i.Confidence,
			&i.Version,
			&i.AccessCount,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.AccessedAt,
			&i.Status,
			&i.ArchivedAt,
			&i.MergedFrom,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const SearchByAttributes = `-- name: SearchByAttributes :many
SELECT id, semantic_id, type, content, summary, entities, attributes, context, embedding, keywords, confidence, version, access_count, created_at, updated_at, accessed_at, status, archived_at, merged_from FROM memories
WHERE status = 'active' AND attributes @> $1::jsonb
ORDER BY updated_at DESC
LIMIT $2
`

type SearchByAttributesParams struct {
	Column1 []byte `json:"column_1"`
	Limit   int32  `json:"limit"`
}

func (q *Queries) SearchByAttributes(ctx context.Context, arg SearchByAttributesParams) ([]*Memory, error) {
	rows, err := q.db.Query(ctx, SearchByAttributes, arg.Column1, arg.Limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Memory{}
	for rows.Next() {
		var i Memory
		if err := rows.Scan(
			&i.ID,
			&i.SemanticID,
			&i.Type,
			&i.Content,
			&i.Summary,
			&i.Entities,
			&i.Attributes,
			&i.Context,
			&i.Embedding,
			&i.Keywords,
			&i.Confidence,
			&i.Version,
			&i.AccessCount,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.AccessedAt,
			&i.Status,
			&i.ArchivedAt,
			&i.MergedFrom,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const SearchMemories = `-- name: SearchMemories :many
WITH vector_scores AS (
    SELECT 
        id,
        CASE 
            WHEN $1::vector IS NOT NULL AND embedding IS NOT NULL 
            THEN (1 - (embedding <=> $1::vector))::REAL
            ELSE 0.0::REAL
        END as similarity
    FROM memories
    WHERE status = 'active'
        AND ($1::vector IS NULL OR embedding IS NOT NULL)
),
text_scores AS (
    SELECT 
        id,
        CASE
            WHEN $2::TEXT IS NOT NULL AND $2 != '' THEN
                CASE
                    -- Higher score for exact matches
                    WHEN content ILIKE $2 THEN 1.0
                    -- Medium score for contains
                    WHEN content ILIKE '%' || $2 || '%' THEN 0.5
                    WHEN COALESCE(summary, '') ILIKE '%' || $2 || '%' THEN 0.3
                    ELSE 0.0
                END
            ELSE 0.0
        END::REAL as text_relevance
    FROM memories
    WHERE status = 'active'
)
SELECT 
    m.id,
    m.semantic_id,
    m.type,
    m.content,
    m.summary,
    m.entities,
    m.attributes,
    m.context,
    m.embedding,
    m.keywords,
    m.confidence,
    m.version,
    m.access_count,
    m.created_at,
    m.updated_at,
    m.accessed_at,
    m.status,
    m.merged_from,
    COALESCE(vs.similarity, 0.0) as similarity,
    -- Multi-dimensional scoring inspired by mem0/papr.ai
    (
        -- 1. Vector similarity (base score: 40%)
        COALESCE(vs.similarity, 0.0) * 
        CASE 
            WHEN $1::vector IS NOT NULL THEN 0.40
            ELSE 0.0
        END +
        
        -- 2. Text relevance (20% or 60% if no embedding)
        COALESCE(ts.text_relevance, 0.0) * 
        CASE 
            WHEN $1::vector IS NOT NULL AND $2::TEXT IS NOT NULL THEN 0.20
            WHEN $1::vector IS NULL AND $2::TEXT IS NOT NULL THEN 0.60
            ELSE 0.0
        END +
        
        -- 3. Recency factor (15%)
        CASE 
            WHEN m.updated_at > NOW() - INTERVAL '1 hour' THEN 0.15
            WHEN m.updated_at > NOW() - INTERVAL '1 day' THEN 0.12
            WHEN m.updated_at > NOW() - INTERVAL '7 days' THEN 0.08
            WHEN m.updated_at > NOW() - INTERVAL '30 days' THEN 0.04
            ELSE 0.0
        END +
        
        -- 4. Access frequency (10%)
        LEAST(m.access_count * 0.01, 0.10) +
        
        -- 5. Reserved for future enhancements (10%)
        0.0 +
        
        -- 6. Confidence factor (5%)
        m.confidence * 0.05
    )::REAL as score
FROM memories m
LEFT JOIN vector_scores vs ON m.id = vs.id
LEFT JOIN text_scores ts ON m.id = ts.id
WHERE m.status = 'active'
    -- Type filter
    AND ($3::memory_type[] IS NULL OR CARDINALITY($3::memory_type[]) = 0 OR m.type = ANY($3::memory_type[]))
    -- Entity filter
    AND ($4::TEXT[] IS NULL OR CARDINALITY($4::TEXT[]) = 0 OR 
        EXISTS (
            SELECT 1 FROM jsonb_array_elements(m.entities) e
            WHERE e->>'name' = ANY($4::TEXT[])
        ))
    -- Time range filter
    AND ($5::TIMESTAMPTZ IS NULL OR 
        COALESCE((m.context->>'occurred_at')::timestamptz, (m.context->>'start_time')::timestamptz) >= $5::TIMESTAMPTZ)
    AND ($6::TIMESTAMPTZ IS NULL OR 
        COALESCE((m.context->>'occurred_at')::timestamptz, (m.context->>'end_time')::timestamptz) <= $6::TIMESTAMPTZ)
    -- Must have either vector match or text match
    AND (
        (vs.similarity > COALESCE($7::REAL, 0.0)) OR
        (ts.text_relevance > 0.0) OR
        ($1::vector IS NULL AND $2::TEXT IS NULL)
    )
ORDER BY score DESC
`

type SearchMemoriesParams struct {
	Embedding     pgvector.Vector    `json:"embedding"`
	Text          string             `json:"text"`
	Types         []MemoryType       `json:"types"`
	Entities      []string           `json:"entities"`
	TimeStart     pgtype.Timestamptz `json:"time_start"`
	TimeEnd       pgtype.Timestamptz `json:"time_end"`
	MinSimilarity float32            `json:"min_similarity"`
}

type SearchMemoriesRow struct {
	ID          pgtype.UUID     `json:"id"`
	SemanticID  string          `json:"semantic_id"`
	Type        MemoryType      `json:"type"`
	Content     string          `json:"content"`
	Summary     pgtype.Text     `json:"summary"`
	Entities    json.RawMessage `json:"entities"`
	Attributes  json.RawMessage `json:"attributes"`
	Context     json.RawMessage `json:"context"`
	Embedding   pgvector.Vector `json:"embedding"`
	Keywords    []string        `json:"keywords"`
	Confidence  pgtype.Float4   `json:"confidence"`
	Version     pgtype.Int4     `json:"version"`
	AccessCount pgtype.Int4     `json:"access_count"`
	CreatedAt   time.Time       `json:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at"`
	AccessedAt  sql.NullTime    `json:"accessed_at"`
	Status      MemoryStatus    `json:"status"`
	MergedFrom  []uuid.UUID     `json:"merged_from"`
	Similarity  float32         `json:"similarity"`
	Score       float32         `json:"score"`
}

// Hybrid search combining vector similarity and text search (NO LIMIT - returns all matches)
func (q *Queries) SearchMemories(ctx context.Context, arg SearchMemoriesParams) ([]*SearchMemoriesRow, error) {
	rows, err := q.db.Query(ctx, SearchMemories,
		arg.Embedding,
		arg.Text,
		arg.Types,
		arg.Entities,
		arg.TimeStart,
		arg.TimeEnd,
		arg.MinSimilarity,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*SearchMemoriesRow{}
	for rows.Next() {
		var i SearchMemoriesRow
		if err := rows.Scan(
			&i.ID,
			&i.SemanticID,
			&i.Type,
			&i.Content,
			&i.Summary,
			&i.Entities,
			&i.Attributes,
			&i.Context,
			&i.Embedding,
			&i.Keywords,
			&i.Confidence,
			&i.Version,
			&i.AccessCount,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.AccessedAt,
			&i.Status,
			&i.MergedFrom,
			&i.Similarity,
			&i.Score,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const SearchSimilarWithLimit = `-- name: SearchSimilarWithLimit :many
SELECT 
    id,
    semantic_id,
    type,
    content,
    summary,
    entities,
    attributes,
    context,
    embedding,
    keywords,
    confidence,
    version,
    access_count,
    created_at,
    updated_at,
    accessed_at,
    status,
    merged_from,
    (1 - (embedding <=> $1::vector))::float4 as similarity
FROM memories
WHERE 
    status = 'active'
    AND embedding IS NOT NULL
    AND ($2::text IS NULL OR status = $2::text)
    AND 1 - (embedding <=> $1::vector) > COALESCE($3::float4, 0.0)
ORDER BY embedding <=> $1::vector
LIMIT $4::int
`

type SearchSimilarWithLimitParams struct {
	Embedding     pgvector.Vector `json:"embedding"`
	Status        string          `json:"status"`
	MinSimilarity float32         `json:"min_similarity"`
	LimitCount    int32           `json:"limit_count"`
}

type SearchSimilarWithLimitRow struct {
	ID          pgtype.UUID     `json:"id"`
	SemanticID  string          `json:"semantic_id"`
	Type        MemoryType      `json:"type"`
	Content     string          `json:"content"`
	Summary     pgtype.Text     `json:"summary"`
	Entities    json.RawMessage `json:"entities"`
	Attributes  json.RawMessage `json:"attributes"`
	Context     json.RawMessage `json:"context"`
	Embedding   pgvector.Vector `json:"embedding"`
	Keywords    []string        `json:"keywords"`
	Confidence  pgtype.Float4   `json:"confidence"`
	Version     pgtype.Int4     `json:"version"`
	AccessCount pgtype.Int4     `json:"access_count"`
	CreatedAt   time.Time       `json:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at"`
	AccessedAt  sql.NullTime    `json:"accessed_at"`
	Status      MemoryStatus    `json:"status"`
	MergedFrom  []uuid.UUID     `json:"merged_from"`
	Similarity  float32         `json:"similarity"`
}

// Optimized vector similarity search that leverages HNSW/IVFFlat indexes
// This query is specifically designed for efficient nearest neighbor search
func (q *Queries) SearchSimilarWithLimit(ctx context.Context, arg SearchSimilarWithLimitParams) ([]*SearchSimilarWithLimitRow, error) {
	rows, err := q.db.Query(ctx, SearchSimilarWithLimit,
		arg.Embedding,
		arg.Status,
		arg.MinSimilarity,
		arg.LimitCount,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*SearchSimilarWithLimitRow{}
	for rows.Next() {
		var i SearchSimilarWithLimitRow
		if err := rows.Scan(
			&i.ID,
			&i.SemanticID,
			&i.Type,
			&i.Content,
			&i.Summary,
			&i.Entities,
			&i.Attributes,
			&i.Context,
			&i.Embedding,
			&i.Keywords,
			&i.Confidence,
			&i.Version,
			&i.AccessCount,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.AccessedAt,
			&i.Status,
			&i.MergedFrom,
			&i.Similarity,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const SetEmbedding = `-- name: SetEmbedding :exec
UPDATE memories
SET embedding = $1
WHERE id = $2
`

type SetEmbeddingParams struct {
	Embedding pgvector.Vector `json:"embedding"`
	ID        pgtype.UUID     `json:"id"`
}

func (q *Queries) SetEmbedding(ctx context.Context, arg SetEmbeddingParams) error {
	_, err := q.db.Exec(ctx, SetEmbedding, arg.Embedding, arg.ID)
	return err
}

const UpdateJob = `-- name: UpdateJob :exec
UPDATE queue
SET 
    status = $2,
    error = $3,
    processed_at = CASE 
        WHEN $2 IN ('completed', 'failed') THEN NOW() 
        ELSE processed_at 
    END
WHERE id = $1
`

type UpdateJobParams struct {
	ID     pgtype.UUID `json:"id"`
	Status pgtype.Text `json:"status"`
	Error  pgtype.Text `json:"error"`
}

func (q *Queries) UpdateJob(ctx context.Context, arg UpdateJobParams) error {
	_, err := q.db.Exec(ctx, UpdateJob, arg.ID, arg.Status, arg.Error)
	return err
}

const UpdateMemory = `-- name: UpdateMemory :exec
UPDATE memories
SET 
    content = $2,
    summary = $3,
    entities = $4,
    attributes = $5,
    context = $6,
    confidence = COALESCE($7, confidence),
    version = version + 1,
    updated_at = NOW()
WHERE id = $1 AND status = 'active'
`

type UpdateMemoryParams struct {
	ID         pgtype.UUID     `json:"id"`
	Content    string          `json:"content"`
	Summary    pgtype.Text     `json:"summary"`
	Entities   json.RawMessage `json:"entities"`
	Attributes json.RawMessage `json:"attributes"`
	Context    json.RawMessage `json:"context"`
	Confidence pgtype.Float4   `json:"confidence"`
}

func (q *Queries) UpdateMemory(ctx context.Context, arg UpdateMemoryParams) error {
	_, err := q.db.Exec(ctx, UpdateMemory,
		arg.ID,
		arg.Content,
		arg.Summary,
		arg.Entities,
		arg.Attributes,
		arg.Context,
		arg.Confidence,
	)
	return err
}

const UpdateRelation = `-- name: UpdateRelation :exec
UPDATE relations
SET 
    strength = COALESCE($4, strength),
    metadata = COALESCE($5, metadata)
WHERE from_id = $1 AND to_id = $2 AND type = $3
`

type UpdateRelationParams struct {
	FromID   pgtype.UUID     `json:"from_id"`
	ToID     pgtype.UUID     `json:"to_id"`
	Type     RelationType    `json:"type"`
	Strength pgtype.Float4   `json:"strength"`
	Metadata json.RawMessage `json:"metadata"`
}

func (q *Queries) UpdateRelation(ctx context.Context, arg UpdateRelationParams) error {
	_, err := q.db.Exec(ctx, UpdateRelation,
		arg.FromID,
		arg.ToID,
		arg.Type,
		arg.Strength,
		arg.Metadata,
	)
	return err
}
