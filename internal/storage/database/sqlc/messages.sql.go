// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: messages.sql

package sqlc

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const CountMessagesByRole = `-- name: CountMessagesByRole :many
SELECT
    role,
    COUNT(*)::int as count
FROM messages
WHERE conversation_id = $1
GROUP BY role
`

type CountMessagesByRoleRow struct {
	Role  MessageRole `json:"role"`
	Count int32       `json:"count"`
}

func (q *Queries) CountMessagesByRole(ctx context.Context, conversationID pgtype.UUID) ([]*CountMessagesByRoleRow, error) {
	rows, err := q.db.Query(ctx, CountMessagesByRole, conversationID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*CountMessagesByRoleRow{}
	for rows.Next() {
		var i CountMessagesByRoleRow
		if err := rows.Scan(&i.Role, &i.Count); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const CreateMessage = `-- name: CreateMessage :one
INSERT INTO messages (
    conversation_id,
    role,
    content,
    tokens_used,
    metadata
) VALUES (
    $1,
    $2::message_role,
    $3,
    COALESCE($4, 0),
    COALESCE($5::jsonb, '{}')
)
RETURNING id, conversation_id, role, content, tokens_used, metadata, created_at
`

type CreateMessageParams struct {
	ConversationID pgtype.UUID `json:"conversation_id"`
	Role           MessageRole `json:"role"`
	Content        string      `json:"content"`
	TokensUsed     interface{} `json:"tokens_used"`
	Metadata       []byte      `json:"metadata"`
}

func (q *Queries) CreateMessage(ctx context.Context, arg CreateMessageParams) (*Message, error) {
	row := q.db.QueryRow(ctx, CreateMessage,
		arg.ConversationID,
		arg.Role,
		arg.Content,
		arg.TokensUsed,
		arg.Metadata,
	)
	var i Message
	err := row.Scan(
		&i.ID,
		&i.ConversationID,
		&i.Role,
		&i.Content,
		&i.TokensUsed,
		&i.Metadata,
		&i.CreatedAt,
	)
	return &i, err
}

const DeleteConversationMessages = `-- name: DeleteConversationMessages :exec
DELETE FROM messages
WHERE conversation_id = $1
`

func (q *Queries) DeleteConversationMessages(ctx context.Context, conversationID pgtype.UUID) error {
	_, err := q.db.Exec(ctx, DeleteConversationMessages, conversationID)
	return err
}

const DeleteMessage = `-- name: DeleteMessage :exec
DELETE FROM messages
WHERE id = $1
`

func (q *Queries) DeleteMessage(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, DeleteMessage, id)
	return err
}

const GetConversationTokenUsage = `-- name: GetConversationTokenUsage :one
SELECT
    COUNT(*)::int as message_count,
    SUM(tokens_used)::int as total_tokens,
    AVG(tokens_used)::float as avg_tokens_per_message
FROM messages
WHERE conversation_id = $1
`

type GetConversationTokenUsageRow struct {
	MessageCount        int32   `json:"message_count"`
	TotalTokens         int32   `json:"total_tokens"`
	AvgTokensPerMessage float64 `json:"avg_tokens_per_message"`
}

func (q *Queries) GetConversationTokenUsage(ctx context.Context, conversationID pgtype.UUID) (*GetConversationTokenUsageRow, error) {
	row := q.db.QueryRow(ctx, GetConversationTokenUsage, conversationID)
	var i GetConversationTokenUsageRow
	err := row.Scan(&i.MessageCount, &i.TotalTokens, &i.AvgTokensPerMessage)
	return &i, err
}

const GetLastMessages = `-- name: GetLastMessages :many
SELECT id, conversation_id, role, content, tokens_used, metadata, created_at FROM messages
WHERE conversation_id = $1
ORDER BY created_at DESC
LIMIT $2
`

type GetLastMessagesParams struct {
	ConversationID pgtype.UUID `json:"conversation_id"`
	LimitCount     int32       `json:"limit_count"`
}

func (q *Queries) GetLastMessages(ctx context.Context, arg GetLastMessagesParams) ([]*Message, error) {
	rows, err := q.db.Query(ctx, GetLastMessages, arg.ConversationID, arg.LimitCount)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Message{}
	for rows.Next() {
		var i Message
		if err := rows.Scan(
			&i.ID,
			&i.ConversationID,
			&i.Role,
			&i.Content,
			&i.TokensUsed,
			&i.Metadata,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetMessage = `-- name: GetMessage :one
SELECT id, conversation_id, role, content, tokens_used, metadata, created_at FROM messages
WHERE id = $1
LIMIT 1
`

func (q *Queries) GetMessage(ctx context.Context, id pgtype.UUID) (*Message, error) {
	row := q.db.QueryRow(ctx, GetMessage, id)
	var i Message
	err := row.Scan(
		&i.ID,
		&i.ConversationID,
		&i.Role,
		&i.Content,
		&i.TokensUsed,
		&i.Metadata,
		&i.CreatedAt,
	)
	return &i, err
}

const ListMessages = `-- name: ListMessages :many
SELECT id, conversation_id, role, content, tokens_used, metadata, created_at FROM messages
WHERE conversation_id = $1
ORDER BY created_at ASC
LIMIT COALESCE($3::int, 100)
OFFSET COALESCE($2::int, 0)
`

type ListMessagesParams struct {
	ConversationID pgtype.UUID `json:"conversation_id"`
	OffsetCount    int32       `json:"offset_count"`
	LimitCount     int32       `json:"limit_count"`
}

func (q *Queries) ListMessages(ctx context.Context, arg ListMessagesParams) ([]*Message, error) {
	rows, err := q.db.Query(ctx, ListMessages, arg.ConversationID, arg.OffsetCount, arg.LimitCount)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Message{}
	for rows.Next() {
		var i Message
		if err := rows.Scan(
			&i.ID,
			&i.ConversationID,
			&i.Role,
			&i.Content,
			&i.TokensUsed,
			&i.Metadata,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const ListRecentMessages = `-- name: ListRecentMessages :many
SELECT id, conversation_id, role, content, tokens_used, metadata, created_at FROM messages
WHERE conversation_id = $1
ORDER BY created_at DESC
LIMIT $2
`

type ListRecentMessagesParams struct {
	ConversationID pgtype.UUID `json:"conversation_id"`
	LimitCount     int32       `json:"limit_count"`
}

func (q *Queries) ListRecentMessages(ctx context.Context, arg ListRecentMessagesParams) ([]*Message, error) {
	rows, err := q.db.Query(ctx, ListRecentMessages, arg.ConversationID, arg.LimitCount)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Message{}
	for rows.Next() {
		var i Message
		if err := rows.Scan(
			&i.ID,
			&i.ConversationID,
			&i.Role,
			&i.Content,
			&i.TokensUsed,
			&i.Metadata,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const SearchMessages = `-- name: SearchMessages :many
SELECT id, conversation_id, role, content, tokens_used, metadata, created_at FROM messages
WHERE 
    conversation_id = $1
    AND content ILIKE '%' || $2 || '%'
ORDER BY created_at DESC
LIMIT $3
`

type SearchMessagesParams struct {
	ConversationID pgtype.UUID `json:"conversation_id"`
	Query          pgtype.Text `json:"query"`
	LimitCount     int32       `json:"limit_count"`
}

func (q *Queries) SearchMessages(ctx context.Context, arg SearchMessagesParams) ([]*Message, error) {
	rows, err := q.db.Query(ctx, SearchMessages, arg.ConversationID, arg.Query, arg.LimitCount)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Message{}
	for rows.Next() {
		var i Message
		if err := rows.Scan(
			&i.ID,
			&i.ConversationID,
			&i.Role,
			&i.Content,
			&i.TokensUsed,
			&i.Metadata,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
