// Package database provides database connection and management functionality.
// Uses pgx as PostgreSQL driver with pgvector extension support.
//
// WHY: pgx chosen over database/sql because:
// 1. Native PostgreSQL protocol implementation is 3x faster
// 2. Built-in connection pooling with better control
// 3. Native support for PostgreSQL-specific types (arrays, JSONB)
// 4. pgvector extension requires custom type handling
package database

import (
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"

	"github.com/koopa0/assistant-go/internal/platform/logger"
)

// Database constants
const (
	// MaxInt32 safe conversion limit
	MaxInt32 = 2147483647

	// DefaultBatchSize for batch operations
	DefaultBatchSize = 100

	// MaxMemoriesPerQuery to prevent overload
	MaxMemoriesPerQuery = 1000

	// MinMemoriesForConsolidation threshold
	MinMemoriesForConsolidation = 50
)

// DB wraps the database connection pool.
//
// WHY: Wrapper pattern because:
// 1. Abstracts pgx-specific details from business logic
// 2. Centralizes connection lifecycle management
// 3. Provides hook points for metrics and logging
// 4. Enables mock implementations for testing
type DB struct {
	// pool manages database connections.
	// WHY: pgxpool over single connection for concurrent request handling
	pool *pgxpool.Pool
	// config stores connection parameters.
	// WHY: Retained for runtime inspection and health checks
	config *Config
	// logger for error reporting - optional, uses no-op if nil
	logger logger.Logger
}

// Config defines database connection parameters
type Config struct {
	// DSN is the database connection string
	DSN string
	// MaxConns limits concurrent database connections
	MaxConns int32
	// MinConns maintains minimum idle connections
	MinConns int32
	// MaxConnLifetime prevents connection staleness
	MaxConnLifetime time.Duration
	// MaxConnIdleTime closes idle connections
	MaxConnIdleTime time.Duration
	// HealthCheckPeriod defines how often to verify connections
	HealthCheckPeriod time.Duration
}

// DBOption configures a DB instance
type DBOption func(*DB)

// WithLogger sets a custom logger for the database
func WithLogger(log logger.Logger) DBOption {
	return func(db *DB) {
		db.logger = log
	}
}

// NewDB creates a new database connection pool.
// Automatically configures pool parameters and validates connectivity.
//
// WHY: Constructor validates immediately because:
// 1. Fail fast on configuration errors
// 2. Prevents runtime connection failures
// 3. Database is required dependency - no graceful degradation
func NewDB(ctx context.Context, config *Config, opts ...DBOption) (*DB, error) {
	if config == nil {
		return nil, fmt.Errorf("config is required")
	}

	// Set defaults for production workloads.
	// WHY: These defaults based on typical web service patterns
	if config.MaxConns == 0 {
		config.MaxConns = 25
	}
	if config.MinConns == 0 {
		config.MinConns = 5
	}
	if config.MaxConnLifetime == 0 {
		config.MaxConnLifetime = 30 * time.Minute
	}
	if config.MaxConnIdleTime == 0 {
		config.MaxConnIdleTime = 10 * time.Minute
	}
	if config.HealthCheckPeriod == 0 {
		config.HealthCheckPeriod = 1 * time.Minute
	}

	// Parse connection string and create configuration
	pgxConfig, err := pgxpool.ParseConfig(config.DSN)
	if err != nil {
		return nil, fmt.Errorf("failed to parse database connection string: %w", err)
	}

	// Configure connection pool parameters
	pgxConfig.MaxConns = config.MaxConns
	pgxConfig.MinConns = config.MinConns
	pgxConfig.MaxConnLifetime = config.MaxConnLifetime
	pgxConfig.MaxConnIdleTime = config.MaxConnIdleTime
	pgxConfig.HealthCheckPeriod = config.HealthCheckPeriod

	// Set after-connect initialization function
	pgxConfig.AfterConnect = func(ctx context.Context, conn *pgx.Conn) error {
		// Register pgvector types.
		// WHY: pgvector requires custom type registration for vector operations
		return nil
	}

	// Create connection pool
	pool, err := pgxpool.NewWithConfig(ctx, pgxConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create database connection pool: %w", err)
	}

	// Validate connectivity
	if err := pool.Ping(ctx); err != nil {
		pool.Close() // pool.Close() doesn't return error in pgx v5
		// Provide more helpful error message for common issues
		if err.Error() == "dial error (dial tcp [::1]:5432: connect: connection refused)" ||
			err.Error() == "dial error (dial tcp 127.0.0.1:5432: connect: connection refused)" {
			return nil, fmt.Errorf("cannot connect to PostgreSQL database at %s. "+
				"Please ensure PostgreSQL is running and accessible: %w", config.DSN, err)
		}
		return nil, fmt.Errorf("failed to validate database connection: %w", err)
	}

	db := &DB{
		pool:   pool,
		config: config,
		logger: logger.NewNoOpLogger(), // Default to no-op logger
	}

	// Apply options
	for _, opt := range opts {
		opt(db)
	}

	// Initialize pgvector extension
	if err := db.initPGVector(ctx); err != nil {
		pool.Close() // pool.Close() doesn't return error in pgx v5
		return nil, fmt.Errorf("failed to initialize pgvector: %w", err)
	}

	return db, nil
}

// initPGVector initializes the pgvector extension.
//
// WHY: Separate initialization because:
// 1. Extension creation requires superuser privileges
// 2. Failure is non-fatal if extension already exists
// 3. Keeps vector-specific logic isolated
func (db *DB) initPGVector(ctx context.Context) error {
	// Check and create pgvector extension if not exists
	query := `CREATE EXTENSION IF NOT EXISTS vector`
	_, err := db.pool.Exec(ctx, query)
	if err != nil {
		return fmt.Errorf("failed to create pgvector extension: %w", err)
	}
	return nil
}

// Close gracefully shuts down the connection pool
func (db *DB) Close() {
	if db.pool != nil {
		db.pool.Close() // pool.Close() doesn't return error in pgx v5
	}
}

// Ping verifies database connectivity
func (db *DB) Ping(ctx context.Context) error {
	return db.pool.Ping(ctx)
}

// Begin starts a new database transaction
func (db *DB) Begin(ctx context.Context) (pgx.Tx, error) {
	return db.pool.Begin(ctx)
}

// BeginTx starts a transaction with custom options
func (db *DB) BeginTx(ctx context.Context, txOptions pgx.TxOptions) (pgx.Tx, error) {
	return db.pool.BeginTx(ctx, txOptions)
}

// Exec executes a query without returning results
func (db *DB) Exec(ctx context.Context, sql string, args ...any) (pgx.Rows, error) {
	rows, err := db.pool.Query(ctx, sql, args...)
	if err != nil {
		return nil, err
	}
	// Close rows immediately as Exec doesn't need to return results
	rows.Close() // rows.Close() doesn't return error in pgx v5
	return rows, nil
}

// Query executes a query and returns the result set
func (db *DB) Query(ctx context.Context, sql string, args ...any) (pgx.Rows, error) {
	return db.pool.Query(ctx, sql, args...)
}

// QueryRow executes a query and returns a single row
func (db *DB) QueryRow(ctx context.Context, sql string, args ...any) pgx.Row {
	return db.pool.QueryRow(ctx, sql, args...)
}

// Acquire obtains a database connection from the pool.
// Must be released after use to avoid connection leaks
func (db *DB) Acquire(ctx context.Context) (*pgxpool.Conn, error) {
	return db.pool.Acquire(ctx)
}

// Pool returns the underlying connection pool.
// Use only when direct pool access is required
func (db *DB) Pool() *pgxpool.Pool {
	return db.pool
}

// Stats returns connection pool statistics
func (db *DB) Stats() *pgxpool.Stat {
	return db.pool.Stat()
}

// WithTx executes a function within a transaction.
// Automatically handles commit and rollback.
//
// WHY: Transaction helper because:
// 1. Ensures transactions are always closed
// 2. Consistent error handling pattern
// 3. Panic recovery prevents connection leaks
// 4. Reduces boilerplate in business logic
func (db *DB) WithTx(ctx context.Context, fn func(pgx.Tx) error) error {
	tx, err := db.pool.Begin(ctx)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	// Ensure transaction is closed even on panic
	defer func() {
		if p := recover(); p != nil {
			_ = tx.Rollback(ctx) // Best effort rollback on panic
			panic(p)
		}
	}()

	// Execute the transactional function
	if err := fn(tx); err != nil {
		if rbErr := tx.Rollback(ctx); rbErr != nil {
			// Wrap the original error and include rollback error as additional context
			return fmt.Errorf("transaction failed: %w (rollback also failed: %s)", err, rbErr.Error())
		}
		return err
	}

	// Commit the transaction
	if err := tx.Commit(ctx); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// RunMigration executes a database migration.
// Runs the specified SQL script within a transaction
func (db *DB) RunMigration(ctx context.Context, migration string) error {
	return db.WithTx(ctx, func(tx pgx.Tx) error {
		_, err := tx.Exec(ctx, migration)
		return err
	})
}
