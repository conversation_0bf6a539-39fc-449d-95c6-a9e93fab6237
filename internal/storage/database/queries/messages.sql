-- name: CreateMessage :one
INSERT INTO messages (
    conversation_id,
    role,
    content,
    tokens_used,
    metadata
) VALUES (
    @conversation_id,
    @role::message_role,
    @content,
    COALESCE(@tokens_used, 0),
    COALESCE(@metadata::jsonb, '{}')
)
RETURNING *;

-- name: GetMessage :one
SELECT * FROM messages
WHERE id = @id
LIMIT 1;

-- name: ListMessages :many
SELECT * FROM messages
WHERE conversation_id = @conversation_id
ORDER BY created_at ASC
LIMIT COALESCE(@limit_count::int, 100)
OFFSET COALESCE(@offset_count::int, 0);

-- name: ListRecentMessages :many
SELECT * FROM messages
WHERE conversation_id = @conversation_id
ORDER BY created_at DESC
LIMIT @limit_count;

-- name: GetLastMessages :many
SELECT * FROM messages
WHERE conversation_id = @conversation_id
ORDER BY created_at DESC
LIMIT @limit_count;

-- name: CountMessagesByRole :many
SELECT
    role,
    COUNT(*)::int as count
FROM messages
WHERE conversation_id = @conversation_id
GROUP BY role;

-- name: GetConversationTokenUsage :one
SELECT
    COUNT(*)::int as message_count,
    SUM(tokens_used)::int as total_tokens,
    AVG(tokens_used)::float as avg_tokens_per_message
FROM messages
WHERE conversation_id = @conversation_id;

-- name: DeleteMessage :exec
DELETE FROM messages
WHERE id = @id;

-- name: DeleteConversationMessages :exec
DELETE FROM messages
WHERE conversation_id = @conversation_id;

-- name: SearchMessages :many
SELECT * FROM messages
WHERE 
    conversation_id = @conversation_id
    AND content ILIKE '%' || @query || '%'
ORDER BY created_at DESC
LIMIT @limit_count;