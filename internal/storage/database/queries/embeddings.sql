-- name: CreateEmbedding :one
INSERT INTO embeddings (
    source_table,
    source_id,
    content,
    embedding,
    model,
    metadata
) VALUES (
    @source_table,
    @source_id,
    @content,
    @embedding,
    COALESCE(@model, 'gemini-embedding-001'),
    COALESCE(@metadata::jsonb, '{}')
)
ON CONFLICT (source_table, source_id) DO UPDATE
SET
    content = EXCLUDED.content,
    embedding = EXCLUDED.embedding,
    model = EXCLUDED.model,
    metadata = EXCLUDED.metadata,
    created_at = CURRENT_TIMESTAMP
RETURNING *;

-- name: GetEmbedding :one
SELECT * FROM embeddings
WHERE id = @id
LIMIT 1;

-- name: GetEmbeddingBySource :one
SELECT * FROM embeddings
WHERE source_table = @source_table AND source_id = @source_id
LIMIT 1;

-- name: UpdateEmbedding :one
UPDATE embeddings
SET
    content = @content,
    embedding = @embedding,
    metadata = COALESCE(@metadata::jsonb, metadata)
WHERE id = @id
RETURNING *;

-- name: DeleteEmbedding :exec
DELETE FROM embeddings
WHERE id = @id;

-- name: DeleteEmbeddingBySource :exec
DELETE FROM embeddings
WHERE source_table = @source_table AND source_id = @source_id;

-- name: ListEmbeddingsBySource :many
SELECT * FROM embeddings
WHERE source_table = @source_table
ORDER BY created_at DESC
LIMIT COALESCE(@limit_count::int, 100)
OFFSET COALESCE(@offset_count::int, 0);

-- name: SearchSimilarEmbeddings :many
SELECT
    id,
    source_table,
    source_id,
    content,
    1 - (embedding <=> @query_embedding) as similarity,
    metadata
FROM embeddings
WHERE
    1 - (embedding <=> @query_embedding) > @similarity_threshold
    AND source_table = COALESCE(@source_table::text, source_table)
ORDER BY embedding <=> @query_embedding
LIMIT @limit_count;

-- name: CountEmbeddingsBySource :many
SELECT
    source_table,
    COUNT(*)::int as count
FROM embeddings
GROUP BY source_table;

-- name: GetEmbeddingStats :one
SELECT
    COUNT(*)::int as total_embeddings,
    COUNT(DISTINCT source_table)::int as source_types,
    MIN(created_at) as oldest_embedding,
    MAX(created_at) as newest_embedding
FROM embeddings;

-- name: DeleteOldEmbeddings :exec
DELETE FROM embeddings
WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '1 day' * @days_old
AND source_table = COALESCE(@source_table::text, source_table);
