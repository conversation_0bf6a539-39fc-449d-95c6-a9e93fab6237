-- name: <PERSON><PERSON><PERSON><PERSON>oryBackup :one
INSERT INTO memory_backups (
    backup_data,
    node_count,
    edge_count,
    version
) VALUES (
    @backup_data,
    @node_count,
    @edge_count,
    COALESCE(@version, '1.0')
)
RETURNING *;

-- name: GetMemoryBackup :one
SELECT * FROM memory_backups
WHERE id = @id
LIMIT 1;

-- name: GetLatestMemoryBackup :one
SELECT * FROM memory_backups
ORDER BY created_at DESC
LIMIT 1;

-- name: ListMemoryBackups :many
SELECT * FROM memory_backups
ORDER BY created_at DESC
LIMIT COALESCE(@limit_count::int, 20)
OFFSET COALESCE(@offset_count::int, 0);

-- name: CountMemoryBackups :one
SELECT COUNT(*)::int as total_backups
FROM memory_backups;

-- name: DeleteMemoryBackup :exec
DELETE FROM memory_backups
WHERE id = @id;

-- name: DeleteOldMemoryBackups :exec
DELETE FROM memory_backups
WHERE id NOT IN (
    SELECT id 
    FROM memory_backups 
    ORDER BY created_at DESC 
    LIMIT @keep_count
)
AND created_at < CURRENT_TIMESTAMP - INTERVAL '1 day' * @keep_days;

-- name: GetBackupStats :one
SELECT
    COUNT(*)::int as total_backups,
    SUM(node_count)::int as total_nodes_backed_up,
    SUM(edge_count)::int as total_edges_backed_up,
    MIN(created_at) as oldest_backup,
    MAX(created_at) as newest_backup
FROM memory_backups;

-- name: GetBackupsWithinTimeRange :many
SELECT * FROM memory_backups
WHERE 
    created_at >= @start_time
    AND created_at <= @end_time
ORDER BY created_at DESC;