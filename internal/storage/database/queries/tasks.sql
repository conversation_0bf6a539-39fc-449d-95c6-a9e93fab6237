-- name: CreateTask :one
INSERT INTO tasks (
    title,
    description,
    status,
    priority,
    due_date,
    labels,
    metadata
) VALUES (
    @title,
    @description,
    COALESCE(@status::task_status, 'todo'),
    COALESCE(@priority::task_priority, 'medium'),
    @due_date,
    COALESCE(@labels, '{}'),
    COALESCE(@metadata::jsonb, '{}')
)
RETURNING *;

-- name: GetTask :one
SELECT * FROM tasks
WHERE id = @id
LIMIT 1;

-- name: UpdateTask :one
UPDATE tasks
SET
    title = COALESCE(@title::text, title),
    description = COALESCE(@description::text, description),
    status = COALESCE(@status::task_status, status),
    priority = COALESCE(@priority::task_priority, priority),
    due_date = COALESCE(@due_date::timestamptz, due_date),
    completed_at = CASE 
        WHEN @status::task_status = 'done' AND completed_at IS NULL THEN CURRENT_TIMESTAMP
        WHEN @status::task_status != 'done' THEN NULL
        ELSE completed_at
    END,
    labels = COALESCE(@labels, labels),
    metadata = COALESCE(@metadata::jsonb, metadata)
WHERE id = @id
RETURNING *;

-- name: CompleteTask :one
UPDATE tasks
SET 
    status = 'done',
    completed_at = CURRENT_TIMESTAMP
WHERE id = @id
RETURNING *;

-- name: DeleteTask :exec
DELETE FROM tasks
WHERE id = @id;

-- name: ListTasks :many
SELECT * FROM tasks
WHERE 
    status = COALESCE(@status::task_status, status)
    AND priority = COALESCE(@priority::task_priority, priority)
    AND (@include_completed::bool OR status != 'done')
ORDER BY
    CASE WHEN status != 'done' THEN 0 ELSE 1 END,
    CASE priority 
        WHEN 'critical' THEN 0
        WHEN 'urgent' THEN 1
        WHEN 'high' THEN 2
        WHEN 'medium' THEN 3
        WHEN 'low' THEN 4
    END,
    due_date ASC NULLS LAST,
    created_at DESC
LIMIT COALESCE(@limit_count::int, 50)
OFFSET COALESCE(@offset_count::int, 0);

-- name: ListPendingTasks :many
SELECT * FROM tasks
WHERE status IN ('todo', 'in_progress')
ORDER BY
    CASE priority 
        WHEN 'critical' THEN 0
        WHEN 'urgent' THEN 1
        WHEN 'high' THEN 2
        WHEN 'medium' THEN 3
        WHEN 'low' THEN 4
    END,
    due_date ASC NULLS LAST,
    created_at ASC
LIMIT @limit_count;

-- name: ListOverdueTasks :many
SELECT * FROM tasks
WHERE 
    status != 'done'
    AND due_date < CURRENT_TIMESTAMP
ORDER BY due_date ASC;

-- name: ListTasksDueToday :many
SELECT * FROM tasks
WHERE 
    status != 'done'
    AND due_date >= CURRENT_DATE
    AND due_date < CURRENT_DATE + INTERVAL '1 day'
ORDER BY due_date ASC, priority DESC;

-- name: ListTasksByLabel :many
SELECT * FROM tasks
WHERE 
    @label = ANY(labels)
    AND (@include_completed::bool OR status != 'done')
ORDER BY created_at DESC
LIMIT @limit_count;

-- name: SearchTasks :many
SELECT * FROM tasks
WHERE 
    (title ILIKE '%' || @query || '%' OR description ILIKE '%' || @query || '%')
    AND (@include_completed::bool OR status != 'done')
ORDER BY 
    CASE WHEN status != 'done' THEN 0 ELSE 1 END,
    updated_at DESC
LIMIT @limit_count;

-- name: GetTaskStats :one
SELECT
    COUNT(*)::int as total_tasks,
    COUNT(CASE WHEN status = 'todo' THEN 1 END)::int as todo_count,
    COUNT(CASE WHEN status = 'in_progress' THEN 1 END)::int as in_progress_count,
    COUNT(CASE WHEN status = 'done' THEN 1 END)::int as done_count,
    COUNT(CASE WHEN status = 'cancelled' THEN 1 END)::int as cancelled_count,
    COUNT(CASE WHEN status != 'done' AND due_date < CURRENT_TIMESTAMP THEN 1 END)::int as overdue_count
FROM tasks;

-- name: GetTasksByPriority :many
SELECT
    priority,
    COUNT(*)::int as count
FROM tasks
WHERE status != 'done'
GROUP BY priority
ORDER BY 
    CASE priority 
        WHEN 'critical' THEN 0
        WHEN 'urgent' THEN 1
        WHEN 'high' THEN 2
        WHEN 'medium' THEN 3
        WHEN 'low' THEN 4
    END;

-- name: UpdateTaskLabels :one
UPDATE tasks
SET labels = @labels
WHERE id = @id
RETURNING *;

-- name: AddTaskLabel :one
UPDATE tasks
SET labels = array_append(labels, @label)
WHERE id = @id AND NOT (@label = ANY(labels))
RETURNING *;

-- name: RemoveTaskLabel :one
UPDATE tasks
SET labels = array_remove(labels, @label)
WHERE id = @id
RETURNING *;