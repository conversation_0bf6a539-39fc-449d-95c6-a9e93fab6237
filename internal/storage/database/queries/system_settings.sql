-- name: GetSystemSetting :one
SELECT * FROM system_settings
WHERE key = @key
LIMIT 1;

-- name: SetSystemSetting :one
INSERT INTO system_settings (
    key,
    value,
    description
) VALUES (
    @key,
    @value,
    @description
)
ON CONFLICT (key) DO UPDATE
SET 
    value = EXCLUDED.value,
    description = COALESCE(EXCLUDED.description, system_settings.description)
RETURNING *;

-- name: UpdateSystemSetting :one
UPDATE system_settings
SET value = @value
WHERE key = @key
RETURNING *;

-- name: DeleteSystemSetting :exec
DELETE FROM system_settings
WHERE key = @key;

-- name: ListSystemSettings :many
SELECT * FROM system_settings
ORDER BY key ASC;

-- name: ListSystemSettingsByPrefix :many
SELECT * FROM system_settings
WHERE key LIKE @prefix || '%'
ORDER BY key ASC;

-- name: GetMultipleSettings :many
SELECT * FROM system_settings
WHERE key = ANY(@keys::text[])
ORDER BY key ASC;

-- name: ExistsSystemSetting :one
SELECT EXISTS (
    SELECT 1 FROM system_settings
    WHERE key = @key
);

-- name: CountSystemSettings :one
SELECT COUNT(*)::int as total_settings
FROM system_settings;

-- name: GetSystemSettingKeys :many
SELECT key
FROM system_settings
ORDER BY key ASC;

-- name: BulkSetSystemSettings :exec
INSERT INTO system_settings (key, value, description)
SELECT 
    unnest(@keys::text[]),
    unnest(@values::jsonb[]),
    unnest(@descriptions::text[])
ON CONFLICT (key) DO UPDATE
SET 
    value = EXCLUDED.value,
    description = COALESCE(EXCLUDED.description, system_settings.description);

-- name: GetSettingHistory :many
SELECT 
    key,
    value,
    created_at,
    updated_at
FROM system_settings
WHERE key = @key
ORDER BY updated_at DESC;