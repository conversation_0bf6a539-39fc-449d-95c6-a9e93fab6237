-- name: SearchConversations :many
-- Search conversations using full-text search
SELECT 
    c.*,
    ts_rank(c.search_vector, plainto_tsquery('english', @search_query::text)) AS rank
FROM conversations c
WHERE 
    c.search_vector @@ plainto_tsquery('english', @search_query::text)
    AND c.status = @status
ORDER BY 
    rank DESC,
    c.last_message_at DESC NULLS LAST
LIMIT @limit_count
OFFSET @offset_count;

-- name: SearchConversationMessages :many
-- Search messages within conversations
SELECT 
    m.*,
    c.title as conversation_title,
    ts_rank(m.search_vector, plainto_tsquery('english', @search_query::text)) AS rank
FROM messages m
JOIN conversations c ON m.conversation_id = c.id
WHERE 
    m.search_vector @@ plainto_tsquery('english', @search_query::text)
    AND (@conversation_id::uuid IS NULL OR m.conversation_id = @conversation_id)
ORDER BY 
    rank DESC,
    m.created_at DESC
LIMIT @limit_count
OFFSET @offset_count;

-- name: SearchConversationsWithHighlight :many
-- Search with highlighted results
SELECT 
    c.*,
    ts_headline('english', c.title, plainto_tsquery('english', @search_query::text), 
        'HighlightAll=true, StartSel=<<, StopSel=>>') as highlighted_title,
    ts_headline('english', COALESCE(c.summary, ''), plainto_tsquery('english', @search_query::text), 
        'HighlightAll=true, StartSel=<<, StopSel=>>') as highlighted_summary,
    ts_rank(c.search_vector, plainto_tsquery('english', @search_query::text)) AS rank
FROM conversations c
WHERE 
    c.search_vector @@ plainto_tsquery('english', @search_query::text)
    AND c.status = @status
ORDER BY 
    rank DESC,
    c.last_message_at DESC NULLS LAST
LIMIT @limit_count;