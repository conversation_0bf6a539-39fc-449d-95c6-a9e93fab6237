-- name: CreateMemory :one
INSERT INTO memories (
    semantic_id, type, content, summary,
    entities, attributes, context,
    embedding, keywords, confidence
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10
)
RETURNING *;

-- name: GetMemory :one
SELECT * FROM memories
WHERE id = $1 AND status = 'active';

-- name: GetMemoryBySemantic :one
SELECT * FROM memories
WHERE semantic_id = $1 AND status = 'active';

-- name: SearchSimilarWithLimit :many
-- Optimized vector similarity search that leverages HNSW/IVFFlat indexes
-- This query is specifically designed for efficient nearest neighbor search
SELECT 
    id,
    semantic_id,
    type,
    content,
    summary,
    entities,
    attributes,
    context,
    embedding,
    keywords,
    confidence,
    version,
    access_count,
    created_at,
    updated_at,
    accessed_at,
    status,
    merged_from,
    (1 - (embedding <=> @embedding::vector))::float4 as similarity
FROM memories
WHERE 
    status = 'active'
    AND embedding IS NOT NULL
    AND (@status::text IS NULL OR status = @status::text)
    AND 1 - (embedding <=> @embedding::vector) > COALESCE(@min_similarity::float4, 0.0)
ORDER BY embedding <=> @embedding::vector
LIMIT @limit_count::int;

-- name: SearchMemories :many
-- Hybrid search combining vector similarity and text search (NO LIMIT - returns all matches)
WITH vector_scores AS (
    SELECT 
        id,
        CASE 
            WHEN @embedding::vector IS NOT NULL AND embedding IS NOT NULL 
            THEN (1 - (embedding <=> @embedding::vector))::REAL
            ELSE 0.0::REAL
        END as similarity
    FROM memories
    WHERE status = 'active'
        AND (@embedding::vector IS NULL OR embedding IS NOT NULL)
),
text_scores AS (
    SELECT 
        id,
        CASE
            WHEN @text::TEXT IS NOT NULL AND @text != '' THEN
                CASE
                    -- Higher score for exact matches
                    WHEN content ILIKE @text THEN 1.0
                    -- Medium score for contains
                    WHEN content ILIKE '%' || @text || '%' THEN 0.5
                    WHEN COALESCE(summary, '') ILIKE '%' || @text || '%' THEN 0.3
                    ELSE 0.0
                END
            ELSE 0.0
        END::REAL as text_relevance
    FROM memories
    WHERE status = 'active'
)
SELECT 
    m.id,
    m.semantic_id,
    m.type,
    m.content,
    m.summary,
    m.entities,
    m.attributes,
    m.context,
    m.embedding,
    m.keywords,
    m.confidence,
    m.version,
    m.access_count,
    m.created_at,
    m.updated_at,
    m.accessed_at,
    m.status,
    m.merged_from,
    COALESCE(vs.similarity, 0.0) as similarity,
    -- Multi-dimensional scoring inspired by mem0/papr.ai
    (
        -- 1. Vector similarity (base score: 40%)
        COALESCE(vs.similarity, 0.0) * 
        CASE 
            WHEN @embedding::vector IS NOT NULL THEN 0.40
            ELSE 0.0
        END +
        
        -- 2. Text relevance (20% or 60% if no embedding)
        COALESCE(ts.text_relevance, 0.0) * 
        CASE 
            WHEN @embedding::vector IS NOT NULL AND @text::TEXT IS NOT NULL THEN 0.20
            WHEN @embedding::vector IS NULL AND @text::TEXT IS NOT NULL THEN 0.60
            ELSE 0.0
        END +
        
        -- 3. Recency factor (15%)
        CASE 
            WHEN m.updated_at > NOW() - INTERVAL '1 hour' THEN 0.15
            WHEN m.updated_at > NOW() - INTERVAL '1 day' THEN 0.12
            WHEN m.updated_at > NOW() - INTERVAL '7 days' THEN 0.08
            WHEN m.updated_at > NOW() - INTERVAL '30 days' THEN 0.04
            ELSE 0.0
        END +
        
        -- 4. Access frequency (10%)
        LEAST(m.access_count * 0.01, 0.10) +
        
        -- 5. Reserved for future enhancements (10%)
        0.0 +
        
        -- 6. Confidence factor (5%)
        m.confidence * 0.05
    )::REAL as score
FROM memories m
LEFT JOIN vector_scores vs ON m.id = vs.id
LEFT JOIN text_scores ts ON m.id = ts.id
WHERE m.status = 'active'
    -- Type filter
    AND (@types::memory_type[] IS NULL OR CARDINALITY(@types::memory_type[]) = 0 OR m.type = ANY(@types::memory_type[]))
    -- Entity filter
    AND (@entities::TEXT[] IS NULL OR CARDINALITY(@entities::TEXT[]) = 0 OR 
        EXISTS (
            SELECT 1 FROM jsonb_array_elements(m.entities) e
            WHERE e->>'name' = ANY(@entities::TEXT[])
        ))
    -- Time range filter
    AND (@time_start::TIMESTAMPTZ IS NULL OR 
        COALESCE((m.context->>'occurred_at')::timestamptz, (m.context->>'start_time')::timestamptz) >= @time_start::TIMESTAMPTZ)
    AND (@time_end::TIMESTAMPTZ IS NULL OR 
        COALESCE((m.context->>'occurred_at')::timestamptz, (m.context->>'end_time')::timestamptz) <= @time_end::TIMESTAMPTZ)
    -- Must have either vector match or text match
    AND (
        (vs.similarity > COALESCE(@min_similarity::REAL, 0.0)) OR
        (ts.text_relevance > 0.0) OR
        (@embedding::vector IS NULL AND @text::TEXT IS NULL)
    )
ORDER BY score DESC;


-- name: UpdateMemory :exec
UPDATE memories
SET 
    content = $2,
    summary = $3,
    entities = $4,
    attributes = $5,
    context = $6,
    confidence = COALESCE($7, confidence),
    version = version + 1,
    updated_at = NOW()
WHERE id = $1 AND status = 'active';

-- name: SetEmbedding :exec
UPDATE memories
SET embedding = $1
WHERE id = $2;

-- name: IncrementAccess :exec
UPDATE memories
SET access_count = access_count + 1, accessed_at = NOW()
WHERE id = $1;

-- name: DeactivateMemory :exec
UPDATE memories
SET status = 'archived'
WHERE id = $1;

-- name: CreateRelation :one
INSERT INTO relations (
    from_id, to_id, type, strength, metadata
) VALUES (
    $1, $2, $3, $4, $5
)
ON CONFLICT (from_id, to_id, type) 
DO UPDATE SET 
    strength = EXCLUDED.strength,
    metadata = EXCLUDED.metadata
RETURNING *;

-- name: CreateRelationsBatch :copyfrom
INSERT INTO relations (
    from_id, to_id, type, strength, metadata
) VALUES (
    $1, $2, $3, $4, $5
);

-- name: GetRelationsByMemory :many
SELECT * FROM relations
WHERE from_id = $1 OR to_id = $1
ORDER BY strength DESC;

-- name: UpdateRelation :exec
UPDATE relations
SET 
    strength = COALESCE($4, strength),
    metadata = COALESCE($5, metadata)
WHERE from_id = $1 AND to_id = $2 AND type = $3;

-- name: GetRelated :many
WITH RECURSIVE related AS (
    -- Direct relations
    SELECT 
        r.to_id as memory_id,
        r.type,
        r.strength,
        1 as depth
    FROM relations r
    WHERE r.from_id = $1::uuid
        AND ($2::relation_type IS NULL OR r.type = $2::relation_type)
    
    UNION
    
    -- Recursive relations up to specified depth
    SELECT 
        r.to_id as memory_id,
        r.type,
        r.strength * rel.strength as strength,
        rel.depth + 1 as depth
    FROM relations r
    INNER JOIN related rel ON r.from_id = rel.memory_id
    WHERE rel.depth < COALESCE($3::int, 1)
)
SELECT DISTINCT ON (m.id)
    m.*,
    rel.type as relation_type,
    rel.strength as relation_strength,
    rel.depth as relation_depth
FROM related rel
INNER JOIN memories m ON m.id = rel.memory_id
WHERE m.status = 'active'
ORDER BY m.id, rel.strength DESC;

-- name: CreateHistory :exec
INSERT INTO history (
    memory_id, action, version,
    content, entities, attributes, context,
    reason, created_by
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9
);

-- name: GetHistory :many
SELECT * FROM history
WHERE memory_id = $1
ORDER BY created_at DESC
LIMIT $2;

-- name: GetAllHistory :many
SELECT * FROM history
ORDER BY created_at DESC
LIMIT $1;

-- name: GetHistoryByAction :many
SELECT * FROM history
WHERE action = $1::action_type
ORDER BY created_at DESC
LIMIT $2;

-- name: ListRecent :many
SELECT * FROM memories
WHERE status = 'active'
ORDER BY updated_at DESC
LIMIT $1;

-- name: ListByType :many
SELECT * FROM memories
WHERE type = $1::memory_type AND status = 'active'
ORDER BY updated_at DESC
LIMIT $2;

-- name: ListByKeyword :many
SELECT * FROM memories
WHERE status = 'active' AND $1 = ANY(keywords)
ORDER BY updated_at DESC
LIMIT $2;

-- name: SearchByAttributes :many
SELECT * FROM memories
WHERE status = 'active' AND attributes @> $1::jsonb
ORDER BY updated_at DESC
LIMIT $2;

-- name: ListInTimeRange :many
SELECT * FROM memories
WHERE status = 'active'
    AND (
        (context->>'occurred_at')::timestamptz BETWEEN $1 AND $2
        OR (context->>'start_time')::timestamptz BETWEEN $1 AND $2
    )
ORDER BY COALESCE(
    (context->>'occurred_at')::timestamptz,
    (context->>'start_time')::timestamptz
);

-- name: ListRecurring :many
SELECT * FROM memories
WHERE status = 'active' AND context->>'recurrence' IS NOT NULL
ORDER BY updated_at DESC;

-- name: CountByType :many
SELECT type, COUNT(*)::int as count
FROM memories
WHERE status = 'active'
GROUP BY type
ORDER BY count DESC;

-- name: GetStats :one
SELECT 
    COUNT(*)::int as total,
    COUNT(DISTINCT type)::int as types,
    AVG(confidence)::float4 as avg_confidence,
    MAX(access_count)::int as max_access,
    MIN(created_at) as oldest,
    MAX(created_at) as newest
FROM memories
WHERE status = 'active';

-- name: DeactivateMany :exec
UPDATE memories
SET status = 'archived'
WHERE id = ANY($1::uuid[]);

-- name: CreateMergedMemory :one
INSERT INTO memories (
    semantic_id, type, content, summary,
    entities, attributes, context,
    embedding, keywords, confidence,
    merged_from
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11
)
RETURNING *;

-- Queue operations

-- name: CreateJob :one
INSERT INTO queue (conversation_id, messages)
VALUES ($1, $2)
RETURNING *;

-- name: GetPendingJobs :many
SELECT * FROM queue
WHERE status = 'pending'
ORDER BY created_at
LIMIT $1;

-- name: UpdateJob :exec
UPDATE queue
SET 
    status = $2,
    error = $3,
    processed_at = CASE 
        WHEN $2 IN ('completed', 'failed') THEN NOW() 
        ELSE processed_at 
    END
WHERE id = $1;

-- name: CleanupJobs :exec
DELETE FROM queue
WHERE status IN ('completed', 'failed')
    AND processed_at < NOW() - INTERVAL '7 days';