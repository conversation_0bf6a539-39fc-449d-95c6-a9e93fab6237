-- name: CreateConversation :one
INSERT INTO conversations (
    title,
    summary,
    status,
    metadata
) VALUES (
    COALESCE(@title::text, '新對話'),
    @summary,
    COALESCE(@status::conversation_status, 'active'),
    COALESCE(@metadata::jsonb, '{}')
)
RETURNING id, title, summary, status, metadata, created_at, updated_at, last_message_at, message_count, total_tokens;

-- name: GetConversation :one
SELECT id, title, summary, status, metadata, created_at, updated_at, last_message_at, message_count, total_tokens
FROM conversations
WHERE id = @id
LIMIT 1;

-- name: UpdateConversation :one
UPDATE conversations
SET
    title = COALESCE(@title::text, title),
    summary = COALESCE(@summary::text, summary),
    status = COALESCE(@status::conversation_status, status),
    metadata = COALESCE(@metadata::jsonb, metadata)
WHERE id = @id
RETURNING id, title, summary, status, metadata, created_at, updated_at, last_message_at, message_count, total_tokens;

-- name: ListConversations :many
SELECT id, title, summary, status, metadata, created_at, updated_at, last_message_at, message_count, total_tokens
FROM conversations
WHERE status = COALESCE(@status::conversation_status, status)
ORDER BY
    CASE WHEN @order_by = 'last_message' THEN last_message_at END DESC NULLS LAST,
    CASE WHEN @order_by = 'created' THEN created_at END DESC,
    updated_at DESC
LIMIT COALESCE(@limit_count::int, 20)
OFFSET COALESCE(@offset_count::int, 0);

-- name: ListActiveConversations :many
SELECT id, title, summary, status, metadata, created_at, updated_at, last_message_at, message_count, total_tokens
FROM conversations
WHERE status = 'active'
ORDER BY COALESCE(last_message_at, created_at) DESC
LIMIT @limit_count;


-- name: GetConversationStats :one
SELECT
    COUNT(DISTINCT c.id) as total_conversations,
    COUNT(DISTINCT CASE WHEN c.status = 'active' THEN c.id END) as active_conversations,
    COUNT(m.id) as total_messages,
    SUM(m.tokens_used) as total_tokens
FROM conversations c
LEFT JOIN messages m ON c.id = m.conversation_id;

-- name: DeleteConversation :exec
DELETE FROM conversations
WHERE id = @id;

-- name: ArchiveConversation :one
UPDATE conversations
SET status = 'archived'
WHERE id = @id
RETURNING id, title, summary, status, metadata, created_at, updated_at, last_message_at, message_count, total_tokens;

-- name: GetRecentConversationsWithMessageCount :many
SELECT 
    c.id, c.title, c.summary, c.status, c.metadata, c.created_at, c.updated_at, c.last_message_at, c.message_count, c.total_tokens,
    COUNT(m.id)::int as message_count,
    MAX(m.created_at) as last_message_time
FROM conversations c
LEFT JOIN messages m ON c.id = m.conversation_id
WHERE c.status = 'active'
GROUP BY c.id
ORDER BY COALESCE(MAX(m.created_at), c.created_at) DESC
LIMIT @limit_count;