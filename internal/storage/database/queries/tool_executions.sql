-- name: CreateToolExecution :one
INSERT INTO tool_executions (
    conversation_id,
    tool_name,
    input,
    status
) VALUES (
    @conversation_id,
    @tool_name,
    COALESCE(@input::jsonb, '{}'),
    COALESCE(@status::tool_status, 'pending')
)
RETURNING *;

-- name: GetToolExecution :one
SELECT * FROM tool_executions
WHERE id = @id
LIMIT 1;

-- name: UpdateToolExecution :one
UPDATE tool_executions
SET
    output = @output,
    status = @status::tool_status,
    error_message = @error_message,
    duration_ms = @duration_ms,
    completed_at = CASE 
        WHEN @status::tool_status IN ('success', 'failed', 'timeout') THEN CURRENT_TIMESTAMP
        ELSE completed_at
    END
WHERE id = @id
RETURNING *;

-- name: StartToolExecution :one
UPDATE tool_executions
SET status = 'running'
WHERE id = @id AND status = 'pending'
RETURNING *;

-- name: CompleteToolExecution :one
UPDATE tool_executions
SET
    output = @output,
    status = 'success',
    duration_ms = EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - created_at)) * 1000,
    completed_at = CURRENT_TIMESTAMP
WHERE id = @id
RETURNING *;

-- name: FailToolExecution :one
UPDATE tool_executions
SET
    status = 'failed',
    error_message = @error_message,
    duration_ms = EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - created_at)) * 1000,
    completed_at = CURRENT_TIMESTAMP
WHERE id = @id
RETURNING *;

-- name: ListToolExecutions :many
SELECT * FROM tool_executions
WHERE 
    tool_name = COALESCE(@tool_name::text, tool_name)
    AND status = COALESCE(@status::tool_status, status)
ORDER BY created_at DESC
LIMIT COALESCE(@limit_count::int, 100)
OFFSET COALESCE(@offset_count::int, 0);

-- name: ListToolExecutionsByConversation :many
SELECT * FROM tool_executions
WHERE conversation_id = @conversation_id
ORDER BY created_at DESC
LIMIT @limit_count;

-- name: ListPendingToolExecutions :many
SELECT * FROM tool_executions
WHERE status IN ('pending', 'running')
ORDER BY created_at ASC
LIMIT @limit_count;

-- name: GetToolExecutionStats :one
SELECT
    COUNT(*)::int as total_executions,
    COUNT(CASE WHEN status = 'success' THEN 1 END)::int as success_count,
    COUNT(CASE WHEN status = 'failed' THEN 1 END)::int as failed_count,
    COUNT(CASE WHEN status = 'timeout' THEN 1 END)::int as timeout_count,
    AVG(CASE WHEN duration_ms IS NOT NULL THEN duration_ms END)::float as avg_duration_ms,
    MAX(duration_ms)::int as max_duration_ms
FROM tool_executions
WHERE 
    created_at >= COALESCE(@since::timestamptz, created_at)
    AND tool_name = COALESCE(@tool_name::text, tool_name);

-- name: GetToolUsageByName :many
SELECT
    tool_name,
    COUNT(*)::int as execution_count,
    COUNT(CASE WHEN status = 'success' THEN 1 END)::int as success_count,
    AVG(CASE WHEN duration_ms IS NOT NULL THEN duration_ms END)::float as avg_duration_ms
FROM tool_executions
WHERE created_at >= COALESCE(@since::timestamptz, created_at)
GROUP BY tool_name
ORDER BY execution_count DESC;

-- name: DeleteOldToolExecutions :exec
DELETE FROM tool_executions
WHERE 
    created_at < CURRENT_TIMESTAMP - INTERVAL '1 day' * @days_old
    AND status IN ('success', 'failed', 'timeout');

-- name: CancelPendingToolExecutions :exec
UPDATE tool_executions
SET 
    status = 'cancelled',
    completed_at = CURRENT_TIMESTAMP
WHERE 
    conversation_id = @conversation_id
    AND status IN ('pending', 'running');