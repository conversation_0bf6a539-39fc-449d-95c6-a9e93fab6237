package database

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewDB(t *testing.T) {
	t.Run("successful connection", func(t *testing.T) {
		// Skip if no test database
		testDBURL := os.Getenv("TEST_DATABASE_URL")
		if testDBURL == "" {
			t.Skip("TEST_DATABASE_URL not set")
		}

		ctx := context.Background()
		config := &Config{
			DSN:             testDBURL,
			MaxConns:        5,
			MinConns:        2,
			MaxConnLifetime: 30 * time.Minute,
			MaxConnIdleTime: 10 * time.Minute,
		}

		db, err := NewDB(ctx, config)
		require.NoError(t, err)
		require.NotNil(t, db)

		// Verify connection is working
		err = db.Ping(ctx)
		assert.NoError(t, err)

		// Clean up
		db.Close()
	})

	t.Run("invalid connection URL", func(t *testing.T) {
		ctx := context.Background()
		config := &Config{
			DSN: "invalid://url",
		}

		db, err := NewDB(ctx, config)
		assert.Error(t, err)
		assert.Nil(t, db)
		assert.Contains(t, err.Error(), "invalid")
	})

	t.Run("connection timeout", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 1*time.Millisecond)
		defer cancel()

		config := &Config{
			DSN: "postgres://localhost:99999/test", // Non-existent port
		}

		db, err := NewDB(ctx, config)
		assert.Error(t, err)
		assert.Nil(t, db)
	})

	t.Run("empty URL", func(t *testing.T) {
		ctx := context.Background()
		config := &Config{
			DSN: "",
		}

		db, err := NewDB(ctx, config)
		assert.Error(t, err)
		assert.Nil(t, db)
		// Empty DSN will try to connect with default settings which will fail
		assert.Contains(t, err.Error(), "failed to validate database connection")
	})

	t.Run("validate pool configuration", func(t *testing.T) {
		testCases := []struct {
			name      string
			config    *Config
			expectErr bool
		}{
			{
				name: "min > max connections",
				config: &Config{
					DSN:      "postgres://test",
					MinConns: 10,
					MaxConns: 5,
				},
				expectErr: true,
			},
			{
				name: "negative connections",
				config: &Config{
					DSN:      "postgres://test",
					MaxConns: -1,
				},
				expectErr: true,
			},
			{
				name: "zero max connections",
				config: &Config{
					DSN:      "postgres://test",
					MaxConns: 0,
				},
				expectErr: true,
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				ctx := context.Background()
				db, err := NewDB(ctx, tc.config)
				if tc.expectErr {
					assert.Error(t, err)
					assert.Nil(t, db)
				} else {
					assert.NoError(t, err)
					if db != nil {
						db.Close()
					}
				}
			})
		}
	})
}

func TestDatabasePool(t *testing.T) {
	testDBURL := os.Getenv("TEST_DATABASE_URL")
	if testDBURL == "" {
		t.Skip("TEST_DATABASE_URL not set")
	}

	t.Run("concurrent connections", func(t *testing.T) {
		ctx := context.Background()
		config := &Config{
			DSN:      testDBURL,
			MaxConns: 5,
			MinConns: 2,
		}

		db, err := NewDB(ctx, config)
		require.NoError(t, err)
		defer db.Close()

		// Test concurrent access
		done := make(chan bool, 10)
		for i := 0; i < 10; i++ {
			go func() {
				err := db.Ping(ctx)
				assert.NoError(t, err)
				done <- true
			}()
		}

		// Wait for all goroutines
		for i := 0; i < 10; i++ {
			<-done
		}
	})

	t.Run("connection lifecycle", func(t *testing.T) {
		ctx := context.Background()
		config := &Config{
			DSN:             testDBURL,
			MaxConns:        3,
			MaxConnIdleTime: 1 * time.Second,
		}

		db, err := NewDB(ctx, config)
		require.NoError(t, err)

		// Use connection
		err = db.Ping(ctx)
		require.NoError(t, err)

		// Wait for idle timeout
		time.Sleep(2 * time.Second)

		// Connection should still work
		err = db.Ping(ctx)
		assert.NoError(t, err)

		db.Close()
	})
}

func TestDatabaseHealthCheck(t *testing.T) {
	testDBURL := os.Getenv("TEST_DATABASE_URL")
	if testDBURL == "" {
		t.Skip("TEST_DATABASE_URL not set")
	}

	t.Run("health check passes", func(t *testing.T) {
		ctx := context.Background()
		config := &Config{
			DSN: testDBURL,
		}

		db, err := NewDB(ctx, config)
		require.NoError(t, err)
		defer db.Close()

		// For now, just test ping
		err = db.Ping(ctx)
		assert.NoError(t, err)
	})

	t.Run("health check after close", func(t *testing.T) {
		ctx := context.Background()
		config := &Config{
			DSN: testDBURL,
		}

		db, err := NewDB(ctx, config)
		require.NoError(t, err)

		// Close connection
		db.Close()

		// Ping should fail after close
		err = db.Ping(ctx)
		assert.Error(t, err)
	})
}

// Benchmark database creation
func BenchmarkNewDB(b *testing.B) {
	testDBURL := os.Getenv("TEST_DATABASE_URL")
	if testDBURL == "" {
		b.Skip("TEST_DATABASE_URL not set")
	}

	ctx := context.Background()
	config := &Config{
		DSN: testDBURL,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		db, err := NewDB(ctx, config)
		if err != nil {
			b.Fatal(err)
		}
		db.Close()
	}
}

// Benchmark concurrent queries
func BenchmarkConcurrentQueries(b *testing.B) {
	testDBURL := os.Getenv("TEST_DATABASE_URL")
	if testDBURL == "" {
		b.Skip("TEST_DATABASE_URL not set")
	}

	ctx := context.Background()
	config := &Config{
		DSN:      testDBURL,
		MaxConns: 10,
	}

	db, err := NewDB(ctx, config)
	if err != nil {
		b.Fatal(err)
	}
	defer db.Close()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			err := db.Ping(ctx)
			if err != nil {
				b.Fatal(err)
			}
		}
	})
}
