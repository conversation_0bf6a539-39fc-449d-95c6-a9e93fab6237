-- 001_initial_schema_clean.down.sql
-- Drop all tables, functions, types, and extensions in reverse order

-- ===========================================
-- Drop permissions
-- ===========================================
REVOKE ALL ON ALL TABLES IN SCHEMA public FROM assistant_app;
REVOKE ALL ON ALL SEQUENCES IN SCHEMA public FROM assistant_app;
REVOKE ALL ON ALL FUNCTIONS IN SCHEMA public FROM assistant_app;
REVOKE USAGE ON SCHEMA public FROM assistant_app;

-- ===========================================
-- Drop functions
-- ===========================================
DROP FUNCTION IF EXISTS find_conflicting_memories(TEXT, UUID);
DROP FUNCTION IF EXISTS get_related(UUID, relation_type[], INTEGER);
DROP FUNCTION IF EXISTS update_memory(TEXT, TEXT, JSONB, JSONB, JSONB, TEXT);
DROP FUNCTION IF EXISTS search_memories_with_vector(vector, memory_type[], TEXT[], TIMESTAMPTZ, TIMESTAMPTZ, TEXT, REAL, INTEGER);
DROP FUNCTION IF EXISTS gen_semantic_id(memory_type, JSONB, JSONB);
DROP FUNCTION IF EXISTS cleanup_old_backups(INTEGER, INTEGER);
DROP FUNCTION IF EXISTS search_similar_embeddings(vector, INTEGER, TEXT[], FLOAT);
DROP FUNCTION IF EXISTS update_conversation_stats() CASCADE;
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;

-- ===========================================
-- Drop tables (in dependency order)
-- ===========================================

-- Memory system tables
DROP TABLE IF EXISTS queue CASCADE;
DROP TABLE IF EXISTS history CASCADE;
DROP TABLE IF EXISTS relations CASCADE;
DROP TABLE IF EXISTS memories CASCADE;

-- Core tables
DROP TABLE IF EXISTS memory_backups CASCADE;
DROP TABLE IF EXISTS system_settings CASCADE;
DROP TABLE IF EXISTS tool_executions CASCADE;
DROP TABLE IF EXISTS tasks CASCADE;
DROP TABLE IF EXISTS embeddings CASCADE;
DROP TABLE IF EXISTS messages CASCADE;
DROP TABLE IF EXISTS conversations CASCADE;

-- ===========================================
-- Drop types
-- ===========================================
DROP TYPE IF EXISTS search_memory_result CASCADE;
DROP TYPE IF EXISTS action_type CASCADE;
DROP TYPE IF EXISTS recurrence_pattern CASCADE;
DROP TYPE IF EXISTS relation_type CASCADE;
DROP TYPE IF EXISTS entity_role CASCADE;
DROP TYPE IF EXISTS entity_type CASCADE;
DROP TYPE IF EXISTS memory_status CASCADE;
DROP TYPE IF EXISTS memory_type CASCADE;
DROP TYPE IF EXISTS tool_status CASCADE;
DROP TYPE IF EXISTS task_priority CASCADE;
DROP TYPE IF EXISTS task_status CASCADE;
DROP TYPE IF EXISTS message_role CASCADE;
DROP TYPE IF EXISTS conversation_status CASCADE;

-- ===========================================
-- Drop extensions
-- ===========================================
-- Note: We don't drop extensions as they might be used by other schemas
-- DROP EXTENSION IF EXISTS btree_gist;
-- DROP EXTENSION IF EXISTS pg_trgm;
-- DROP EXTENSION IF EXISTS vector;
-- DROP EXTENSION IF EXISTS "uuid-ossp";