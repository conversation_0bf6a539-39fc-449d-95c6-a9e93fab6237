-- 001_initial_schema_clean.up.sql
-- Assistant Go consolidated database schema
-- This consolidates all migrations from 001 to 017 into a single schema
-- Removes unused knowledge_nodes and knowledge_edges tables

-- ===========================================
-- Enable necessary extensions
-- ===========================================
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";      -- UUID generation
CREATE EXTENSION IF NOT EXISTS "vector";         -- Vector search (pgvector)
CREATE EXTENSION IF NOT EXISTS "pg_trgm";        -- Text search
CREATE EXTENSION IF NOT EXISTS "btree_gist";     -- Exclusion constraint support

-- ===========================================
-- ENUMs - Using PostgreSQL native type system
-- ===========================================

-- Conversation status
CREATE TYPE conversation_status AS ENUM (
    'active',      -- In progress
    'archived',    -- Archived
    'deleted'      -- Deleted (soft delete)
);

-- Message role
CREATE TYPE message_role AS ENUM (
    'user',        -- User
    'assistant',   -- AI assistant
    'system',      -- System message
    'tool',        -- Tool output
    'error'        -- Error message
);

-- Task status
CREATE TYPE task_status AS ENUM (
    'todo',        -- To do
    'in_progress', -- In progress
    'done',        -- Completed
    'cancelled',   -- Cancelled
    'deferred'     -- Deferred
);

-- Task priority
CREATE TYPE task_priority AS ENUM (
    'low',         -- Low
    'medium',      -- Medium
    'high',        -- High
    'urgent',      -- Urgent
    'critical'     -- Critical
);

-- Tool execution status
CREATE TYPE tool_status AS ENUM (
    'pending',     -- Pending execution
    'running',     -- Running
    'success',     -- Success
    'failed',      -- Failed
    'timeout',     -- Timeout
    'cancelled'    -- Cancelled
);

-- Memory types
CREATE TYPE memory_type AS ENUM (
    'fact',
    'preference',
    'schedule',
    'relationship',
    'goal',
    'skill'
);

-- Memory status (replaces is_active boolean)
CREATE TYPE memory_status AS ENUM (
    'active',      -- Active memory
    'archived',    -- Archived memory
    'deleted'      -- Soft deleted
);

-- Entity types and roles
CREATE TYPE entity_type AS ENUM ('person', 'place', 'activity', 'time', 'thing', 'concept');
CREATE TYPE entity_role AS ENUM ('subject', 'object', 'location', 'time', 'with');

-- Relations and actions
CREATE TYPE relation_type AS ENUM ('updates', 'contradicts', 'relates_to', 'follows', 'precedes', 'part_of');
CREATE TYPE recurrence_pattern AS ENUM ('daily', 'weekly', 'monthly', 'yearly');
CREATE TYPE action_type AS ENUM ('ADD', 'UPDATE', 'DELETE', 'SKIP', 'MERGE');

-- Search result type for sqlc compatibility
CREATE TYPE search_memory_result AS (
    id UUID,
    semantic_id TEXT,
    type memory_type,
    content TEXT,
    summary TEXT,
    entities JSONB,
    attributes JSONB,
    context JSONB,
    embedding vector,
    keywords TEXT[],
    confidence REAL,
    version INTEGER,
    access_count INTEGER,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    accessed_at TIMESTAMPTZ,
    status memory_status,
    archived_at TIMESTAMPTZ,
    merged_from UUID[],
    similarity REAL,
    score REAL
);

-- ===========================================
-- Core tables
-- ===========================================

-- Conversations table
CREATE TABLE conversations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL DEFAULT 'New Conversation',
    summary TEXT,
    status conversation_status NOT NULL DEFAULT 'active',
    metadata JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_message_at TIMESTAMPTZ,
    message_count INTEGER NOT NULL DEFAULT 0,
    total_tokens INTEGER NOT NULL DEFAULT 0,

    -- Constraints
    CONSTRAINT conv_message_count_positive CHECK (message_count >= 0),
    CONSTRAINT conv_total_tokens_positive CHECK (total_tokens >= 0),
    CONSTRAINT conv_title_not_empty CHECK (trim(title) != '')
);

-- Messages table
CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    role message_role NOT NULL,
    content TEXT NOT NULL,
    tokens_used INTEGER NOT NULL DEFAULT 0,
    metadata JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    CONSTRAINT msg_tokens_positive CHECK (tokens_used >= 0),
    CONSTRAINT msg_content_not_empty CHECK (trim(content) != '')
);

-- Unified embeddings table
CREATE TABLE embeddings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    source_table TEXT NOT NULL,
    source_id UUID NOT NULL,
    content TEXT NOT NULL,
    embedding vector(768) NOT NULL,  -- Standardized to 768 dimensions
    model TEXT NOT NULL DEFAULT 'gemini-embedding-001',
    metadata JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    CONSTRAINT emb_content_not_empty CHECK (trim(content) != ''),
    CONSTRAINT emb_source_table_valid CHECK (source_table IN ('messages', 'tasks', 'memories')),
    CONSTRAINT emb_unique_source UNIQUE (source_table, source_id)
);

-- Tasks table
CREATE TABLE tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    description TEXT,
    status task_status NOT NULL DEFAULT 'todo',
    priority task_priority NOT NULL DEFAULT 'medium',
    due_date TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    labels TEXT[] NOT NULL DEFAULT '{}',
    metadata JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    CONSTRAINT task_title_not_empty CHECK (trim(title) != ''),
    CONSTRAINT task_completion_consistency CHECK (
        (status = 'done' AND completed_at IS NOT NULL) OR
        (status != 'done' AND completed_at IS NULL)
    ),
    CONSTRAINT task_due_date_future CHECK (due_date IS NULL OR due_date > created_at)
);

-- Tool execution logs table
CREATE TABLE tool_executions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID REFERENCES conversations(id) ON DELETE SET NULL,
    tool_name TEXT NOT NULL,
    input JSONB NOT NULL DEFAULT '{}',
    output JSONB,
    status tool_status NOT NULL DEFAULT 'pending',
    error_message TEXT,
    duration_ms INTEGER,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMPTZ,

    -- Constraints
    CONSTRAINT tool_name_not_empty CHECK (trim(tool_name) != ''),
    CONSTRAINT tool_duration_positive CHECK (duration_ms IS NULL OR duration_ms >= 0),
    CONSTRAINT tool_completion_consistency CHECK (
        (status IN ('success', 'failed', 'timeout') AND completed_at IS NOT NULL) OR
        (status IN ('pending', 'running', 'cancelled') AND completed_at IS NULL)
    )
);

-- System settings table
CREATE TABLE system_settings (
    key TEXT PRIMARY KEY,
    value JSONB NOT NULL,
    description TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    CONSTRAINT setting_key_not_empty CHECK (trim(key) != ''),
    CONSTRAINT setting_key_format CHECK (key ~ '^[a-z][a-z0-9_]*$')
);

-- Memory backups table
CREATE TABLE memory_backups (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    backup_data JSONB NOT NULL,
    node_count INTEGER NOT NULL DEFAULT 0,
    edge_count INTEGER NOT NULL DEFAULT 0,
    version TEXT NOT NULL DEFAULT '1.0',
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    CONSTRAINT backup_counts_positive CHECK (node_count >= 0 AND edge_count >= 0)
);

-- ===========================================
-- Memory System Tables
-- ===========================================

-- Memories table
CREATE TABLE memories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    semantic_id TEXT NOT NULL,
    type memory_type NOT NULL,
    content TEXT NOT NULL,
    summary TEXT,
    entities JSONB NOT NULL DEFAULT '[]',
    attributes JSONB NOT NULL DEFAULT '{}',
    context JSONB NOT NULL DEFAULT '{}',
    embedding vector(768), -- Gemini gemini-embedding-001 with OutputDimensionality=768
    keywords TEXT[] DEFAULT '{}',
    confidence REAL DEFAULT 0.8 CHECK (confidence BETWEEN 0 AND 1),
    version INTEGER DEFAULT 1,
    access_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    accessed_at TIMESTAMPTZ,
    status memory_status NOT NULL DEFAULT 'active',
    archived_at TIMESTAMPTZ,
    merged_from UUID[]
);

-- Relations between memories
CREATE TABLE relations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    from_id UUID NOT NULL REFERENCES memories(id) ON DELETE CASCADE,
    to_id UUID NOT NULL REFERENCES memories(id) ON DELETE CASCADE,
    type relation_type NOT NULL,
    strength REAL DEFAULT 1.0 CHECK (strength BETWEEN 0 AND 1),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(from_id, to_id, type)
);

-- Memory history tracking
CREATE TABLE history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    memory_id UUID NOT NULL,
    action action_type NOT NULL,
    version INTEGER NOT NULL,
    content TEXT NOT NULL,
    entities JSONB NOT NULL,
    attributes JSONB NOT NULL,
    context JSONB NOT NULL,
    reason TEXT,
    created_by TEXT DEFAULT 'system',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Processing queue for async memory operations
CREATE TABLE queue (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID,
    messages JSONB NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    error TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    processed_at TIMESTAMPTZ
);

-- ===========================================
-- Indexes - Optimize query performance
-- ===========================================

-- Conversation indexes
CREATE INDEX idx_conversations_status ON conversations(status) WHERE status = 'active';
CREATE INDEX idx_conversations_updated_at ON conversations(updated_at DESC);
CREATE INDEX idx_conversations_last_message ON conversations(last_message_at DESC NULLS LAST);
CREATE INDEX idx_conversations_metadata ON conversations USING GIN (metadata);
CREATE INDEX idx_conversations_search ON conversations USING GIN (to_tsvector('english', title || ' ' || COALESCE(summary, '')));

-- Message indexes
CREATE INDEX idx_messages_conversation ON messages(conversation_id, created_at DESC);
CREATE INDEX idx_messages_role ON messages(role) WHERE role != 'user';
CREATE INDEX idx_messages_created_at ON messages(created_at DESC);
CREATE INDEX idx_messages_metadata ON messages USING GIN (metadata) WHERE jsonb_typeof(metadata) = 'object';
CREATE INDEX idx_messages_search ON messages USING GIN (to_tsvector('english', content));

-- Embedding indexes
CREATE INDEX idx_embeddings_source ON embeddings(source_table, source_id);
CREATE INDEX idx_embeddings_vector ON embeddings USING hnsw (embedding vector_cosine_ops) WITH (m = 16, ef_construction = 64);
CREATE INDEX idx_embeddings_created_at ON embeddings(created_at DESC);

-- Task indexes
CREATE INDEX idx_tasks_status ON tasks(status) WHERE status IN ('todo', 'in_progress');
CREATE INDEX idx_tasks_priority ON tasks(priority, created_at DESC) WHERE status != 'done';
CREATE INDEX idx_tasks_due_date ON tasks(due_date ASC NULLS LAST) WHERE status != 'done';
CREATE INDEX idx_tasks_labels ON tasks USING GIN (labels);
CREATE INDEX idx_tasks_metadata ON tasks USING GIN (metadata);

-- Tool execution indexes
CREATE INDEX idx_tool_executions_conversation ON tool_executions(conversation_id) WHERE conversation_id IS NOT NULL;
CREATE INDEX idx_tool_executions_tool ON tool_executions(tool_name);
CREATE INDEX idx_tool_executions_status ON tool_executions(status) WHERE status IN ('running', 'pending');
CREATE INDEX idx_tool_executions_created ON tool_executions(created_at DESC);

-- System settings indexes
CREATE INDEX idx_system_settings_updated ON system_settings(updated_at DESC);

-- Memory backup indexes
CREATE INDEX idx_memory_backups_created ON memory_backups(created_at DESC);

-- Memory indexes
CREATE INDEX idx_mem_semantic ON memories(semantic_id) WHERE status = 'active';
CREATE INDEX idx_mem_type ON memories(type) WHERE status = 'active';
CREATE INDEX idx_mem_updated ON memories(updated_at DESC) WHERE status = 'active';
CREATE INDEX idx_mem_keywords ON memories USING gin(keywords) WHERE status = 'active';
CREATE INDEX idx_mem_entities ON memories USING gin(entities) WHERE status = 'active';
CREATE INDEX idx_mem_attrs ON memories USING gin(attributes) WHERE status = 'active';
CREATE INDEX idx_mem_context ON memories USING gin(context) WHERE status = 'active';
CREATE INDEX idx_mem_embedding ON memories USING hnsw (embedding vector_cosine_ops) WITH (m = 16, ef_construction = 64) WHERE status = 'active';
CREATE INDEX idx_memories_status ON memories(status);
CREATE INDEX idx_memories_status_updated_at ON memories(status, updated_at DESC);
CREATE INDEX idx_memories_status_accessed_at ON memories(status, accessed_at DESC NULLS LAST);
CREATE INDEX idx_memories_confidence ON memories(confidence DESC);
CREATE INDEX idx_memories_updated_at ON memories(updated_at DESC) WHERE status = 'active';

-- Relation indexes
CREATE INDEX idx_rel_from ON relations(from_id);
CREATE INDEX idx_rel_to ON relations(to_id);
CREATE INDEX idx_rel_type ON relations(type);

-- History indexes
CREATE INDEX idx_hist_memory ON history(memory_id);
CREATE INDEX idx_hist_created ON history(created_at DESC);
CREATE INDEX idx_hist_action ON history(action);

-- Queue indexes
CREATE INDEX idx_queue_status ON queue(status, created_at);

-- ===========================================
-- Trigger functions
-- ===========================================

-- Update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Update conversation's last_message_at and statistics
CREATE OR REPLACE FUNCTION update_conversation_stats()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE conversations
    SET
        last_message_at = NEW.created_at,
        message_count = message_count + 1,
        total_tokens = total_tokens + NEW.tokens_used
    WHERE id = NEW.conversation_id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ===========================================
-- Triggers
-- ===========================================

-- updated_at triggers
CREATE TRIGGER update_conversations_updated_at BEFORE UPDATE ON conversations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tasks_updated_at BEFORE UPDATE ON tasks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_settings_updated_at BEFORE UPDATE ON system_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_memories_updated_at BEFORE UPDATE ON memories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Conversation statistics trigger
CREATE TRIGGER update_conversation_stats_on_message AFTER INSERT ON messages
    FOR EACH ROW EXECUTE FUNCTION update_conversation_stats();

-- ===========================================
-- Functions - Business logic
-- ===========================================

-- Search similar embeddings
CREATE OR REPLACE FUNCTION search_similar_embeddings(
    query_embedding vector(768),
    search_limit INTEGER DEFAULT 10,
    source_tables TEXT[] DEFAULT NULL,
    similarity_threshold FLOAT DEFAULT 0.5
)
RETURNS TABLE (
    id UUID,
    source_table TEXT,
    source_id UUID,
    content TEXT,
    similarity FLOAT,
    metadata JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        e.id,
        e.source_table,
        e.source_id,
        e.content,
        1 - (e.embedding <=> query_embedding) AS similarity,
        e.metadata
    FROM embeddings e
    WHERE
        (source_tables IS NULL OR e.source_table = ANY(source_tables))
        AND 1 - (e.embedding <=> query_embedding) > similarity_threshold
    ORDER BY e.embedding <=> query_embedding
    LIMIT search_limit;
END;
$$ LANGUAGE plpgsql;

-- Clean up old backups
CREATE OR REPLACE FUNCTION cleanup_old_backups(
    keep_count INTEGER DEFAULT 10,
    keep_days INTEGER DEFAULT 30
)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    WITH backups_to_delete AS (
        SELECT id
        FROM memory_backups
        WHERE id NOT IN (
            SELECT id
            FROM memory_backups
            ORDER BY created_at DESC
            LIMIT keep_count
        )
        AND created_at < CURRENT_TIMESTAMP - INTERVAL '1 day' * keep_days
    )
    DELETE FROM memory_backups
    WHERE id IN (SELECT id FROM backups_to_delete);

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Generate semantic ID for memories
CREATE OR REPLACE FUNCTION gen_semantic_id(
    p_type memory_type,
    p_entities JSONB,
    p_context JSONB
)
RETURNS TEXT AS $$
DECLARE
    v_entity TEXT;
    v_date TEXT := '';
BEGIN
    -- Get primary entity
    SELECT e->>'name' INTO v_entity
    FROM jsonb_array_elements(p_entities) e
    WHERE e->>'role' IN ('subject', 'object')
    LIMIT 1;

    v_entity := COALESCE(v_entity, p_type::text);

    -- Add date for schedules
    IF p_type = 'schedule' THEN
        v_date := COALESCE(
            to_char((p_context->>'occurred_at')::timestamptz, '_YYYYMMDD'),
            to_char((p_context->>'start_time')::timestamptz, '_YYYYMMDD'),
            ''
        );
    END IF;

    RETURN LOWER(REGEXP_REPLACE(
        p_type::text || '_' || v_entity || v_date,
        '[^a-z0-9_]+', '_', 'g'
    ));
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Search memories with vector similarity
CREATE OR REPLACE FUNCTION search_memories_with_vector(
    p_embedding vector,
    p_types memory_type[],
    p_entities TEXT[],
    p_time_start TIMESTAMPTZ,
    p_time_end TIMESTAMPTZ,
    p_text TEXT,
    p_min_similarity REAL,
    p_max_results INTEGER
) RETURNS TABLE (
    id UUID,
    semantic_id TEXT,
    type memory_type,
    content TEXT,
    summary TEXT,
    entities JSONB,
    attributes JSONB,
    context JSONB,
    embedding vector,
    keywords TEXT[],
    confidence REAL,
    version INTEGER,
    access_count INTEGER,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    accessed_at TIMESTAMPTZ,
    status memory_status,
    archived_at TIMESTAMPTZ,
    merged_from UUID[],
    similarity REAL,
    score REAL
) AS $$
BEGIN
    RETURN QUERY
    WITH base_search AS (
        SELECT
            m.*,
            CASE
                WHEN p_embedding IS NOT NULL AND m.embedding IS NOT NULL
                THEN (1 - (m.embedding <=> p_embedding))::REAL
                ELSE 0.0
            END as calc_similarity
        FROM memories m
        WHERE m.status = 'active'
            -- Type filter
            AND (p_types IS NULL OR CARDINALITY(p_types) = 0 OR m.type = ANY(p_types))
            -- Entity filter
            AND (p_entities IS NULL OR CARDINALITY(p_entities) = 0 OR
                EXISTS (
                    SELECT 1 FROM jsonb_array_elements(m.entities) e
                    WHERE e->>'name' = ANY(p_entities)
                ))
            -- Time range filter
            AND (p_time_start IS NULL OR
                (m.context->>'occurred_at')::timestamptz >= p_time_start OR
                (m.context->>'start_time')::timestamptz >= p_time_start)
            AND (p_time_end IS NULL OR
                (m.context->>'occurred_at')::timestamptz <= p_time_end OR
                (m.context->>'end_time')::timestamptz <= p_time_end)
            -- Text search
            AND (p_text IS NULL OR p_text = '' OR
                m.content ILIKE '%' || p_text || '%' OR
                COALESCE(m.summary, '') ILIKE '%' || p_text || '%')
    ),
    scored_search AS (
        SELECT
            bs.*,
            bs.calc_similarity as similarity,
            -- Improved scoring algorithm
            (
                -- Similarity weight
                bs.calc_similarity * 0.5 +

                -- Recency weight
                CASE
                    WHEN bs.updated_at > NOW() - INTERVAL '1 hour' THEN 0.25
                    WHEN bs.updated_at > NOW() - INTERVAL '1 day' THEN 0.20
                    WHEN bs.updated_at > NOW() - INTERVAL '3 days' THEN 0.15
                    WHEN bs.updated_at > NOW() - INTERVAL '7 days' THEN 0.10
                    WHEN bs.updated_at > NOW() - INTERVAL '30 days' THEN 0.05
                    ELSE 0.02
                END +

                -- Access frequency weight
                CASE
                    WHEN bs.access_count > 20 THEN 0.15
                    WHEN bs.access_count > 10 THEN 0.10
                    WHEN bs.access_count > 5 THEN 0.05
                    ELSE 0.0
                END +

                -- Confidence weight
                COALESCE(bs.confidence, 0.8) * 0.1
            )::REAL as score
        FROM base_search bs
        WHERE bs.calc_similarity >= COALESCE(p_min_similarity, 0.0) OR p_embedding IS NULL
    )
    SELECT
        ss.id, ss.semantic_id, ss.type, ss.content, ss.summary,
        ss.entities, ss.attributes, ss.context, ss.embedding,
        ss.keywords, ss.confidence, ss.version, ss.access_count,
        ss.created_at, ss.updated_at, ss.accessed_at, ss.status,
        ss.archived_at, ss.merged_from, ss.similarity, ss.score
    FROM scored_search ss
    ORDER BY ss.score DESC
    LIMIT COALESCE(p_max_results, 20);
END;
$$ LANGUAGE plpgsql;

-- Update memory atomically
CREATE OR REPLACE FUNCTION update_memory(
    p_semantic_id TEXT,
    p_content TEXT,
    p_entities JSONB,
    p_attributes JSONB,
    p_context JSONB,
    p_reason TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    v_mem memories%ROWTYPE;
BEGIN
    -- Lock and get current
    SELECT * INTO v_mem
    FROM memories
    WHERE semantic_id = p_semantic_id AND status = 'active'
    FOR UPDATE;

    IF NOT FOUND THEN
        RETURN NULL;
    END IF;

    -- Save history
    INSERT INTO history (
        memory_id, action, version, content, entities, attributes, context, reason
    ) VALUES (
        v_mem.id, 'UPDATE', v_mem.version, v_mem.content,
        v_mem.entities, v_mem.attributes, v_mem.context, p_reason
    );

    -- Update
    UPDATE memories SET
        content = p_content,
        entities = p_entities,
        attributes = v_mem.attributes || p_attributes,
        context = v_mem.context || p_context,
        version = v_mem.version + 1,
        updated_at = NOW()
    WHERE id = v_mem.id;

    RETURN v_mem.id;
END;
$$ LANGUAGE plpgsql;

-- Get related memories
CREATE OR REPLACE FUNCTION get_related(
    p_id UUID,
    p_types relation_type[] DEFAULT NULL,
    p_limit INTEGER DEFAULT 10
)
RETURNS TABLE (
    memory_id UUID,
    semantic_id TEXT,
    type memory_type,
    content TEXT,
    rel_type relation_type,
    strength REAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        m.id,
        m.semantic_id,
        m.type,
        m.content,
        r.type,
        r.strength
    FROM relations r
    JOIN memories m ON (
        (r.from_id = p_id AND m.id = r.to_id) OR
        (r.to_id = p_id AND m.id = r.from_id)
    )
    WHERE m.status = 'active'
        AND (p_types IS NULL OR r.type = ANY(p_types))
    ORDER BY r.strength DESC, m.updated_at DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql STABLE;

-- Find conflicting memories
CREATE OR REPLACE FUNCTION find_conflicting_memories(
    p_activity TEXT,
    p_exclude_id UUID DEFAULT NULL
) RETURNS TABLE (
    id UUID,
    semantic_id TEXT,
    content TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    conflict_reason TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        m.id,
        m.semantic_id,
        m.content,
        m.created_at,
        m.updated_at,
        CASE
            WHEN m.semantic_id LIKE '%' || p_activity || '%' THEN 'Same activity with different schedule'
            WHEN m.content ILIKE '%' || p_activity || '%' THEN 'Content mentions same activity'
            ELSE 'Related memory'
        END as conflict_reason
    FROM memories m
    WHERE m.status = 'active'
        AND m.type = 'schedule'
        AND (p_exclude_id IS NULL OR m.id != p_exclude_id)
        AND (
            m.semantic_id LIKE '%' || p_activity || '%' OR
            m.content ILIKE '%' || p_activity || '%' OR
            EXISTS (
                SELECT 1 FROM jsonb_array_elements(m.entities) e
                WHERE e->>'name' ILIKE '%' || p_activity || '%'
            )
        )
    ORDER BY m.updated_at DESC;
END;
$$ LANGUAGE plpgsql;

-- ===========================================
-- Initial data
-- ===========================================

-- System settings initial values
INSERT INTO system_settings (key, value, description) VALUES
    ('owner', '{"name": "Koopa", "id": "koopa", "timezone": "Asia/Taipei", "language": "zh-TW"}', 'System owner information'),
    ('assistant', '{"name": "Assistant", "version": "1.0.0", "model": "claude-3-sonnet"}', 'AI assistant configuration'),
    ('preferences', '{"theme": "dark", "language": "zh-TW", "date_format": "YYYY-MM-DD"}', 'User preferences'),
    ('features', '{"rag_enabled": true, "tools_enabled": true, "memory_persistence": true}', 'Feature toggles')
ON CONFLICT (key) DO NOTHING;

-- ===========================================
-- Permissions and security
-- ===========================================

-- Create application-specific role (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_roles WHERE rolname = 'assistant_app') THEN
        CREATE ROLE assistant_app WITH LOGIN;
    END IF;
END $$;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO assistant_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO assistant_app;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO assistant_app;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO assistant_app;

-- ===========================================
-- Comment documentation
-- ===========================================

COMMENT ON SCHEMA public IS 'Assistant Go main database schema';

-- Core tables
COMMENT ON TABLE conversations IS 'Conversation session records, tracking user and AI interaction history';
COMMENT ON TABLE messages IS 'Messages within conversations, including user input and AI responses';
COMMENT ON TABLE embeddings IS 'Unified vector embeddings table, supporting semantic search and similarity matching';
COMMENT ON TABLE tasks IS 'Task management system, tracking to-do items';
COMMENT ON TABLE tool_executions IS 'Tool execution history, recording AI tool usage';
COMMENT ON TABLE system_settings IS 'System settings key-value pairs, storing application configuration';
COMMENT ON TABLE memory_backups IS 'Knowledge graph backups, ensuring memory persistence';

-- Memory system tables
COMMENT ON TABLE memories IS 'Structured memory system with semantic IDs and embeddings';
COMMENT ON COLUMN memories.semantic_id IS 'Semantic ID for updates (e.g., schedule_thai_boxing_20250714)';
COMMENT ON COLUMN memories.entities IS 'Entities with types and roles';
COMMENT ON COLUMN memories.attributes IS 'Key-value attributes';
COMMENT ON COLUMN memories.context IS 'Temporal and spatial context';
COMMENT ON COLUMN memories.embedding IS 'Embedding vector for gemini-embedding-001 with OutputDimensionality=768';
COMMENT ON COLUMN memories.status IS 'Memory status: active, archived, or deleted';
COMMENT ON TABLE relations IS 'Memory relationships';
COMMENT ON TABLE history IS 'Change history for memories';
COMMENT ON TABLE queue IS 'Async processing queue for memory operations';

-- Functions
COMMENT ON FUNCTION search_similar_embeddings IS 'Semantic search based on vector similarity';
COMMENT ON FUNCTION cleanup_old_backups IS 'Clean up expired memory backups';
COMMENT ON FUNCTION gen_semantic_id IS 'Generate semantic ID for memory updates';
COMMENT ON FUNCTION search_memories_with_vector IS 'Search memories with vector similarity and scoring';
COMMENT ON FUNCTION update_memory IS 'Atomically update memory with history tracking';
COMMENT ON FUNCTION get_related IS 'Get memories related to a specific memory';
COMMENT ON FUNCTION find_conflicting_memories IS 'Find memories that might conflict with a given activity';
