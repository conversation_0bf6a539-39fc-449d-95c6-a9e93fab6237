// Code generated by MockGen. DO NOT EDIT.
// Source: sqlc/querier.go
//
// Generated by this command:
//
//	mockgen -source=sqlc/querier.go -destination=mocks/mock_querier.go -package=mocks Querier
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	pgtype "github.com/jackc/pgx/v5/pgtype"
	sqlc "github.com/koopa0/assistant-go/internal/storage/database/sqlc"
	gomock "go.uber.org/mock/gomock"
)

// MockQuerier is a mock of Querier interface.
type MockQuerier struct {
	ctrl     *gomock.Controller
	recorder *MockQuerierMockRecorder
	isgomock struct{}
}

// MockQuerierMockRecorder is the mock recorder for MockQuerier.
type MockQuerierMockRecorder struct {
	mock *MockQuerier
}

// NewMockQuerier creates a new mock instance.
func NewMockQuerier(ctrl *gomock.Controller) *MockQuerier {
	mock := &MockQuerier{ctrl: ctrl}
	mock.recorder = &MockQuerierMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockQuerier) EXPECT() *MockQuerierMockRecorder {
	return m.recorder
}

// AddTaskLabel mocks base method.
func (m *MockQuerier) AddTaskLabel(ctx context.Context, arg sqlc.AddTaskLabelParams) (*sqlc.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddTaskLabel", ctx, arg)
	ret0, _ := ret[0].(*sqlc.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddTaskLabel indicates an expected call of AddTaskLabel.
func (mr *MockQuerierMockRecorder) AddTaskLabel(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddTaskLabel", reflect.TypeOf((*MockQuerier)(nil).AddTaskLabel), ctx, arg)
}

// ArchiveConversation mocks base method.
func (m *MockQuerier) ArchiveConversation(ctx context.Context, id pgtype.UUID) (*sqlc.Conversation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ArchiveConversation", ctx, id)
	ret0, _ := ret[0].(*sqlc.Conversation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ArchiveConversation indicates an expected call of ArchiveConversation.
func (mr *MockQuerierMockRecorder) ArchiveConversation(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ArchiveConversation", reflect.TypeOf((*MockQuerier)(nil).ArchiveConversation), ctx, id)
}

// BulkSetSystemSettings mocks base method.
func (m *MockQuerier) BulkSetSystemSettings(ctx context.Context, arg sqlc.BulkSetSystemSettingsParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BulkSetSystemSettings", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// BulkSetSystemSettings indicates an expected call of BulkSetSystemSettings.
func (mr *MockQuerierMockRecorder) BulkSetSystemSettings(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkSetSystemSettings", reflect.TypeOf((*MockQuerier)(nil).BulkSetSystemSettings), ctx, arg)
}

// CancelPendingToolExecutions mocks base method.
func (m *MockQuerier) CancelPendingToolExecutions(ctx context.Context, conversationID pgtype.UUID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelPendingToolExecutions", ctx, conversationID)
	ret0, _ := ret[0].(error)
	return ret0
}

// CancelPendingToolExecutions indicates an expected call of CancelPendingToolExecutions.
func (mr *MockQuerierMockRecorder) CancelPendingToolExecutions(ctx, conversationID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelPendingToolExecutions", reflect.TypeOf((*MockQuerier)(nil).CancelPendingToolExecutions), ctx, conversationID)
}

// CleanupJobs mocks base method.
func (m *MockQuerier) CleanupJobs(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CleanupJobs", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// CleanupJobs indicates an expected call of CleanupJobs.
func (mr *MockQuerierMockRecorder) CleanupJobs(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CleanupJobs", reflect.TypeOf((*MockQuerier)(nil).CleanupJobs), ctx)
}

// CompleteTask mocks base method.
func (m *MockQuerier) CompleteTask(ctx context.Context, id pgtype.UUID) (*sqlc.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CompleteTask", ctx, id)
	ret0, _ := ret[0].(*sqlc.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CompleteTask indicates an expected call of CompleteTask.
func (mr *MockQuerierMockRecorder) CompleteTask(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CompleteTask", reflect.TypeOf((*MockQuerier)(nil).CompleteTask), ctx, id)
}

// CompleteToolExecution mocks base method.
func (m *MockQuerier) CompleteToolExecution(ctx context.Context, arg sqlc.CompleteToolExecutionParams) (*sqlc.ToolExecution, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CompleteToolExecution", ctx, arg)
	ret0, _ := ret[0].(*sqlc.ToolExecution)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CompleteToolExecution indicates an expected call of CompleteToolExecution.
func (mr *MockQuerierMockRecorder) CompleteToolExecution(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CompleteToolExecution", reflect.TypeOf((*MockQuerier)(nil).CompleteToolExecution), ctx, arg)
}

// CountByType mocks base method.
func (m *MockQuerier) CountByType(ctx context.Context) ([]*sqlc.CountByTypeRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountByType", ctx)
	ret0, _ := ret[0].([]*sqlc.CountByTypeRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountByType indicates an expected call of CountByType.
func (mr *MockQuerierMockRecorder) CountByType(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountByType", reflect.TypeOf((*MockQuerier)(nil).CountByType), ctx)
}

// CountEmbeddingsBySource mocks base method.
func (m *MockQuerier) CountEmbeddingsBySource(ctx context.Context) ([]*sqlc.CountEmbeddingsBySourceRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountEmbeddingsBySource", ctx)
	ret0, _ := ret[0].([]*sqlc.CountEmbeddingsBySourceRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountEmbeddingsBySource indicates an expected call of CountEmbeddingsBySource.
func (mr *MockQuerierMockRecorder) CountEmbeddingsBySource(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountEmbeddingsBySource", reflect.TypeOf((*MockQuerier)(nil).CountEmbeddingsBySource), ctx)
}

// CountMemoryBackups mocks base method.
func (m *MockQuerier) CountMemoryBackups(ctx context.Context) (int32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountMemoryBackups", ctx)
	ret0, _ := ret[0].(int32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountMemoryBackups indicates an expected call of CountMemoryBackups.
func (mr *MockQuerierMockRecorder) CountMemoryBackups(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountMemoryBackups", reflect.TypeOf((*MockQuerier)(nil).CountMemoryBackups), ctx)
}

// CountMessagesByRole mocks base method.
func (m *MockQuerier) CountMessagesByRole(ctx context.Context, conversationID pgtype.UUID) ([]*sqlc.CountMessagesByRoleRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountMessagesByRole", ctx, conversationID)
	ret0, _ := ret[0].([]*sqlc.CountMessagesByRoleRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountMessagesByRole indicates an expected call of CountMessagesByRole.
func (mr *MockQuerierMockRecorder) CountMessagesByRole(ctx, conversationID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountMessagesByRole", reflect.TypeOf((*MockQuerier)(nil).CountMessagesByRole), ctx, conversationID)
}

// CountSystemSettings mocks base method.
func (m *MockQuerier) CountSystemSettings(ctx context.Context) (int32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountSystemSettings", ctx)
	ret0, _ := ret[0].(int32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountSystemSettings indicates an expected call of CountSystemSettings.
func (mr *MockQuerierMockRecorder) CountSystemSettings(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountSystemSettings", reflect.TypeOf((*MockQuerier)(nil).CountSystemSettings), ctx)
}

// CreateConversation mocks base method.
func (m *MockQuerier) CreateConversation(ctx context.Context, arg sqlc.CreateConversationParams) (*sqlc.Conversation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateConversation", ctx, arg)
	ret0, _ := ret[0].(*sqlc.Conversation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateConversation indicates an expected call of CreateConversation.
func (mr *MockQuerierMockRecorder) CreateConversation(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateConversation", reflect.TypeOf((*MockQuerier)(nil).CreateConversation), ctx, arg)
}

// CreateEmbedding mocks base method.
func (m *MockQuerier) CreateEmbedding(ctx context.Context, arg sqlc.CreateEmbeddingParams) (*sqlc.Embedding, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateEmbedding", ctx, arg)
	ret0, _ := ret[0].(*sqlc.Embedding)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateEmbedding indicates an expected call of CreateEmbedding.
func (mr *MockQuerierMockRecorder) CreateEmbedding(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateEmbedding", reflect.TypeOf((*MockQuerier)(nil).CreateEmbedding), ctx, arg)
}

// CreateHistory mocks base method.
func (m *MockQuerier) CreateHistory(ctx context.Context, arg sqlc.CreateHistoryParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateHistory", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateHistory indicates an expected call of CreateHistory.
func (mr *MockQuerierMockRecorder) CreateHistory(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateHistory", reflect.TypeOf((*MockQuerier)(nil).CreateHistory), ctx, arg)
}

// CreateJob mocks base method.
func (m *MockQuerier) CreateJob(ctx context.Context, arg sqlc.CreateJobParams) (*sqlc.Queue, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateJob", ctx, arg)
	ret0, _ := ret[0].(*sqlc.Queue)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateJob indicates an expected call of CreateJob.
func (mr *MockQuerierMockRecorder) CreateJob(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateJob", reflect.TypeOf((*MockQuerier)(nil).CreateJob), ctx, arg)
}

// CreateMemory mocks base method.
func (m *MockQuerier) CreateMemory(ctx context.Context, arg sqlc.CreateMemoryParams) (*sqlc.Memory, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateMemory", ctx, arg)
	ret0, _ := ret[0].(*sqlc.Memory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateMemory indicates an expected call of CreateMemory.
func (mr *MockQuerierMockRecorder) CreateMemory(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateMemory", reflect.TypeOf((*MockQuerier)(nil).CreateMemory), ctx, arg)
}

// CreateMemoryBackup mocks base method.
func (m *MockQuerier) CreateMemoryBackup(ctx context.Context, arg sqlc.CreateMemoryBackupParams) (*sqlc.MemoryBackup, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateMemoryBackup", ctx, arg)
	ret0, _ := ret[0].(*sqlc.MemoryBackup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateMemoryBackup indicates an expected call of CreateMemoryBackup.
func (mr *MockQuerierMockRecorder) CreateMemoryBackup(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateMemoryBackup", reflect.TypeOf((*MockQuerier)(nil).CreateMemoryBackup), ctx, arg)
}

// CreateMergedMemory mocks base method.
func (m *MockQuerier) CreateMergedMemory(ctx context.Context, arg sqlc.CreateMergedMemoryParams) (*sqlc.Memory, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateMergedMemory", ctx, arg)
	ret0, _ := ret[0].(*sqlc.Memory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateMergedMemory indicates an expected call of CreateMergedMemory.
func (mr *MockQuerierMockRecorder) CreateMergedMemory(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateMergedMemory", reflect.TypeOf((*MockQuerier)(nil).CreateMergedMemory), ctx, arg)
}

// CreateMessage mocks base method.
func (m *MockQuerier) CreateMessage(ctx context.Context, arg sqlc.CreateMessageParams) (*sqlc.Message, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateMessage", ctx, arg)
	ret0, _ := ret[0].(*sqlc.Message)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateMessage indicates an expected call of CreateMessage.
func (mr *MockQuerierMockRecorder) CreateMessage(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateMessage", reflect.TypeOf((*MockQuerier)(nil).CreateMessage), ctx, arg)
}

// CreateRelation mocks base method.
func (m *MockQuerier) CreateRelation(ctx context.Context, arg sqlc.CreateRelationParams) (*sqlc.Relation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRelation", ctx, arg)
	ret0, _ := ret[0].(*sqlc.Relation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRelation indicates an expected call of CreateRelation.
func (mr *MockQuerierMockRecorder) CreateRelation(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRelation", reflect.TypeOf((*MockQuerier)(nil).CreateRelation), ctx, arg)
}

// CreateRelationsBatch mocks base method.
func (m *MockQuerier) CreateRelationsBatch(ctx context.Context, arg []sqlc.CreateRelationsBatchParams) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRelationsBatch", ctx, arg)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRelationsBatch indicates an expected call of CreateRelationsBatch.
func (mr *MockQuerierMockRecorder) CreateRelationsBatch(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRelationsBatch", reflect.TypeOf((*MockQuerier)(nil).CreateRelationsBatch), ctx, arg)
}

// CreateTask mocks base method.
func (m *MockQuerier) CreateTask(ctx context.Context, arg sqlc.CreateTaskParams) (*sqlc.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTask", ctx, arg)
	ret0, _ := ret[0].(*sqlc.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTask indicates an expected call of CreateTask.
func (mr *MockQuerierMockRecorder) CreateTask(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTask", reflect.TypeOf((*MockQuerier)(nil).CreateTask), ctx, arg)
}

// CreateToolExecution mocks base method.
func (m *MockQuerier) CreateToolExecution(ctx context.Context, arg sqlc.CreateToolExecutionParams) (*sqlc.ToolExecution, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateToolExecution", ctx, arg)
	ret0, _ := ret[0].(*sqlc.ToolExecution)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateToolExecution indicates an expected call of CreateToolExecution.
func (mr *MockQuerierMockRecorder) CreateToolExecution(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateToolExecution", reflect.TypeOf((*MockQuerier)(nil).CreateToolExecution), ctx, arg)
}

// DeactivateMany mocks base method.
func (m *MockQuerier) DeactivateMany(ctx context.Context, dollar_1 []pgtype.UUID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeactivateMany", ctx, dollar_1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeactivateMany indicates an expected call of DeactivateMany.
func (mr *MockQuerierMockRecorder) DeactivateMany(ctx, dollar_1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeactivateMany", reflect.TypeOf((*MockQuerier)(nil).DeactivateMany), ctx, dollar_1)
}

// DeactivateMemory mocks base method.
func (m *MockQuerier) DeactivateMemory(ctx context.Context, id pgtype.UUID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeactivateMemory", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeactivateMemory indicates an expected call of DeactivateMemory.
func (mr *MockQuerierMockRecorder) DeactivateMemory(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeactivateMemory", reflect.TypeOf((*MockQuerier)(nil).DeactivateMemory), ctx, id)
}

// DeleteConversation mocks base method.
func (m *MockQuerier) DeleteConversation(ctx context.Context, id pgtype.UUID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteConversation", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteConversation indicates an expected call of DeleteConversation.
func (mr *MockQuerierMockRecorder) DeleteConversation(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteConversation", reflect.TypeOf((*MockQuerier)(nil).DeleteConversation), ctx, id)
}

// DeleteConversationMessages mocks base method.
func (m *MockQuerier) DeleteConversationMessages(ctx context.Context, conversationID pgtype.UUID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteConversationMessages", ctx, conversationID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteConversationMessages indicates an expected call of DeleteConversationMessages.
func (mr *MockQuerierMockRecorder) DeleteConversationMessages(ctx, conversationID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteConversationMessages", reflect.TypeOf((*MockQuerier)(nil).DeleteConversationMessages), ctx, conversationID)
}

// DeleteEmbedding mocks base method.
func (m *MockQuerier) DeleteEmbedding(ctx context.Context, id pgtype.UUID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteEmbedding", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteEmbedding indicates an expected call of DeleteEmbedding.
func (mr *MockQuerierMockRecorder) DeleteEmbedding(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteEmbedding", reflect.TypeOf((*MockQuerier)(nil).DeleteEmbedding), ctx, id)
}

// DeleteEmbeddingBySource mocks base method.
func (m *MockQuerier) DeleteEmbeddingBySource(ctx context.Context, arg sqlc.DeleteEmbeddingBySourceParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteEmbeddingBySource", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteEmbeddingBySource indicates an expected call of DeleteEmbeddingBySource.
func (mr *MockQuerierMockRecorder) DeleteEmbeddingBySource(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteEmbeddingBySource", reflect.TypeOf((*MockQuerier)(nil).DeleteEmbeddingBySource), ctx, arg)
}

// DeleteMemoryBackup mocks base method.
func (m *MockQuerier) DeleteMemoryBackup(ctx context.Context, id pgtype.UUID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteMemoryBackup", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteMemoryBackup indicates an expected call of DeleteMemoryBackup.
func (mr *MockQuerierMockRecorder) DeleteMemoryBackup(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteMemoryBackup", reflect.TypeOf((*MockQuerier)(nil).DeleteMemoryBackup), ctx, id)
}

// DeleteMessage mocks base method.
func (m *MockQuerier) DeleteMessage(ctx context.Context, id pgtype.UUID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteMessage", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteMessage indicates an expected call of DeleteMessage.
func (mr *MockQuerierMockRecorder) DeleteMessage(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteMessage", reflect.TypeOf((*MockQuerier)(nil).DeleteMessage), ctx, id)
}

// DeleteOldEmbeddings mocks base method.
func (m *MockQuerier) DeleteOldEmbeddings(ctx context.Context, arg sqlc.DeleteOldEmbeddingsParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteOldEmbeddings", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteOldEmbeddings indicates an expected call of DeleteOldEmbeddings.
func (mr *MockQuerierMockRecorder) DeleteOldEmbeddings(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteOldEmbeddings", reflect.TypeOf((*MockQuerier)(nil).DeleteOldEmbeddings), ctx, arg)
}

// DeleteOldMemoryBackups mocks base method.
func (m *MockQuerier) DeleteOldMemoryBackups(ctx context.Context, arg sqlc.DeleteOldMemoryBackupsParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteOldMemoryBackups", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteOldMemoryBackups indicates an expected call of DeleteOldMemoryBackups.
func (mr *MockQuerierMockRecorder) DeleteOldMemoryBackups(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteOldMemoryBackups", reflect.TypeOf((*MockQuerier)(nil).DeleteOldMemoryBackups), ctx, arg)
}

// DeleteOldToolExecutions mocks base method.
func (m *MockQuerier) DeleteOldToolExecutions(ctx context.Context, daysOld any) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteOldToolExecutions", ctx, daysOld)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteOldToolExecutions indicates an expected call of DeleteOldToolExecutions.
func (mr *MockQuerierMockRecorder) DeleteOldToolExecutions(ctx, daysOld any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteOldToolExecutions", reflect.TypeOf((*MockQuerier)(nil).DeleteOldToolExecutions), ctx, daysOld)
}

// DeleteSystemSetting mocks base method.
func (m *MockQuerier) DeleteSystemSetting(ctx context.Context, key string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteSystemSetting", ctx, key)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteSystemSetting indicates an expected call of DeleteSystemSetting.
func (mr *MockQuerierMockRecorder) DeleteSystemSetting(ctx, key any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteSystemSetting", reflect.TypeOf((*MockQuerier)(nil).DeleteSystemSetting), ctx, key)
}

// DeleteTask mocks base method.
func (m *MockQuerier) DeleteTask(ctx context.Context, id pgtype.UUID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTask", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteTask indicates an expected call of DeleteTask.
func (mr *MockQuerierMockRecorder) DeleteTask(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTask", reflect.TypeOf((*MockQuerier)(nil).DeleteTask), ctx, id)
}

// ExistsSystemSetting mocks base method.
func (m *MockQuerier) ExistsSystemSetting(ctx context.Context, key string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExistsSystemSetting", ctx, key)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExistsSystemSetting indicates an expected call of ExistsSystemSetting.
func (mr *MockQuerierMockRecorder) ExistsSystemSetting(ctx, key any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExistsSystemSetting", reflect.TypeOf((*MockQuerier)(nil).ExistsSystemSetting), ctx, key)
}

// FailToolExecution mocks base method.
func (m *MockQuerier) FailToolExecution(ctx context.Context, arg sqlc.FailToolExecutionParams) (*sqlc.ToolExecution, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FailToolExecution", ctx, arg)
	ret0, _ := ret[0].(*sqlc.ToolExecution)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FailToolExecution indicates an expected call of FailToolExecution.
func (mr *MockQuerierMockRecorder) FailToolExecution(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FailToolExecution", reflect.TypeOf((*MockQuerier)(nil).FailToolExecution), ctx, arg)
}

// GetAllHistory mocks base method.
func (m *MockQuerier) GetAllHistory(ctx context.Context, limit int32) ([]*sqlc.History, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllHistory", ctx, limit)
	ret0, _ := ret[0].([]*sqlc.History)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllHistory indicates an expected call of GetAllHistory.
func (mr *MockQuerierMockRecorder) GetAllHistory(ctx, limit any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllHistory", reflect.TypeOf((*MockQuerier)(nil).GetAllHistory), ctx, limit)
}

// GetBackupStats mocks base method.
func (m *MockQuerier) GetBackupStats(ctx context.Context) (*sqlc.GetBackupStatsRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBackupStats", ctx)
	ret0, _ := ret[0].(*sqlc.GetBackupStatsRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBackupStats indicates an expected call of GetBackupStats.
func (mr *MockQuerierMockRecorder) GetBackupStats(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBackupStats", reflect.TypeOf((*MockQuerier)(nil).GetBackupStats), ctx)
}

// GetBackupsWithinTimeRange mocks base method.
func (m *MockQuerier) GetBackupsWithinTimeRange(ctx context.Context, arg sqlc.GetBackupsWithinTimeRangeParams) ([]*sqlc.MemoryBackup, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBackupsWithinTimeRange", ctx, arg)
	ret0, _ := ret[0].([]*sqlc.MemoryBackup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBackupsWithinTimeRange indicates an expected call of GetBackupsWithinTimeRange.
func (mr *MockQuerierMockRecorder) GetBackupsWithinTimeRange(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBackupsWithinTimeRange", reflect.TypeOf((*MockQuerier)(nil).GetBackupsWithinTimeRange), ctx, arg)
}

// GetConversation mocks base method.
func (m *MockQuerier) GetConversation(ctx context.Context, id pgtype.UUID) (*sqlc.Conversation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConversation", ctx, id)
	ret0, _ := ret[0].(*sqlc.Conversation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConversation indicates an expected call of GetConversation.
func (mr *MockQuerierMockRecorder) GetConversation(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConversation", reflect.TypeOf((*MockQuerier)(nil).GetConversation), ctx, id)
}

// GetConversationStats mocks base method.
func (m *MockQuerier) GetConversationStats(ctx context.Context) (*sqlc.GetConversationStatsRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConversationStats", ctx)
	ret0, _ := ret[0].(*sqlc.GetConversationStatsRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConversationStats indicates an expected call of GetConversationStats.
func (mr *MockQuerierMockRecorder) GetConversationStats(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConversationStats", reflect.TypeOf((*MockQuerier)(nil).GetConversationStats), ctx)
}

// GetConversationTokenUsage mocks base method.
func (m *MockQuerier) GetConversationTokenUsage(ctx context.Context, conversationID pgtype.UUID) (*sqlc.GetConversationTokenUsageRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConversationTokenUsage", ctx, conversationID)
	ret0, _ := ret[0].(*sqlc.GetConversationTokenUsageRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConversationTokenUsage indicates an expected call of GetConversationTokenUsage.
func (mr *MockQuerierMockRecorder) GetConversationTokenUsage(ctx, conversationID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConversationTokenUsage", reflect.TypeOf((*MockQuerier)(nil).GetConversationTokenUsage), ctx, conversationID)
}

// GetEmbedding mocks base method.
func (m *MockQuerier) GetEmbedding(ctx context.Context, id pgtype.UUID) (*sqlc.Embedding, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEmbedding", ctx, id)
	ret0, _ := ret[0].(*sqlc.Embedding)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEmbedding indicates an expected call of GetEmbedding.
func (mr *MockQuerierMockRecorder) GetEmbedding(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEmbedding", reflect.TypeOf((*MockQuerier)(nil).GetEmbedding), ctx, id)
}

// GetEmbeddingBySource mocks base method.
func (m *MockQuerier) GetEmbeddingBySource(ctx context.Context, arg sqlc.GetEmbeddingBySourceParams) (*sqlc.Embedding, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEmbeddingBySource", ctx, arg)
	ret0, _ := ret[0].(*sqlc.Embedding)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEmbeddingBySource indicates an expected call of GetEmbeddingBySource.
func (mr *MockQuerierMockRecorder) GetEmbeddingBySource(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEmbeddingBySource", reflect.TypeOf((*MockQuerier)(nil).GetEmbeddingBySource), ctx, arg)
}

// GetEmbeddingStats mocks base method.
func (m *MockQuerier) GetEmbeddingStats(ctx context.Context) (*sqlc.GetEmbeddingStatsRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEmbeddingStats", ctx)
	ret0, _ := ret[0].(*sqlc.GetEmbeddingStatsRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEmbeddingStats indicates an expected call of GetEmbeddingStats.
func (mr *MockQuerierMockRecorder) GetEmbeddingStats(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEmbeddingStats", reflect.TypeOf((*MockQuerier)(nil).GetEmbeddingStats), ctx)
}

// GetHistory mocks base method.
func (m *MockQuerier) GetHistory(ctx context.Context, arg sqlc.GetHistoryParams) ([]*sqlc.History, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHistory", ctx, arg)
	ret0, _ := ret[0].([]*sqlc.History)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHistory indicates an expected call of GetHistory.
func (mr *MockQuerierMockRecorder) GetHistory(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHistory", reflect.TypeOf((*MockQuerier)(nil).GetHistory), ctx, arg)
}

// GetHistoryByAction mocks base method.
func (m *MockQuerier) GetHistoryByAction(ctx context.Context, arg sqlc.GetHistoryByActionParams) ([]*sqlc.History, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHistoryByAction", ctx, arg)
	ret0, _ := ret[0].([]*sqlc.History)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHistoryByAction indicates an expected call of GetHistoryByAction.
func (mr *MockQuerierMockRecorder) GetHistoryByAction(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHistoryByAction", reflect.TypeOf((*MockQuerier)(nil).GetHistoryByAction), ctx, arg)
}

// GetLastMessages mocks base method.
func (m *MockQuerier) GetLastMessages(ctx context.Context, arg sqlc.GetLastMessagesParams) ([]*sqlc.Message, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastMessages", ctx, arg)
	ret0, _ := ret[0].([]*sqlc.Message)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastMessages indicates an expected call of GetLastMessages.
func (mr *MockQuerierMockRecorder) GetLastMessages(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastMessages", reflect.TypeOf((*MockQuerier)(nil).GetLastMessages), ctx, arg)
}

// GetLatestMemoryBackup mocks base method.
func (m *MockQuerier) GetLatestMemoryBackup(ctx context.Context) (*sqlc.MemoryBackup, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestMemoryBackup", ctx)
	ret0, _ := ret[0].(*sqlc.MemoryBackup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestMemoryBackup indicates an expected call of GetLatestMemoryBackup.
func (mr *MockQuerierMockRecorder) GetLatestMemoryBackup(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestMemoryBackup", reflect.TypeOf((*MockQuerier)(nil).GetLatestMemoryBackup), ctx)
}

// GetMemory mocks base method.
func (m *MockQuerier) GetMemory(ctx context.Context, id pgtype.UUID) (*sqlc.Memory, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMemory", ctx, id)
	ret0, _ := ret[0].(*sqlc.Memory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMemory indicates an expected call of GetMemory.
func (mr *MockQuerierMockRecorder) GetMemory(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMemory", reflect.TypeOf((*MockQuerier)(nil).GetMemory), ctx, id)
}

// GetMemoryBackup mocks base method.
func (m *MockQuerier) GetMemoryBackup(ctx context.Context, id pgtype.UUID) (*sqlc.MemoryBackup, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMemoryBackup", ctx, id)
	ret0, _ := ret[0].(*sqlc.MemoryBackup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMemoryBackup indicates an expected call of GetMemoryBackup.
func (mr *MockQuerierMockRecorder) GetMemoryBackup(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMemoryBackup", reflect.TypeOf((*MockQuerier)(nil).GetMemoryBackup), ctx, id)
}

// GetMemoryBySemantic mocks base method.
func (m *MockQuerier) GetMemoryBySemantic(ctx context.Context, semanticID string) (*sqlc.Memory, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMemoryBySemantic", ctx, semanticID)
	ret0, _ := ret[0].(*sqlc.Memory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMemoryBySemantic indicates an expected call of GetMemoryBySemantic.
func (mr *MockQuerierMockRecorder) GetMemoryBySemantic(ctx, semanticID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMemoryBySemantic", reflect.TypeOf((*MockQuerier)(nil).GetMemoryBySemantic), ctx, semanticID)
}

// GetMessage mocks base method.
func (m *MockQuerier) GetMessage(ctx context.Context, id pgtype.UUID) (*sqlc.Message, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMessage", ctx, id)
	ret0, _ := ret[0].(*sqlc.Message)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMessage indicates an expected call of GetMessage.
func (mr *MockQuerierMockRecorder) GetMessage(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMessage", reflect.TypeOf((*MockQuerier)(nil).GetMessage), ctx, id)
}

// GetMultipleSettings mocks base method.
func (m *MockQuerier) GetMultipleSettings(ctx context.Context, keys []string) ([]*sqlc.SystemSetting, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMultipleSettings", ctx, keys)
	ret0, _ := ret[0].([]*sqlc.SystemSetting)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultipleSettings indicates an expected call of GetMultipleSettings.
func (mr *MockQuerierMockRecorder) GetMultipleSettings(ctx, keys any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultipleSettings", reflect.TypeOf((*MockQuerier)(nil).GetMultipleSettings), ctx, keys)
}

// GetPendingJobs mocks base method.
func (m *MockQuerier) GetPendingJobs(ctx context.Context, limit int32) ([]*sqlc.Queue, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPendingJobs", ctx, limit)
	ret0, _ := ret[0].([]*sqlc.Queue)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPendingJobs indicates an expected call of GetPendingJobs.
func (mr *MockQuerierMockRecorder) GetPendingJobs(ctx, limit any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPendingJobs", reflect.TypeOf((*MockQuerier)(nil).GetPendingJobs), ctx, limit)
}

// GetRecentConversationsWithMessageCount mocks base method.
func (m *MockQuerier) GetRecentConversationsWithMessageCount(ctx context.Context, limitCount int32) ([]*sqlc.GetRecentConversationsWithMessageCountRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecentConversationsWithMessageCount", ctx, limitCount)
	ret0, _ := ret[0].([]*sqlc.GetRecentConversationsWithMessageCountRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecentConversationsWithMessageCount indicates an expected call of GetRecentConversationsWithMessageCount.
func (mr *MockQuerierMockRecorder) GetRecentConversationsWithMessageCount(ctx, limitCount any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecentConversationsWithMessageCount", reflect.TypeOf((*MockQuerier)(nil).GetRecentConversationsWithMessageCount), ctx, limitCount)
}

// GetRelated mocks base method.
func (m *MockQuerier) GetRelated(ctx context.Context, arg sqlc.GetRelatedParams) ([]*sqlc.GetRelatedRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRelated", ctx, arg)
	ret0, _ := ret[0].([]*sqlc.GetRelatedRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRelated indicates an expected call of GetRelated.
func (mr *MockQuerierMockRecorder) GetRelated(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRelated", reflect.TypeOf((*MockQuerier)(nil).GetRelated), ctx, arg)
}

// GetRelationsByMemory mocks base method.
func (m *MockQuerier) GetRelationsByMemory(ctx context.Context, fromID pgtype.UUID) ([]*sqlc.Relation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRelationsByMemory", ctx, fromID)
	ret0, _ := ret[0].([]*sqlc.Relation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRelationsByMemory indicates an expected call of GetRelationsByMemory.
func (mr *MockQuerierMockRecorder) GetRelationsByMemory(ctx, fromID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRelationsByMemory", reflect.TypeOf((*MockQuerier)(nil).GetRelationsByMemory), ctx, fromID)
}

// GetSettingHistory mocks base method.
func (m *MockQuerier) GetSettingHistory(ctx context.Context, key string) ([]*sqlc.GetSettingHistoryRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSettingHistory", ctx, key)
	ret0, _ := ret[0].([]*sqlc.GetSettingHistoryRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSettingHistory indicates an expected call of GetSettingHistory.
func (mr *MockQuerierMockRecorder) GetSettingHistory(ctx, key any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSettingHistory", reflect.TypeOf((*MockQuerier)(nil).GetSettingHistory), ctx, key)
}

// GetStats mocks base method.
func (m *MockQuerier) GetStats(ctx context.Context) (*sqlc.GetStatsRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStats", ctx)
	ret0, _ := ret[0].(*sqlc.GetStatsRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStats indicates an expected call of GetStats.
func (mr *MockQuerierMockRecorder) GetStats(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStats", reflect.TypeOf((*MockQuerier)(nil).GetStats), ctx)
}

// GetSystemSetting mocks base method.
func (m *MockQuerier) GetSystemSetting(ctx context.Context, key string) (*sqlc.SystemSetting, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSystemSetting", ctx, key)
	ret0, _ := ret[0].(*sqlc.SystemSetting)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSystemSetting indicates an expected call of GetSystemSetting.
func (mr *MockQuerierMockRecorder) GetSystemSetting(ctx, key any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSystemSetting", reflect.TypeOf((*MockQuerier)(nil).GetSystemSetting), ctx, key)
}

// GetSystemSettingKeys mocks base method.
func (m *MockQuerier) GetSystemSettingKeys(ctx context.Context) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSystemSettingKeys", ctx)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSystemSettingKeys indicates an expected call of GetSystemSettingKeys.
func (mr *MockQuerierMockRecorder) GetSystemSettingKeys(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSystemSettingKeys", reflect.TypeOf((*MockQuerier)(nil).GetSystemSettingKeys), ctx)
}

// GetTask mocks base method.
func (m *MockQuerier) GetTask(ctx context.Context, id pgtype.UUID) (*sqlc.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTask", ctx, id)
	ret0, _ := ret[0].(*sqlc.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTask indicates an expected call of GetTask.
func (mr *MockQuerierMockRecorder) GetTask(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTask", reflect.TypeOf((*MockQuerier)(nil).GetTask), ctx, id)
}

// GetTaskStats mocks base method.
func (m *MockQuerier) GetTaskStats(ctx context.Context) (*sqlc.GetTaskStatsRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTaskStats", ctx)
	ret0, _ := ret[0].(*sqlc.GetTaskStatsRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTaskStats indicates an expected call of GetTaskStats.
func (mr *MockQuerierMockRecorder) GetTaskStats(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTaskStats", reflect.TypeOf((*MockQuerier)(nil).GetTaskStats), ctx)
}

// GetTasksByPriority mocks base method.
func (m *MockQuerier) GetTasksByPriority(ctx context.Context) ([]*sqlc.GetTasksByPriorityRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTasksByPriority", ctx)
	ret0, _ := ret[0].([]*sqlc.GetTasksByPriorityRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTasksByPriority indicates an expected call of GetTasksByPriority.
func (mr *MockQuerierMockRecorder) GetTasksByPriority(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTasksByPriority", reflect.TypeOf((*MockQuerier)(nil).GetTasksByPriority), ctx)
}

// GetToolExecution mocks base method.
func (m *MockQuerier) GetToolExecution(ctx context.Context, id pgtype.UUID) (*sqlc.ToolExecution, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetToolExecution", ctx, id)
	ret0, _ := ret[0].(*sqlc.ToolExecution)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetToolExecution indicates an expected call of GetToolExecution.
func (mr *MockQuerierMockRecorder) GetToolExecution(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetToolExecution", reflect.TypeOf((*MockQuerier)(nil).GetToolExecution), ctx, id)
}

// GetToolExecutionStats mocks base method.
func (m *MockQuerier) GetToolExecutionStats(ctx context.Context, arg sqlc.GetToolExecutionStatsParams) (*sqlc.GetToolExecutionStatsRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetToolExecutionStats", ctx, arg)
	ret0, _ := ret[0].(*sqlc.GetToolExecutionStatsRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetToolExecutionStats indicates an expected call of GetToolExecutionStats.
func (mr *MockQuerierMockRecorder) GetToolExecutionStats(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetToolExecutionStats", reflect.TypeOf((*MockQuerier)(nil).GetToolExecutionStats), ctx, arg)
}

// GetToolUsageByName mocks base method.
func (m *MockQuerier) GetToolUsageByName(ctx context.Context, since pgtype.Timestamptz) ([]*sqlc.GetToolUsageByNameRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetToolUsageByName", ctx, since)
	ret0, _ := ret[0].([]*sqlc.GetToolUsageByNameRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetToolUsageByName indicates an expected call of GetToolUsageByName.
func (mr *MockQuerierMockRecorder) GetToolUsageByName(ctx, since any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetToolUsageByName", reflect.TypeOf((*MockQuerier)(nil).GetToolUsageByName), ctx, since)
}

// IncrementAccess mocks base method.
func (m *MockQuerier) IncrementAccess(ctx context.Context, id pgtype.UUID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrementAccess", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncrementAccess indicates an expected call of IncrementAccess.
func (mr *MockQuerierMockRecorder) IncrementAccess(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrementAccess", reflect.TypeOf((*MockQuerier)(nil).IncrementAccess), ctx, id)
}

// ListActiveConversations mocks base method.
func (m *MockQuerier) ListActiveConversations(ctx context.Context, limitCount int32) ([]*sqlc.Conversation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListActiveConversations", ctx, limitCount)
	ret0, _ := ret[0].([]*sqlc.Conversation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListActiveConversations indicates an expected call of ListActiveConversations.
func (mr *MockQuerierMockRecorder) ListActiveConversations(ctx, limitCount any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListActiveConversations", reflect.TypeOf((*MockQuerier)(nil).ListActiveConversations), ctx, limitCount)
}

// ListByKeyword mocks base method.
func (m *MockQuerier) ListByKeyword(ctx context.Context, arg sqlc.ListByKeywordParams) ([]*sqlc.Memory, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByKeyword", ctx, arg)
	ret0, _ := ret[0].([]*sqlc.Memory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByKeyword indicates an expected call of ListByKeyword.
func (mr *MockQuerierMockRecorder) ListByKeyword(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByKeyword", reflect.TypeOf((*MockQuerier)(nil).ListByKeyword), ctx, arg)
}

// ListByType mocks base method.
func (m *MockQuerier) ListByType(ctx context.Context, arg sqlc.ListByTypeParams) ([]*sqlc.Memory, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByType", ctx, arg)
	ret0, _ := ret[0].([]*sqlc.Memory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByType indicates an expected call of ListByType.
func (mr *MockQuerierMockRecorder) ListByType(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByType", reflect.TypeOf((*MockQuerier)(nil).ListByType), ctx, arg)
}

// ListConversations mocks base method.
func (m *MockQuerier) ListConversations(ctx context.Context, arg sqlc.ListConversationsParams) ([]*sqlc.Conversation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListConversations", ctx, arg)
	ret0, _ := ret[0].([]*sqlc.Conversation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListConversations indicates an expected call of ListConversations.
func (mr *MockQuerierMockRecorder) ListConversations(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListConversations", reflect.TypeOf((*MockQuerier)(nil).ListConversations), ctx, arg)
}

// ListEmbeddingsBySource mocks base method.
func (m *MockQuerier) ListEmbeddingsBySource(ctx context.Context, arg sqlc.ListEmbeddingsBySourceParams) ([]*sqlc.Embedding, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListEmbeddingsBySource", ctx, arg)
	ret0, _ := ret[0].([]*sqlc.Embedding)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListEmbeddingsBySource indicates an expected call of ListEmbeddingsBySource.
func (mr *MockQuerierMockRecorder) ListEmbeddingsBySource(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListEmbeddingsBySource", reflect.TypeOf((*MockQuerier)(nil).ListEmbeddingsBySource), ctx, arg)
}

// ListInTimeRange mocks base method.
func (m *MockQuerier) ListInTimeRange(ctx context.Context, arg sqlc.ListInTimeRangeParams) ([]*sqlc.Memory, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInTimeRange", ctx, arg)
	ret0, _ := ret[0].([]*sqlc.Memory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInTimeRange indicates an expected call of ListInTimeRange.
func (mr *MockQuerierMockRecorder) ListInTimeRange(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInTimeRange", reflect.TypeOf((*MockQuerier)(nil).ListInTimeRange), ctx, arg)
}

// ListMemoryBackups mocks base method.
func (m *MockQuerier) ListMemoryBackups(ctx context.Context, arg sqlc.ListMemoryBackupsParams) ([]*sqlc.MemoryBackup, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListMemoryBackups", ctx, arg)
	ret0, _ := ret[0].([]*sqlc.MemoryBackup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListMemoryBackups indicates an expected call of ListMemoryBackups.
func (mr *MockQuerierMockRecorder) ListMemoryBackups(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListMemoryBackups", reflect.TypeOf((*MockQuerier)(nil).ListMemoryBackups), ctx, arg)
}

// ListMessages mocks base method.
func (m *MockQuerier) ListMessages(ctx context.Context, arg sqlc.ListMessagesParams) ([]*sqlc.Message, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListMessages", ctx, arg)
	ret0, _ := ret[0].([]*sqlc.Message)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListMessages indicates an expected call of ListMessages.
func (mr *MockQuerierMockRecorder) ListMessages(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListMessages", reflect.TypeOf((*MockQuerier)(nil).ListMessages), ctx, arg)
}

// ListOverdueTasks mocks base method.
func (m *MockQuerier) ListOverdueTasks(ctx context.Context) ([]*sqlc.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListOverdueTasks", ctx)
	ret0, _ := ret[0].([]*sqlc.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListOverdueTasks indicates an expected call of ListOverdueTasks.
func (mr *MockQuerierMockRecorder) ListOverdueTasks(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListOverdueTasks", reflect.TypeOf((*MockQuerier)(nil).ListOverdueTasks), ctx)
}

// ListPendingTasks mocks base method.
func (m *MockQuerier) ListPendingTasks(ctx context.Context, limitCount int32) ([]*sqlc.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListPendingTasks", ctx, limitCount)
	ret0, _ := ret[0].([]*sqlc.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListPendingTasks indicates an expected call of ListPendingTasks.
func (mr *MockQuerierMockRecorder) ListPendingTasks(ctx, limitCount any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListPendingTasks", reflect.TypeOf((*MockQuerier)(nil).ListPendingTasks), ctx, limitCount)
}

// ListPendingToolExecutions mocks base method.
func (m *MockQuerier) ListPendingToolExecutions(ctx context.Context, limitCount int32) ([]*sqlc.ToolExecution, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListPendingToolExecutions", ctx, limitCount)
	ret0, _ := ret[0].([]*sqlc.ToolExecution)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListPendingToolExecutions indicates an expected call of ListPendingToolExecutions.
func (mr *MockQuerierMockRecorder) ListPendingToolExecutions(ctx, limitCount any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListPendingToolExecutions", reflect.TypeOf((*MockQuerier)(nil).ListPendingToolExecutions), ctx, limitCount)
}

// ListRecent mocks base method.
func (m *MockQuerier) ListRecent(ctx context.Context, limit int32) ([]*sqlc.Memory, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListRecent", ctx, limit)
	ret0, _ := ret[0].([]*sqlc.Memory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListRecent indicates an expected call of ListRecent.
func (mr *MockQuerierMockRecorder) ListRecent(ctx, limit any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListRecent", reflect.TypeOf((*MockQuerier)(nil).ListRecent), ctx, limit)
}

// ListRecentMessages mocks base method.
func (m *MockQuerier) ListRecentMessages(ctx context.Context, arg sqlc.ListRecentMessagesParams) ([]*sqlc.Message, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListRecentMessages", ctx, arg)
	ret0, _ := ret[0].([]*sqlc.Message)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListRecentMessages indicates an expected call of ListRecentMessages.
func (mr *MockQuerierMockRecorder) ListRecentMessages(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListRecentMessages", reflect.TypeOf((*MockQuerier)(nil).ListRecentMessages), ctx, arg)
}

// ListRecurring mocks base method.
func (m *MockQuerier) ListRecurring(ctx context.Context) ([]*sqlc.Memory, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListRecurring", ctx)
	ret0, _ := ret[0].([]*sqlc.Memory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListRecurring indicates an expected call of ListRecurring.
func (mr *MockQuerierMockRecorder) ListRecurring(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListRecurring", reflect.TypeOf((*MockQuerier)(nil).ListRecurring), ctx)
}

// ListSystemSettings mocks base method.
func (m *MockQuerier) ListSystemSettings(ctx context.Context) ([]*sqlc.SystemSetting, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListSystemSettings", ctx)
	ret0, _ := ret[0].([]*sqlc.SystemSetting)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListSystemSettings indicates an expected call of ListSystemSettings.
func (mr *MockQuerierMockRecorder) ListSystemSettings(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListSystemSettings", reflect.TypeOf((*MockQuerier)(nil).ListSystemSettings), ctx)
}

// ListSystemSettingsByPrefix mocks base method.
func (m *MockQuerier) ListSystemSettingsByPrefix(ctx context.Context, prefix pgtype.Text) ([]*sqlc.SystemSetting, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListSystemSettingsByPrefix", ctx, prefix)
	ret0, _ := ret[0].([]*sqlc.SystemSetting)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListSystemSettingsByPrefix indicates an expected call of ListSystemSettingsByPrefix.
func (mr *MockQuerierMockRecorder) ListSystemSettingsByPrefix(ctx, prefix any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListSystemSettingsByPrefix", reflect.TypeOf((*MockQuerier)(nil).ListSystemSettingsByPrefix), ctx, prefix)
}

// ListTasks mocks base method.
func (m *MockQuerier) ListTasks(ctx context.Context, arg sqlc.ListTasksParams) ([]*sqlc.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTasks", ctx, arg)
	ret0, _ := ret[0].([]*sqlc.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTasks indicates an expected call of ListTasks.
func (mr *MockQuerierMockRecorder) ListTasks(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTasks", reflect.TypeOf((*MockQuerier)(nil).ListTasks), ctx, arg)
}

// ListTasksByLabel mocks base method.
func (m *MockQuerier) ListTasksByLabel(ctx context.Context, arg sqlc.ListTasksByLabelParams) ([]*sqlc.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTasksByLabel", ctx, arg)
	ret0, _ := ret[0].([]*sqlc.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTasksByLabel indicates an expected call of ListTasksByLabel.
func (mr *MockQuerierMockRecorder) ListTasksByLabel(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTasksByLabel", reflect.TypeOf((*MockQuerier)(nil).ListTasksByLabel), ctx, arg)
}

// ListTasksDueToday mocks base method.
func (m *MockQuerier) ListTasksDueToday(ctx context.Context) ([]*sqlc.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTasksDueToday", ctx)
	ret0, _ := ret[0].([]*sqlc.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTasksDueToday indicates an expected call of ListTasksDueToday.
func (mr *MockQuerierMockRecorder) ListTasksDueToday(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTasksDueToday", reflect.TypeOf((*MockQuerier)(nil).ListTasksDueToday), ctx)
}

// ListToolExecutions mocks base method.
func (m *MockQuerier) ListToolExecutions(ctx context.Context, arg sqlc.ListToolExecutionsParams) ([]*sqlc.ToolExecution, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListToolExecutions", ctx, arg)
	ret0, _ := ret[0].([]*sqlc.ToolExecution)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListToolExecutions indicates an expected call of ListToolExecutions.
func (mr *MockQuerierMockRecorder) ListToolExecutions(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListToolExecutions", reflect.TypeOf((*MockQuerier)(nil).ListToolExecutions), ctx, arg)
}

// ListToolExecutionsByConversation mocks base method.
func (m *MockQuerier) ListToolExecutionsByConversation(ctx context.Context, arg sqlc.ListToolExecutionsByConversationParams) ([]*sqlc.ToolExecution, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListToolExecutionsByConversation", ctx, arg)
	ret0, _ := ret[0].([]*sqlc.ToolExecution)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListToolExecutionsByConversation indicates an expected call of ListToolExecutionsByConversation.
func (mr *MockQuerierMockRecorder) ListToolExecutionsByConversation(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListToolExecutionsByConversation", reflect.TypeOf((*MockQuerier)(nil).ListToolExecutionsByConversation), ctx, arg)
}

// RemoveTaskLabel mocks base method.
func (m *MockQuerier) RemoveTaskLabel(ctx context.Context, arg sqlc.RemoveTaskLabelParams) (*sqlc.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveTaskLabel", ctx, arg)
	ret0, _ := ret[0].(*sqlc.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemoveTaskLabel indicates an expected call of RemoveTaskLabel.
func (mr *MockQuerierMockRecorder) RemoveTaskLabel(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveTaskLabel", reflect.TypeOf((*MockQuerier)(nil).RemoveTaskLabel), ctx, arg)
}

// SearchByAttributes mocks base method.
func (m *MockQuerier) SearchByAttributes(ctx context.Context, arg sqlc.SearchByAttributesParams) ([]*sqlc.Memory, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchByAttributes", ctx, arg)
	ret0, _ := ret[0].([]*sqlc.Memory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchByAttributes indicates an expected call of SearchByAttributes.
func (mr *MockQuerierMockRecorder) SearchByAttributes(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchByAttributes", reflect.TypeOf((*MockQuerier)(nil).SearchByAttributes), ctx, arg)
}

// SearchConversationMessages mocks base method.
func (m *MockQuerier) SearchConversationMessages(ctx context.Context, arg sqlc.SearchConversationMessagesParams) ([]*sqlc.SearchConversationMessagesRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchConversationMessages", ctx, arg)
	ret0, _ := ret[0].([]*sqlc.SearchConversationMessagesRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchConversationMessages indicates an expected call of SearchConversationMessages.
func (mr *MockQuerierMockRecorder) SearchConversationMessages(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchConversationMessages", reflect.TypeOf((*MockQuerier)(nil).SearchConversationMessages), ctx, arg)
}

// SearchConversations mocks base method.
func (m *MockQuerier) SearchConversations(ctx context.Context, arg sqlc.SearchConversationsParams) ([]*sqlc.SearchConversationsRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchConversations", ctx, arg)
	ret0, _ := ret[0].([]*sqlc.SearchConversationsRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchConversations indicates an expected call of SearchConversations.
func (mr *MockQuerierMockRecorder) SearchConversations(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchConversations", reflect.TypeOf((*MockQuerier)(nil).SearchConversations), ctx, arg)
}

// SearchConversationsWithHighlight mocks base method.
func (m *MockQuerier) SearchConversationsWithHighlight(ctx context.Context, arg sqlc.SearchConversationsWithHighlightParams) ([]*sqlc.SearchConversationsWithHighlightRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchConversationsWithHighlight", ctx, arg)
	ret0, _ := ret[0].([]*sqlc.SearchConversationsWithHighlightRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchConversationsWithHighlight indicates an expected call of SearchConversationsWithHighlight.
func (mr *MockQuerierMockRecorder) SearchConversationsWithHighlight(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchConversationsWithHighlight", reflect.TypeOf((*MockQuerier)(nil).SearchConversationsWithHighlight), ctx, arg)
}

// SearchMemories mocks base method.
func (m *MockQuerier) SearchMemories(ctx context.Context, arg sqlc.SearchMemoriesParams) ([]*sqlc.SearchMemoriesRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchMemories", ctx, arg)
	ret0, _ := ret[0].([]*sqlc.SearchMemoriesRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchMemories indicates an expected call of SearchMemories.
func (mr *MockQuerierMockRecorder) SearchMemories(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchMemories", reflect.TypeOf((*MockQuerier)(nil).SearchMemories), ctx, arg)
}

// SearchMessages mocks base method.
func (m *MockQuerier) SearchMessages(ctx context.Context, arg sqlc.SearchMessagesParams) ([]*sqlc.Message, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchMessages", ctx, arg)
	ret0, _ := ret[0].([]*sqlc.Message)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchMessages indicates an expected call of SearchMessages.
func (mr *MockQuerierMockRecorder) SearchMessages(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchMessages", reflect.TypeOf((*MockQuerier)(nil).SearchMessages), ctx, arg)
}

// SearchSimilarEmbeddings mocks base method.
func (m *MockQuerier) SearchSimilarEmbeddings(ctx context.Context, arg sqlc.SearchSimilarEmbeddingsParams) ([]*sqlc.SearchSimilarEmbeddingsRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchSimilarEmbeddings", ctx, arg)
	ret0, _ := ret[0].([]*sqlc.SearchSimilarEmbeddingsRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchSimilarEmbeddings indicates an expected call of SearchSimilarEmbeddings.
func (mr *MockQuerierMockRecorder) SearchSimilarEmbeddings(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchSimilarEmbeddings", reflect.TypeOf((*MockQuerier)(nil).SearchSimilarEmbeddings), ctx, arg)
}

// SearchSimilarWithLimit mocks base method.
func (m *MockQuerier) SearchSimilarWithLimit(ctx context.Context, arg sqlc.SearchSimilarWithLimitParams) ([]*sqlc.SearchSimilarWithLimitRow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchSimilarWithLimit", ctx, arg)
	ret0, _ := ret[0].([]*sqlc.SearchSimilarWithLimitRow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchSimilarWithLimit indicates an expected call of SearchSimilarWithLimit.
func (mr *MockQuerierMockRecorder) SearchSimilarWithLimit(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchSimilarWithLimit", reflect.TypeOf((*MockQuerier)(nil).SearchSimilarWithLimit), ctx, arg)
}

// SearchTasks mocks base method.
func (m *MockQuerier) SearchTasks(ctx context.Context, arg sqlc.SearchTasksParams) ([]*sqlc.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchTasks", ctx, arg)
	ret0, _ := ret[0].([]*sqlc.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchTasks indicates an expected call of SearchTasks.
func (mr *MockQuerierMockRecorder) SearchTasks(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchTasks", reflect.TypeOf((*MockQuerier)(nil).SearchTasks), ctx, arg)
}

// SetEmbedding mocks base method.
func (m *MockQuerier) SetEmbedding(ctx context.Context, arg sqlc.SetEmbeddingParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetEmbedding", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetEmbedding indicates an expected call of SetEmbedding.
func (mr *MockQuerierMockRecorder) SetEmbedding(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetEmbedding", reflect.TypeOf((*MockQuerier)(nil).SetEmbedding), ctx, arg)
}

// SetSystemSetting mocks base method.
func (m *MockQuerier) SetSystemSetting(ctx context.Context, arg sqlc.SetSystemSettingParams) (*sqlc.SystemSetting, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetSystemSetting", ctx, arg)
	ret0, _ := ret[0].(*sqlc.SystemSetting)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetSystemSetting indicates an expected call of SetSystemSetting.
func (mr *MockQuerierMockRecorder) SetSystemSetting(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSystemSetting", reflect.TypeOf((*MockQuerier)(nil).SetSystemSetting), ctx, arg)
}

// StartToolExecution mocks base method.
func (m *MockQuerier) StartToolExecution(ctx context.Context, id pgtype.UUID) (*sqlc.ToolExecution, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartToolExecution", ctx, id)
	ret0, _ := ret[0].(*sqlc.ToolExecution)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartToolExecution indicates an expected call of StartToolExecution.
func (mr *MockQuerierMockRecorder) StartToolExecution(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartToolExecution", reflect.TypeOf((*MockQuerier)(nil).StartToolExecution), ctx, id)
}

// UpdateConversation mocks base method.
func (m *MockQuerier) UpdateConversation(ctx context.Context, arg sqlc.UpdateConversationParams) (*sqlc.Conversation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateConversation", ctx, arg)
	ret0, _ := ret[0].(*sqlc.Conversation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateConversation indicates an expected call of UpdateConversation.
func (mr *MockQuerierMockRecorder) UpdateConversation(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateConversation", reflect.TypeOf((*MockQuerier)(nil).UpdateConversation), ctx, arg)
}

// UpdateEmbedding mocks base method.
func (m *MockQuerier) UpdateEmbedding(ctx context.Context, arg sqlc.UpdateEmbeddingParams) (*sqlc.Embedding, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateEmbedding", ctx, arg)
	ret0, _ := ret[0].(*sqlc.Embedding)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateEmbedding indicates an expected call of UpdateEmbedding.
func (mr *MockQuerierMockRecorder) UpdateEmbedding(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEmbedding", reflect.TypeOf((*MockQuerier)(nil).UpdateEmbedding), ctx, arg)
}

// UpdateJob mocks base method.
func (m *MockQuerier) UpdateJob(ctx context.Context, arg sqlc.UpdateJobParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateJob", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateJob indicates an expected call of UpdateJob.
func (mr *MockQuerierMockRecorder) UpdateJob(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateJob", reflect.TypeOf((*MockQuerier)(nil).UpdateJob), ctx, arg)
}

// UpdateMemory mocks base method.
func (m *MockQuerier) UpdateMemory(ctx context.Context, arg sqlc.UpdateMemoryParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateMemory", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateMemory indicates an expected call of UpdateMemory.
func (mr *MockQuerierMockRecorder) UpdateMemory(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateMemory", reflect.TypeOf((*MockQuerier)(nil).UpdateMemory), ctx, arg)
}

// UpdateRelation mocks base method.
func (m *MockQuerier) UpdateRelation(ctx context.Context, arg sqlc.UpdateRelationParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRelation", ctx, arg)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRelation indicates an expected call of UpdateRelation.
func (mr *MockQuerierMockRecorder) UpdateRelation(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRelation", reflect.TypeOf((*MockQuerier)(nil).UpdateRelation), ctx, arg)
}

// UpdateSystemSetting mocks base method.
func (m *MockQuerier) UpdateSystemSetting(ctx context.Context, arg sqlc.UpdateSystemSettingParams) (*sqlc.SystemSetting, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSystemSetting", ctx, arg)
	ret0, _ := ret[0].(*sqlc.SystemSetting)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateSystemSetting indicates an expected call of UpdateSystemSetting.
func (mr *MockQuerierMockRecorder) UpdateSystemSetting(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSystemSetting", reflect.TypeOf((*MockQuerier)(nil).UpdateSystemSetting), ctx, arg)
}

// UpdateTask mocks base method.
func (m *MockQuerier) UpdateTask(ctx context.Context, arg sqlc.UpdateTaskParams) (*sqlc.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTask", ctx, arg)
	ret0, _ := ret[0].(*sqlc.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTask indicates an expected call of UpdateTask.
func (mr *MockQuerierMockRecorder) UpdateTask(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTask", reflect.TypeOf((*MockQuerier)(nil).UpdateTask), ctx, arg)
}

// UpdateTaskLabels mocks base method.
func (m *MockQuerier) UpdateTaskLabels(ctx context.Context, arg sqlc.UpdateTaskLabelsParams) (*sqlc.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTaskLabels", ctx, arg)
	ret0, _ := ret[0].(*sqlc.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTaskLabels indicates an expected call of UpdateTaskLabels.
func (mr *MockQuerierMockRecorder) UpdateTaskLabels(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTaskLabels", reflect.TypeOf((*MockQuerier)(nil).UpdateTaskLabels), ctx, arg)
}

// UpdateToolExecution mocks base method.
func (m *MockQuerier) UpdateToolExecution(ctx context.Context, arg sqlc.UpdateToolExecutionParams) (*sqlc.ToolExecution, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateToolExecution", ctx, arg)
	ret0, _ := ret[0].(*sqlc.ToolExecution)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateToolExecution indicates an expected call of UpdateToolExecution.
func (mr *MockQuerierMockRecorder) UpdateToolExecution(ctx, arg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateToolExecution", reflect.TypeOf((*MockQuerier)(nil).UpdateToolExecution), ctx, arg)
}
