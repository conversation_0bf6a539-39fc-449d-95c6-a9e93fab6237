package database

import (
	"context"
	"fmt"
	"testing"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/koopa0/assistant-go/internal/storage/database/sqlc"
	"github.com/koopa0/assistant-go/internal/testing/testdb"
	"github.com/pgvector/pgvector-go"
)

// BenchmarkMemoryInsert measures memory insertion performance
func BenchmarkMemoryInsert(b *testing.B) {
	ctx := context.Background()
	db := testdb.Setup(b, ctx)
	defer db.Close()

	queries := New(db)

	// Prepare test data
	embedding := make([]float32, 768)
	for i := range embedding {
		embedding[i] = float32(i) / 768.0
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		params := sqlc.CreateMemoryParams{
			UserID:     fmt.Sprintf("bench-user-%d", i%10),
			SemanticID: fmt.Sprintf("bench-memory-%d", i),
			Type:       "fact",
			Content:    fmt.Sprintf("Benchmark memory content %d", i),
			Embedding:  pgvector.NewVector(embedding),
			Confidence: 0.95,
			Status:     "active",
		}

		_, err := queries.CreateMemory(ctx, params)
		if err != nil {
			b.Fatalf("failed to create memory: %v", err)
		}
	}
}

// BenchmarkMemoryVectorSearch benchmarks vector similarity search
func BenchmarkMemoryVectorSearch(b *testing.B) {
	ctx := context.Background()
	db := testdb.Setup(b, ctx)
	defer db.Close()

	queries := New(db)

	// Insert test memories with different embeddings
	for i := 0; i < 1000; i++ {
		embedding := make([]float32, 768)
		for j := range embedding {
			embedding[j] = float32((i+j)%100) / 100.0
		}

		params := sqlc.CreateMemoryParams{
			UserID:     "bench-user",
			SemanticID: fmt.Sprintf("bench-memory-%d", i),
			Type:       "fact",
			Content:    fmt.Sprintf("Test memory %d for vector search", i),
			Embedding:  pgvector.NewVector(embedding),
			Confidence: 0.9,
			Status:     "active",
		}

		_, err := queries.CreateMemory(ctx, params)
		if err != nil {
			b.Fatalf("failed to create memory: %v", err)
		}
	}

	// Create search embedding
	searchEmbedding := make([]float32, 768)
	for i := range searchEmbedding {
		searchEmbedding[i] = 0.5
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		params := sqlc.SearchMemoriesParams{
			UserID:    "bench-user",
			Embedding: pgtype.FlatArray[float32]{searchEmbedding},
			Limit:     10,
		}

		_, err := queries.SearchMemories(ctx, params)
		if err != nil {
			b.Fatalf("failed to search memories: %v", err)
		}
	}
}

// BenchmarkMemoryTextSearch benchmarks text-based memory search
func BenchmarkMemoryTextSearch(b *testing.B) {
	ctx := context.Background()
	db := testdb.Setup(b, ctx)
	defer db.Close()

	queries := New(db)

	// Insert test memories
	keywords := []string{"golang", "testing", "benchmark", "performance", "database"}
	for i := 0; i < 500; i++ {
		params := sqlc.CreateMemoryParams{
			UserID:     "bench-user",
			SemanticID: fmt.Sprintf("bench-memory-%d", i),
			Type:       "fact",
			Content:    fmt.Sprintf("Memory about %s and %s", keywords[i%len(keywords)], keywords[(i+1)%len(keywords)]),
			Confidence: 0.8,
			Status:     "active",
		}

		_, err := queries.CreateMemory(ctx, params)
		if err != nil {
			b.Fatalf("failed to create memory: %v", err)
		}
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		searchText := keywords[i%len(keywords)]
		params := sqlc.SearchMemoriesParams{
			UserID: "bench-user",
			Text:   &searchText,
			Limit:  10,
		}

		_, err := queries.SearchMemories(ctx, params)
		if err != nil {
			b.Fatalf("failed to search memories: %v", err)
		}
	}
}

// BenchmarkConversationOperations benchmarks conversation CRUD operations
func BenchmarkConversationOperations(b *testing.B) {
	ctx := context.Background()
	db := testdb.Setup(b, ctx)
	defer db.Close()

	queries := New(db)

	b.Run("Create", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			params := sqlc.CreateConversationParams{
				UserID: fmt.Sprintf("bench-user-%d", i%10),
				Title:  fmt.Sprintf("Benchmark conversation %d", i),
			}

			_, err := queries.CreateConversation(ctx, params)
			if err != nil {
				b.Fatalf("failed to create conversation: %v", err)
			}
		}
	})

	// Create a conversation for update/get benchmarks
	conv, err := queries.CreateConversation(ctx, sqlc.CreateConversationParams{
		UserID: "bench-user",
		Title:  "Test conversation",
	})
	if err != nil {
		b.Fatalf("failed to create test conversation: %v", err)
	}

	b.Run("Update", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			params := sqlc.UpdateConversationParams{
				ID:    conv.ID,
				Title: fmt.Sprintf("Updated title %d", i),
			}

			_, err := queries.UpdateConversation(ctx, params)
			if err != nil {
				b.Fatalf("failed to update conversation: %v", err)
			}
		}
	})

	b.Run("Get", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := queries.GetConversation(ctx, conv.ID)
			if err != nil {
				b.Fatalf("failed to get conversation: %v", err)
			}
		}
	})
}

// BenchmarkMessageOperations benchmarks message operations
func BenchmarkMessageOperations(b *testing.B) {
	ctx := context.Background()
	db := testdb.Setup(b, ctx)
	defer db.Close()

	queries := New(db)

	// Create a conversation
	conv, err := queries.CreateConversation(ctx, sqlc.CreateConversationParams{
		UserID: "bench-user",
		Title:  "Benchmark conversation",
	})
	if err != nil {
		b.Fatalf("failed to create conversation: %v", err)
	}

	b.Run("CreateMessage", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			params := sqlc.CreateMessageParams{
				ConversationID: conv.ID,
				Role:           "user",
				Content:        fmt.Sprintf("Benchmark message %d with some content that might be fairly long in real usage", i),
			}

			_, err := queries.CreateMessage(ctx, params)
			if err != nil {
				b.Fatalf("failed to create message: %v", err)
			}
		}
	})

	b.Run("GetMessages", func(b *testing.B) {
		// Insert some messages first
		for i := 0; i < 100; i++ {
			params := sqlc.CreateMessageParams{
				ConversationID: conv.ID,
				Role:           "user",
				Content:        fmt.Sprintf("Message %d", i),
			}
			_, err := queries.CreateMessage(ctx, params)
			if err != nil {
				b.Fatalf("failed to create message: %v", err)
			}
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			params := sqlc.GetMessagesParams{
				ConversationID: conv.ID,
				Limit:          20,
			}

			_, err := queries.GetMessages(ctx, params)
			if err != nil {
				b.Fatalf("failed to get messages: %v", err)
			}
		}
	})
}

// BenchmarkMemoryHistoryOperations benchmarks memory history tracking
func BenchmarkMemoryHistoryOperations(b *testing.B) {
	ctx := context.Background()
	db := testdb.Setup(b, ctx)
	defer db.Close()

	queries := New(db)

	// Create a memory
	mem, err := queries.CreateMemory(ctx, sqlc.CreateMemoryParams{
		UserID:     "bench-user",
		SemanticID: "bench-memory",
		Type:       "fact",
		Content:    "Benchmark memory",
		Confidence: 0.9,
		Status:     "active",
	})
	if err != nil {
		b.Fatalf("failed to create memory: %v", err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		params := sqlc.CreateMemoryHistoryParams{
			MemoryID:  mem.ID,
			UserID:    "bench-user",
			Action:    "UPDATE",
			OldValues: []byte(`{"content": "old value"}`),
			NewValues: []byte(fmt.Sprintf(`{"content": "new value %d"}`, i)),
		}

		_, err := queries.CreateMemoryHistory(ctx, params)
		if err != nil {
			b.Fatalf("failed to create memory history: %v", err)
		}
	}
}

// BenchmarkTransactionOperations benchmarks operations within transactions
func BenchmarkTransactionOperations(b *testing.B) {
	ctx := context.Background()
	db := testdb.Setup(b, ctx)
	defer db.Close()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		tx, err := db.Begin(ctx)
		if err != nil {
			b.Fatalf("failed to begin transaction: %v", err)
		}

		qtx := New(tx)

		// Create conversation
		conv, err := qtx.CreateConversation(ctx, sqlc.CreateConversationParams{
			UserID: fmt.Sprintf("bench-user-%d", i),
			Title:  "Transaction benchmark",
		})
		if err != nil {
			tx.Rollback(ctx)
			b.Fatalf("failed to create conversation: %v", err)
		}

		// Create messages
		for j := 0; j < 5; j++ {
			_, err = qtx.CreateMessage(ctx, sqlc.CreateMessageParams{
				ConversationID: conv.ID,
				Role:           "user",
				Content:        fmt.Sprintf("Message %d", j),
			})
			if err != nil {
				tx.Rollback(ctx)
				b.Fatalf("failed to create message: %v", err)
			}
		}

		if err := tx.Commit(ctx); err != nil {
			b.Fatalf("failed to commit transaction: %v", err)
		}
	}
}

// BenchmarkBulkOperations benchmarks bulk insert/update scenarios
func BenchmarkBulkOperations(b *testing.B) {
	ctx := context.Background()
	db := testdb.Setup(b, ctx)
	defer db.Close()

	queries := New(db)

	b.Run("BulkMemoryInsert", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			// Simulate bulk insert of memories from a conversation
			tx, _ := db.Begin(ctx)
			qtx := New(tx)

			for j := 0; j < 10; j++ {
				params := sqlc.CreateMemoryParams{
					UserID:     "bench-user",
					SemanticID: fmt.Sprintf("bulk-%d-%d", i, j),
					Type:       "fact",
					Content:    fmt.Sprintf("Bulk memory %d-%d", i, j),
					Confidence: 0.85,
					Status:     "active",
				}

				_, err := qtx.CreateMemory(ctx, params)
				if err != nil {
					tx.Rollback(ctx)
					b.Fatalf("failed to create memory: %v", err)
				}
			}

			tx.Commit(ctx)
		}
	})
}
