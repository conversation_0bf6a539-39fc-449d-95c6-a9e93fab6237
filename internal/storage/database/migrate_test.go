package database

import (
	"context"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/koopa0/assistant-go/internal/platform/logger"
)

// TestDB_RunMigrations tests the migration functionality
func TestDB_RunMigrations(t *testing.T) {
	t.Run("migrations with real database", func(t *testing.T) {
		// Skip if no test database
		testDBURL := os.Getenv("TEST_DATABASE_URL")
		if testDBURL == "" {
			t.Skip("TEST_DATABASE_URL not set")
		}

		ctx := context.Background()
		config := &Config{
			DSN: testDBURL,
		}

		// Create DB instance
		db, err := NewDB(ctx, config, WithLogger(logger.NewNoOpLogger()))
		require.NoError(t, err)
		defer db.Close()

		// Run migrations
		err = db.RunMigrations()
		assert.NoError(t, err)

		// Run migrations again - should handle ErrNoChange
		err = db.RunMigrations()
		assert.NoError(t, err)
	})

	t.Run("migrations with invalid DSN", func(t *testing.T) {
		db := &DB{
			config: &Config{
				DSN: "invalid://dsn",
			},
			logger: logger.NewNoOpLogger(),
		}

		err := db.RunMigrations()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to create migrator")
	})

	t.Run("migrations with nil logger", func(t *testing.T) {
		// This tests that migrations work even without a logger
		testDBURL := os.Getenv("TEST_DATABASE_URL")
		if testDBURL == "" {
			t.Skip("TEST_DATABASE_URL not set")
		}

		db := &DB{
			config: &Config{
				DSN: testDBURL,
			},
			logger: nil, // Explicitly set to nil
		}

		// Should not panic
		assert.NotPanics(t, func() {
			_ = db.RunMigrations()
		})
	})
}

// TestMigrationFiles tests that migration files are properly embedded
func TestMigrationFiles(t *testing.T) {
	// Check that migrations directory exists in embed
	entries, err := migrations.ReadDir("migrations")
	assert.NoError(t, err)
	assert.NotEmpty(t, entries, "migrations directory should contain files")

	// Verify we have both up and down migrations
	var hasUp, hasDown bool
	for _, entry := range entries {
		name := entry.Name()
		if len(name) > 3 {
			if name[len(name)-6:] == "up.sql" {
				hasUp = true
			} else if name[len(name)-8:] == "down.sql" {
				hasDown = true
			}
		}
	}

	assert.True(t, hasUp, "should have at least one up migration")
	assert.True(t, hasDown, "should have at least one down migration")
}

// TestMigrationContent tests that migration files have valid content
func TestMigrationContent(t *testing.T) {
	entries, err := migrations.ReadDir("migrations")
	require.NoError(t, err)

	for _, entry := range entries {
		if entry.IsDir() {
			continue
		}

		t.Run(entry.Name(), func(t *testing.T) {
			content, err := migrations.ReadFile("migrations/" + entry.Name())
			assert.NoError(t, err)
			assert.NotEmpty(t, content, "migration file should not be empty")

			// Basic validation - file should contain SQL
			contentStr := string(content)
			if entry.Name()[len(entry.Name())-6:] == "up.sql" {
				// Up migrations typically create tables/indexes
				assert.Contains(t, contentStr, "CREATE", "up migration should contain CREATE statements")
			} else if entry.Name()[len(entry.Name())-8:] == "down.sql" {
				// Down migrations typically drop tables/indexes
				assert.Contains(t, contentStr, "DROP", "down migration should contain DROP statements")
			}
		})
	}
}
