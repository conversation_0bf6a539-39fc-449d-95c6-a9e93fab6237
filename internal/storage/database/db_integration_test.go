//go:build integration
// +build integration

package database

import (
	"context"
	"errors"
	"os"
	"sync"
	"testing"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/koopa0/assistant-go/internal/platform/logger"
)

// TestDB_WithTx_Integration tests transaction handling with real database
func TestDB_WithTx_Integration(t *testing.T) {
	testDBURL := os.Getenv("TEST_DATABASE_URL")
	if testDBURL == "" {
		t.Skip("TEST_DATABASE_URL not set")
	}

	ctx := context.Background()
	config := &Config{
		DSN: testDBURL,
	}

	db, err := NewDB(ctx, config, WithLogger(logger.NewNoOpLogger()))
	require.NoError(t, err)
	defer db.Close()

	t.Run("successful transaction", func(t *testing.T) {
		executed := false
		err := db.WithTx(ctx, func(tx pgx.Tx) error {
			executed = true
			// Do a simple query to verify transaction works
			var result int
			err := tx.QueryRow(ctx, "SELECT 1").Scan(&result)
			assert.NoError(t, err)
			assert.Equal(t, 1, result)
			return nil
		})

		assert.NoError(t, err)
		assert.True(t, executed)
	})

	t.Run("transaction rollback on error", func(t *testing.T) {
		// Create a test table
		_, err := db.pool.Exec(ctx, `
			CREATE TABLE IF NOT EXISTS test_rollback (
				id SERIAL PRIMARY KEY,
				value TEXT NOT NULL
			)
		`)
		require.NoError(t, err)

		// Clean up before test
		_, _ = db.pool.Exec(ctx, "DELETE FROM test_rollback")

		// Insert should be rolled back
		testErr := errors.New("test error")
		err = db.WithTx(ctx, func(tx pgx.Tx) error {
			_, err := tx.Exec(ctx, "INSERT INTO test_rollback (value) VALUES ($1)", "should_rollback")
			require.NoError(t, err)
			return testErr
		})

		assert.Error(t, err)
		assert.Equal(t, testErr, err)

		// Verify no rows were inserted
		var count int
		err = db.pool.QueryRow(ctx, "SELECT COUNT(*) FROM test_rollback").Scan(&count)
		require.NoError(t, err)
		assert.Equal(t, 0, count)

		// Clean up
		_, _ = db.pool.Exec(ctx, "DROP TABLE test_rollback")
	})

	t.Run("transaction commit", func(t *testing.T) {
		// Create a test table
		_, err := db.pool.Exec(ctx, `
			CREATE TABLE IF NOT EXISTS test_commit (
				id SERIAL PRIMARY KEY,
				value TEXT NOT NULL
			)
		`)
		require.NoError(t, err)

		// Clean up before test
		_, _ = db.pool.Exec(ctx, "DELETE FROM test_commit")

		// Insert should be committed
		err = db.WithTx(ctx, func(tx pgx.Tx) error {
			_, err := tx.Exec(ctx, "INSERT INTO test_commit (value) VALUES ($1)", "should_commit")
			return err
		})

		assert.NoError(t, err)

		// Verify row was inserted
		var count int
		err = db.pool.QueryRow(ctx, "SELECT COUNT(*) FROM test_commit").Scan(&count)
		require.NoError(t, err)
		assert.Equal(t, 1, count)

		// Clean up
		_, _ = db.pool.Exec(ctx, "DROP TABLE test_commit")
	})

	t.Run("panic recovery", func(t *testing.T) {
		// Create a test table
		_, err := db.pool.Exec(ctx, `
			CREATE TABLE IF NOT EXISTS test_panic (
				id SERIAL PRIMARY KEY,
				value TEXT NOT NULL
			)
		`)
		require.NoError(t, err)

		// Clean up before test
		_, _ = db.pool.Exec(ctx, "DELETE FROM test_panic")

		// Transaction should be rolled back on panic
		assert.Panics(t, func() {
			_ = db.WithTx(ctx, func(tx pgx.Tx) error {
				_, _ = tx.Exec(ctx, "INSERT INTO test_panic (value) VALUES ($1)", "should_rollback_on_panic")
				panic("test panic")
			})
		})

		// Verify no rows were inserted
		var count int
		err = db.pool.QueryRow(ctx, "SELECT COUNT(*) FROM test_panic").Scan(&count)
		require.NoError(t, err)
		assert.Equal(t, 0, count)

		// Clean up
		_, _ = db.pool.Exec(ctx, "DROP TABLE test_panic")
	})
}

// TestDB_ConcurrentTransactions tests concurrent transaction handling
func TestDB_ConcurrentTransactions(t *testing.T) {
	testDBURL := os.Getenv("TEST_DATABASE_URL")
	if testDBURL == "" {
		t.Skip("TEST_DATABASE_URL not set")
	}

	ctx := context.Background()
	config := &Config{
		DSN:      testDBURL,
		MaxConns: 10,
	}

	db, err := NewDB(ctx, config)
	require.NoError(t, err)
	defer db.Close()

	// Create a test table
	_, err = db.pool.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS test_concurrent (
			id SERIAL PRIMARY KEY,
			value INT NOT NULL
		)
	`)
	require.NoError(t, err)

	// Clean up before test
	_, _ = db.pool.Exec(ctx, "DELETE FROM test_concurrent")

	// Run concurrent transactions
	const numGoroutines = 10
	var wg sync.WaitGroup
	wg.Add(numGoroutines)

	errors := make(chan error, numGoroutines)

	for i := 0; i < numGoroutines; i++ {
		go func(n int) {
			defer wg.Done()
			err := db.WithTx(ctx, func(tx pgx.Tx) error {
				_, err := tx.Exec(ctx, "INSERT INTO test_concurrent (value) VALUES ($1)", n)
				return err
			})
			if err != nil {
				errors <- err
			}
		}(i)
	}

	wg.Wait()
	close(errors)

	// Check for errors
	for err := range errors {
		t.Errorf("Transaction error: %v", err)
	}

	// Verify all rows were inserted
	var count int
	err = db.pool.QueryRow(ctx, "SELECT COUNT(*) FROM test_concurrent").Scan(&count)
	require.NoError(t, err)
	assert.Equal(t, numGoroutines, count)

	// Clean up
	_, _ = db.pool.Exec(ctx, "DROP TABLE test_concurrent")
}

// TestDB_RunMigration_Integration tests migration execution with real database
func TestDB_RunMigration_Integration(t *testing.T) {
	testDBURL := os.Getenv("TEST_DATABASE_URL")
	if testDBURL == "" {
		t.Skip("TEST_DATABASE_URL not set")
	}

	ctx := context.Background()
	config := &Config{
		DSN: testDBURL,
	}

	db, err := NewDB(ctx, config)
	require.NoError(t, err)
	defer db.Close()

	t.Run("successful migration", func(t *testing.T) {
		// Create a simple migration
		migration := `
			CREATE TABLE IF NOT EXISTS test_migration_table (
				id SERIAL PRIMARY KEY,
				created_at TIMESTAMP DEFAULT NOW()
			);
		`

		err := db.RunMigration(ctx, migration)
		assert.NoError(t, err)

		// Verify table was created
		var exists bool
		err = db.pool.QueryRow(ctx, `
			SELECT EXISTS (
				SELECT 1 FROM information_schema.tables 
				WHERE table_name = 'test_migration_table'
			)
		`).Scan(&exists)
		require.NoError(t, err)
		assert.True(t, exists)

		// Clean up
		_, _ = db.pool.Exec(ctx, "DROP TABLE IF EXISTS test_migration_table")
	})

	t.Run("migration error", func(t *testing.T) {
		// Invalid SQL should cause error
		migration := `CREATE TABLE invalid syntax;`

		err := db.RunMigration(ctx, migration)
		assert.Error(t, err)
	})
}

// TestDB_ConnectionPool tests connection pool behavior
func TestDB_ConnectionPool(t *testing.T) {
	testDBURL := os.Getenv("TEST_DATABASE_URL")
	if testDBURL == "" {
		t.Skip("TEST_DATABASE_URL not set")
	}

	ctx := context.Background()

	t.Run("pool statistics", func(t *testing.T) {
		config := &Config{
			DSN:      testDBURL,
			MaxConns: 5,
			MinConns: 2,
		}

		db, err := NewDB(ctx, config)
		require.NoError(t, err)
		defer db.Close()

		// Get initial stats
		stats := db.Stats()
		assert.NotNil(t, stats)
		assert.GreaterOrEqual(t, stats.IdleConns(), int32(0))
		assert.LessOrEqual(t, stats.TotalConns(), config.MaxConns)

		// Use some connections
		var wg sync.WaitGroup
		for i := 0; i < 3; i++ {
			wg.Add(1)
			go func() {
				defer wg.Done()
				err := db.Ping(ctx)
				assert.NoError(t, err)
			}()
		}
		wg.Wait()

		// Check stats after usage
		stats = db.Stats()
		assert.GreaterOrEqual(t, stats.AcquireCount(), int64(3))
	})

	t.Run("acquire and release", func(t *testing.T) {
		config := &Config{
			DSN:      testDBURL,
			MaxConns: 2,
		}

		db, err := NewDB(ctx, config)
		require.NoError(t, err)
		defer db.Close()

		// Acquire a connection
		conn, err := db.Acquire(ctx)
		require.NoError(t, err)
		assert.NotNil(t, conn)

		// Use the connection
		var result int
		err = conn.QueryRow(ctx, "SELECT 1").Scan(&result)
		assert.NoError(t, err)
		assert.Equal(t, 1, result)

		// Release the connection
		conn.Release()

		// Verify pool still works
		err = db.Ping(ctx)
		assert.NoError(t, err)
	})
}

// TestDB_Queries tests query execution methods
func TestDB_Queries(t *testing.T) {
	testDBURL := os.Getenv("TEST_DATABASE_URL")
	if testDBURL == "" {
		t.Skip("TEST_DATABASE_URL not set")
	}

	ctx := context.Background()
	config := &Config{
		DSN: testDBURL,
	}

	db, err := NewDB(ctx, config)
	require.NoError(t, err)
	defer db.Close()

	t.Run("QueryRow", func(t *testing.T) {
		var result int
		row := db.QueryRow(ctx, "SELECT 42")
		err := row.Scan(&result)
		assert.NoError(t, err)
		assert.Equal(t, 42, result)
	})

	t.Run("Query", func(t *testing.T) {
		rows, err := db.Query(ctx, "SELECT generate_series(1, 3) as num")
		require.NoError(t, err)
		defer rows.Close()

		var numbers []int
		for rows.Next() {
			var num int
			err := rows.Scan(&num)
			require.NoError(t, err)
			numbers = append(numbers, num)
		}

		assert.Equal(t, []int{1, 2, 3}, numbers)
	})

	t.Run("Exec", func(t *testing.T) {
		// Create a temporary table for testing
		_, err := db.pool.Exec(ctx, `
			CREATE TEMP TABLE test_exec (
				id SERIAL PRIMARY KEY,
				value TEXT
			)
		`)
		require.NoError(t, err)

		// Use Exec to insert data
		rows, err := db.Exec(ctx, "INSERT INTO test_exec (value) VALUES ($1)", "test_value")
		assert.NoError(t, err)
		assert.NotNil(t, rows)
	})
}

// TestDB_EdgeCases tests edge cases and error conditions
func TestDB_EdgeCases(t *testing.T) {
	t.Run("nil config", func(t *testing.T) {
		ctx := context.Background()
		db, err := NewDB(ctx, nil)
		assert.Error(t, err)
		assert.Nil(t, db)
		assert.Contains(t, err.Error(), "config is required")
	})

	t.Run("context cancellation", func(t *testing.T) {
		testDBURL := os.Getenv("TEST_DATABASE_URL")
		if testDBURL == "" {
			t.Skip("TEST_DATABASE_URL not set")
		}

		ctx, cancel := context.WithCancel(context.Background())
		config := &Config{
			DSN: testDBURL,
		}

		db, err := NewDB(ctx, config)
		require.NoError(t, err)
		defer db.Close()

		// Cancel context
		cancel()

		// Operations should fail with canceled context
		err = db.Ping(ctx)
		assert.Error(t, err)
		assert.ErrorIs(t, err, context.Canceled)
	})

	t.Run("timeout", func(t *testing.T) {
		testDBURL := os.Getenv("TEST_DATABASE_URL")
		if testDBURL == "" {
			t.Skip("TEST_DATABASE_URL not set")
		}

		ctx := context.Background()
		config := &Config{
			DSN: testDBURL,
		}

		db, err := NewDB(ctx, config)
		require.NoError(t, err)
		defer db.Close()

		// Create a very short timeout
		timeoutCtx, cancel := context.WithTimeout(ctx, 1*time.Nanosecond)
		defer cancel()

		// Wait to ensure timeout
		time.Sleep(10 * time.Millisecond)

		// Query should timeout
		_, err = db.Query(timeoutCtx, "SELECT pg_sleep(1)")
		assert.Error(t, err)
		assert.ErrorIs(t, err, context.DeadlineExceeded)
	})
}

// BenchmarkDB_Queries benchmarks query performance
func BenchmarkDB_Queries(b *testing.B) {
	testDBURL := os.Getenv("TEST_DATABASE_URL")
	if testDBURL == "" {
		b.Skip("TEST_DATABASE_URL not set")
	}

	ctx := context.Background()
	config := &Config{
		DSN:      testDBURL,
		MaxConns: 10,
	}

	db, err := NewDB(ctx, config)
	if err != nil {
		b.Fatal(err)
	}
	defer db.Close()

	b.Run("QueryRow", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			var result int
			row := db.QueryRow(ctx, "SELECT 1")
			_ = row.Scan(&result)
		}
	})

	b.Run("Ping", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = db.Ping(ctx)
		}
	})

	b.Run("WithTx", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = db.WithTx(ctx, func(tx pgx.Tx) error {
				var result int
				return tx.QueryRow(ctx, "SELECT 1").Scan(&result)
			})
		}
	})
}

// BenchmarkDB_ConcurrentQueries benchmarks concurrent query performance
func BenchmarkDB_ConcurrentQueries(b *testing.B) {
	testDBURL := os.Getenv("TEST_DATABASE_URL")
	if testDBURL == "" {
		b.Skip("TEST_DATABASE_URL not set")
	}

	ctx := context.Background()
	config := &Config{
		DSN:      testDBURL,
		MaxConns: 20,
	}

	db, err := NewDB(ctx, config)
	if err != nil {
		b.Fatal(err)
	}
	defer db.Close()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			var result int
			row := db.QueryRow(ctx, "SELECT 1")
			_ = row.Scan(&result)
		}
	})
}
