-- Improve memory scoring to better handle recency and conflicts

-- Drop and recreate the search function with improved scoring
DROP FUNCTION IF EXISTS search_memories_with_vector(
    p_embedding vector,
    p_types memory_type[],
    p_entities TEXT[],
    p_time_start TIMESTAMPTZ,
    p_time_end TIMESTAMPTZ,
    p_text TEXT,
    p_min_similarity REAL,
    p_max_results INTEGER
);

CREATE OR REPLACE FUNCTION search_memories_with_vector(
    p_embedding vector,
    p_types memory_type[],
    p_entities TEXT[],
    p_time_start TIMESTAMPTZ,
    p_time_end TIMESTAMPTZ,
    p_text TEXT,
    p_min_similarity REAL,
    p_max_results INTEGER
) RETURNS TABLE (
    id UUID,
    semantic_id TEXT,
    type memory_type,
    content TEXT,
    summary TEXT,
    entities JSONB,
    attributes JSONB,
    context JSONB,
    embedding vector,
    keywords TEXT[],
    confidence REAL,
    version INTEGER,
    access_count INTEGER,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    accessed_at TIMESTAMPTZ,
    is_active BOOLEAN,
    merged_from UUID[],
    similarity REAL,
    score REAL
) AS $$
BEGIN
    RETURN QUERY
    WITH base_search AS (
        SELECT 
            m.*,
            CASE 
                WHEN p_embedding IS NOT NULL AND m.embedding IS NOT NULL 
                THEN (1 - (m.embedding <=> p_embedding))::REAL
                ELSE 0.0
            END as calc_similarity
        FROM memories m
        WHERE m.is_active = true
            -- Type filter
            AND (p_types IS NULL OR CARDINALITY(p_types) = 0 OR m.type = ANY(p_types))
            -- Entity filter
            AND (p_entities IS NULL OR CARDINALITY(p_entities) = 0 OR 
                EXISTS (
                    SELECT 1 FROM jsonb_array_elements(m.entities) e
                    WHERE e->>'name' = ANY(p_entities)
                ))
            -- Time range filter
            AND (p_time_start IS NULL OR 
                (m.context->>'occurred_at')::timestamptz >= p_time_start OR
                (m.context->>'start_time')::timestamptz >= p_time_start)
            AND (p_time_end IS NULL OR 
                (m.context->>'occurred_at')::timestamptz <= p_time_end OR
                (m.context->>'end_time')::timestamptz <= p_time_end)
            -- Text search
            AND (p_text IS NULL OR p_text = '' OR
                m.content ILIKE '%' || p_text || '%' OR
                COALESCE(m.summary, '') ILIKE '%' || p_text || '%')
    ),
    scored_search AS (
        SELECT 
            bs.*,
            bs.calc_similarity as similarity,
            -- Improved scoring algorithm
            (
                -- Similarity weight (reduced from 0.6 to 0.5)
                bs.calc_similarity * 0.5 +
                
                -- Recency weight (increased and more granular)
                CASE 
                    WHEN bs.updated_at > NOW() - INTERVAL '1 hour' THEN 0.25
                    WHEN bs.updated_at > NOW() - INTERVAL '1 day' THEN 0.20
                    WHEN bs.updated_at > NOW() - INTERVAL '3 days' THEN 0.15
                    WHEN bs.updated_at > NOW() - INTERVAL '7 days' THEN 0.10
                    WHEN bs.updated_at > NOW() - INTERVAL '30 days' THEN 0.05
                    ELSE 0.02
                END +
                
                -- Access frequency weight
                CASE
                    WHEN bs.access_count > 20 THEN 0.15
                    WHEN bs.access_count > 10 THEN 0.10
                    WHEN bs.access_count > 5 THEN 0.05
                    ELSE 0.0
                END +
                
                -- Confidence weight
                COALESCE(bs.confidence, 0.8) * 0.1
            )::REAL as score
        FROM base_search bs
        WHERE bs.calc_similarity >= COALESCE(p_min_similarity, 0.0) OR p_embedding IS NULL
    )
    SELECT 
        ss.id, ss.semantic_id, ss.type, ss.content, ss.summary,
        ss.entities, ss.attributes, ss.context, ss.embedding,
        ss.keywords, ss.confidence, ss.version, ss.access_count,
        ss.created_at, ss.updated_at, ss.accessed_at, ss.is_active,
        ss.merged_from, ss.similarity, ss.score
    FROM scored_search ss
    ORDER BY ss.score DESC
    LIMIT COALESCE(p_max_results, 20);
END;
$$ LANGUAGE plpgsql;

-- Create an index to improve search performance on updated_at
CREATE INDEX IF NOT EXISTS idx_memories_updated_at 
ON memories(updated_at DESC) 
WHERE is_active = true;

-- Create a function to detect and mark conflicting memories
CREATE OR REPLACE FUNCTION find_conflicting_memories(
    p_activity TEXT,
    p_exclude_id UUID DEFAULT NULL
) RETURNS TABLE (
    id UUID,
    semantic_id TEXT,
    content TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    conflict_reason TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        m.id,
        m.semantic_id,
        m.content,
        m.created_at,
        m.updated_at,
        CASE
            WHEN m.semantic_id LIKE '%' || p_activity || '%' THEN 'Same activity with different schedule'
            WHEN m.content ILIKE '%' || p_activity || '%' THEN 'Content mentions same activity'
            ELSE 'Related memory'
        END as conflict_reason
    FROM memories m
    WHERE m.is_active = true
        AND m.type = 'schedule'
        AND (p_exclude_id IS NULL OR m.id != p_exclude_id)
        AND (
            m.semantic_id LIKE '%' || p_activity || '%' OR
            m.content ILIKE '%' || p_activity || '%' OR
            EXISTS (
                SELECT 1 FROM jsonb_array_elements(m.entities) e
                WHERE e->>'name' ILIKE '%' || p_activity || '%'
            )
        )
    ORDER BY m.updated_at DESC;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION find_conflicting_memories IS 
'Finds memories that might conflict with a given activity, useful for detecting schedule conflicts';