-- 007_memory_semantic_identity.down.sql
-- Revert semantic identity and versioning changes

-- Drop functions
DROP FUNCTION IF EXISTS find_similar_memories_by_topic;
DROP FUNCTION IF EXISTS merge_memories;
DROP FUNCTION IF EXISTS update_memory_atomic;
DROP FUNCTION IF EXISTS generate_semantic_id;

-- Drop memory versions table
DROP TABLE IF EXISTS memory_versions;

-- Drop indexes
DROP INDEX IF EXISTS idx_memories_semantic_id;
DROP INDEX IF EXISTS idx_memories_topic;
DROP INDEX IF EXISTS idx_memories_entity;
DROP INDEX IF EXISTS idx_memories_is_active;

-- Remove columns from memories table
ALTER TABLE memories 
DROP COLUMN IF EXISTS semantic_id,
DROP COLUMN IF EXISTS topic,
DROP COLUMN IF EXISTS entity,
DROP COLUMN IF EXISTS attributes,
DROP COLUMN IF EXISTS version,
DROP COLUMN IF EXISTS merged_from,
DROP COLUMN IF EXISTS is_active;