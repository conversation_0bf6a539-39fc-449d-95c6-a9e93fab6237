-- 008_structured_memory_system.down.sql
-- Rollback structured memory system

DROP TABLE IF EXISTS queue CASCADE;
DROP TABLE IF EXISTS history CASCADE;
DROP TABLE IF EXISTS relations CASCADE;
DROP TABLE IF EXISTS memories CASCADE;

DROP TYPE IF EXISTS action_type CASCADE;
DROP TYPE IF EXISTS recurrence_pattern CASCADE;
DROP TYPE IF EXISTS relation_type CASCADE;
DROP TYPE IF EXISTS entity_role CASCADE;
DROP TYPE IF EXISTS entity_type CASCADE;
DROP TYPE IF EXISTS memory_type CASCADE;

DROP FUNCTION IF EXISTS gen_semantic_id CASCADE;
DROP FUNCTION IF EXISTS search_memories CASCADE;
DROP FUNCTION IF EXISTS update_memory CASCADE;
DROP FUNCTION IF EXISTS get_related CASCADE;