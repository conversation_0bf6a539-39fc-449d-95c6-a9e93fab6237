-- Add full-text search support for conversations

-- Add tsvector column for full-text search
ALTER TABLE conversations
ADD COLUMN IF NOT EXISTS search_vector tsvector;

-- Create index for fast full-text search
CREATE INDEX IF NOT EXISTS idx_conversations_search
ON conversations USING GIN(search_vector);

-- Function to update search vector
CREATE OR REPLACE FUNCTION update_conversation_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector :=
        setweight(to_tsvector('english', COALESCE(NEW.title, '')), 'A') ||
        setweight(to_tsvector('english', COALESCE(NEW.summary, '')), 'B');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update search vector
DROP TRIGGER IF EXISTS update_conversation_search ON conversations;
CREATE TRIGGER update_conversation_search
BEFORE INSERT OR UPDATE ON conversations
FOR EACH ROW
EXECUTE FUNCTION update_conversation_search_vector();

-- Update existing conversations
UPDATE conversations
SET search_vector =
    setweight(to_tsvector('english', COALESCE(title, '')), 'A') ||
    setweight(to_tsvector('english', COALESCE(summary, '')), 'B');

-- Add search vector for messages
ALTER TABLE messages
ADD COLUMN IF NOT EXISTS search_vector tsvector;

-- Create index for messages
CREATE INDEX IF NOT EXISTS idx_messages_search
ON messages USING GIN(search_vector);

-- Function to update message search vector
CREATE OR REPLACE FUNCTION update_message_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := to_tsvector('english', COALESCE(NEW.content, ''));
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for messages
DROP TRIGGER IF EXISTS update_message_search ON messages;
CREATE TRIGGER update_message_search
BEFORE INSERT OR UPDATE ON messages
FOR EACH ROW
EXECUTE FUNCTION update_message_search_vector();

-- Update existing messages
UPDATE messages
SET search_vector = to_tsvector('english', COALESCE(content, ''));
