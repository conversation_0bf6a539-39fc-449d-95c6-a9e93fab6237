-- Migration: Simplify edge types by removing unused types
-- Based on codebase analysis, we're removing: relates_to, causes, includes, contradicts

-- First, migrate existing edges to appropriate new types
UPDATE knowledge_edges SET type = 
    CASE 
        WHEN type = 'relates_to' THEN 'mentions'     -- General relationships become mentions
        WHEN type = 'causes' THEN 'precedes'         -- Causal becomes temporal
        WHEN type = 'includes' THEN 'belongs_to'     -- Containment becomes ownership
        WHEN type = 'contradicts' THEN 'mentions'    -- Contradictions become mentions with metadata
        ELSE type
    END
WHERE type IN ('relates_to', 'causes', 'includes', 'contradicts');

-- Add metadata to preserve original edge type information
UPDATE knowledge_edges 
SET metadata = jsonb_set(
    COALESCE(metadata, '{}'::jsonb),
    '{legacy_type}',
    to_jsonb(type)
)
WHERE type IN ('relates_to', 'causes', 'includes', 'contradicts');

-- Create temporary enum with simplified types
CREATE TYPE knowledge_edge_type_temp AS ENUM (
    'belongs_to',     -- Ownership or membership
    'mentions',       -- References or mentions
    'depends_on',     -- Dependency relationship
    'precedes',       -- Temporal ordering
    'supports',       -- Supporting evidence
    'derived_from'    -- Derivation or source
);

-- Alter the edges table to use the temporary type
ALTER TABLE knowledge_edges 
    ALTER COLUMN type TYPE knowledge_edge_type_temp 
    USING type::text::knowledge_edge_type_temp;

-- Drop the old type and rename the temporary one
DROP TYPE knowledge_edge_type;
ALTER TYPE knowledge_edge_type_temp RENAME TO knowledge_edge_type;