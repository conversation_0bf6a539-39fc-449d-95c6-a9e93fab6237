-- 007_memory_semantic_identity.up.sql
-- Add semantic identity and versioning to memory system

-- Add new columns to memories table
ALTER TABLE memories 
ADD COLUMN IF NOT EXISTS semantic_id TEXT,
ADD COLUMN IF NOT EXISTS topic TEXT,
ADD COLUMN IF NOT EXISTS entity TEXT,
ADD COLUMN IF NOT EXISTS attributes JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS version INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS merged_from UUID[],
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;

-- Create index for semantic_id
CREATE INDEX IF NOT EXISTS idx_memories_semantic_id ON memories(semantic_id);
CREATE INDEX IF NOT EXISTS idx_memories_topic ON memories(topic);
CREATE INDEX IF NOT EXISTS idx_memories_entity ON memories(entity);
CREATE INDEX IF NOT EXISTS idx_memories_is_active ON memories(is_active);

-- Create memory versions table for history
CREATE TABLE IF NOT EXISTS memory_versions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    memory_id UUID NOT NULL REFERENCES memories(id) ON DELETE CASCADE,
    version INTEGER NOT NULL,
    content TEXT NOT NULL,
    attributes JSONB NOT NULL,
    change_type TEXT NOT NULL, -- 'UPDATE', 'MERGE', 'MANUAL'
    change_reason TEXT,
    changed_by TEXT DEFAULT 'system',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(memory_id, version)
);

CREATE INDEX idx_memory_versions_memory_id ON memory_versions(memory_id);
CREATE INDEX idx_memory_versions_created_at ON memory_versions(created_at DESC);

-- Function to generate semantic_id based on topic and entity
CREATE OR REPLACE FUNCTION generate_semantic_id(p_topic TEXT, p_entity TEXT)
RETURNS TEXT AS $$
BEGIN
    IF p_topic IS NULL OR p_entity IS NULL THEN
        RETURN NULL;
    END IF;
    -- Simple semantic ID: lowercase, replace spaces with underscores
    RETURN LOWER(REPLACE(p_topic || '_' || p_entity, ' ', '_'));
END;
$$ LANGUAGE plpgsql;

-- Function to atomically update a memory
CREATE OR REPLACE FUNCTION update_memory_atomic(
    p_semantic_id TEXT,
    p_content TEXT,
    p_attributes JSONB,
    p_change_reason TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    v_memory_id UUID;
    v_old_version INTEGER;
    v_new_version INTEGER;
    v_old_content TEXT;
    v_old_attributes JSONB;
BEGIN
    -- Find the active memory with this semantic_id
    SELECT id, version, content, attributes 
    INTO v_memory_id, v_old_version, v_old_content, v_old_attributes
    FROM memories
    WHERE semantic_id = p_semantic_id AND is_active = true
    FOR UPDATE;
    
    IF v_memory_id IS NULL THEN
        -- No existing memory, this should be an INSERT instead
        RETURN NULL;
    END IF;
    
    -- Calculate new version
    v_new_version := v_old_version + 1;
    
    -- Save to version history
    INSERT INTO memory_versions (
        memory_id, version, content, attributes, 
        change_type, change_reason
    ) VALUES (
        v_memory_id, v_old_version, v_old_content, v_old_attributes,
        'UPDATE', p_change_reason
    );
    
    -- Update the memory
    UPDATE memories SET
        content = p_content,
        attributes = p_attributes,
        version = v_new_version,
        updated_at = NOW()
    WHERE id = v_memory_id;
    
    RETURN v_memory_id;
END;
$$ LANGUAGE plpgsql;

-- Function to merge multiple memories
CREATE OR REPLACE FUNCTION merge_memories(
    p_memory_ids UUID[],
    p_new_content TEXT,
    p_new_attributes JSONB,
    p_topic TEXT,
    p_entity TEXT
)
RETURNS UUID AS $$
DECLARE
    v_new_memory_id UUID;
    v_semantic_id TEXT;
    v_merged_attributes JSONB := '{}';
    v_memory RECORD;
BEGIN
    -- Generate semantic_id
    v_semantic_id := generate_semantic_id(p_topic, p_entity);
    
    -- Merge attributes from all memories
    FOR v_memory IN 
        SELECT attributes FROM memories 
        WHERE id = ANY(p_memory_ids) AND is_active = true
    LOOP
        v_merged_attributes := v_merged_attributes || v_memory.attributes;
    END LOOP;
    
    -- Override with new attributes
    v_merged_attributes := v_merged_attributes || p_new_attributes;
    
    -- Create new merged memory
    INSERT INTO memories (
        type, content, content_hash, attributes,
        semantic_id, topic, entity, merged_from
    ) VALUES (
        'semantic'::memory_type,
        p_new_content,
        MD5(p_new_content),
        v_merged_attributes,
        v_semantic_id,
        p_topic,
        p_entity,
        p_memory_ids
    ) RETURNING id INTO v_new_memory_id;
    
    -- Deactivate old memories
    UPDATE memories 
    SET is_active = false 
    WHERE id = ANY(p_memory_ids);
    
    -- Record in versions
    INSERT INTO memory_versions (
        memory_id, version, content, attributes,
        change_type, change_reason
    ) VALUES (
        v_new_memory_id, 1, p_new_content, v_merged_attributes,
        'MERGE', 'Merged from ' || array_length(p_memory_ids, 1) || ' memories'
    );
    
    RETURN v_new_memory_id;
END;
$$ LANGUAGE plpgsql;

-- Function to find memories by semantic similarity and topic
CREATE OR REPLACE FUNCTION find_similar_memories_by_topic(
    p_embedding vector(768),
    p_topic TEXT,
    p_limit INTEGER DEFAULT 5,
    p_threshold REAL DEFAULT 0.7
)
RETURNS TABLE (
    id UUID,
    semantic_id TEXT,
    content TEXT,
    attributes JSONB,
    similarity REAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        m.id,
        m.semantic_id,
        m.content,
        m.attributes,
        1 - (m.embedding <=> p_embedding) as similarity
    FROM memories m
    WHERE 
        m.is_active = true
        AND (p_topic IS NULL OR m.topic = p_topic)
        AND m.embedding IS NOT NULL
        AND 1 - (m.embedding <=> p_embedding) >= p_threshold
    ORDER BY m.embedding <=> p_embedding
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- Add comments
COMMENT ON COLUMN memories.semantic_id IS 'Semantic identifier for grouping similar memories';
COMMENT ON COLUMN memories.topic IS 'High-level topic of the memory (e.g., "thai_boxing_schedule")';
COMMENT ON COLUMN memories.entity IS 'Main entity this memory is about (e.g., "thai_boxing_class")';
COMMENT ON COLUMN memories.attributes IS 'Updateable attributes as key-value pairs';
COMMENT ON COLUMN memories.version IS 'Version number for tracking changes';
COMMENT ON COLUMN memories.merged_from IS 'Array of memory IDs this was merged from';
COMMENT ON COLUMN memories.is_active IS 'Whether this memory is currently active';
COMMENT ON TABLE memory_versions IS 'Version history for memory updates and merges';