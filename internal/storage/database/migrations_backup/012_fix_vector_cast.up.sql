-- Fix the vector casting issue in SearchMemories
-- The problem: pgvector-go already sends the parameter as vector type
-- The SQL shouldn't cast it again with ::vector(768)

-- This is a temporary fix by creating a wrapper function
-- The proper fix would be to update the SQL query in memories.sql

-- Drop the function if it exists
DROP FUNCTION IF EXISTS search_memories_with_vector(
    p_embedding vector,
    p_types memory_type[],
    p_entities TEXT[],
    p_time_start TIMESTAMPTZ,
    p_time_end TIMESTAMPTZ,
    p_text TEXT,
    p_min_similarity REAL,
    p_max_results INTEGER
);

-- Create a fixed search function
CREATE OR REPLACE FUNCTION search_memories_with_vector(
    p_embedding vector,
    p_types memory_type[],
    p_entities TEXT[],
    p_time_start TIMESTAMPTZ,
    p_time_end TIMESTAMPTZ,
    p_text TEXT,
    p_min_similarity REAL,
    p_max_results INTEGER
) RETURNS TABLE (
    id UUID,
    semantic_id TEXT,
    type memory_type,
    content TEXT,
    summary TEXT,
    entities JSONB,
    attributes JSONB,
    context JSONB,
    embedding vector,
    keywords TEXT[],
    confidence REAL,
    version INTEGER,
    access_count INTEGER,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    accessed_at TIMESTAMPTZ,
    is_active BOOLEAN,
    merged_from UUID[],
    similarity REAL,
    score REAL
) AS $$
BEGIN
    RETURN QUERY
    WITH base_search AS (
        SELECT 
            m.*,
            CASE 
                WHEN p_embedding IS NOT NULL AND m.embedding IS NOT NULL 
                THEN (1 - (m.embedding <=> p_embedding))::REAL
                ELSE 0.0
            END as calc_similarity
        FROM memories m
        WHERE m.is_active = true
            -- Type filter
            AND (p_types IS NULL OR CARDINALITY(p_types) = 0 OR m.type = ANY(p_types))
            -- Entity filter
            AND (p_entities IS NULL OR CARDINALITY(p_entities) = 0 OR 
                EXISTS (
                    SELECT 1 FROM jsonb_array_elements(m.entities) e
                    WHERE e->>'name' = ANY(p_entities)
                ))
            -- Time range filter
            AND (p_time_start IS NULL OR 
                (m.context->>'occurred_at')::timestamptz >= p_time_start OR
                (m.context->>'start_time')::timestamptz >= p_time_start)
            AND (p_time_end IS NULL OR 
                (m.context->>'occurred_at')::timestamptz <= p_time_end OR
                (m.context->>'end_time')::timestamptz <= p_time_end)
            -- Text search
            AND (p_text IS NULL OR p_text = '' OR
                m.content ILIKE '%' || p_text || '%' OR
                COALESCE(m.summary, '') ILIKE '%' || p_text || '%')
    ),
    scored_search AS (
        SELECT 
            bs.*,
            bs.calc_similarity as similarity,
            -- Combined score
            (
                bs.calc_similarity * 0.6 +
                CASE 
                    WHEN bs.updated_at > NOW() - INTERVAL '1 day' THEN 0.2
                    WHEN bs.updated_at > NOW() - INTERVAL '7 days' THEN 0.15
                    WHEN bs.updated_at > NOW() - INTERVAL '30 days' THEN 0.1
                    ELSE 0.05
                END +
                CASE
                    WHEN bs.access_count > 10 THEN 0.1
                    WHEN bs.access_count > 5 THEN 0.05
                    ELSE 0.0
                END +
                COALESCE(bs.confidence, 0.8) * 0.1
            )::REAL as score
        FROM base_search bs
        WHERE bs.calc_similarity >= COALESCE(p_min_similarity, 0.0) OR p_embedding IS NULL
    )
    SELECT 
        ss.id, ss.semantic_id, ss.type, ss.content, ss.summary,
        ss.entities, ss.attributes, ss.context, ss.embedding,
        ss.keywords, ss.confidence, ss.version, ss.access_count,
        ss.created_at, ss.updated_at, ss.accessed_at, ss.is_active,
        ss.merged_from, ss.similarity, ss.score
    FROM scored_search ss
    ORDER BY ss.score DESC
    LIMIT COALESCE(p_max_results, 20);
END;
$$ LANGUAGE plpgsql;