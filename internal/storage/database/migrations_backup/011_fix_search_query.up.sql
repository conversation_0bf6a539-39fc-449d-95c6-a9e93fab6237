-- Fix the SearchMemories query to handle NULL similarities properly
-- This doesn't change the stored procedure, just documents the issue

-- The issue is in the CTE calculation:
-- CASE 
--     WHEN @embedding::vector(768) IS NOT NULL AND m.embedding IS NOT NULL 
--     THEN (1 - (m.embedding <=> @embedding::vector(768)))::REAL
--     ELSE 0.0
-- END as similarity

-- The problem might be:
-- 1. The vector cast @embedding::vector(768) might fail
-- 2. The similarity calculation might return NULL
-- 3. The WHERE clause logic might be wrong

-- For now, create a simpler search function for debugging
CREATE OR REPLACE FUNCTION search_memories_simple(
    query_embedding vector(768),
    max_results INT DEFAULT 20
) RETURNS TABLE (
    id UUID,
    semantic_id TEXT,
    content TEXT,
    type memory_type,
    similarity REAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        m.id,
        m.semantic_id,
        m.content,
        m.type,
        CASE 
            WHEN query_embedding IS NOT NULL AND m.embedding IS NOT NULL THEN
                (1 - (m.embedding <=> query_embedding))::REAL
            ELSE 
                0.0::REAL
        END as similarity
    FROM memories m
    WHERE m.is_active = true
        AND m.embedding IS NOT NULL
        AND query_embedding IS NOT NULL
    ORDER BY m.embedding <=> query_embedding
    LIMIT max_results;
END;
$$ LANGUAGE plpgsql;