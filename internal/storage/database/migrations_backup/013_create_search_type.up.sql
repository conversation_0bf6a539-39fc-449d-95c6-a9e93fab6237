-- Create a composite type for search results to help sqlc
DROP TYPE IF EXISTS search_memory_result CASCADE;

CREATE TYPE search_memory_result AS (
    id UUID,
    semantic_id TEXT,
    type memory_type,
    content TEXT,
    summary TEXT,
    entities JSONB,
    attributes JSONB,
    context JSONB,
    embedding vector,
    keywords TEXT[],
    confidence REAL,
    version INTEGER,
    access_count INTEGER,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    accessed_at TIMESTAMPTZ,
    is_active BOOLEAN,
    merged_from UUID[],
    similarity REAL,
    score REAL
);