-- Add status column to memories table
-- This replaces the is_active boolean with a more flexible status enum

-- Create memory status enum
CREATE TYPE memory_status AS ENUM ('active', 'archived', 'deleted');

-- Add status column with default value
ALTER TABLE memories ADD COLUMN IF NOT EXISTS status memory_status DEFAULT 'active';

-- Migrate existing data from is_active to status
UPDATE memories 
SET status = CASE 
    WHEN is_active = true THEN 'active'::memory_status
    ELSE 'archived'::memory_status
END
WHERE status IS NULL;

-- Make status NOT NULL after data migration
ALTER TABLE memories ALTER COLUMN status SET NOT NULL;

-- Add archived_at column for tracking when memories were archived
ALTER TABLE memories ADD COLUMN IF NOT EXISTS archived_at TIMESTAMPTZ;

-- Update archived_at for non-active memories
UPDATE memories 
SET archived_at = updated_at 
WHERE status != 'active' AND archived_at IS NULL;

-- Drop the old is_active column if it exists
ALTER TABLE memories DROP COLUMN IF EXISTS is_active;

-- Create index for efficient status filtering
CREATE INDEX IF NOT EXISTS idx_memories_status ON memories(status);

-- Create composite index for status and time-based queries
CREATE INDEX IF NOT EXISTS idx_memories_status_updated_at ON memories(status, updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_memories_status_accessed_at ON memories(status, accessed_at DESC NULLS LAST);