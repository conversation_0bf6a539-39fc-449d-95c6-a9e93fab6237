-- Remove user_id from personal assistant as it's single-user

-- Drop user_id index from history
DROP INDEX IF EXISTS idx_hist_user;

-- Remove user_id column from history
ALTER TABLE history 
DROP COLUMN IF EXISTS user_id;

-- Drop user_id index from memory_history if table exists
DROP INDEX IF EXISTS idx_memory_history_user_id;

-- Remove user_id column from memory_history if it exists
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'memory_history' AND column_name = 'user_id') THEN
        ALTER TABLE memory_history DROP COLUMN user_id;
    END IF;
END $$;