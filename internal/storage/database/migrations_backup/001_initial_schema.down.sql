-- 001_initial_schema.down.sql
-- 回滾初始資料庫架構

-- ===========================================
-- 撤銷權限
-- ===========================================
REVOKE ALL ON ALL FUNCTIONS IN SCHEMA public FROM assistant_app;
REVOKE ALL ON ALL SEQUENCES IN SCHEMA public FROM assistant_app;
REVOKE ALL ON ALL TABLES IN SCHEMA public FROM assistant_app;
REVOKE USAGE ON SCHEMA public FROM assistant_app;

-- ===========================================
-- 刪除觸發器
-- ===========================================
DROP TRIGGER IF EXISTS update_conversations_updated_at ON conversations;
DROP TRIGGER IF EXISTS update_knowledge_nodes_updated_at ON knowledge_nodes;
DROP TRIGGER IF EXISTS update_knowledge_edges_updated_at ON knowledge_edges;
DROP TRIGGER IF EXISTS update_tasks_updated_at ON tasks;
DROP TRIGGER IF EXISTS update_system_settings_updated_at ON system_settings;
DROP TRIGGER IF EXISTS update_conversation_stats_on_message ON messages;
DROP TRIGGER IF EXISTS update_node_access_on_select ON knowledge_nodes;

-- ===========================================
-- 刪除函數
-- ===========================================
DROP FUNCTION IF EXISTS update_updated_at_column();
DROP FUNCTION IF EXISTS update_conversation_stats();
DROP FUNCTION IF EXISTS update_node_access();
DROP FUNCTION IF EXISTS search_similar_embeddings(vector, INTEGER, TEXT[], FLOAT);
DROP FUNCTION IF EXISTS search_knowledge_nodes(TEXT, knowledge_node_type, INTEGER);
DROP FUNCTION IF EXISTS get_node_relations(UUID, INTEGER, knowledge_edge_type[]);
DROP FUNCTION IF EXISTS cleanup_old_backups(INTEGER, INTEGER);

-- ===========================================
-- 刪除索引（會隨表格一起刪除，但明確列出以確保完整性）
-- ===========================================
DROP INDEX IF EXISTS idx_conversations_status;
DROP INDEX IF EXISTS idx_conversations_updated_at;
DROP INDEX IF EXISTS idx_conversations_last_message;
DROP INDEX IF EXISTS idx_conversations_metadata;

DROP INDEX IF EXISTS idx_messages_conversation;
DROP INDEX IF EXISTS idx_messages_role;
DROP INDEX IF EXISTS idx_messages_created_at;
DROP INDEX IF EXISTS idx_messages_metadata;

DROP INDEX IF EXISTS idx_knowledge_nodes_type;
DROP INDEX IF EXISTS idx_knowledge_nodes_name_trgm;
DROP INDEX IF EXISTS idx_knowledge_nodes_importance;
DROP INDEX IF EXISTS idx_knowledge_nodes_access;
DROP INDEX IF EXISTS idx_knowledge_nodes_metadata;

DROP INDEX IF EXISTS idx_knowledge_edges_from;
DROP INDEX IF EXISTS idx_knowledge_edges_to;
DROP INDEX IF EXISTS idx_knowledge_edges_type;
DROP INDEX IF EXISTS idx_knowledge_edges_weight;

DROP INDEX IF EXISTS idx_embeddings_source;
DROP INDEX IF EXISTS idx_embeddings_vector;
DROP INDEX IF EXISTS idx_embeddings_created_at;

DROP INDEX IF EXISTS idx_tasks_status;
DROP INDEX IF EXISTS idx_tasks_priority;
DROP INDEX IF EXISTS idx_tasks_due_date;
DROP INDEX IF EXISTS idx_tasks_labels;
DROP INDEX IF EXISTS idx_tasks_metadata;

DROP INDEX IF EXISTS idx_tool_executions_conversation;
DROP INDEX IF EXISTS idx_tool_executions_tool;
DROP INDEX IF EXISTS idx_tool_executions_status;
DROP INDEX IF EXISTS idx_tool_executions_created;

DROP INDEX IF EXISTS idx_system_settings_updated;
DROP INDEX IF EXISTS idx_memory_backups_created;

-- ===========================================
-- 刪除表格（按照依賴順序）
-- ===========================================
DROP TABLE IF EXISTS memory_backups CASCADE;
DROP TABLE IF EXISTS system_settings CASCADE;
DROP TABLE IF EXISTS tool_executions CASCADE;
DROP TABLE IF EXISTS tasks CASCADE;
DROP TABLE IF EXISTS embeddings CASCADE;
DROP TABLE IF EXISTS knowledge_edges CASCADE;
DROP TABLE IF EXISTS knowledge_nodes CASCADE;
DROP TABLE IF EXISTS messages CASCADE;
DROP TABLE IF EXISTS conversations CASCADE;

-- ===========================================
-- 刪除自定義類型
-- ===========================================
DROP TYPE IF EXISTS tool_status CASCADE;
DROP TYPE IF EXISTS knowledge_edge_type CASCADE;
DROP TYPE IF EXISTS knowledge_node_type CASCADE;
DROP TYPE IF EXISTS task_priority CASCADE;
DROP TYPE IF EXISTS task_status CASCADE;
DROP TYPE IF EXISTS message_role CASCADE;
DROP TYPE IF EXISTS conversation_status CASCADE;

-- ===========================================
-- 刪除擴展（保留基礎擴展，因為其他應用可能使用）
-- ===========================================
-- 注意：不刪除 uuid-ossp, vector, pg_trgm, btree_gist
-- 因為這些擴展可能被其他應用使用

-- ===========================================
-- 刪除角色（如果確定不再需要）
-- ===========================================
-- DROP ROLE IF EXISTS assistant_app;
-- 注意：通常不在 migration 中刪除角色，以避免影響其他環境