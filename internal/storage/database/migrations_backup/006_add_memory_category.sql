-- Add category field to memories table
ALTER TABLE memories ADD COLUMN IF NOT EXISTS category VARCHAR(50);

-- Create index for category queries
CREATE INDEX IF NOT EXISTS idx_memories_category ON memories(category);

-- Create memory_history table for tracking changes
CREATE TABLE IF NOT EXISTS memory_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    memory_id UUID NOT NULL REFERENCES memories(id) ON DELETE CASCADE,
    user_id VARCHAR(255) NOT NULL,
    action VARCHAR(20) NOT NULL, -- ADD, UPDATE, DELETE
    old_content TEXT,
    new_content TEXT,
    old_metadata JSONB,
    new_metadata JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for memory history
CREATE INDEX IF NOT EXISTS idx_memory_history_memory_id ON memory_history(memory_id);
CREATE INDEX IF NOT EXISTS idx_memory_history_user_id ON memory_history(user_id);
CREATE INDEX IF NOT EXISTS idx_memory_history_created_at ON memory_history(created_at DESC);
