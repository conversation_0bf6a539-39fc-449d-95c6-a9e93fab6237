-- Rollback: Restore original edge types

-- Create temporary enum with all original types
CREATE TYPE knowledge_edge_type_temp AS ENUM (
    'relates_to',      -- General relationship
    'belongs_to',      -- Ownership or membership
    'mentions',        -- References or mentions
    'depends_on',      -- Dependency relationship
    'causes',          -- Causal relationship
    'precedes',        -- Temporal ordering
    'includes',        -- Containment
    'contradicts',     -- Conflicting information
    'supports',        -- Supporting evidence
    'derived_from'     -- Derivation or source
);

-- Alter the edges table to use the temporary type
ALTER TABLE knowledge_edges 
    ALTER COLUMN type TYPE knowledge_edge_type_temp 
    USING type::text::knowledge_edge_type_temp;

-- Restore original edge types from metadata if available
UPDATE knowledge_edges 
SET type = (metadata->>'legacy_type')::knowledge_edge_type_temp
WHERE metadata ? 'legacy_type'
  AND (metadata->>'legacy_type')::text IN ('relates_to', 'causes', 'includes', 'contradicts');

-- Remove legacy_type from metadata
UPDATE knowledge_edges 
SET metadata = metadata - 'legacy_type'
WHERE metadata ? 'legacy_type';

-- Drop the current type and rename the temporary one
DROP TYPE knowledge_edge_type;
ALTER TYPE knowledge_edge_type_temp RENAME TO knowledge_edge_type;