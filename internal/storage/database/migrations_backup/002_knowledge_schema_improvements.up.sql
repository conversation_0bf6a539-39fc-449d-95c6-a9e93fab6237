-- 002_knowledge_schema_improvements.up.sql
-- Improve knowledge_nodes schema for better type safety and performance
-- Following PostgreSQL 17 and Go best practices

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS pgcrypto; -- For digest function

-- ===========================================
-- Step 1: Add new columns to knowledge_nodes
-- ===========================================

-- Add content as a dedicated column (not in metadata)
ALTER TABLE knowledge_nodes 
ADD COLUMN content TEXT;

-- Add content hash for deduplication
ALTER TABLE knowledge_nodes 
ADD COLUMN content_hash VARCHAR(64);

-- Add observations as native PostgreSQL array
ALTER TABLE knowledge_nodes 
ADD COLUMN observations TEXT[] NOT NULL DEFAULT '{}';

-- Add structured time context
ALTER TABLE knowledge_nodes 
ADD COLUMN learned_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- Add source reference (e.g., conversation_id)
ALTER TABLE knowledge_nodes 
ADD COLUMN source_id UUID;
ALTER TABLE knowledge_nodes 
ADD COLUMN source_type TEXT;

-- ===========================================
-- Step 2: Create new node types enum
-- ===========================================

-- First, let's create a temporary type with our desired values
CREATE TYPE knowledge_node_type_new AS ENUM (
    'fact',       -- General facts and information
    'preference', -- User preferences
    'person',     -- People and relationships
    'event',      -- Events and meetings
    'task',       -- Tasks and todos
    'note',       -- Notes and thoughts
    'skill',      -- Skills and abilities
    'location',   -- Places and locations
    'concept'     -- Abstract concepts
);

-- Update existing data to map old types to new types
UPDATE knowledge_nodes 
SET type = CASE 
    WHEN type = 'owner' THEN 'person'::knowledge_node_type
    WHEN type = 'object' THEN 'fact'::knowledge_node_type
    WHEN type = 'schedule' THEN 'event'::knowledge_node_type
    ELSE type
END;

-- Now we need to alter the column to use the new type
ALTER TABLE knowledge_nodes 
ALTER COLUMN type TYPE knowledge_node_type_new 
USING type::text::knowledge_node_type_new;

-- Drop the old type and rename the new one
DROP TYPE knowledge_node_type CASCADE;
ALTER TYPE knowledge_node_type_new RENAME TO knowledge_node_type;

-- ===========================================
-- Step 3: Migrate existing data
-- ===========================================

-- Extract content from metadata JSONB to the new content column
UPDATE knowledge_nodes 
SET content = metadata->>'content',
    observations = COALESCE(
        ARRAY(SELECT jsonb_array_elements_text(metadata->'observations')), 
        '{}'::TEXT[]
    )
WHERE metadata ? 'content';

-- Generate content hash for existing content
UPDATE knowledge_nodes 
SET content_hash = encode(digest(content, 'sha256'), 'hex')
WHERE content IS NOT NULL;

-- Extract time context if available
UPDATE knowledge_nodes 
SET learned_at = COALESCE(
    (metadata->>'created_at')::TIMESTAMPTZ,
    created_at
);

-- ===========================================
-- Step 4: Add constraints
-- ===========================================

-- Ensure content is not empty when provided
ALTER TABLE knowledge_nodes 
ADD CONSTRAINT node_content_not_empty 
CHECK (content IS NULL OR trim(content) != '');

-- Ensure content hash matches content
ALTER TABLE knowledge_nodes 
ADD CONSTRAINT node_content_hash_consistency 
CHECK (
    (content IS NULL AND content_hash IS NULL) OR
    (content IS NOT NULL AND content_hash IS NOT NULL)
);

-- Source reference constraints
ALTER TABLE knowledge_nodes 
ADD CONSTRAINT node_source_consistency 
CHECK (
    (source_id IS NULL AND source_type IS NULL) OR
    (source_id IS NOT NULL AND source_type IS NOT NULL)
);

ALTER TABLE knowledge_nodes 
ADD CONSTRAINT node_source_type_valid 
CHECK (source_type IN ('conversation', 'tool', 'import', 'system'));

-- ===========================================
-- Step 5: Create indexes for new columns
-- ===========================================

-- Index for content search (using trigram for fuzzy search)
CREATE INDEX idx_knowledge_nodes_content_trgm 
ON knowledge_nodes 
USING GIN (content gin_trgm_ops) 
WHERE content IS NOT NULL;

-- Index for content hash (for deduplication checks)
CREATE INDEX idx_knowledge_nodes_content_hash 
ON knowledge_nodes (content_hash) 
WHERE content_hash IS NOT NULL;

-- Index for source tracking
CREATE INDEX idx_knowledge_nodes_source 
ON knowledge_nodes (source_type, source_id) 
WHERE source_id IS NOT NULL;

-- Index for temporal queries
CREATE INDEX idx_knowledge_nodes_learned_at 
ON knowledge_nodes (learned_at DESC);

-- Composite index for type-based content search
CREATE INDEX idx_knowledge_nodes_type_content 
ON knowledge_nodes (type, content_hash) 
WHERE content IS NOT NULL;

-- ===========================================
-- Step 6: Update edge types for consistency
-- ===========================================

-- Create new edge type enum
CREATE TYPE knowledge_edge_type_new AS ENUM (
    'relates_to',      -- General relationship
    'belongs_to',      -- Ownership or membership
    'mentions',        -- References or mentions
    'depends_on',      -- Dependency relationship
    'causes',          -- Causal relationship
    'precedes',        -- Temporal ordering
    'includes',        -- Containment
    'contradicts',     -- Conflicting information
    'supports',        -- Supporting evidence
    'derived_from'     -- Derivation or source
);

-- Since we need to handle the case where there might be existing data,
-- we'll use a DO block to handle this safely
DO $$
BEGIN
    -- Check if there are any edges to migrate
    IF EXISTS (SELECT 1 FROM knowledge_edges LIMIT 1) THEN
        -- Map old edge types to new ones using text casting
        UPDATE knowledge_edges 
        SET type = CASE type::text
            WHEN 'likes' THEN 'relates_to'
            WHEN 'dislikes' THEN 'relates_to'
            WHEN 'knows' THEN 'relates_to'
            WHEN 'owns' THEN 'belongs_to'
            WHEN 'located_at' THEN 'belongs_to'
            WHEN 'scheduled_at' THEN 'relates_to'
            WHEN 'related_to' THEN 'relates_to'
            WHEN 'interested_in' THEN 'relates_to'
            WHEN 'skilled_in' THEN 'relates_to'
            ELSE type::text
        END::knowledge_edge_type;
    END IF;
END $$;

-- Update the column type
ALTER TABLE knowledge_edges 
ALTER COLUMN type TYPE knowledge_edge_type_new 
USING type::text::knowledge_edge_type_new;

-- Drop old type and rename new one
DROP TYPE knowledge_edge_type CASCADE;
ALTER TYPE knowledge_edge_type_new RENAME TO knowledge_edge_type;

-- ===========================================
-- Step 7: Create helper functions
-- ===========================================

-- Function to safely create or update a node
CREATE OR REPLACE FUNCTION upsert_knowledge_node(
    p_name TEXT,
    p_type knowledge_node_type,
    p_content TEXT DEFAULT NULL,
    p_observations TEXT[] DEFAULT '{}',
    p_metadata JSONB DEFAULT '{}',
    p_source_id UUID DEFAULT NULL,
    p_source_type TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    v_node_id UUID;
    v_content_hash VARCHAR(64);
BEGIN
    -- Calculate content hash if content is provided
    IF p_content IS NOT NULL THEN
        v_content_hash := encode(digest(p_content, 'sha256'), 'hex');
    END IF;
    
    -- Try to find existing node
    SELECT id INTO v_node_id
    FROM knowledge_nodes
    WHERE name = p_name AND type = p_type;
    
    IF v_node_id IS NULL THEN
        -- Insert new node
        INSERT INTO knowledge_nodes (
            name, type, content, content_hash, 
            observations, metadata, source_id, source_type
        ) VALUES (
            p_name, p_type, p_content, v_content_hash,
            p_observations, p_metadata, p_source_id, p_source_type
        )
        RETURNING id INTO v_node_id;
    ELSE
        -- Update existing node
        UPDATE knowledge_nodes 
        SET 
            content = COALESCE(p_content, content),
            content_hash = COALESCE(v_content_hash, content_hash),
            observations = array_cat(observations, p_observations),
            metadata = metadata || p_metadata,
            source_id = COALESCE(p_source_id, source_id),
            source_type = COALESCE(p_source_type, source_type),
            access_count = access_count + 1
        WHERE id = v_node_id;
    END IF;
    
    RETURN v_node_id;
END;
$$ LANGUAGE plpgsql;

-- Function to search nodes by content
CREATE OR REPLACE FUNCTION search_nodes_by_content(
    p_query TEXT,
    p_types knowledge_node_type[] DEFAULT NULL,
    p_limit INTEGER DEFAULT 20
)
RETURNS TABLE (
    id UUID,
    name TEXT,
    type knowledge_node_type,
    content TEXT,
    observations TEXT[],
    score FLOAT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        n.id,
        n.name,
        n.type,
        n.content,
        n.observations,
        similarity(n.content, p_query) AS score
    FROM knowledge_nodes n
    WHERE 
        n.content IS NOT NULL
        AND (p_types IS NULL OR n.type = ANY(p_types))
        AND n.content % p_query  -- Trigram similarity operator
    ORDER BY score DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- ===========================================
-- Step 8: Clean up metadata
-- ===========================================

-- Remove migrated fields from metadata
UPDATE knowledge_nodes
SET metadata = metadata - 'content' - 'observations' - 'created_at'
WHERE metadata ? 'content' OR metadata ? 'observations' OR metadata ? 'created_at';

-- ===========================================
-- Step 9: Add comments for documentation
-- ===========================================

COMMENT ON COLUMN knowledge_nodes.content IS 'Main content of the node (previously stored in metadata)';
COMMENT ON COLUMN knowledge_nodes.content_hash IS 'SHA-256 hash of content for deduplication';
COMMENT ON COLUMN knowledge_nodes.observations IS 'Additional observations or notes about this knowledge';
COMMENT ON COLUMN knowledge_nodes.learned_at IS 'When this knowledge was learned or created';
COMMENT ON COLUMN knowledge_nodes.source_id IS 'Reference to the source (e.g., conversation_id)';
COMMENT ON COLUMN knowledge_nodes.source_type IS 'Type of source: conversation, tool, import, or system';

COMMENT ON FUNCTION upsert_knowledge_node IS 'Safely create or update a knowledge node with deduplication';
COMMENT ON FUNCTION search_nodes_by_content IS 'Search nodes by content similarity using trigram matching';