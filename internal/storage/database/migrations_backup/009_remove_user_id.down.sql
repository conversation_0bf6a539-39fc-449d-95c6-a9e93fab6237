-- Restore user_id columns for rollback

-- Add user_id back to history table
ALTER TABLE history 
ADD COLUMN user_id TEXT NOT NULL DEFAULT 'personal';

-- Recreate index
CREATE INDEX idx_hist_user ON history(user_id);

-- Add user_id back to memory_history table if it exists
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'memory_history') THEN
        ALTER TABLE memory_history 
        ADD COLUMN user_id VARCHAR(255) NOT NULL DEFAULT 'personal';
        
        CREATE INDEX idx_memory_history_user_id ON memory_history(user_id);
    END IF;
END $$;