-- 005_memory_system_redesign.down.sql
-- Rollback memory system redesign
-- WARNING: This will lose all memory data! Backup before running!

-- Drop new functions
DROP FUNCTION IF EXISTS search_memories_by_embedding;
DROP FUNCTION IF EXISTS search_memories_hybrid;
DROP FUNCTION IF EXISTS update_memory_access;
DROP FUNCTION IF EXISTS compute_memory_importance;
DROP FUNCTION IF EXISTS update_memory_importance_scores;

-- Drop views
DROP VIEW IF EXISTS memory_statistics;

-- Drop new tables
DROP TABLE IF EXISTS memory_consolidations;
DROP TABLE IF EXISTS memory_relations;
DROP TABLE IF EXISTS memories;

-- Drop new types
DROP TYPE IF EXISTS memory_type;

-- Note: This does not restore the old knowledge_nodes and knowledge_edges tables
-- You would need to restore from backup or recreate them with migrations 001-004