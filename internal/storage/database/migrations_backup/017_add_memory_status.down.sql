-- Revert status column back to is_active boolean

-- Add back is_active column
ALTER TABLE memories ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;

-- Migrate data from status to is_active
UPDATE memories 
SET is_active = CASE 
    WHEN status = 'active' THEN true
    ELSE false
END;

-- Drop indexes
DROP INDEX IF EXISTS idx_memories_status_accessed_at;
DROP INDEX IF EXISTS idx_memories_status_updated_at;
DROP INDEX IF EXISTS idx_memories_status;

-- Drop columns
ALTER TABLE memories DROP COLUMN IF EXISTS archived_at;
ALTER TABLE memories DROP COLUMN IF EXISTS status;

-- Drop enum type
DROP TYPE IF EXISTS memory_status;