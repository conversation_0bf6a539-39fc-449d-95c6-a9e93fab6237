-- 008_structured_memory_system.up.sql
-- Complete refactor to structured memory system with entities, relations, and context
-- WARNING: This is a breaking change that drops existing memory tables

-- Drop existing memory tables and types
DROP TABLE IF EXISTS memory_versions CASCADE;
DROP TABLE IF EXISTS memory_history CASCADE;
DROP TABLE IF EXISTS memory_consolidations CASCADE;
DROP TABLE IF EXISTS memory_relations CASCADE;
DROP TABLE IF EXISTS memories CASCADE;
DROP TYPE IF EXISTS memory_type CASCADE;

-- Memory types
CREATE TYPE memory_type AS ENUM (
    'fact',
    'preference',
    'schedule',
    'relationship',
    'goal',
    'skill'
);

-- Entity types and roles
CREATE TYPE entity_type AS ENUM ('person', 'place', 'activity', 'time', 'thing', 'concept');
CREATE TYPE entity_role AS ENUM ('subject', 'object', 'location', 'time', 'with');

-- Relations and actions
CREATE TYPE relation_type AS ENUM ('updates', 'contradicts', 'relates_to', 'follows', 'precedes', 'part_of');
CREATE TYPE recurrence_pattern AS ENUM ('daily', 'weekly', 'monthly', 'yearly');
CREATE TYPE action_type AS ENUM ('ADD', 'UPDATE', 'DELETE', 'SKIP', 'MERGE');

-- Memories table
CREATE TABLE memories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    semantic_id TEXT NOT NULL,
    type memory_type NOT NULL,
    content TEXT NOT NULL,
    summary TEXT,
    entities JSONB NOT NULL DEFAULT '[]',
    attributes JSONB NOT NULL DEFAULT '{}',
    context JSONB NOT NULL DEFAULT '{}',
    embedding vector(768), -- Gemini gemini-embedding-001
    keywords TEXT[] DEFAULT '{}',
    confidence REAL DEFAULT 0.8 CHECK (confidence BETWEEN 0 AND 1),
    version INTEGER DEFAULT 1,
    access_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    accessed_at TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT true,
    merged_from UUID[]
);

-- Indexes
CREATE INDEX idx_mem_semantic ON memories(semantic_id) WHERE is_active;
CREATE INDEX idx_mem_type ON memories(type) WHERE is_active;
CREATE INDEX idx_mem_updated ON memories(updated_at DESC) WHERE is_active;
CREATE INDEX idx_mem_keywords ON memories USING gin(keywords) WHERE is_active;
CREATE INDEX idx_mem_entities ON memories USING gin(entities) WHERE is_active;
CREATE INDEX idx_mem_attrs ON memories USING gin(attributes) WHERE is_active;
CREATE INDEX idx_mem_context ON memories USING gin(context) WHERE is_active;
-- CREATE INDEX idx_mem_embedding ON memories USING hnsw (embedding vector_cosine_ops) WHERE is_active;

-- Relations
CREATE TABLE relations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    from_id UUID NOT NULL REFERENCES memories(id) ON DELETE CASCADE,
    to_id UUID NOT NULL REFERENCES memories(id) ON DELETE CASCADE,
    type relation_type NOT NULL,
    strength REAL DEFAULT 1.0 CHECK (strength BETWEEN 0 AND 1),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(from_id, to_id, type)
);

CREATE INDEX idx_rel_from ON relations(from_id);
CREATE INDEX idx_rel_to ON relations(to_id);
CREATE INDEX idx_rel_type ON relations(type);

-- History
CREATE TABLE history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    memory_id UUID NOT NULL,
    user_id TEXT NOT NULL DEFAULT 'personal',
    action action_type NOT NULL,
    version INTEGER NOT NULL,
    content TEXT NOT NULL,
    entities JSONB NOT NULL,
    attributes JSONB NOT NULL,
    context JSONB NOT NULL,
    reason TEXT,
    created_by TEXT DEFAULT 'system',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_hist_memory ON history(memory_id);
CREATE INDEX idx_hist_user ON history(user_id);
CREATE INDEX idx_hist_created ON history(created_at DESC);
CREATE INDEX idx_hist_action ON history(action);

-- Processing queue
CREATE TABLE queue (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID,
    messages JSONB NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    error TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    processed_at TIMESTAMPTZ
);

CREATE INDEX idx_queue_status ON queue(status, created_at);

-- Update timestamp trigger
CREATE TRIGGER update_memories_updated_at BEFORE UPDATE ON memories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Generate semantic ID
CREATE OR REPLACE FUNCTION gen_semantic_id(
    p_type memory_type,
    p_entities JSONB,
    p_context JSONB
)
RETURNS TEXT AS $$
DECLARE
    v_entity TEXT;
    v_date TEXT := '';
BEGIN
    -- Get primary entity
    SELECT e->>'name' INTO v_entity
    FROM jsonb_array_elements(p_entities) e
    WHERE e->>'role' IN ('subject', 'object')
    LIMIT 1;

    v_entity := COALESCE(v_entity, p_type::text);

    -- Add date for schedules
    IF p_type = 'schedule' THEN
        v_date := COALESCE(
            to_char((p_context->>'occurred_at')::timestamptz, '_YYYYMMDD'),
            to_char((p_context->>'start_time')::timestamptz, '_YYYYMMDD'),
            ''
        );
    END IF;

    RETURN LOWER(REGEXP_REPLACE(
        p_type::text || '_' || v_entity || v_date,
        '[^a-z0-9_]+', '_', 'g'
    ));
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Search memories
CREATE OR REPLACE FUNCTION search_memories(
    p_embedding vector(768),
    p_text TEXT DEFAULT NULL,
    p_types memory_type[] DEFAULT NULL,
    p_entities TEXT[] DEFAULT NULL,
    p_time_start TIMESTAMPTZ DEFAULT NULL,
    p_time_end TIMESTAMPTZ DEFAULT NULL,
    p_limit INTEGER DEFAULT 10,
    p_min_similarity REAL DEFAULT 0.7
)
RETURNS TABLE (
    id UUID,
    semantic_id TEXT,
    type memory_type,
    content TEXT,
    summary TEXT,
    entities JSONB,
    attributes JSONB,
    context JSONB,
    similarity REAL,
    score REAL
) AS $$
BEGIN
    RETURN QUERY
    WITH base_search AS (
        SELECT
            m.*,
            CASE
                WHEN p_embedding IS NOT NULL AND m.embedding IS NOT NULL
                THEN (1 - (m.embedding <=> p_embedding))::REAL
                ELSE 0.0
            END as similarity
        FROM memories m
        WHERE m.is_active = true
            -- Type filter
            AND (p_types IS NULL OR m.type = ANY(p_types))
            -- Entity filter (search in JSONB array)
            AND (p_entities IS NULL OR
                EXISTS (
                    SELECT 1 FROM jsonb_array_elements(m.entities) e
                    WHERE e->>'name' = ANY(p_entities)
                ))
            -- Time range filter
            AND (p_time_start IS NULL OR
                (m.context->>'occurred_at')::timestamptz >= p_time_start OR
                (m.context->>'start_time')::timestamptz >= p_time_start)
            AND (p_time_end IS NULL OR
                (m.context->>'occurred_at')::timestamptz <= p_time_end OR
                (m.context->>'end_time')::timestamptz <= p_time_end)
            -- Text search
            AND (p_text IS NULL OR
                m.content ILIKE '%' || p_text || '%' OR
                m.summary ILIKE '%' || p_text || '%')
    ),
    scored_search AS (
        SELECT
            bs.*,
            -- Combined score: similarity + recency + access frequency
            (
                bs.similarity * 0.6 +
                CASE
                    WHEN bs.updated_at > NOW() - INTERVAL '1 day' THEN 0.2
                    WHEN bs.updated_at > NOW() - INTERVAL '7 days' THEN 0.15
                    WHEN bs.updated_at > NOW() - INTERVAL '30 days' THEN 0.1
                    ELSE 0.05
                END +
                CASE
                    WHEN bs.access_count > 10 THEN 0.1
                    WHEN bs.access_count > 5 THEN 0.05
                    ELSE 0.0
                END +
                bs.confidence * 0.1
            ) as score
        FROM base_search bs
        WHERE bs.similarity >= p_min_similarity OR p_embedding IS NULL
    )
    SELECT
        ss.id,
        ss.semantic_id,
        ss.type,
        ss.content,
        ss.summary,
        ss.entities,
        ss.attributes,
        ss.context,
        ss.similarity,
        ss.score
    FROM scored_search ss
    ORDER BY ss.score DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- Update memory atomically
CREATE OR REPLACE FUNCTION update_memory(
    p_semantic_id TEXT,
    p_content TEXT,
    p_entities JSONB,
    p_attributes JSONB,
    p_context JSONB,
    p_reason TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    v_mem memories%ROWTYPE;
BEGIN
    -- Lock and get current
    SELECT * INTO v_mem
    FROM memories
    WHERE semantic_id = p_semantic_id AND is_active
    FOR UPDATE;

    IF NOT FOUND THEN
        RETURN NULL;
    END IF;

    -- Save history
    INSERT INTO history (
        memory_id, action, version, content, entities, attributes, context, reason
    ) VALUES (
        v_mem.id, 'UPDATE', v_mem.version, v_mem.content,
        v_mem.entities, v_mem.attributes, v_mem.context, p_reason
    );

    -- Update
    UPDATE memories SET
        content = p_content,
        entities = p_entities,
        attributes = v_mem.attributes || p_attributes,
        context = v_mem.context || p_context,
        version = v_mem.version + 1,
        updated_at = NOW()
    WHERE id = v_mem.id;

    RETURN v_mem.id;
END;
$$ LANGUAGE plpgsql;

-- Get related memories
CREATE OR REPLACE FUNCTION get_related(
    p_id UUID,
    p_types relation_type[] DEFAULT NULL,
    p_limit INTEGER DEFAULT 10
)
RETURNS TABLE (
    memory_id UUID,
    semantic_id TEXT,
    type memory_type,
    content TEXT,
    rel_type relation_type,
    strength REAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        m.id,
        m.semantic_id,
        m.type,
        m.content,
        r.type,
        r.strength
    FROM relations r
    JOIN memories m ON (
        (r.from_id = p_id AND m.id = r.to_id) OR
        (r.to_id = p_id AND m.id = r.from_id)
    )
    WHERE m.is_active
        AND (p_types IS NULL OR r.type = ANY(p_types))
    ORDER BY r.strength DESC, m.updated_at DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql STABLE;

-- Comments
COMMENT ON TABLE memories IS 'Structured memory system';
COMMENT ON COLUMN memories.semantic_id IS 'Semantic ID for updates (e.g., schedule_thai_boxing_20250714)';
COMMENT ON COLUMN memories.entities IS 'Entities with types and roles';
COMMENT ON COLUMN memories.attributes IS 'Key-value attributes';
COMMENT ON COLUMN memories.context IS 'Temporal and spatial context';
COMMENT ON TABLE relations IS 'Memory relationships';
COMMENT ON TABLE history IS 'Change history';
COMMENT ON TABLE queue IS 'Async processing queue';
