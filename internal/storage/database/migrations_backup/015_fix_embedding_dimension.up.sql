-- 015_fix_embedding_dimension.up.sql
-- Fix embedding model configuration to use 768 dimensions with OutputDimensionality parameter

-- The embedding column already exists with 768 dimensions in the original schema
-- We just need to ensure the HNSW index is created properly
DROP INDEX IF EXISTS idx_mem_embedding;
CREATE INDEX idx_mem_embedding ON memories USING hnsw (embedding vector_cosine_ops) WHERE is_active;

-- The search_memories function already uses vector(768), no changes needed
-- Just add a comment to clarify the configuration
COMMENT ON COLUMN memories.embedding IS 'Embedding vector for gemini-embedding-001 with OutputDimensionality=768';