-- Add vector indexes for efficient similarity search

-- First, ensure the vector extension is installed
CREATE EXTENSION IF NOT EXISTS vector;

-- Create HNSW index for more accurate similarity search
-- HNSW provides better recall but uses more memory
-- Parameters:
-- m: Maximum number of connections for each node (16 is a good default)
-- ef_construction: Size of the dynamic candidate list (64 provides good accuracy)
CREATE INDEX IF NOT EXISTS idx_memories_embedding_hnsw 
ON memories 
USING hnsw (embedding vector_cosine_ops)
WITH (m = 16, ef_construction = 64);

-- Alternative: IVFFlat index for faster but less accurate search
-- Uncomment if you prefer speed over accuracy
-- Note: IVFFlat requires training on existing data
-- CREATE INDEX IF NOT EXISTS idx_memories_embedding_ivfflat 
-- ON memories 
-- USING ivfflat (embedding vector_cosine_ops)
-- WITH (lists = 100);

-- Note: Status indexes will be created in migration 017_add_memory_status.up.sql

-- Add index for confidence-based filtering
CREATE INDEX IF NOT EXISTS idx_memories_confidence ON memories(confidence DESC);

-- Analyze the table to update statistics for query planner
ANALY<PERSON><PERSON> memories;