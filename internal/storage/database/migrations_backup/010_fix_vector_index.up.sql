-- 010_fix_vector_index.up.sql
-- Fix vector search by creating the missing index

-- First ensure pgvector is installed
CREATE EXTENSION IF NOT EXISTS vector;

-- Create the vector index that was commented out
-- Using ivfflat instead of hnsw for better compatibility
CREATE INDEX IF NOT EXISTS idx_mem_embedding 
ON memories USING ivfflat (embedding vector_cosine_ops) 
WITH (lists = 100)
WHERE is_active;

-- Also create an index for L2 distance (alternative similarity measure)
CREATE INDEX IF NOT EXISTS idx_mem_embedding_l2 
ON memories USING ivfflat (embedding vector_l2_ops) 
WITH (lists = 100)
WHERE is_active;

-- Analyze the table to update statistics
ANALYZE memories;