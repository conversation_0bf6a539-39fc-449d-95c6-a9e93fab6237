-- 005_memory_system_redesign.up.sql
-- Complete redesign of memory system inspired by mem0 and MCP best practices
-- This is a breaking change that replaces the knowledge graph with a unified memory system

-- Drop old knowledge system tables (backup data first in production!)
DROP TABLE IF EXISTS knowledge_edges CASCADE;
DROP TABLE IF EXISTS knowledge_nodes CASCADE;

-- Create memory type enum
CREATE TYPE memory_type AS ENUM ('episodic', 'semantic', 'procedural');

-- Create main memories table
CREATE TABLE memories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type memory_type NOT NULL,
    content TEXT NOT NULL,
    content_hash TEXT NOT NULL,
    embedding vector(768), -- Gemini gemini-embedding-001 dimension
    metadata JSONB DEFAULT '{}',
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    importance REAL DEFAULT 0.5 CHECK (importance >= 0 AND importance <= 1),
    access_count INTEGER DEFAULT 0,
    last_accessed TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_memories_type ON memories(type);
CREATE INDEX idx_memories_timestamp ON memories(timestamp DESC);
CREATE INDEX idx_memories_importance ON memories(importance DESC);
CREATE INDEX idx_memories_content_trgm ON memories USING gin(content gin_trgm_ops);
CREATE UNIQUE INDEX idx_memories_content_hash ON memories(content_hash);

-- Vector similarity index will be created after data exists
-- CREATE INDEX idx_memories_embedding ON memories USING hnsw (embedding vector_cosine_ops);

-- Create memory relations table
CREATE TABLE memory_relations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    from_memory_id UUID REFERENCES memories(id) ON DELETE CASCADE,
    to_memory_id UUID REFERENCES memories(id) ON DELETE CASCADE,
    relation_type TEXT NOT NULL,
    strength REAL DEFAULT 1.0 CHECK (strength >= 0 AND strength <= 1),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(from_memory_id, to_memory_id, relation_type)
);

-- Create indexes for relations
CREATE INDEX idx_relations_from ON memory_relations(from_memory_id);
CREATE INDEX idx_relations_to ON memory_relations(to_memory_id);
CREATE INDEX idx_relations_type ON memory_relations(relation_type);

-- Create memory consolidation tracking
CREATE TABLE memory_consolidations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    original_memory_ids UUID[] NOT NULL,
    consolidated_memory_id UUID REFERENCES memories(id) ON DELETE CASCADE,
    consolidation_type TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Update timestamp trigger for memories
CREATE TRIGGER update_memories_updated_at BEFORE UPDATE ON memories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create vector similarity search function
CREATE OR REPLACE FUNCTION search_memories_by_embedding(
    query_embedding vector(768),
    memory_types memory_type[] DEFAULT NULL,
    limit_count INTEGER DEFAULT 10,
    similarity_threshold REAL DEFAULT 0.7
)
RETURNS TABLE (
    id UUID,
    type memory_type,
    content TEXT,
    metadata JSONB,
    similarity REAL,
    importance REAL,
    access_count INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        m.id,
        m.type,
        m.content,
        m.metadata,
        1 - (m.embedding <=> query_embedding) as similarity,
        m.importance,
        m.access_count
    FROM memories m
    WHERE
        (memory_types IS NULL OR m.type = ANY(memory_types))
        AND m.embedding IS NOT NULL
        AND 1 - (m.embedding <=> query_embedding) >= similarity_threshold
    ORDER BY m.embedding <=> query_embedding
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- Create hybrid search function (vector + text)
CREATE OR REPLACE FUNCTION search_memories_hybrid(
    query_embedding vector(768),
    query_text TEXT,
    memory_types memory_type[] DEFAULT NULL,
    limit_count INTEGER DEFAULT 10,
    similarity_threshold REAL DEFAULT 0.7,
    vector_weight REAL DEFAULT 0.7
)
RETURNS TABLE (
    id UUID,
    type memory_type,
    content TEXT,
    metadata JSONB,
    combined_score REAL
) AS $$
BEGIN
    RETURN QUERY
    WITH vector_search AS (
        SELECT
            m.id,
            m.type,
            m.content,
            m.metadata,
            1 - (m.embedding <=> query_embedding) as similarity
        FROM memories m
        WHERE
            (memory_types IS NULL OR m.type = ANY(memory_types))
            AND m.embedding IS NOT NULL
            AND 1 - (m.embedding <=> query_embedding) >= similarity_threshold
    ),
    text_search AS (
        SELECT
            m.id,
            m.type,
            m.content,
            m.metadata,
            ts_rank_cd(
                to_tsvector('english', m.content),
                plainto_tsquery('english', query_text)
            ) as rank
        FROM memories m
        WHERE
            query_text IS NOT NULL
            AND (memory_types IS NULL OR m.type = ANY(memory_types))
            AND to_tsvector('english', m.content) @@ plainto_tsquery('english', query_text)
    ),
    combined AS (
        SELECT
            COALESCE(v.id, t.id) as id,
            COALESCE(v.type, t.type) as type,
            COALESCE(v.content, t.content) as content,
            COALESCE(v.metadata, t.metadata) as metadata,
            COALESCE(v.similarity * vector_weight, 0) +
            COALESCE(t.rank * (1 - vector_weight), 0) as combined_score
        FROM vector_search v
        FULL OUTER JOIN text_search t ON v.id = t.id
    )
    SELECT * FROM combined
    WHERE combined_score > 0
    ORDER BY combined_score DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- Function to update access count and timestamp
CREATE OR REPLACE FUNCTION update_memory_access(memory_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE memories
    SET
        access_count = access_count + 1,
        last_accessed = NOW()
    WHERE id = memory_id;
END;
$$ LANGUAGE plpgsql;

-- Function to compute memory importance based on access patterns
CREATE OR REPLACE FUNCTION compute_memory_importance(
    access_count INTEGER,
    created_at TIMESTAMPTZ,
    last_accessed TIMESTAMPTZ
)
RETURNS REAL AS $$
DECLARE
    age_days INTEGER;
    recency_days INTEGER;
    frequency_score REAL;
    recency_score REAL;
BEGIN
    age_days := EXTRACT(EPOCH FROM (NOW() - created_at)) / 86400;
    recency_days := EXTRACT(EPOCH FROM (NOW() - last_accessed)) / 86400;

    -- Frequency component (logarithmic scale)
    frequency_score := LEAST(ln(access_count + 1) / 10, 1);

    -- Recency component (exponential decay)
    recency_score := exp(-recency_days / 30);

    -- Combined score
    RETURN (frequency_score * 0.4 + recency_score * 0.6);
END;
$$ LANGUAGE plpgsql;

-- Create background job to update importance scores
CREATE OR REPLACE FUNCTION update_memory_importance_scores()
RETURNS void AS $$
BEGIN
    UPDATE memories
    SET importance = compute_memory_importance(access_count, created_at, last_accessed)
    WHERE last_accessed > NOW() - INTERVAL '30 days';
END;
$$ LANGUAGE plpgsql;

-- Create monitoring view
CREATE OR REPLACE VIEW memory_statistics AS
SELECT
    type,
    COUNT(*) as count,
    AVG(importance) as avg_importance,
    AVG(access_count) as avg_access_count,
    MAX(created_at) as latest_memory,
    SUM(CASE WHEN embedding IS NOT NULL THEN 1 ELSE 0 END) as embedded_count
FROM memories
GROUP BY type;

-- Migrate existing data if needed (optional, customize based on needs)
-- This is a template - adjust based on your specific data
/*
INSERT INTO memories (type, content, metadata, timestamp, importance)
SELECT
    CASE
        WHEN type = 'fact' THEN 'semantic'::memory_type
        WHEN type = 'preference' THEN 'semantic'::memory_type
        WHEN type = 'event' THEN 'episodic'::memory_type
        ELSE 'semantic'::memory_type
    END as type,
    content,
    metadata,
    created_at as timestamp,
    importance
FROM knowledge_nodes
WHERE content IS NOT NULL AND content != '';
*/

-- Add comment explaining the system
COMMENT ON TABLE memories IS 'Unified memory system inspired by mem0 architecture. Stores episodic (events), semantic (facts/knowledge), and procedural (how-to) memories with vector embeddings for similarity search.';
COMMENT ON COLUMN memories.embedding IS 'Vector embedding from Gemini gemini-embedding-001 model (768 dimensions)';
COMMENT ON COLUMN memories.importance IS 'Computed importance score based on access frequency and recency (0-1)';
COMMENT ON TABLE memory_relations IS 'Relationships between memories for graph-based traversal';
COMMENT ON TABLE memory_consolidations IS 'Tracks memory consolidation operations for audit and rollback';
