-- 002_knowledge_schema_improvements.down.sql
-- Rollback knowledge_nodes schema improvements

-- ===========================================
-- Step 1: Drop new functions
-- ===========================================

DROP FUNCTION IF EXISTS search_nodes_by_content;
DROP FUNCTION IF EXISTS upsert_knowledge_node;

-- ===========================================
-- Step 2: Restore data to metadata
-- ===========================================

-- Move content back to metadata
UPDATE knowledge_nodes 
SET metadata = jsonb_set(
    jsonb_set(
        jsonb_set(
            metadata,
            '{content}',
            to_jsonb(content)
        ),
        '{observations}',
        to_jsonb(observations)
    ),
    '{created_at}',
    to_jsonb(learned_at)
)
WHERE content IS NOT NULL OR array_length(observations, 1) > 0;

-- ===========================================
-- Step 3: Drop indexes
-- ===========================================

DROP INDEX IF EXISTS idx_knowledge_nodes_type_content;
DROP INDEX IF EXISTS idx_knowledge_nodes_learned_at;
DROP INDEX IF EXISTS idx_knowledge_nodes_source;
DROP INDEX IF EXISTS idx_knowledge_nodes_content_hash;
DROP INDEX IF EXISTS idx_knowledge_nodes_content_trgm;

-- ===========================================
-- Step 4: Drop constraints
-- ===========================================

ALTER TABLE knowledge_nodes DROP CONSTRAINT IF EXISTS node_source_type_valid;
ALTER TABLE knowledge_nodes DROP CONSTRAINT IF EXISTS node_source_consistency;
ALTER TABLE knowledge_nodes DROP CONSTRAINT IF EXISTS node_content_hash_consistency;
ALTER TABLE knowledge_nodes DROP CONSTRAINT IF EXISTS node_content_not_empty;

-- ===========================================
-- Step 5: Drop columns
-- ===========================================

ALTER TABLE knowledge_nodes DROP COLUMN IF EXISTS source_type;
ALTER TABLE knowledge_nodes DROP COLUMN IF EXISTS source_id;
ALTER TABLE knowledge_nodes DROP COLUMN IF EXISTS learned_at;
ALTER TABLE knowledge_nodes DROP COLUMN IF EXISTS observations;
ALTER TABLE knowledge_nodes DROP COLUMN IF EXISTS content_hash;
ALTER TABLE knowledge_nodes DROP COLUMN IF EXISTS content;

-- ===========================================
-- Step 6: Restore original node types
-- ===========================================

-- Create the original type
CREATE TYPE knowledge_node_type_old AS ENUM (
    'owner',
    'person',
    'preference',
    'fact',
    'skill',
    'event',
    'location',
    'object',
    'concept',
    'schedule'
);

-- Map back to original types
UPDATE knowledge_nodes 
SET type = CASE 
    WHEN type = 'task' THEN 'fact'::knowledge_node_type
    WHEN type = 'note' THEN 'fact'::knowledge_node_type
    ELSE type
END;

-- Update the column type
ALTER TABLE knowledge_nodes 
ALTER COLUMN type TYPE knowledge_node_type_old 
USING type::text::knowledge_node_type_old;

-- Drop the new type and rename the old one
DROP TYPE knowledge_node_type CASCADE;
ALTER TYPE knowledge_node_type_old RENAME TO knowledge_node_type;

-- ===========================================
-- Step 7: Restore original edge types
-- ===========================================

-- Create the original edge type
CREATE TYPE knowledge_edge_type_old AS ENUM (
    'likes',
    'dislikes',
    'knows',
    'owns',
    'located_at',
    'scheduled_at',
    'related_to',
    'depends_on',
    'interested_in',
    'skilled_in'
);

-- Map back to original types (best effort)
UPDATE knowledge_edges 
SET type = CASE 
    WHEN type = 'relates_to' THEN 'related_to'::knowledge_edge_type
    WHEN type = 'belongs_to' THEN 'owns'::knowledge_edge_type
    WHEN type = 'mentions' THEN 'related_to'::knowledge_edge_type
    WHEN type = 'causes' THEN 'related_to'::knowledge_edge_type
    WHEN type = 'precedes' THEN 'related_to'::knowledge_edge_type
    WHEN type = 'includes' THEN 'related_to'::knowledge_edge_type
    WHEN type = 'contradicts' THEN 'related_to'::knowledge_edge_type
    WHEN type = 'supports' THEN 'related_to'::knowledge_edge_type
    WHEN type = 'derived_from' THEN 'related_to'::knowledge_edge_type
    ELSE type
END;

-- Update the column type
ALTER TABLE knowledge_edges 
ALTER COLUMN type TYPE knowledge_edge_type_old 
USING type::text::knowledge_edge_type_old;

-- Drop the new type and rename the old one
DROP TYPE knowledge_edge_type CASCADE;
ALTER TYPE knowledge_edge_type_old RENAME TO knowledge_edge_type;