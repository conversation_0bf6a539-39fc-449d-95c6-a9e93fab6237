-- 001_initial_schema.up.sql
-- Assistant Go initial database schema
-- Following PostgreSQL 17 best practices

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";      -- UUID generation
CREATE EXTENSION IF NOT EXISTS "vector";         -- Vector search
CREATE EXTENSION IF NOT EXISTS "pg_trgm";        -- Text search
CREATE EXTENSION IF NOT EXISTS "btree_gist";     -- Exclusion constraint support

-- ===========================================
-- ENUMs - Using PostgreSQL native type system
-- ===========================================

-- Conversation status
CREATE TYPE conversation_status AS ENUM (
    'active',      -- In progress
    'archived',    -- Archived
    'deleted'      -- Deleted (soft delete)
);

-- Message role
CREATE TYPE message_role AS ENUM (
    'user',        -- User
    'assistant',   -- AI assistant
    'system',      -- System message
    'tool',        -- Tool output
    'error'        -- Error message
);

-- Task status
CREATE TYPE task_status AS ENUM (
    'todo',        -- To do
    'in_progress', -- In progress
    'done',        -- Completed
    'cancelled',   -- Cancelled
    'deferred'     -- Deferred
);

-- Task priority
CREATE TYPE task_priority AS ENUM (
    'low',         -- Low
    'medium',      -- Medium
    'high',        -- High
    'urgent',      -- Urgent
    'critical'     -- Critical
);

-- Knowledge node type
CREATE TYPE knowledge_node_type AS ENUM (
    'owner',       -- Owner
    'person',      -- Person
    'preference',  -- Preference
    'fact',        -- Fact
    'skill',       -- Skill
    'event',       -- Event
    'location',    -- Location
    'object',      -- Object
    'concept',     -- Concept
    'schedule'     -- Schedule
);

-- Knowledge edge type (relationships)
CREATE TYPE knowledge_edge_type AS ENUM (
    'likes',           -- Likes
    'dislikes',        -- Dislikes
    'knows',           -- Knows
    'owns',            -- Owns
    'located_at',      -- Located at
    'scheduled_at',    -- Scheduled at
    'related_to',      -- Related to
    'depends_on',      -- Depends on
    'interested_in',   -- Interested in
    'skilled_in'       -- Skilled in
);

-- Tool execution status
CREATE TYPE tool_status AS ENUM (
    'pending',     -- Pending execution
    'running',     -- Running
    'success',     -- Success
    'failed',      -- Failed
    'timeout',     -- Timeout
    'cancelled'    -- Cancelled
);

-- ===========================================
-- Core tables
-- ===========================================

-- Conversations table
CREATE TABLE conversations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL DEFAULT 'New Conversation',
    summary TEXT,
    status conversation_status NOT NULL DEFAULT 'active',
    metadata JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_message_at TIMESTAMPTZ,
    message_count INTEGER NOT NULL DEFAULT 0,
    total_tokens INTEGER NOT NULL DEFAULT 0,

    -- Constraints
    CONSTRAINT conv_message_count_positive CHECK (message_count >= 0),
    CONSTRAINT conv_total_tokens_positive CHECK (total_tokens >= 0),
    CONSTRAINT conv_title_not_empty CHECK (trim(title) != '')
);

-- Messages table
CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    role message_role NOT NULL,
    content TEXT NOT NULL,
    tokens_used INTEGER NOT NULL DEFAULT 0,
    metadata JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    CONSTRAINT msg_tokens_positive CHECK (tokens_used >= 0),
    CONSTRAINT msg_content_not_empty CHECK (trim(content) != '')
);

-- Knowledge graph nodes table
CREATE TABLE knowledge_nodes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    type knowledge_node_type NOT NULL,
    metadata JSONB NOT NULL DEFAULT '{}',
    importance FLOAT NOT NULL DEFAULT 0.5,
    access_count INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_accessed_at TIMESTAMPTZ,

    -- Constraints
    CONSTRAINT node_name_not_empty CHECK (trim(name) != ''),
    CONSTRAINT node_importance_range CHECK (importance >= 0 AND importance <= 1),
    CONSTRAINT node_access_count_positive CHECK (access_count >= 0),
    CONSTRAINT node_unique_name_type UNIQUE (name, type)
);

-- Knowledge graph edges table
CREATE TABLE knowledge_edges (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    from_node_id UUID NOT NULL REFERENCES knowledge_nodes(id) ON DELETE CASCADE,
    to_node_id UUID NOT NULL REFERENCES knowledge_nodes(id) ON DELETE CASCADE,
    type knowledge_edge_type NOT NULL,
    weight FLOAT NOT NULL DEFAULT 1.0,
    metadata JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    CONSTRAINT edge_no_self_loop CHECK (from_node_id != to_node_id),
    CONSTRAINT edge_weight_positive CHECK (weight > 0),
    CONSTRAINT edge_unique_relationship UNIQUE (from_node_id, to_node_id, type)
);

-- Unified embeddings table
CREATE TABLE embeddings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    source_table TEXT NOT NULL,
    source_id UUID NOT NULL,
    content TEXT NOT NULL,
    embedding vector(768) NOT NULL,  -- Standardized to 768 dimensions
    model TEXT NOT NULL DEFAULT 'gemini-embedding-001',
    metadata JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    CONSTRAINT emb_content_not_empty CHECK (trim(content) != ''),
    CONSTRAINT emb_source_table_valid CHECK (source_table IN ('messages', 'knowledge_nodes', 'tasks', 'memory_backup')),
    CONSTRAINT emb_unique_source UNIQUE (source_table, source_id)
);

-- Tasks table
CREATE TABLE tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    description TEXT,
    status task_status NOT NULL DEFAULT 'todo',
    priority task_priority NOT NULL DEFAULT 'medium',
    due_date TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    labels TEXT[] NOT NULL DEFAULT '{}',
    metadata JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    CONSTRAINT task_title_not_empty CHECK (trim(title) != ''),
    CONSTRAINT task_completion_consistency CHECK (
        (status = 'done' AND completed_at IS NOT NULL) OR
        (status != 'done' AND completed_at IS NULL)
    ),
    CONSTRAINT task_due_date_future CHECK (due_date IS NULL OR due_date > created_at)
);

-- Tool execution logs table
CREATE TABLE tool_executions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID REFERENCES conversations(id) ON DELETE SET NULL,
    tool_name TEXT NOT NULL,
    input JSONB NOT NULL DEFAULT '{}',
    output JSONB,
    status tool_status NOT NULL DEFAULT 'pending',
    error_message TEXT,
    duration_ms INTEGER,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMPTZ,

    -- Constraints
    CONSTRAINT tool_name_not_empty CHECK (trim(tool_name) != ''),
    CONSTRAINT tool_duration_positive CHECK (duration_ms IS NULL OR duration_ms >= 0),
    CONSTRAINT tool_completion_consistency CHECK (
        (status IN ('success', 'failed', 'timeout') AND completed_at IS NOT NULL) OR
        (status IN ('pending', 'running', 'cancelled') AND completed_at IS NULL)
    )
);

-- System settings table
CREATE TABLE system_settings (
    key TEXT PRIMARY KEY,
    value JSONB NOT NULL,
    description TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    CONSTRAINT setting_key_not_empty CHECK (trim(key) != ''),
    CONSTRAINT setting_key_format CHECK (key ~ '^[a-z][a-z0-9_]*$')
);

-- Memory backups table
CREATE TABLE memory_backups (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    backup_data JSONB NOT NULL,
    node_count INTEGER NOT NULL DEFAULT 0,
    edge_count INTEGER NOT NULL DEFAULT 0,
    version TEXT NOT NULL DEFAULT '1.0',
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    CONSTRAINT backup_counts_positive CHECK (node_count >= 0 AND edge_count >= 0)
);

-- ===========================================
-- Indexes - Optimize query performance
-- ===========================================

-- Conversation indexes
CREATE INDEX idx_conversations_status ON conversations(status) WHERE status = 'active';
CREATE INDEX idx_conversations_updated_at ON conversations(updated_at DESC);
CREATE INDEX idx_conversations_last_message ON conversations(last_message_at DESC NULLS LAST);
CREATE INDEX idx_conversations_metadata ON conversations USING GIN (metadata);

-- Message indexes
CREATE INDEX idx_messages_conversation ON messages(conversation_id, created_at DESC);
CREATE INDEX idx_messages_role ON messages(role) WHERE role != 'user';
CREATE INDEX idx_messages_created_at ON messages(created_at DESC);
CREATE INDEX idx_messages_metadata ON messages USING GIN (metadata) WHERE jsonb_typeof(metadata) = 'object';

-- Knowledge node indexes
CREATE INDEX idx_knowledge_nodes_type ON knowledge_nodes(type);
CREATE INDEX idx_knowledge_nodes_name_trgm ON knowledge_nodes USING GIN (name gin_trgm_ops);
CREATE INDEX idx_knowledge_nodes_importance ON knowledge_nodes(importance DESC) WHERE importance > 0.5;
CREATE INDEX idx_knowledge_nodes_access ON knowledge_nodes(access_count DESC, last_accessed_at DESC NULLS LAST);
CREATE INDEX idx_knowledge_nodes_metadata ON knowledge_nodes USING GIN (metadata);

-- Knowledge edge indexes
CREATE INDEX idx_knowledge_edges_from ON knowledge_edges(from_node_id);
CREATE INDEX idx_knowledge_edges_to ON knowledge_edges(to_node_id);
CREATE INDEX idx_knowledge_edges_type ON knowledge_edges(type);
CREATE INDEX idx_knowledge_edges_weight ON knowledge_edges(weight DESC) WHERE weight > 1.0;

-- Embedding vector indexes
CREATE INDEX idx_embeddings_source ON embeddings(source_table, source_id);
CREATE INDEX idx_embeddings_vector ON embeddings USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);
CREATE INDEX idx_embeddings_created_at ON embeddings(created_at DESC);

-- Task indexes
CREATE INDEX idx_tasks_status ON tasks(status) WHERE status IN ('todo', 'in_progress');
CREATE INDEX idx_tasks_priority ON tasks(priority, created_at DESC) WHERE status != 'done';
CREATE INDEX idx_tasks_due_date ON tasks(due_date ASC NULLS LAST) WHERE status != 'done';
CREATE INDEX idx_tasks_labels ON tasks USING GIN (labels);
CREATE INDEX idx_tasks_metadata ON tasks USING GIN (metadata);

-- Tool execution indexes
CREATE INDEX idx_tool_executions_conversation ON tool_executions(conversation_id) WHERE conversation_id IS NOT NULL;
CREATE INDEX idx_tool_executions_tool ON tool_executions(tool_name);
CREATE INDEX idx_tool_executions_status ON tool_executions(status) WHERE status IN ('running', 'pending');
CREATE INDEX idx_tool_executions_created ON tool_executions(created_at DESC);

-- System settings indexes
CREATE INDEX idx_system_settings_updated ON system_settings(updated_at DESC);

-- Memory backup indexes
CREATE INDEX idx_memory_backups_created ON memory_backups(created_at DESC);

-- ===========================================
-- Trigger functions
-- ===========================================

-- Update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Update conversation's last_message_at and statistics
CREATE OR REPLACE FUNCTION update_conversation_stats()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE conversations
    SET
        last_message_at = NEW.created_at,
        message_count = message_count + 1,
        total_tokens = total_tokens + NEW.tokens_used
    WHERE id = NEW.conversation_id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Update knowledge node access statistics
CREATE OR REPLACE FUNCTION update_node_access()
RETURNS TRIGGER AS $$
BEGIN
    NEW.access_count = OLD.access_count + 1;
    NEW.last_accessed_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ===========================================
-- Triggers
-- ===========================================

-- updated_at triggers
CREATE TRIGGER update_conversations_updated_at BEFORE UPDATE ON conversations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_knowledge_nodes_updated_at BEFORE UPDATE ON knowledge_nodes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_knowledge_edges_updated_at BEFORE UPDATE ON knowledge_edges
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tasks_updated_at BEFORE UPDATE ON tasks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_settings_updated_at BEFORE UPDATE ON system_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Conversation statistics trigger
CREATE TRIGGER update_conversation_stats_on_message AFTER INSERT ON messages
    FOR EACH ROW EXECUTE FUNCTION update_conversation_stats();

-- Node access trigger
CREATE TRIGGER update_node_access_on_select BEFORE UPDATE ON knowledge_nodes
    FOR EACH ROW
    WHEN (OLD.access_count IS DISTINCT FROM NEW.access_count)
    EXECUTE FUNCTION update_node_access();

-- ===========================================
-- Functions - Business logic
-- ===========================================

-- Search similar embeddings
CREATE OR REPLACE FUNCTION search_similar_embeddings(
    query_embedding vector(768),
    search_limit INTEGER DEFAULT 10,
    source_tables TEXT[] DEFAULT NULL,
    similarity_threshold FLOAT DEFAULT 0.5
)
RETURNS TABLE (
    id UUID,
    source_table TEXT,
    source_id UUID,
    content TEXT,
    similarity FLOAT,
    metadata JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        e.id,
        e.source_table,
        e.source_id,
        e.content,
        1 - (e.embedding <=> query_embedding) AS similarity,
        e.metadata
    FROM embeddings e
    WHERE
        (source_tables IS NULL OR e.source_table = ANY(source_tables))
        AND 1 - (e.embedding <=> query_embedding) > similarity_threshold
    ORDER BY e.embedding <=> query_embedding
    LIMIT search_limit;
END;
$$ LANGUAGE plpgsql;

-- Full-text search knowledge nodes
CREATE OR REPLACE FUNCTION search_knowledge_nodes(
    query_text TEXT,
    node_type knowledge_node_type DEFAULT NULL,
    search_limit INTEGER DEFAULT 20
)
RETURNS TABLE (
    id UUID,
    name TEXT,
    type knowledge_node_type,
    metadata JSONB,
    importance FLOAT,
    relevance FLOAT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        n.id,
        n.name,
        n.type,
        n.metadata,
        n.importance,
        ts_rank(
            to_tsvector('english', n.name || ' ' || COALESCE(n.metadata->>'description', '')),
            plainto_tsquery('english', query_text)
        ) * n.importance AS relevance
    FROM knowledge_nodes n
    WHERE
        (node_type IS NULL OR n.type = node_type)
        AND (
            n.name ILIKE '%' || query_text || '%'
            OR n.metadata->>'description' ILIKE '%' || query_text || '%'
        )
    ORDER BY relevance DESC, n.updated_at DESC
    LIMIT search_limit;
END;
$$ LANGUAGE plpgsql;

-- Get node relationship graph
CREATE OR REPLACE FUNCTION get_node_relations(
    p_node_id UUID,
    p_depth INTEGER DEFAULT 1,
    p_edge_types knowledge_edge_type[] DEFAULT NULL
)
RETURNS TABLE (
    level INTEGER,
    node_id UUID,
    node_name TEXT,
    node_type knowledge_node_type,
    edge_id UUID,
    edge_type knowledge_edge_type,
    edge_weight FLOAT,
    related_node_id UUID,
    related_node_name TEXT,
    related_node_type knowledge_node_type,
    direction TEXT
) AS $$
BEGIN
    RETURN QUERY
    WITH RECURSIVE relations AS (
    -- Base level (level 0)
    SELECT
        0 AS level,
        n.id AS node_id,
        n.name AS node_name,
        n.type AS node_type,
        NULL::UUID AS edge_id,
        NULL::knowledge_edge_type AS edge_type,
        NULL::FLOAT AS edge_weight,
        NULL::UUID AS related_node_id,
        NULL::TEXT AS related_node_name,
        NULL::knowledge_node_type AS related_node_type,
        NULL::TEXT AS direction
    FROM knowledge_nodes n
    WHERE n.id = p_node_id

    UNION ALL

    -- Recursive query for outgoing edges
    SELECT
        r.level + 1,
        r.node_id,
        r.node_name,
        r.node_type,
        e.id,
        e.type,
        e.weight,
        n2.id,
        n2.name,
        n2.type,
        'outgoing'::TEXT
    FROM relations r
    JOIN knowledge_edges e ON r.node_id = e.from_node_id
    JOIN knowledge_nodes n2 ON e.to_node_id = n2.id
    WHERE
        r.level < p_depth
        AND (p_edge_types IS NULL OR e.type = ANY(p_edge_types))

    UNION ALL

    -- Recursive query for incoming edges
    SELECT
        r.level + 1,
        r.node_id,
        r.node_name,
        r.node_type,
        e.id,
        e.type,
        e.weight,
        n2.id,
        n2.name,
        n2.type,
        'incoming'::TEXT
    FROM relations r
    JOIN knowledge_edges e ON r.node_id = e.to_node_id
    JOIN knowledge_nodes n2 ON e.from_node_id = n2.id
    WHERE
        r.level < p_depth
        AND (p_edge_types IS NULL OR e.type = ANY(p_edge_types))
)
SELECT * FROM relations
WHERE level > 0
ORDER BY level, node_id, direction, edge_weight DESC;
END;
$$ LANGUAGE plpgsql;

-- Clean up old backups
CREATE OR REPLACE FUNCTION cleanup_old_backups(
    keep_count INTEGER DEFAULT 10,
    keep_days INTEGER DEFAULT 30
)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    WITH backups_to_delete AS (
        SELECT id
        FROM memory_backups
        WHERE id NOT IN (
            SELECT id
            FROM memory_backups
            ORDER BY created_at DESC
            LIMIT keep_count
        )
        AND created_at < CURRENT_TIMESTAMP - INTERVAL '1 day' * keep_days
    )
    DELETE FROM memory_backups
    WHERE id IN (SELECT id FROM backups_to_delete);

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- ===========================================
-- Initial data
-- ===========================================

-- System settings initial values
INSERT INTO system_settings (key, value, description) VALUES
    ('owner', '{"name": "Koopa", "id": "koopa", "timezone": "Asia/Taipei", "language": "zh-TW"}', 'System owner information'),
    ('assistant', '{"name": "Assistant", "version": "1.0.0", "model": "claude-3-sonnet"}', 'AI assistant configuration'),
    ('preferences', '{"theme": "dark", "language": "zh-TW", "date_format": "YYYY-MM-DD"}', 'User preferences'),
    ('features', '{"rag_enabled": true, "tools_enabled": true, "memory_persistence": true}', 'Feature toggles')
ON CONFLICT (key) DO NOTHING;

-- Create Koopa's owner node
INSERT INTO knowledge_nodes (name, type, metadata, importance) VALUES
    ('Koopa', 'owner', '{"description": "System owner", "attributes": {"language": "Traditional Chinese", "timezone": "Asia/Taipei"}}', 1.0)
ON CONFLICT (name, type) DO NOTHING;

-- ===========================================
-- Permissions and security
-- ===========================================

-- Create application-specific role (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_roles WHERE rolname = 'assistant_app') THEN
        CREATE ROLE assistant_app WITH LOGIN;
    END IF;
END $$;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO assistant_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO assistant_app;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO assistant_app;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO assistant_app;

-- ===========================================
-- Comment documentation
-- ===========================================

COMMENT ON SCHEMA public IS 'Assistant Go main database schema';

COMMENT ON TABLE conversations IS 'Conversation session records, tracking user and AI interaction history';
COMMENT ON TABLE messages IS 'Messages within conversations, including user input and AI responses';
COMMENT ON TABLE knowledge_nodes IS 'Knowledge graph nodes, storing entities, concepts, facts, and other information';
COMMENT ON TABLE knowledge_edges IS 'Knowledge graph edges, defining relationships between nodes';
COMMENT ON TABLE embeddings IS 'Unified vector embeddings table, supporting semantic search and similarity matching';
COMMENT ON TABLE tasks IS 'Task management system, tracking to-do items';
COMMENT ON TABLE tool_executions IS 'Tool execution history, recording AI tool usage';
COMMENT ON TABLE system_settings IS 'System settings key-value pairs, storing application configuration';
COMMENT ON TABLE memory_backups IS 'Knowledge graph backups, ensuring memory persistence';

COMMENT ON FUNCTION search_similar_embeddings IS 'Semantic search based on vector similarity';
COMMENT ON FUNCTION search_knowledge_nodes IS 'Full-text search for knowledge nodes';
COMMENT ON FUNCTION get_node_relations IS 'Recursively query node relationship network';
COMMENT ON FUNCTION cleanup_old_backups IS 'Clean up expired memory backups';
