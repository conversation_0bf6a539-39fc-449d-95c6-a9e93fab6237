//go:build integration
// +build integration

package database

import (
	"context"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"

	"github.com/koopa0/assistant-go/internal/storage/database/mocks"
	"github.com/koopa0/assistant-go/internal/storage/database/sqlc"
)

// TestNewClient tests client creation
func TestNewClient(t *testing.T) {
	t.Run("successful creation", func(t *testing.T) {
		testDBURL := os.Getenv("TEST_DATABASE_URL")
		if testDBURL == "" {
			t.Skip("TEST_DATABASE_URL not set")
		}

		ctx := context.Background()
		config := &Config{
			DSN: testDBURL,
		}

		client, err := NewClient(ctx, config)
		require.NoError(t, err)
		require.NotNil(t, client)
		defer client.Close()

		// Verify client is functional
		err = client.Ping(ctx)
		assert.NoError(t, err)

		// Verify queries is set
		assert.NotNil(t, client.Queries())

		// Verify DB is accessible
		assert.NotNil(t, client.DB())

		// Verify Pool is accessible
		assert.NotNil(t, client.Pool())
	})

	t.Run("invalid config", func(t *testing.T) {
		ctx := context.Background()

		// Test with nil config
		client, err := NewClient(ctx, nil)
		assert.Error(t, err)
		assert.Nil(t, client)
		assert.Contains(t, err.Error(), "config is required")

		// Test with invalid DSN
		config := &Config{
			DSN: "invalid://dsn",
		}
		client, err = NewClient(ctx, config)
		assert.Error(t, err)
		assert.Nil(t, client)
		assert.Contains(t, err.Error(), "failed to create database connection")
	})

	t.Run("connection failure", func(t *testing.T) {
		ctx := context.Background()
		config := &Config{
			DSN: "postgres://invalid:invalid@localhost:99999/invalid",
		}

		client, err := NewClient(ctx, config)
		assert.Error(t, err)
		assert.Nil(t, client)
		assert.Contains(t, err.Error(), "failed to create database connection")
	})
}

// TestClient_Methods tests various client methods
func TestClient_Methods(t *testing.T) {
	testDBURL := os.Getenv("TEST_DATABASE_URL")
	if testDBURL == "" {
		t.Skip("TEST_DATABASE_URL not set")
	}

	ctx := context.Background()
	config := &Config{
		DSN: testDBURL,
	}

	client, err := NewClient(ctx, config)
	require.NoError(t, err)
	defer client.Close()

	t.Run("DB method", func(t *testing.T) {
		db := client.DB()
		assert.NotNil(t, db)

		// Verify the DB is functional
		err := db.Ping(ctx)
		assert.NoError(t, err)
	})

	t.Run("Queries method", func(t *testing.T) {
		queries := client.Queries()
		assert.NotNil(t, queries)

		// Verify it's the correct type
		_, ok := queries.(*sqlc.Queries)
		assert.True(t, ok, "Queries should return *sqlc.Queries")
	})

	t.Run("Pool method", func(t *testing.T) {
		pool := client.Pool()
		assert.NotNil(t, pool)

		// Verify pool is functional
		err := pool.Ping(ctx)
		assert.NoError(t, err)

		// Check pool stats
		stats := pool.Stat()
		assert.NotNil(t, stats)
		assert.GreaterOrEqual(t, stats.TotalConns(), int32(0))
	})

	t.Run("Ping method", func(t *testing.T) {
		err := client.Ping(ctx)
		assert.NoError(t, err)
	})

	t.Run("Close method", func(t *testing.T) {
		// Create a new client for this test
		newClient, err := NewClient(ctx, config)
		require.NoError(t, err)

		// Close should not error
		assert.NotPanics(t, func() {
			newClient.Close()
		})

		// After close, ping should fail
		err = newClient.Ping(ctx)
		assert.Error(t, err)
	})
}

// TestClient_WithTx tests transaction handling through client
func TestClient_WithTx(t *testing.T) {
	testDBURL := os.Getenv("TEST_DATABASE_URL")
	if testDBURL == "" {
		t.Skip("TEST_DATABASE_URL not set")
	}

	ctx := context.Background()
	config := &Config{
		DSN: testDBURL,
	}

	client, err := NewClient(ctx, config)
	require.NoError(t, err)
	defer client.Close()

	t.Run("successful transaction", func(t *testing.T) {
		executed := false
		err := client.WithTx(ctx, func(queries sqlc.Querier) error {
			executed = true
			// Verify we got a valid querier
			assert.NotNil(t, queries)
			return nil
		})

		assert.NoError(t, err)
		assert.True(t, executed)
	})

	t.Run("transaction with error", func(t *testing.T) {
		testErr := assert.AnError
		err := client.WithTx(ctx, func(queries sqlc.Querier) error {
			return testErr
		})

		assert.Error(t, err)
		assert.Equal(t, testErr, err)
	})

	t.Run("transaction with queries", func(t *testing.T) {
		// Create a test table
		_, err := client.Pool().Exec(ctx, `
			CREATE TABLE IF NOT EXISTS test_client_tx (
				id SERIAL PRIMARY KEY,
				value TEXT NOT NULL
			)
		`)
		require.NoError(t, err)

		// Clean up before test
		_, _ = client.Pool().Exec(ctx, "DELETE FROM test_client_tx")

		// Use transaction to insert data
		err = client.WithTx(ctx, func(queries sqlc.Querier) error {
			// In a real scenario, you would use queries methods here
			// For this test, we'll just verify the querier is valid
			_, ok := queries.(*sqlc.Queries)
			require.True(t, ok)

			// In real usage, you would call queries.CreateSomething() etc.
			return nil
		})

		assert.NoError(t, err)

		// Clean up
		_, _ = client.Pool().Exec(ctx, "DROP TABLE test_client_tx")
	})
}

// TestClient_RunMigrations tests migration functionality
func TestClient_RunMigrations(t *testing.T) {
	testDBURL := os.Getenv("TEST_DATABASE_URL")
	if testDBURL == "" {
		t.Skip("TEST_DATABASE_URL not set")
	}

	ctx := context.Background()
	config := &Config{
		DSN: testDBURL,
	}

	client, err := NewClient(ctx, config)
	require.NoError(t, err)
	defer client.Close()

	t.Run("run migrations", func(t *testing.T) {
		err := client.RunMigrations()
		assert.NoError(t, err)

		// Run again - should handle already migrated state
		err = client.RunMigrations()
		assert.NoError(t, err)
	})
}

// TestClient_QueriesInterface tests that Queries returns proper interface
func TestClient_QueriesInterface(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockQuerier := mocks.NewMockQuerier(ctrl)

	client := &Client{
		db:      nil,
		queries: mockQuerier,
	}

	queries := client.Queries()
	assert.Equal(t, mockQuerier, queries)

	// Verify it implements sqlc.Querier
	var _ sqlc.Querier = queries
}

// BenchmarkClient_Methods benchmarks client method performance
func BenchmarkClient_Methods(b *testing.B) {
	testDBURL := os.Getenv("TEST_DATABASE_URL")
	if testDBURL == "" {
		b.Skip("TEST_DATABASE_URL not set")
	}

	ctx := context.Background()
	config := &Config{
		DSN: testDBURL,
	}

	client, err := NewClient(ctx, config)
	if err != nil {
		b.Fatal(err)
	}
	defer client.Close()

	b.Run("Queries", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = client.Queries()
		}
	})

	b.Run("DB", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = client.DB()
		}
	})

	b.Run("Pool", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = client.Pool()
		}
	})

	b.Run("Ping", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = client.Ping(ctx)
		}
	})

	b.Run("WithTx", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = client.WithTx(ctx, func(q sqlc.Querier) error {
				return nil
			})
		}
	})
}
