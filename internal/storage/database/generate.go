// Package database code generation directives
package database

// Generate SQL code using sqlc
//go:generate sqlc generate -f ../../../configs/sqlc.yaml

// Generate mocks for testing
//go:generate mockgen -source=sqlc/querier.go -destination=mocks/mock_querier.go -package=mocks Querier

// To regenerate all database-related code, run:
//   go generate ./internal/storage/database/...
// or use the Makefile:
//   make generate
