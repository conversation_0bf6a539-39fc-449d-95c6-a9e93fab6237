// Package database provides database access layer.
//
// WHY: Separate database package because:
// 1. Database concerns (pooling, transactions) are orthogonal to business logic
// 2. sqlc generated code needs a stable home independent of domain changes
// 3. Connection lifecycle management requires centralized control
// 4. Testing requires ability to swap real database for test fixtures
package database

import (
	"context"
	"fmt"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"

	"github.com/koopa0/assistant-go/internal/storage/database/sqlc"
)

// Client provides unified database access.
//
// WHY: Client wrapper pattern because:
// 1. sqlc.Queries alone doesn't manage connection lifecycle
// 2. Transaction coordination requires access to underlying pool
// 3. Metrics and logging need centralized interception point
// 4. Future sharding/read replicas need abstraction layer
type Client struct {
	// db manages the connection pool.
	// WHY: Encapsulated to control connection parameters and lifecycle
	db *DB
	// queries provides type-safe database operations.
	// WHY: sqlc generates from SQL, ensuring queries are valid at compile time
	queries sqlc.Querier
}

// NewClient creates a new database client with connection pool
func NewClient(ctx context.Context, config *Config) (*Client, error) {
	// Create database connection pool
	db, err := NewDB(ctx, config)
	if err != nil {
		return nil, fmt.Errorf("failed to create database connection: %w", err)
	}

	// Create sqlc queries instance
	queries := sqlc.New(db.pool)

	return &Client{
		db:      db,
		queries: queries,
	}, nil
}

// Close closes the database connection pool
// Note: pgxpool.Pool.Close() doesn't return an error in pgx v5
func (c *Client) Close() {
	c.db.Close()
}

// DB returns the underlying database connection pool
func (c *Client) DB() *DB {
	return c.db
}

// Queries returns the sqlc query interface
func (c *Client) Queries() sqlc.Querier {
	return c.queries
}

// WithTx executes a function within a transaction
// Automatically handles commit and rollback
func (c *Client) WithTx(ctx context.Context, fn func(sqlc.Querier) error) error {
	return c.db.WithTx(ctx, func(tx pgx.Tx) error {
		// Type assertion is safe here as we know queries is *sqlc.Queries
		q, ok := c.queries.(*sqlc.Queries)
		if !ok {
			return fmt.Errorf("queries is not *sqlc.Queries")
		}
		qtx := q.WithTx(tx)
		return fn(qtx)
	})
}

// Pool returns the connection pool
func (c *Client) Pool() *pgxpool.Pool {
	return c.db.Pool()
}

// Ping checks database connection
func (c *Client) Ping(ctx context.Context) error {
	return c.db.Ping(ctx)
}

// RunMigrations executes database migrations
func (c *Client) RunMigrations() error {
	return c.db.RunMigrations()
}
