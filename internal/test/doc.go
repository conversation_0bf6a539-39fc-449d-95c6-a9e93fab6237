// Package test provides testing utilities and helpers for the assistant-go project.
//
// This package contains common test fixtures, database helpers, and mock
// implementations used across the test suite. It follows Go testing best
// practices while providing convenient utilities for integration testing.
//
// # Test Database
//
// The package provides database setup helpers for tests that require
// PostgreSQL:
//
//	db := test.SetupTestDB(t)
//	defer test.CleanupTestDB(t, db)
//
// # Mock Implementations
//
// Common mocks are provided for testing:
//
//   - Mock AI clients for predictable responses
//   - Mock tool implementations
//   - Mock conversation stores
//   - Test data generators
//
// # Test Fixtures
//
// Pre-defined test data for common scenarios:
//
//   - Sample conversations
//   - Knowledge graph nodes and edges
//   - Memory entries
//   - Tool execution results
//
// # Best Practices
//
// When using this package:
//
//  1. Always clean up resources with defer
//  2. Use sub-tests for better organization
//  3. Leverage table-driven tests
//  4. Mock external dependencies
//  5. Test both success and error paths
//
// # Environment Variables
//
// Test behavior can be controlled via environment:
//
//   - TEST_DATABASE_URL: PostgreSQL connection string
//   - TEST_LOG_LEVEL: Logging verbosity (debug, info, warn, error)
//   - TEST_PARALLEL: Enable parallel test execution
//   - TEST_TIMEOUT: Override default test timeout
package test
