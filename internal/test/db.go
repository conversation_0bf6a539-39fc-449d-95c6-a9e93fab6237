package test

import (
	"context"
	"os"
	"testing"

	"github.com/jackc/pgx/v5/pgxpool"
)

// NewTestDB creates a new test database connection pool
// It automatically handles cleanup when the test completes
func NewTestDB(t *testing.T) *pgxpool.Pool {
	t.Helper()

	// Use test database URL from environment
	dbURL := os.Getenv("TEST_DATABASE_URL")
	if dbURL == "" {
		// If not set, try the regular DATABASE_URL
		dbURL = os.Getenv("DATABASE_URL")
	}

	if dbURL == "" {
		t.Skip("TEST_DATABASE_URL or DATABASE_URL not set")
	}

	// Create pool with test configuration
	config, err := pgxpool.ParseConfig(dbURL)
	if err != nil {
		t.Fatalf("failed to parse database URL: %v", err)
	}

	// Use smaller pool for tests
	config.MaxConns = 5
	config.MinConns = 1

	pool, err := pgxpool.NewWithConfig(context.Background(), config)
	if err != nil {
		t.Fatalf("failed to create test pool: %v", err)
	}

	// Verify connection
	if err := pool.Ping(context.Background()); err != nil {
		pool.Close()
		t.Fatalf("failed to ping test database: %v", err)
	}

	// Register cleanup
	t.Cleanup(func() {
		// Truncate all relevant tables to ensure test isolation
		if _, err := pool.Exec(context.Background(), `
			TRUNCATE TABLE
				memories,
				relations,
				history,
				queue,
				memory_backups,
				conversations,
				messages,
				tasks,
				tool_executions,
				embeddings
			CASCADE;
		`); err != nil {
			// Log the error but don't fail the test, as it's a cleanup step
			t.Logf("WARN: failed to truncate tables during cleanup: %v", err)
		}
		pool.Close()
	})

	return pool
}

// NewTestQueries creates a new test Queries instance
// This is a convenience function that combines NewTestDB with sqlc.New
func NewTestQueries(t *testing.T) interface{} {
	t.Helper()
	// This returns interface{} to avoid import cycle
	// Caller should type assert to *sqlc.Queries
	return NewTestDB(t)
}

// NewTestClient creates a new test database client
// This wraps the pool in a database.Client for tests
func NewTestClient(t *testing.T) interface{} {
	t.Helper()

	pool := NewTestDB(t)

	// Return a simple wrapper that implements the minimal interface
	// Tests should type assert to what they need
	return &testClient{pool: pool}
}

type testClient struct {
	pool *pgxpool.Pool
}

// Queries returns the sqlc queries instance
func (c *testClient) Queries() interface{} {
	// This will be type asserted by the caller
	// We can't import sqlc here to avoid cycles
	return c.pool
}
