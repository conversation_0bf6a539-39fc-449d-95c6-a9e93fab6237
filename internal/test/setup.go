// Package testkit provides test utilities following Go testing best practices
//
// WHY: Tests should be fast, isolated, and deterministic. This package provides
// common test infrastructure while avoiding the "test helper" anti-pattern.
// Each helper function is focused and composable, not a magical setup function.
package test

import (
	"context"
	"fmt"
	"os"
	"testing"

	"github.com/jackc/pgx/v5/pgxpool"

	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/koopa0/assistant-go/internal/ai/claude"
	"github.com/koopa0/assistant-go/internal/ai/gemini"
	config "github.com/koopa0/assistant-go/internal/platform/config"
	logger "github.com/koopa0/assistant-go/internal/platform/logger"

	// "github.com/koopa0/assistant-go/internal/memory"  // Removed - requires MCP setup
	"github.com/koopa0/assistant-go/internal/storage/database/sqlc"
)

// TestContext provides isolated test dependencies
//
// WHY: Each test should have its own isolated context to prevent
// test interference and make failures easier to debug.
type TestContext struct {
	T         *testing.T
	Context   context.Context
	Config    *config.Config
	Logger    logger.Logger
	DB        *pgxpool.Pool
	AIService *ai.Service
	Queries   sqlc.Querier
}

// NewTestContext creates a test context with minimal setup
//
// WHY: Tests should be explicit about their dependencies. This function
// only sets up the bare minimum - additional services should be added
// explicitly by each test as needed.
func NewTestContext(t *testing.T) *TestContext {
	t.Helper()

	ctx := context.Background()
	cfg := createTestConfig(t)
	log := logger.NewDefaultSlogLogger()

	return &TestContext{
		T:       t,
		Context: ctx,
		Config:  cfg,
		Logger:  log,
	}
}

// WithDatabase adds database connection to test context
//
// WHY: Not all tests need database access. This allows tests to opt-in
// to database dependencies, making unit tests faster.
func (tc *TestContext) WithDatabase() *TestContext {
	tc.T.Helper()

	// Skip if no database URL provided
	dbURL := os.Getenv("TEST_DATABASE_URL")
	if dbURL == "" {
		dbURL = os.Getenv("DATABASE_URL")
	}
	if dbURL == "" {
		tc.T.Skip("Skipping test: no database URL provided")
		return tc
	}

	pool, err := pgxpool.New(tc.Context, dbURL)
	if err != nil {
		tc.T.Fatalf("failed to connect to test database: %v", err)
	}

	// Register cleanup
	tc.T.Cleanup(func() {
		pool.Close()
	})

	tc.DB = pool
	tc.Queries = sqlc.New(pool)

	return tc
}

// WithAIService adds AI service to test context
//
// WHY: AI service tests can use mocks for unit tests or real services
// for integration tests. This flexibility is key for test speed.
func (tc *TestContext) WithAIService(mock bool) *TestContext {
	tc.T.Helper()

	if mock {
		tc.AIService = createMockAIService(tc.T)
		return tc
	}

	// Use real AI service for integration tests
	clients := make(map[ai.Provider]ai.Client)

	// Try Claude first
	if apiKey := os.Getenv("CLAUDE_API_KEY"); apiKey != "" {
		client, err := claude.New(apiKey)
		if err == nil {
			clients[ai.Claude] = client
		}
	}

	// Try Gemini as fallback
	if apiKey := os.Getenv("GEMINI_API_KEY"); apiKey != "" && len(clients) == 0 {
		client, err := gemini.New(tc.Context, apiKey)
		if err == nil {
			clients[ai.Gemini] = client
		}
	}

	if len(clients) == 0 {
		tc.T.Skip("Skipping test: no AI providers configured")
		return tc
	}

	// Determine default provider
	defaultProvider := ai.Claude
	if _, hasClaude := clients[ai.Claude]; !hasClaude {
		defaultProvider = ai.Gemini
	}

	service, err := ai.NewService(clients, defaultProvider)
	if err != nil {
		tc.T.Fatalf("failed to create AI service: %v", err)
	}

	tc.AIService = service
	return tc
}

// WithMemoryService adds memory service to test context
//
// WHY: Memory service requires both database and AI service.
// This ensures proper dependency ordering.
func (tc *TestContext) WithMemoryService() *TestContext {
	tc.T.Helper()

	if tc.DB == nil {
		tc.T.Fatal("memory service requires database - call WithDatabase() first")
	}
	if tc.AIService == nil {
		tc.T.Fatal("memory service requires AI service - call WithAIService() first")
	}

	// Memory service requires MCP client
	// Tests should create their own instance with proper MCP setup
	// _ = memory.New(mcpClient, logger)

	return tc
}

// createTestConfig creates minimal test configuration
//
// WHY: Tests should use realistic configuration but with test-specific
// values to ensure isolation from production settings.
func createTestConfig(t *testing.T) *config.Config {
	t.Helper()

	return &config.Config{
		Owner: config.OwnerConfig{
			Name: "Test User",
		},
		AI: config.AIConfig{
			Provider: "claude",
		},
		Database: config.DatabaseConfig{
			URL: os.Getenv("TEST_DATABASE_URL"),
		},
	}
}

// createMockAIService creates a mock AI service for unit tests
//
// WHY: Unit tests should be fast and deterministic. Mock AI responses
// allow testing business logic without external dependencies.
func createMockAIService(t *testing.T) *ai.Service {
	t.Helper()

	// Create a simple mock client
	mockClient := &MockAIClient{
		responses: map[string]string{
			"default": "This is a mock AI response for testing.",
		},
	}

	clients := map[ai.Provider]ai.Client{
		ai.Claude: mockClient,
	}

	service, err := ai.NewService(clients, ai.Claude)
	if err != nil {
		t.Fatalf("failed to create mock AI service: %v", err)
	}

	return service
}

// MockAIClient implements ai.Client for testing
type MockAIClient struct {
	responses map[string]string
}

func (m *MockAIClient) Chat(ctx context.Context, req *ai.Request) (*ai.Response, error) {
	response := m.responses["default"]
	if custom, ok := m.responses[req.Messages[len(req.Messages)-1].Content]; ok {
		response = custom
	}

	return &ai.Response{
		Content:  response,
		Model:    req.Model,
		Provider: ai.Claude,
		Usage: ai.Usage{
			Input:  10,
			Output: 20,
			Total:  30,
		},
	}, nil
}

func (m *MockAIClient) Stream(ctx context.Context, req *ai.Request) (<-chan ai.Stream, error) {
	ch := make(chan ai.Stream, 1)
	go func() {
		defer close(ch)
		response, _ := m.Chat(ctx, req)
		ch <- ai.Stream{
			Delta: response.Content,
			Done:  true,
		}
	}()
	return ch, nil
}

func (m *MockAIClient) Embed(ctx context.Context, req *ai.EmbedRequest) (*ai.EmbedResponse, error) {
	// Return a simple mock embedding
	embedding := make([]float32, 768)
	for i := range embedding {
		embedding[i] = float32(i) / 768.0
	}

	return &ai.EmbedResponse{
		Embedding: embedding,
		Model:     "mock-embedding",
		Usage: ai.Usage{
			Input:  len(req.Text) / 4,
			Output: 0,
			Total:  len(req.Text) / 4,
		},
	}, nil
}

func (m *MockAIClient) IsAvailable(ctx context.Context) bool {
	return true
}

func (m *MockAIClient) Close() error {
	return nil
}

func (m *MockAIClient) Provider() ai.Provider {
	return ai.Claude
}

// AssertNoError is a helper for checking errors in tests
//
// WHY: Consistent error checking makes tests more readable and
// provides better failure messages than raw t.Fatal calls.
func AssertNoError(t *testing.T, err error, msg string) {
	t.Helper()
	if err != nil {
		t.Fatalf("%s: %v", msg, err)
	}
}

// AssertError is a helper for expecting errors in tests
func AssertError(t *testing.T, err error, msg string) {
	t.Helper()
	if err == nil {
		t.Fatalf("%s: expected error but got nil", msg)
	}
}

// SetupEnvVars sets up test environment variables
//
// WHY: Tests should be explicit about their environment dependencies.
// This function documents what environment variables are needed.
func SetupEnvVars(t *testing.T) {
	t.Helper()

	// Only set if not already set
	envVars := map[string]string{
		"APP_MODE":  "test",
		"LOG_LEVEL": "error", // Quiet logs during tests
	}

	for key, value := range envVars {
		if os.Getenv(key) == "" {
			_ = os.Setenv(key, value)
			t.Cleanup(func() {
				_ = os.Unsetenv(key)
			})
		}
	}
}

// TruncateAllTables clears all data from test database
//
// WHY: Tests should start with a clean state. This ensures
// no data from previous tests affects the current test.
func TruncateAllTables(t *testing.T, db *pgxpool.Pool) {
	t.Helper()

	ctx := context.Background()

	// Tables to truncate in dependency order
	tables := []string{
		"embeddings",
		"messages",
		"memories",
		"conversations",
	}

	for _, table := range tables {
		_, err := db.Exec(ctx, fmt.Sprintf("TRUNCATE TABLE %s CASCADE", table))
		if err != nil {
			t.Logf("Warning: failed to truncate %s: %v", table, err)
		}
	}
}
