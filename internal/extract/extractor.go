// Package extract implements LLM-based fact extraction with self-healing capabilities.
package extract

import (
	"context"
	"fmt"
	"strings"

	"github.com/koopa0/assistant-go/internal/ai"
)

// LLMExtractor extracts facts using LLM with self-healing retry logic.
type LLMExtractor struct {
	aiClient   ai.Client
	maxRetries int
}

// New creates a new LLM-based extractor.
func New(aiClient ai.Client) *LLMExtractor {
	return &LLMExtractor{
		aiClient:   aiClient,
		maxRetries: 3,
	}
}

// NewLLMExtractor creates a new LLM-based extractor (deprecated: use New instead).
func NewLLMExtractor(aiClient ai.Client) *LLMExtractor {
	return New(aiClient)
}

// Extract analyzes messages and returns structured facts. It implements a self-healing
// retry loop to handle the inherent unpredictability of LLM outputs. If the initial
// JSON parsing or validation fails, it constructs a repair prompt, including the
// error and the malformed output, to guide the LLM towards a correct format on
// the subsequent attempt.
func (e *LLMExtractor) Extract(ctx context.Context, messages []ai.Message) ([]Fact, error) {
	// Debug: Extraction layer called
	// Debug: Extract called with messages
	if len(messages) == 0 {
		return nil, nil
	}

	// Build conversation text
	var conversation strings.Builder
	for _, msg := range messages {
		if msg.Role == ai.System {
			continue
		}
		role := "User"
		if msg.Role == ai.Assistant {
			role = "Assistant"
		}
		conversation.WriteString(fmt.Sprintf("%s: %s\n", role, msg.Content))
		// Debug: Processing message
	}

	// Prepare base prompts
	systemPrompt := e.buildSystemPrompt()
	userPrompt := e.buildUserPrompt(conversation.String())

	// Self-healing retry loop
	var lastError error
	var lastBadOutput string

	for attempt := 0; attempt < e.maxRetries; attempt++ {
		// Debug: Extraction attempt

		// Prepare messages for this attempt
		messagesToSend := []ai.Message{
			{Role: ai.System, Content: systemPrompt},
			{Role: ai.User, Content: userPrompt},
		}

		// If this is a retry, add repair prompt
		if attempt > 0 && lastError != nil {
			repairPrompt := e.buildRepairPrompt(lastBadOutput, lastError)
			messagesToSend = append(messagesToSend, ai.Message{
				Role:    ai.User,
				Content: repairPrompt,
			})
			// Debug: Adding repair prompt due to error
		}

		// Log the final prompt
		// Debug: Sending prompt to LLM
		// Debug: Messages prepared for sending
		// Debug: End of prompt

		// Call LLM
		resp, err := e.aiClient.Chat(ctx, &ai.Request{
			Messages:    messagesToSend,
			Temperature: 0.1, // Low temperature for consistency
			MaxTokens:   3000,
			Model:       ai.Claude35Sonnet, // Use best model for extraction
		})
		if err != nil {
			// Error: LLM call failed
			return nil, fmt.Errorf("LLM extraction failed: %w", err)
		}

		// Debug: Received LLM response

		// Log specific JSON extraction
		jsonContent := extractJSON(resp.Content)
		// Debug: Check if JSON was extracted from response
		_ = jsonContent // Variable is used for debugging purposes

		// Try to parse the response
		facts, parseErr := e.parseResponse(resp.Content)

		// Success: got valid facts
		if parseErr == nil && len(facts) > 0 {
			// Debug: Successfully extracted facts
			// Debug: Facts extracted successfully
			return facts, nil
		}

		// Success: no facts to extract
		if parseErr == nil && len(facts) == 0 {
			// Info: No facts to extract from conversation
			return nil, nil
		}

		// Failed: save error for retry
		lastError = parseErr
		lastBadOutput = resp.Content
		// Debug: Parse error occurred
	}

	// All retries exhausted
	// Error: Failed after all retry attempts
	return nil, fmt.Errorf("failed to extract facts after %d attempts: %w", e.maxRetries, lastError)
}

// buildSystemPrompt creates the system prompt for extraction.
func (e *LLMExtractor) buildSystemPrompt() string {
	// Simplified extraction prompt with comprehensive few-shot examples
	return `You are a JSON-only responder. Your ONLY output must be valid JSON matching the exact schema below.

CRITICAL JSON RULES:
- Output ONLY valid JSON, nothing else
- The root object MUST have a "facts" array
- Each fact MUST have exactly 6 fields: content, type, score, reason, keywords, confidence
- Each fact MUST include keywords array to help with topic identification
- Do NOT add any extra fields beyond the required ones
- Do NOT output explanations, just JSON

CONTENT RULES:
1. ALWAYS preserve the ORIGINAL LANGUAGE of the content
2. If user speaks Chinese, content MUST be in Chinese
3. If user speaks English, content MUST be in English
4. NEVER translate the content
5. Extract MULTIPLE facts when user mentions multiple items
6. Each preference/item should be a SEPARATE fact
7. Detect NEGATIVE statements (不喜歡, don't like, 不要, etc.)
8. Extract keywords that capture main topics for conflict detection

EXACT JSON STRUCTURE:
{
  "facts": [
    {
      "content": "The extracted information in ORIGINAL LANGUAGE",
      "type": "fact|preference|schedule|relationship|goal|skill",
      "score": 1-5,
      "reason": "Why this score",
      "keywords": ["main topic", "activity", "concept"],
      "confidence": 0.1-1.0
    }
  ]
}

Valid type values: fact, preference, schedule, relationship, goal, skill

Score guidelines:
- 5: Critical information (birthdays, schedules, explicit preferences, personal info)
- 4: Important information (goals, relationships, strong preferences)
- 3: Useful context (general preferences, skills)
- 2: Minor details
- 1: Trivial information

COMPREHENSIVE EXAMPLES (Edge Cases Included):

1. Greeting/Acknowledgment (Return empty):
Input: "好的，謝謝你"
Output: {"facts": []}

Input: "Hi there!"
Output: {"facts": []}

2. Negation Pattern (Preserve negative form):
Input: "我不喝咖啡了"
Output: {"facts": [{"content": "不喝咖啡了", "type": "preference", "score": 4, "reason": "Changed beverage preference", "keywords": ["咖啡", "飲料", "偏好"], "confidence": 0.95}]}

Input: "I don't like spicy food anymore"
Output: {"facts": [{"content": "Don't like spicy food anymore", "type": "preference", "score": 4, "reason": "Changed food preference", "keywords": ["spicy food", "food", "preference"], "confidence": 0.95}]}

3. Multiple Items (Split into separate facts):
Input: "我喜歡紅茶，也喜歡綠茶"
Output: {"facts": [{"content": "喜歡紅茶", "type": "preference", "score": 4, "reason": "Beverage preference", "keywords": ["紅茶", "茶", "飲料"], "confidence": 0.95}, {"content": "喜歡綠茶", "type": "preference", "score": 4, "reason": "Beverage preference", "keywords": ["綠茶", "茶", "飲料"], "confidence": 0.95}]}

Input: "My hobbies are reading, swimming, and coding"
Output: {"facts": [{"content": "Hobby is reading", "type": "preference", "score": 3, "reason": "Personal hobby", "keywords": ["reading", "hobby", "activity"], "confidence": 0.9}, {"content": "Hobby is swimming", "type": "preference", "score": 3, "reason": "Personal hobby", "keywords": ["swimming", "hobby", "activity", "sport"], "confidence": 0.9}, {"content": "Hobby is coding", "type": "preference", "score": 3, "reason": "Personal hobby", "keywords": ["coding", "hobby", "activity", "programming"], "confidence": 0.9}]}

4. Complex Schedule:
Input: "我的泰拳課是星期三晚上7點30~8點30跟星期六早上11點~12點"
Output: {"facts": [{"content": "星期三晚上7點30~8點30上泰拳課", "type": "schedule", "score": 5, "reason": "Fixed weekly schedule", "keywords": ["泰拳", "運動", "課程", "星期三"], "confidence": 1.0}, {"content": "星期六早上11點~12點上泰拳課", "type": "schedule", "score": 5, "reason": "Fixed weekly schedule", "keywords": ["泰拳", "運動", "課程", "星期六"], "confidence": 1.0}]}

5. Mixed Content (Extract only valuable parts):
Input: "順便說一下，我妻子叫小美，她也喜歡喝咖啡"
Output: {"facts": [{"content": "妻子叫小美", "type": "relationship", "score": 5, "reason": "Important relationship information", "keywords": ["妻子", "小美", "家人", "關係"], "confidence": 1.0}, {"content": "妻子喜歡喝咖啡", "type": "fact", "score": 3, "reason": "Information about family member", "keywords": ["妻子", "咖啡", "偏好"], "confidence": 0.9}]}

6. Goal Statement:
Input: "我想在今年內學會彈吉他"
Output: {"facts": [{"content": "想在今年內學會彈吉他", "type": "goal", "score": 4, "reason": "Personal learning goal with timeframe", "keywords": ["吉他", "學習", "目標", "音樂"], "confidence": 0.95}]}

Remember: Return ONLY valid JSON. No explanations, no markdown, just JSON.`
}

// buildUserPrompt creates the user prompt with conversation.
func (e *LLMExtractor) buildUserPrompt(conversation string) string {
	return fmt.Sprintf(`Extract structured information from this conversation:

%s

Return JSON only, no explanation.`, conversation)
}

// buildRepairPrompt creates a repair prompt for retry attempts.
func (e *LLMExtractor) buildRepairPrompt(badOutput string, parseError error) string {
	return fmt.Sprintf(`Your JSON output was invalid.

Error: %v

Your output:
%s

CRITICAL REMINDERS:
1. PRESERVE ORIGINAL LANGUAGE - if user speaks Chinese, content MUST be in Chinese
2. Extract MULTIPLE facts when user mentions multiple items
3. Each item should be a SEPARATE fact

Correct format (EXACTLY 6 fields per fact, including keywords):
{
  "facts": [
    {
      "content": "The information IN ORIGINAL LANGUAGE",
      "type": "fact|preference|schedule|relationship|goal|skill",
      "score": 1-5,
      "reason": "Why this score",
      "keywords": ["main topic", "activity", "concept"],
      "confidence": 0.1-1.0
    }
  ]
}

Common mistakes:
- Translating content to English (NEVER translate!)
- Combining multiple items into one fact (split them!)
- Wrong root key (must be "facts")
- Missing required fields (ALL 6 fields are required)
- Extra fields (entities, context, etc. are NOT allowed - only the 6 required fields)

Examples:
- User says "我的生日是12月31日" → content: "生日是12月31日" (NOT "birthday is...")
- User says "我喜歡紅茶、綠茶" → TWO facts: "喜歡喝紅茶" AND "喜歡喝綠茶"

Return ONLY the corrected JSON.`, parseError, badOutput)
}

// parseResponse parses the LLM response into facts with strict validation.
func (e *LLMExtractor) parseResponse(response string) ([]Fact, error) {
	// Extract JSON from response
	content := extractJSON(response)

	// Try strict parsing with validation
	factsResp, err := ParseAndValidate(content)
	if err == nil {
		// Successfully parsed and validated
		facts := make([]Fact, 0, len(factsResp.Facts))
		for _, ef := range factsResp.Facts {
			facts = append(facts, ef.ToFact())
		}
		// Debug: Successfully parsed facts
		return facts, nil
	}

	// Validation failed - return error for retry
	// Debug: Validation failed
	return nil, err
}

// extractJSON extracts JSON content from LLM response.
func extractJSON(content string) string {
	// Handle markdown code blocks
	if strings.Contains(content, "```json") {
		start := strings.Index(content, "```json") + 7
		end := strings.LastIndex(content, "```")
		if start > 6 && end > start {
			return strings.TrimSpace(content[start:end])
		}
	} else if strings.Contains(content, "```") {
		start := strings.Index(content, "```") + 3
		end := strings.LastIndex(content, "```")
		if start > 2 && end > start {
			return strings.TrimSpace(content[start:end])
		}
	}

	// Try to find JSON object boundaries
	content = strings.TrimSpace(content)

	// Simple JSON extraction - look for outermost braces
	if startIdx := strings.Index(content, "{"); startIdx >= 0 {
		// Count braces to find matching closing brace
		braceCount := 0
		inString := false
		escape := false

		for i := startIdx; i < len(content); i++ {
			char := content[i]

			// Handle string literals
			if char == '"' && !escape {
				inString = !inString
			}

			// Track escape sequences
			if char == '\\' && !escape {
				escape = true
				continue
			}
			escape = false

			// Count braces only outside strings
			if !inString {
				if char == '{' {
					braceCount++
				} else if char == '}' {
					braceCount--
					if braceCount == 0 {
						// Found matching closing brace
						return content[startIdx : i+1]
					}
				}
			}
		}
	}

	return content
}
