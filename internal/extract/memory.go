// Package extract defines common types for information extraction.
package extract

import (
	"time"
)

// FactType represents the type of extracted information.
type FactType string

// Standard fact types that can be extracted.
const (
	FactTypeFact         FactType = "fact"
	FactTypePreference   FactType = "preference"
	FactTypeSchedule     FactType = "schedule"
	FactTypeRelationship FactType = "relationship"
	FactTypeGoal         FactType = "goal"
	FactTypeSkill        FactType = "skill"
	FactTypePersonalInfo FactType = "personal_info"
)

// EntityType represents the type of an entity.
type EntityType string

// Standard entity types.
const (
	EntityPerson   EntityType = "person"
	EntityPlace    EntityType = "place"
	EntityActivity EntityType = "activity"
	EntityTime     EntityType = "time"
	EntityThing    EntityType = "thing"
	EntityConcept  EntityType = "concept"
)

// EntityRole represents the role of an entity in a fact.
type EntityRole string

// Standard entity roles.
const (
	RoleSubject  EntityRole = "subject"
	RoleObject   EntityRole = "object"
	RoleWith     EntityRole = "with"
	RoleLocation EntityRole = "location"
	RoleTime     EntityRole = "time"
)

// Entity represents a named entity in a fact.
type Entity struct {
	Name       string                 `json:"name"`
	Type       EntityType             `json:"type"`
	Role       EntityRole             `json:"role"`
	Properties map[string]interface{} `json:"properties,omitempty"`
}

// RecurrencePattern defines how often something repeats.
type RecurrencePattern string

// Standard recurrence patterns.
const (
	RecurrenceDaily   RecurrencePattern = "daily"
	RecurrenceWeekly  RecurrencePattern = "weekly"
	RecurrenceMonthly RecurrencePattern = "monthly"
	RecurrenceYearly  RecurrencePattern = "yearly"
)

// Recurrence represents a recurring pattern.
type Recurrence struct {
	Pattern RecurrencePattern `json:"pattern"`
	Days    []time.Weekday    `json:"days,omitempty"`
	Count   int               `json:"count,omitempty"`
	Until   *time.Time        `json:"until,omitempty"`
}

// Location represents a physical location.
type Location struct {
	Name    string  `json:"name"`
	Address string  `json:"address,omitempty"`
	Lat     float64 `json:"lat,omitempty"`
	Lng     float64 `json:"lng,omitempty"`
}

// Context provides temporal and spatial context for a fact.
type Context struct {
	OccurredAt *time.Time  `json:"occurred_at,omitempty"`
	StartTime  *time.Time  `json:"start_time,omitempty"`
	EndTime    *time.Time  `json:"end_time,omitempty"`
	Recurrence *Recurrence `json:"recurrence,omitempty"`
	Location   *Location   `json:"location,omitempty"`
}

// Fact represents a single piece of extracted information.
type Fact struct {
	Content    string         `json:"content"`
	Type       FactType       `json:"type"`
	Score      int            `json:"score"`  // 1-5 rating of memory value
	Reason     string         `json:"reason"` // Why this score was assigned
	Entities   []Entity       `json:"entities"`
	Attributes map[string]any `json:"attributes"`
	Context    Context        `json:"context"`
	Keywords   []string       `json:"keywords"`
	Confidence float32        `json:"confidence"`
}
