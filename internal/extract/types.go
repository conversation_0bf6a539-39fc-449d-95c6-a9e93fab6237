// Package extract provides strongly typed fact extraction.
package extract

import (
	"encoding/json"
	"fmt"
	"time"
)

// FactsResponse represents the expected JSON structure from LLM.
// This is the ONLY acceptable format for extraction responses.
type FactsResponse struct {
	Facts []ExtractedFact `json:"facts" validate:"required"`
}

// ExtractedFact represents a single extracted fact with validation.
type ExtractedFact struct {
	Content    string                 `json:"content" validate:"required,min=1"`
	Type       string                 `json:"type" validate:"required,oneof=fact preference schedule relationship goal skill personal_info"`
	Score      int                    `json:"score" validate:"required,min=1,max=5"`
	Reason     string                 `json:"reason" validate:"required"`
	Entities   []ExtractedEntity      `json:"entities,omitempty"`
	Attributes map[string]interface{} `json:"attributes,omitempty"`
	Context    *ExtractedContext      `json:"context,omitempty"`
	Keywords   []string               `json:"keywords,omitempty"`
	Confidence float32                `json:"confidence" validate:"min=0,max=1"`
}

// ExtractedEntity represents an entity with validation.
type ExtractedEntity struct {
	Name string `json:"name" validate:"required"`
	Type string `json:"type" validate:"required,oneof=person place activity time thing concept"`
	Role string `json:"role" validate:"required,oneof=subject object with location time"`
}

// ExtractedContext represents contextual information.
type ExtractedContext struct {
	StartTime  *time.Time           `json:"start_time,omitempty"`
	EndTime    *time.Time           `json:"end_time,omitempty"`
	Location   *ExtractedLocation   `json:"location,omitempty"`
	Recurrence *ExtractedRecurrence `json:"recurrence,omitempty"`
}

// ExtractedLocation represents location information.
type ExtractedLocation struct {
	Name string `json:"name" validate:"required"`
}

// ExtractedRecurrence represents recurrence pattern.
type ExtractedRecurrence struct {
	Pattern string   `json:"pattern" validate:"required,oneof=daily weekly monthly yearly"`
	Days    []string `json:"days,omitempty" validate:"dive,oneof=monday tuesday wednesday thursday friday saturday sunday"`
}

// Validate checks if the response is valid.
func (r *FactsResponse) Validate() error {
	if r.Facts == nil {
		return fmt.Errorf("facts array is required but was nil")
	}

	// Validate each fact
	for i, fact := range r.Facts {
		if err := fact.Validate(); err != nil {
			return fmt.Errorf("fact[%d] validation failed: %w", i, err)
		}
	}

	return nil
}

// Validate checks if a fact is valid.
func (f *ExtractedFact) Validate() error {
	// Check required fields
	if f.Content == "" {
		return fmt.Errorf("content is required")
	}

	// Validate type
	validTypes := map[string]bool{
		"fact": true, "preference": true, "schedule": true,
		"relationship": true, "goal": true, "skill": true,
		"personal_info": true,
	}
	if !validTypes[f.Type] {
		return fmt.Errorf("invalid type '%s', must be one of: fact, preference, schedule, relationship, goal, skill, personal_info", f.Type)
	}

	// Validate score
	if f.Score < 1 || f.Score > 5 {
		return fmt.Errorf("score must be between 1-5, got %d", f.Score)
	}

	// Validate reason
	if f.Reason == "" {
		return fmt.Errorf("reason is required for score assignment")
	}

	// Validate confidence
	if f.Confidence < 0 || f.Confidence > 1 {
		return fmt.Errorf("confidence must be between 0 and 1, got %f", f.Confidence)
	}

	// Validate entities
	for i, entity := range f.Entities {
		if err := entity.Validate(); err != nil {
			return fmt.Errorf("entity[%d]: %w", i, err)
		}
	}

	// Validate context if present
	if f.Context != nil {
		if err := f.Context.Validate(); err != nil {
			return fmt.Errorf("context: %w", err)
		}
	}

	return nil
}

// Validate checks if an entity is valid.
func (e *ExtractedEntity) Validate() error {
	if e.Name == "" {
		return fmt.Errorf("name is required")
	}

	validTypes := map[string]bool{
		"person": true, "place": true, "activity": true,
		"time": true, "thing": true, "concept": true,
	}
	if !validTypes[e.Type] {
		return fmt.Errorf("invalid type '%s'", e.Type)
	}

	validRoles := map[string]bool{
		"subject": true, "object": true, "with": true,
		"location": true, "time": true,
	}
	if !validRoles[e.Role] {
		return fmt.Errorf("invalid role '%s'", e.Role)
	}

	return nil
}

// Validate checks if context is valid.
func (c *ExtractedContext) Validate() error {
	// If we have recurrence, validate it
	if c.Recurrence != nil {
		if err := c.Recurrence.Validate(); err != nil {
			return fmt.Errorf("recurrence: %w", err)
		}
	}

	// If we have location, validate it
	if c.Location != nil {
		if c.Location.Name == "" {
			return fmt.Errorf("location name is required")
		}
	}

	// Validate time consistency
	if c.StartTime != nil && c.EndTime != nil {
		if c.EndTime.Before(*c.StartTime) {
			return fmt.Errorf("end_time cannot be before start_time")
		}
	}

	return nil
}

// Validate checks if recurrence is valid.
func (r *ExtractedRecurrence) Validate() error {
	validPatterns := map[string]bool{
		"daily": true, "weekly": true, "monthly": true, "yearly": true,
	}
	if !validPatterns[r.Pattern] {
		return fmt.Errorf("invalid pattern '%s'", r.Pattern)
	}

	// Validate days if present
	if len(r.Days) > 0 {
		validDays := map[string]bool{
			"monday": true, "tuesday": true, "wednesday": true, "thursday": true,
			"friday": true, "saturday": true, "sunday": true,
		}
		for _, day := range r.Days {
			if !validDays[day] {
				return fmt.Errorf("invalid day '%s'", day)
			}
		}
	}

	return nil
}

// ParseAndValidate parses JSON and validates the response.
func ParseAndValidate(jsonData string) (*FactsResponse, error) {
	var response FactsResponse

	// Parse JSON
	if err := json.Unmarshal([]byte(jsonData), &response); err != nil {
		return nil, fmt.Errorf("JSON parse error: %w", err)
	}

	// Validate structure
	if err := response.Validate(); err != nil {
		return nil, fmt.Errorf("validation error: %w", err)
	}

	return &response, nil
}

// ToFact converts ExtractedFact to the internal Fact type.
func (f *ExtractedFact) ToFact() Fact {
	fact := Fact{
		Content:    f.Content,
		Type:       FactType(f.Type),
		Score:      f.Score,
		Reason:     f.Reason,
		Attributes: f.Attributes,
		Keywords:   f.Keywords,
		Confidence: f.Confidence,
	}

	// Convert entities
	for _, e := range f.Entities {
		fact.Entities = append(fact.Entities, Entity{
			Name: e.Name,
			Type: EntityType(e.Type),
			Role: EntityRole(e.Role),
		})
	}

	// Convert context
	if f.Context != nil {
		fact.Context = Context{}

		if f.Context.StartTime != nil {
			fact.Context.StartTime = f.Context.StartTime
		}
		if f.Context.EndTime != nil {
			fact.Context.EndTime = f.Context.EndTime
		}

		if f.Context.Location != nil {
			fact.Context.Location = &Location{
				Name: f.Context.Location.Name,
			}
		}

		if f.Context.Recurrence != nil {
			fact.Context.Recurrence = &Recurrence{
				Pattern: RecurrencePattern(f.Context.Recurrence.Pattern),
			}
			for _, day := range f.Context.Recurrence.Days {
				fact.Context.Recurrence.Days = append(fact.Context.Recurrence.Days, time.Weekday(DayToWeekday(day)))
			}
		}
	}

	return fact
}

// DayToWeekday converts string day name to time.Weekday
func DayToWeekday(day string) int {
	days := map[string]int{
		"sunday": 0, "monday": 1, "tuesday": 2, "wednesday": 3,
		"thursday": 4, "friday": 5, "saturday": 6,
	}
	if d, ok := days[day]; ok {
		return d
	}
	return 0
}
