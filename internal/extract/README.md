# Extract Package

**Package:** `extract`
**Version:** 1.0.0

## Overview

The `extract` package is a crucial component of the assistant's cognitive architecture. Its sole responsibility is to act as a sophisticated data transformation pipeline, converting raw, unstructured conversational data into clean, validated, and structured `Fact` objects. It serves as the primary gateway for information, ensuring data quality and consistency before it enters the stateful `memory` system.

This package is designed to be **stateless** and **self-contained**. It does not depend on the `memory` or `storage` layers, making it a highly reusable and testable component for information extraction.

## Core Responsibilities

1.  **LLM-Powered Extraction**: Leverages Large Language Models (LLMs) to understand natural language and extract key pieces of information.
2.  **Self-Healing Parsing**: Implements a robust retry loop that can dynamically generate repair prompts to correct malformed JSON outputs from the LLM, significantly increasing the reliability of the extraction process.
3.  **Strict Schema Validation**: Enforces a rigorous schema on the LLM's output. No data is passed onward without successfully passing validation, preventing data corruption downstream.
4.  **Post-Extraction Analysis**: Provides a suite of tools in `analysis.go` to perform preliminary analysis on extracted facts, such as:
    *   **Negation Detection**: Identifying negative statements (e.g., "I don't like coffee anymore").
    *   **Triviality Filtering**: Skipping facts that are conversational filler (e.g., "Okay", "Thank you").
    *   **Form Extraction**: Deriving the positive form of a negative statement for conflict detection purposes.

## Architecture and Design Philosophy

-   **Separation of Concerns**: The package strictly separates the *act of extracting* from the *act of storing or managing* information. This makes the system more modular and easier to maintain.
-   **Robustness over Trust**: The design assumes that the LLM output can be unreliable. The self-healing and validation layers are built to handle this unpredictability, making the system resilient.
-   **Statelessness**: The `LLMExtractor` and its related components do not hold any state across calls, making them safe for concurrent use.

### Key Files

-   `extractor.go`: Contains the `LLMExtractor` which orchestrates the entire extraction process, including the self-healing retry logic.
-   `types.go`: Defines the strict data structures (`Fact`, `Entity`, `Context`) that represent the extracted information. It also contains the validation logic.
-   `analysis.go`: Provides pure, stateless functions for analyzing and filtering extracted facts.

## Usage

```go
import (
    "github.com/koopa0/assistant-go/internal/extract"
    "github.com/koopa0/assistant-go/internal/ai"
)

// Initialize the AI service
aiService := ai.NewService(...)

// Create the extractor
extractor := extract.New(aiService)

// A slice of messages from a conversation
messages := []ai.Message{
    {Role: ai.User, Content: "Please remember I don't drink coffee anymore, I prefer tea."},
}

// Extract facts
facts, err := extractor.Extract(context.Background(), messages)
if err != nil {
    // Handle error
}

// Process the validated, structured facts
for _, fact := range facts {
    fmt.Printf("Extracted Fact: %s\n", fact.Content)
    // -> "Don't drink coffee anymore"
    // -> "Prefer tea"

    isNegative := extract.IsNegationPattern(fact.Content)
    if isNegative {
        positiveForm := extract.ExtractPositiveForm(fact.Content)
        fmt.Printf("Positive form: %s\n", positiveForm) // -> "drink coffee"
    }
}
```

## Future Development

-   **Adaptive Parsing**: While the self-healing mechanism is robust, a more advanced adaptive parser could be developed to handle a wider variety of non-standard LLM outputs without requiring a retry.
-   **Configuration**: The `maxRetries` value is currently hardcoded. This could be moved to a configuration file to allow for easier tuning.
