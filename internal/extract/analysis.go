package extract

import (
	"regexp"
	"strings"
)

// Constants for fact analysis
const (
	// MinConfidenceThreshold is the minimum confidence to process a fact
	MinConfidenceThreshold = 0.3
)

// Pre-compiled regex patterns for performance
var (
	// Negation patterns
	regexChineseNotVerb = regexp.MustCompile(`不\S+了`)
	regexEnglishNoMore  = regexp.MustCompile(`(?i)no\s+more\s+\w+`)

	// Complex negation patterns for positive form extraction
	negationPatterns = []struct {
		pattern     *regexp.Regexp
		replacement string
	}{
		// Chinese patterns - non-greedy with punctuation boundaries
		{regexp.MustCompile(`不喜歡(\S+?)(?:，|。|,|\.|;|；|$)`), "喜歡$1"},
		{regexp.MustCompile(`不愛(\S+?)(?:，|。|,|\.|;|；|$)`), "愛$1"},
		{regexp.MustCompile(`不想(\S+?)(?:，|。|,|\.|;|；|$)`), "想$1"},
		{regexp.MustCompile(`不要(\S+?)(?:，|。|,|\.|;|；|$)`), "要$1"},
		{regexp.MustCompile(`不再(\S+?)(?:了)?(?:，|。|,|\.|;|；|$)`), "$1"},
		{regexp.MustCompile(`已經不(\S+?)了(?:，|。|,|\.|;|；|$)`), "$1"},
		{regexp.MustCompile(`再也不(\S+?)(?:，|。|,|\.|;|；|$)`), "$1"},
		{regexp.MustCompile(`沒有(\S+?)(?:，|。|,|\.|;|；|$)`), "有$1"},
		{regexp.MustCompile(`討厭(\S+?)(?:，|。|,|\.|;|；|$)`), "喜歡$1"},
		{regexp.MustCompile(`不(\S+?)了(?:，|。|,|\.|;|；|$)`), "$1"}, // General "不...了" pattern

		// English patterns - word boundaries and non-greedy
		{regexp.MustCompile(`(?i)don't\s+like\s+(\w+(?:\s+\w+){0,2}?)(?:[,\.;!?]|\s+(?:because|since|and|but)|$)`), "like $1"},
		{regexp.MustCompile(`(?i)don't\s+want\s+(\w+(?:\s+\w+){0,2}?)(?:[,\.;!?]|\s+(?:because|since|and|but)|$)`), "want $1"},
		{regexp.MustCompile(`(?i)dislike\s+(\w+(?:\s+\w+){0,2}?)(?:[,\.;!?]|\s+(?:because|since|and|but)|$)`), "like $1"},
		{regexp.MustCompile(`(?i)hate\s+(\w+(?:\s+\w+){0,2}?)(?:[,\.;!?]|\s+(?:because|since|and|but)|$)`), "like $1"},
		{regexp.MustCompile(`(?i)no\s+longer\s+(\w+(?:\s+\w+){0,2}?)(?:[,\.;!?]|\s+(?:because|since|and|but)|$)`), "$1"},
		{regexp.MustCompile(`(?i)not\s+(\w+(?:\s+\w+){0,2}?)\s+anymore(?:[,\.;!?]|$)`), "$1"},
		{regexp.MustCompile(`(?i)stopped\s+(\w+ing)(?:[,\.;!?]|$)`), "$1"},
		{regexp.MustCompile(`(?i)quit\s+(\w+ing)(?:[,\.;!?]|$)`), "$1"},
		{regexp.MustCompile(`(?i)i\s+don't\s+(\w+)\s+anymore(?:[,\.;!?]|$)`), "I $1"},
	}

	// Whitespace cleanup regex
	regexWhitespace = regexp.MustCompile(`\s+`)
)

// IsNegationPattern checks if content contains meaningful negation patterns.
func IsNegationPattern(content string) bool {
	contentLower := strings.ToLower(content)

	// Check for explicit negation patterns with context
	negationPatterns := []struct {
		pattern         string
		requiresContext bool
		contextWords    []string
	}{
		// Chinese patterns
		{pattern: "不喜歡", requiresContext: false},
		{pattern: "不愛", requiresContext: false},
		{pattern: "不想", requiresContext: false},
		{pattern: "不要", requiresContext: false},
		{pattern: "不會", requiresContext: false},
		{pattern: "沒有", requiresContext: false},
		{pattern: "無法", requiresContext: false},
		{pattern: "討厭", requiresContext: false},
		{pattern: "不再", requiresContext: false},
		{pattern: "已經不", requiresContext: false},
		{pattern: "再也不", requiresContext: false},

		// English patterns
		{pattern: "don't like", requiresContext: false},
		{pattern: "don't want", requiresContext: false},
		{pattern: "dislike", requiresContext: false},
		{pattern: "hate", requiresContext: false},
		{pattern: "no longer", requiresContext: false},
		{pattern: "not anymore", requiresContext: false},
		{pattern: "never", requiresContext: true, contextWords: []string{"eat", "drink", "like", "want", "go"}},
		{pattern: "stopped", requiresContext: true, contextWords: []string{"eating", "drinking", "liking", "going"}},
		{pattern: "quit", requiresContext: true, contextWords: []string{"smoking", "drinking", "eating"}},
	}

	// Check each pattern
	for _, np := range negationPatterns {
		if strings.Contains(contentLower, np.pattern) {
			if !np.requiresContext {
				return true
			}
			// Check if context words are present
			for _, ctx := range np.contextWords {
				if strings.Contains(contentLower, ctx) {
					return true
				}
			}
		}
	}

	// Check for negation with specific structures
	// "不[verb]了" pattern in Chinese
	if regexChineseNotVerb.MatchString(content) {
		return true
	}

	// "no more [noun]" pattern in English
	if regexEnglishNoMore.MatchString(contentLower) {
		return true
	}

	// Avoid false positives
	falsePositives := []string{
		"不錯", "不過", "不然", "不同", "不定", "不少", "不久",
		"not bad", "not only", "not sure", "cannot wait",
	}

	for _, fp := range falsePositives {
		if strings.Contains(contentLower, fp) {
			return false
		}
	}

	return false
}

// PositiveForm attempts to extract the positive form from a negation.
func PositiveForm(content string) string {
	result := content

	// Use pre-compiled patterns
	for _, np := range negationPatterns {
		if np.pattern.MatchString(result) {
			result = np.pattern.ReplaceAllString(result, np.replacement)
			break // Apply only the first matching pattern
		}
	}

	// Simple replacements for remaining cases
	simpleReplacements := map[string]string{
		"不":     "",
		"沒":     "",
		"無":     "",
		"don't": "do",
		"not":   "",
		"no":    "",
	}

	// Only apply simple replacements if no complex pattern matched
	if result == content {
		for neg, pos := range simpleReplacements {
			if strings.Contains(result, neg) {
				result = strings.Replace(result, neg, pos, 1)
				break
			}
		}
	}

	// Clean up extra spaces
	result = regexWhitespace.ReplaceAllString(result, " ")

	return strings.TrimSpace(result)
}

// ShouldSkipFact determines if a fact should be skipped from processing.
func ShouldSkipFact(fact Fact) bool {
	// Skip if confidence is too low (primary filter)
	if fact.Confidence < MinConfidenceThreshold {
		return true
	}

	// Skip very low score facts unless they have important entities
	if fact.Score <= 1 && len(fact.Entities) == 0 {
		return true
	}

	content := strings.ToLower(strings.TrimSpace(fact.Content))

	// Analyze fact characteristics
	hasEntities := len(fact.Entities) > 0
	hasAttributes := len(fact.Attributes) > 0
	hasContext := fact.Context.OccurredAt != nil || fact.Context.Location != nil
	contentLength := len([]rune(content)) // Use rune for proper Unicode handling

	// Don't skip if fact has rich metadata despite short content
	if hasEntities || hasAttributes || hasContext {
		return false
	}

	// Skip very short content without metadata
	if contentLength <= 3 && fact.Score <= 2 {
		return true
	}

	// Check if it's a greeting or acknowledgment without context
	if isSimpleGreeting(content) && !hasEntities && fact.Score <= 2 {
		return true
	}

	// Skip repetitive acknowledgments in conversation
	if isAcknowledgment(content) && fact.Type == "fact" && fact.Score <= 2 {
		return true
	}

	// Keep facts with specific types that are usually important
	importantTypes := map[FactType]bool{
		"schedule":     true,
		"preference":   true,
		"relationship": true,
		"goal":         true,
		"skill":        true,
	}
	if importantTypes[fact.Type] {
		return false
	}

	// Skip generic facts with no entities and low scores
	if fact.Type == "fact" && !hasEntities && fact.Score <= 2 {
		return true
	}

	return false
}

// isSimpleGreeting checks if content is a simple greeting
func isSimpleGreeting(content string) bool {
	// Use a map for O(1) lookup
	greetings := map[string]bool{
		// Chinese
		"早安": true, "午安": true, "晚安": true, "你好": true,
		"嗨": true, "哈囉": true, "再見": true, "拜拜": true,
		"早上好": true, "下午好": true, "晚上好": true,
		// English
		"hi": true, "hello": true, "hey": true, "bye": true,
		"goodbye": true, "good morning": true, "good afternoon": true,
		"good evening": true, "good night": true,
		// Common
		"morning": true, "evening": true, "night": true,
	}

	// Check exact match
	if greetings[content] {
		return true
	}

	// Check if it starts with greeting (e.g., "hello there")
	words := strings.Fields(content)
	if len(words) > 0 && len(words) <= 3 && greetings[words[0]] {
		return true
	}

	return false
}

// isAcknowledgment checks if content is a simple acknowledgment
func isAcknowledgment(content string) bool {
	acknowledgments := map[string]bool{
		// Chinese
		"ok": true, "okay": true, "好": true, "是": true,
		"對": true, "嗯": true, "哦": true, "喔": true,
		"謝謝": true, "不客氣": true, "thanks": true, "thank you": true,
		// English
		"yes": true, "no": true, "yeah": true, "yep": true,
		"nope": true, "sure": true, "alright": true, "got it": true,
		// Phrases
		"我知道了": true, "i see": true, "i understand": true,
		"明白": true, "了解": true, "知道了": true,
	}

	return acknowledgments[content]
}
