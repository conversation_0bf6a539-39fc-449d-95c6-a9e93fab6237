// Package extract provides a robust, self-healing pipeline for converting
// unstructured conversational data into high-quality, structured facts.
//
// The core responsibility of this package is to serve as the primary entry point
// for raw data, ensuring that only well-formed and meaningful information
// proceeds to the memory system. It is designed to be stateless and focused
// purely on the transformation and validation of data.
//
// # Key Features:
//
//   - LLM-based Extraction: Utilizes Large Language Models to parse conversations
//     and extract structured data according to a strict JSON schema, guided by
//     comprehensive few-shot examples.
//
//   - Self-Healing Parser: Implements a retry mechanism with dynamic repair prompts
//     to handle malformed or unexpected LLM outputs, ensuring high reliability.
//
//   - Strict Validation: All extracted data is rigorously validated against a
//     predefined schema before being returned, preventing corrupted data from
//     entering the system.
//
//   - Fact Analysis: Includes utilities for post-extraction analysis, such as
//     detecting negation, filtering out trivial information, and preparing
//     data for further processing.
package extract
