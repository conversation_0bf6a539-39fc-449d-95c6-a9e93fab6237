// Package ollama provides Ollama API client implementation for local embeddings
package ollama

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/koopa0/assistant-go/internal/ai"
)

// Client implements the ai.Client interface for Ollama
type Client struct {
	baseURL string
	client  *http.Client
}

// New connects to Ollama instance for local embedding generation
func New(baseURL string) (*Client, error) {
	if baseURL == "" {
		baseURL = "http://localhost:11434" // Default Ollama URL
	}

	return &Client{
		baseURL: baseURL,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}, nil
}

// Chat implements the ai.Client interface - Ollama can do chat too
func (c *Client) Chat(ctx context.Context, req *ai.Request) (*ai.Response, error) {
	return nil, &ai.OpError{
		Op:       "chat",
		Provider: ai.Ollama,
		Err:      fmt.Errorf("ollama chat not implemented - use for embeddings only"),
	}
}

// Stream implements the ai.Client interface
func (c *Client) Stream(ctx context.Context, req *ai.Request) (<-chan ai.Stream, error) {
	return nil, &ai.OpError{
		Op:       "stream",
		Provider: ai.Ollama,
		Err:      fmt.Errorf("ollama chat stream not implemented - use for embeddings only"),
	}
}

// Embed implements the ai.Embedding interface for embeddings
func (c *Client) Embed(ctx context.Context, req *ai.EmbedRequest) (*ai.EmbedResponse, error) {
	// Prepare request
	ollamaReq := struct {
		Model  string `json:"model"`
		Prompt string `json:"prompt"`
	}{
		Model:  "nomic-embed-text", // Popular embedding model for Ollama
		Prompt: req.Text,
	}

	reqBody, err := json.Marshal(ollamaReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	httpReq, err := http.NewRequestWithContext(ctx, "POST", c.baseURL+"/api/embeddings", bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}
	httpReq.Header.Set("Content-Type", "application/json")

	// Send request
	resp, err := c.client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("ollama API error: %s", string(body))
	}

	// Parse response
	var ollamaResp struct {
		Embedding []float64 `json:"embedding"`
	}
	if err := json.Unmarshal(body, &ollamaResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	// Convert float64 to float32
	embedding := make([]float32, len(ollamaResp.Embedding))
	for i, v := range ollamaResp.Embedding {
		embedding[i] = float32(v)
	}

	return &ai.EmbedResponse{
		Embedding: embedding,
		Model:     "nomic-embed-text",
		Usage: ai.Usage{
			Input:  len(req.Text) / 4, // Rough estimate
			Output: 0,
			Total:  len(req.Text) / 4,
		},
	}, nil
}

// Provider implements ai.Client interface
func (c *Client) Provider() ai.Provider {
	return ai.Ollama
}

// IsAvailable checks if the provider is currently available
func (c *Client) IsAvailable(ctx context.Context) bool {
	// Check if Ollama is running by making a lightweight request
	req, err := http.NewRequestWithContext(ctx, "GET", c.baseURL+"/api/tags", nil)
	if err != nil {
		return false
	}
	resp, err := c.client.Do(req)
	if err != nil {
		return false
	}
	_ = resp.Body.Close()
	return resp.StatusCode == http.StatusOK
}

// Close implements ai.Client interface
func (c *Client) Close() error {
	// Nothing to close
	return nil
}
