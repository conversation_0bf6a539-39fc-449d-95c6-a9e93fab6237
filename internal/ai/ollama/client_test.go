package ollama

import (
	"context"
	"strings"
	"testing"
	"time"

	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNew(t *testing.T) {
	tests := []struct {
		name     string
		baseURL  string
		expected string
	}{
		{
			name:     "default URL when empty",
			baseURL:  "",
			expected: "http://localhost:11434",
		},
		{
			name:     "custom URL",
			baseURL:  "http://custom:11434",
			expected: "http://custom:11434",
		},
		{
			name:     "URL with path",
			baseURL:  "http://localhost:8080/ollama",
			expected: "http://localhost:8080/ollama",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client, err := New(tt.baseURL)
			require.NoError(t, err)
			require.NotNil(t, client)
			assert.Equal(t, tt.expected, client.baseURL)
			assert.NotNil(t, client.client)
			assert.Equal(t, 30*time.Second, client.client.Timeout)
		})
	}
}

func TestClient_Chat(t *testing.T) {
	client, _ := New("")
	ctx := context.Background()
	req := &ai.Request{
		Messages: []ai.Message{
			{Role: ai.User, Content: "Hello"},
		},
	}

	resp, err := client.Chat(ctx, req)
	assert.Nil(t, resp)
	require.Error(t, err)

	var opErr *ai.OpError
	require.ErrorAs(t, err, &opErr)
	assert.Equal(t, "chat", opErr.Op)
	assert.Equal(t, ai.Ollama, opErr.Provider)
	assert.Contains(t, opErr.Err.Error(), "not implemented")
}

func TestClient_Stream(t *testing.T) {
	client, _ := New("")
	ctx := context.Background()
	req := &ai.Request{
		Messages: []ai.Message{
			{Role: ai.User, Content: "Hello"},
		},
	}

	stream, err := client.Stream(ctx, req)
	assert.Nil(t, stream)
	require.Error(t, err)

	var opErr *ai.OpError
	require.ErrorAs(t, err, &opErr)
	assert.Equal(t, "stream", opErr.Op)
	assert.Equal(t, ai.Ollama, opErr.Provider)
	assert.Contains(t, opErr.Err.Error(), "not implemented")
}

func TestClient_Provider(t *testing.T) {
	client := &Client{}
	assert.Equal(t, ai.Ollama, client.Provider())
}

func TestClient_IsAvailable(t *testing.T) {
	client, err := New("")
	require.NoError(t, err)
	ctx := context.Background()
	// Without actual connection, it should return false
	assert.False(t, client.IsAvailable(ctx))
}

func TestClient_Close(t *testing.T) {
	client, _ := New("")
	err := client.Close()
	assert.NoError(t, err)
}

// Fuzzing test for baseURL handling
func FuzzNew(f *testing.F) {
	// Add seed corpus
	f.Add("")
	f.Add("http://localhost:11434")
	f.Add("https://example.com:8080/ollama")
	f.Add("invalid://url")
	f.Add(strings.Repeat("http://", 100) + "test.com")
	f.Add("http://[::1]:11434")
	f.Add("http://*************:11434")

	f.Fuzz(func(t *testing.T, baseURL string) {
		// Should never panic
		client, err := New(baseURL)
		require.NoError(t, err)
		require.NotNil(t, client)

		// Check defaults
		if baseURL == "" {
			assert.Equal(t, "http://localhost:11434", client.baseURL)
		} else {
			assert.Equal(t, baseURL, client.baseURL)
		}

		assert.NotNil(t, client.client)
		assert.Equal(t, 30*time.Second, client.client.Timeout)
	})
}

// Test OpError construction
func TestOpError(t *testing.T) {
	tests := []struct {
		name     string
		opError  *ai.OpError
		expected string
	}{
		{
			name: "basic error",
			opError: &ai.OpError{
				Op:       "test",
				Provider: ai.Ollama,
				Err:      assert.AnError,
			},
			expected: "ollama test: assert.AnError general error for testing",
		},
		{
			name: "with provider",
			opError: &ai.OpError{
				Op:       "embed",
				Provider: ai.Ollama,
				Err:      assert.AnError,
			},
			expected: "ollama embed: assert.AnError general error for testing",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.expected, tt.opError.Error())
		})
	}
}

// Test Embed method (will fail without actual Ollama server)
func TestClient_Embed(t *testing.T) {
	client, _ := New("")
	ctx := context.Background()
	req := &ai.EmbedRequest{
		Text:  "test text",
		Model: "nomic-embed-text",
	}

	resp, err := client.Embed(ctx, req)
	assert.Nil(t, resp)
	require.Error(t, err)
	// Without a running Ollama server, this will fail with connection error
	assert.Contains(t, err.Error(), "failed to send request")
}
