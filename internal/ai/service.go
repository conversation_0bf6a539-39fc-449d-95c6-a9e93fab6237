// Package ai provides AI service abstraction
package ai

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/koopa0/assistant-go/internal/platform/logger"
)

// Service manages multiple AI providers with health monitoring and failover.
// It provides a unified interface while handling provider-specific details internally.
// The service monitors provider health and automatically switches to healthy alternatives.
type Service struct {
	// clients holds all available AI provider clients
	clients map[Provider]Client
	// defaultProvider specifies the preferred provider
	defaultProvider Provider
	// mu protects concurrent access to the clients map
	mu sync.RWMutex
	// healthCheckInterval configures how often to check provider health
	healthCheckInterval time.Duration
	// providerHealth tracks the health status of each provider
	providerHealth map[Provider]bool
	// healthStatus tracks detailed health information for each provider
	healthStatus map[Provider]*providerHealthStatus
	// healthMu protects concurrent access to health status
	healthMu sync.RWMutex
	// healthCheckCancel cancels the health check goroutine
	healthCheckCancel context.CancelFunc
	// logger for health status changes (optional)
	logger logger.Logger
}

// ServiceOption defines service configuration option
type ServiceOption func(*Service)

// WithHealthCheckInterval sets health check interval
func WithHealthCheckInterval(interval time.Duration) ServiceOption {
	return func(s *Service) {
		s.healthCheckInterval = interval
	}
}

// WithLogger sets the logger for the service
func WithLogger(log logger.Logger) ServiceOption {
	return func(s *Service) {
		s.logger = log
	}
}

// NewService creates a new AI service with multiple provider support.
// It manages provider health, automatic failover, and unified access to different AI providers.
// The service monitors provider availability and switches to healthy alternatives when needed.
func NewService(clients map[Provider]Client, defaultProvider Provider, opts ...ServiceOption) (*Service, error) {
	if len(clients) == 0 {
		return nil, errors.New("no AI clients provided")
	}

	// Validate default provider exists
	if _, exists := clients[defaultProvider]; !exists {
		// If not, use the first available provider
		for provider := range clients {
			defaultProvider = provider
			break
		}
	}

	s := &Service{
		clients:             clients,
		defaultProvider:     defaultProvider,
		healthCheckInterval: 2 * time.Minute, // Default health check interval
		providerHealth:      make(map[Provider]bool),
		healthStatus:        make(map[Provider]*providerHealthStatus),
	}

	// Apply configuration options
	for _, opt := range opts {
		opt(s)
	}

	// Initialize health status
	s.initHealthCheck()

	// Start health check with proper lifecycle management
	ctx, cancel := context.WithCancel(context.Background())
	s.healthCheckCancel = cancel
	go s.startHealthCheck(ctx)

	return s, nil
}

// Chat uses the default provider for conversation
func (s *Service) Chat(ctx context.Context, req *Request) (*Response, error) {
	// Get available provider
	provider := s.getAvailableProvider(s.defaultProvider)
	if provider == "" {
		return nil, errors.New("no AI provider available")
	}

	return s.ChatWithProvider(ctx, provider, req)
}

// Stream uses streaming for conversation
func (s *Service) Stream(ctx context.Context, req *Request) (<-chan Stream, error) {
	// Get available provider
	provider := s.getAvailableProvider(s.defaultProvider)
	if provider == "" {
		return nil, errors.New("no AI provider available")
	}

	s.mu.RLock()
	client, exists := s.clients[provider]
	s.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("provider %s does not exist", provider)
	}

	// If requested model doesn't match provider, auto-adjust
	req.Model = s.adjustModelForProvider(provider, req.Model)

	return client.Stream(ctx, req)
}

// ChatWithProvider uses the specified provider for conversation
func (s *Service) ChatWithProvider(ctx context.Context, provider Provider, req *Request) (*Response, error) {
	s.mu.RLock()
	client, exists := s.clients[provider]
	s.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("provider %s does not exist", provider)
	}

	// Check if provider is healthy
	if !s.isProviderHealthy(provider) {
		// Try using fallback provider
		fallbackProvider := s.getFallbackProvider(provider)
		if fallbackProvider != "" {
			// Use structured logging instead of fmt.Printf
			return s.ChatWithProvider(ctx, fallbackProvider, req)
		}
		return nil, fmt.Errorf("provider %s is currently unavailable", provider)
	}

	// If requested model doesn't match provider, auto-adjust
	req.Model = s.adjustModelForProvider(provider, req.Model)

	// Send request
	response, err := client.Chat(ctx, req)
	if err != nil {
		// Mark provider as unhealthy if connection error
		if s.isConnectionError(err) {
			s.markProviderUnhealthy(provider)
		}
		return nil, fmt.Errorf("AI chat failed (provider=%s, model=%s, messages=%d): %w",
			provider, req.Model, len(req.Messages), err)
	}

	return response, nil
}

// getAvailableProvider returns an available provider (with preference)
func (s *Service) getAvailableProvider(preferred Provider) Provider {
	// Check if preferred provider is healthy
	if s.isProviderHealthy(preferred) {
		return preferred
	}

	// Find any healthy provider
	s.healthMu.RLock()
	defer s.healthMu.RUnlock()

	for provider, healthy := range s.providerHealth {
		if healthy {
			return provider
		}
	}

	return ""
}

// getFallbackProvider returns a fallback provider
func (s *Service) getFallbackProvider(current Provider) Provider {
	s.mu.RLock()
	defer s.mu.RUnlock()

	for provider := range s.clients {
		if provider != current && s.isProviderHealthy(provider) {
			return provider
		}
	}

	return ""
}

// adjustModelForProvider adjusts the model based on the provider
func (s *Service) adjustModelForProvider(provider Provider, model string) string {
	// If model is already correct for provider, return as-is
	if s.isModelForProvider(model, provider) {
		return model
	}

	// Convert to appropriate model for the provider
	switch provider {
	case Claude:
		// Map to equivalent Claude model
		switch {
		case strings.Contains(strings.ToLower(model), "gemini"):
			return Claude35Sonnet
		default:
			return Claude35Sonnet
		}
	case Gemini:
		// Map to equivalent Gemini model
		switch {
		case strings.Contains(strings.ToLower(model), "claude"):
			return Gemini15Flash
		default:
			return Gemini15Flash
		}
	default:
		return model
	}
}

// isModelForProvider checks if a model belongs to a specific provider
func (s *Service) isModelForProvider(model string, provider Provider) bool {
	modelStr := strings.ToLower(model)
	switch provider {
	case Claude:
		return strings.Contains(modelStr, "claude")
	case Gemini:
		return strings.Contains(modelStr, "gemini")
	default:
		return true
	}
}

// getDefaultEmbeddingModelForProvider returns the default embedding model for a provider
func (s *Service) getDefaultEmbeddingModelForProvider(provider Provider) string {
	switch provider {
	case Gemini:
		return "gemini-embedding-001"
	default:
		// Most providers don't have dedicated embedding models
		return ""
	}
}

// Embedding is an alias for backward compatibility
func (s *Service) Embedding(ctx context.Context, req *EmbedRequest) (*EmbedResponse, error) {
	return s.Embed(ctx, req)
}

// EmbeddingWithProvider uses the specified provider for text embeddings
func (s *Service) EmbeddingWithProvider(ctx context.Context, provider Provider, req *EmbedRequest) (*EmbedResponse, error) {
	s.mu.RLock()
	client, exists := s.clients[provider]
	s.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("provider %s does not exist", provider)
	}

	// Check if provider is healthy
	if !s.isProviderHealthy(provider) {
		// Try using fallback provider
		fallbackProvider := s.getFallbackProvider(provider)
		if fallbackProvider != "" {
			return s.EmbeddingWithProvider(ctx, fallbackProvider, req)
		}
		return nil, fmt.Errorf("provider %s is currently unavailable", provider)
	}

	// Check if the client supports embeddings
	embeddingClient, ok := client.(Embedding)
	if !ok {
		// Debug: log the actual type
		if s.logger != nil {
			s.logger.Error("Client does not implement Embedding interface",
				"provider", provider,
				"clientType", fmt.Sprintf("%T", client))
		}
		return nil, fmt.Errorf("provider %s does not support embeddings", provider)
	}

	// If model is not set, use default for provider
	if req.Model == "" {
		req.Model = s.getDefaultEmbeddingModelForProvider(provider)
	}

	// Send request
	response, err := embeddingClient.Embed(ctx, req)
	if err != nil {
		// Mark provider as unhealthy if connection error
		if s.isConnectionError(err) {
			s.markProviderUnhealthy(provider)
		}
		return nil, fmt.Errorf("AI embedding failed (provider=%s, model=%s): %w",
			provider, req.Model, err)
	}

	return response, nil
}

// Shutdown gracefully shuts down the service
func (s *Service) Shutdown() {
	if s.healthCheckCancel != nil {
		s.healthCheckCancel()
	}
}

// Provider returns the current default provider.
func (s *Service) Provider() Provider {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.defaultProvider
}

// IsAvailable checks if the service has any available providers.
func (s *Service) IsAvailable(ctx context.Context) bool {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// Check if we have any healthy providers
	for provider := range s.clients {
		if s.isProviderHealthy(provider) {
			return true
		}
	}
	return false
}

// Close closes all AI clients and stops health monitoring.
func (s *Service) Close() error {
	// Cancel health check
	if s.healthCheckCancel != nil {
		s.healthCheckCancel()
	}

	// Close all clients
	s.mu.Lock()
	defer s.mu.Unlock()

	var errs []error
	for provider, client := range s.clients {
		if err := client.Close(); err != nil {
			errs = append(errs, fmt.Errorf("close %s: %w", provider, err))
		}
	}

	if len(errs) > 0 {
		return fmt.Errorf("close errors: %v", errs)
	}
	return nil
}
