// Package ai provides AI service functionality.
package ai

import (
	"errors"
	"fmt"
	"time"
)

// Provider identifies the AI service provider.
type Provider string

// Provider constants.
const (
	<PERSON> Provider = "claude"
	Gemini Provider = "gemini"
	Ollama Provider = "ollama"
)

// Common model identifiers.
const (
	Claude37Sonnet = "claude-3.7-sonnet"
	Claude35Sonnet = "claude-3.5-sonnet"
	Claude35Haiku  = "claude-3.5-haiku"
	Claude3Haiku   = "claude-3-haiku"
	Gemini20Flash  = "gemini-2.0-flash"
	Gemini15Pro    = "gemini-1.5-pro"
	Gemini15Flash  = "gemini-1.5-flash"
)

// Message roles.
const (
	System    = "system"
	User      = "user"
	Assistant = "assistant"
)

// Common errors.
var (
	ErrInvalidModel  = errors.New("invalid model")
	ErrEmptyMessages = errors.New("empty messages")
	ErrRateLimit     = errors.New("rate limit exceeded")
)

// Request represents a chat request.
type Request struct {
	Messages    []Message `json:"messages"`
	Model       string    `json:"model,omitempty"`
	Temperature float32   `json:"temperature,omitempty"`
	MaxTokens   int       `json:"max_tokens,omitempty"`
	Stream      bool      `json:"stream,omitempty"`
	System      string    `json:"system,omitempty"` // System prompt
	Tools       []Tool    `json:"tools,omitempty"`
}

// Validate checks if the request is valid.
func (r *Request) Validate() error {
	if len(r.Messages) == 0 {
		return ErrEmptyMessages
	}
	// First message must be from user or system, not assistant
	if r.Messages[0].Role == Assistant {
		return fmt.Errorf("first message cannot be from assistant")
	}
	for i, msg := range r.Messages {
		if msg.Content == "" && len(msg.Images) == 0 {
			return fmt.Errorf("message %d: empty content", i)
		}
		if msg.Role != User && msg.Role != Assistant && msg.Role != System {
			return fmt.Errorf("message %d: invalid role %q", i, msg.Role)
		}
	}
	if r.MaxTokens < 0 {
		return errors.New("negative max tokens")
	}
	if r.Temperature < 0 || r.Temperature > 2 {
		return errors.New("temperature must be between 0 and 2")
	}
	return nil
}

// Response represents a chat response.
type Response struct {
	Content   string     `json:"content"`
	Model     string     `json:"model,omitempty"`
	Provider  Provider   `json:"provider,omitempty"`
	Usage     Usage      `json:"usage,omitempty"`
	ToolCalls []ToolCall `json:"tool_calls,omitempty"`
}

// Message represents a conversation message.
type Message struct {
	Role    string    `json:"role"`
	Content string    `json:"content"`
	Images  []Image   `json:"images,omitempty"`
	Time    time.Time `json:"time,omitempty"`
}

// Image contains image data for multimodal requests.
type Image struct {
	Data     []byte `json:"data"`      // Raw image data
	MimeType string `json:"mime_type"` // e.g., "image/jpeg"
}

// Usage tracks token consumption.
type Usage struct {
	Input  int `json:"input"`
	Output int `json:"output"`
	Total  int `json:"total"`
}

// Add safely adds token counts with overflow protection.
func (u *Usage) Add(other Usage) {
	const maxInt = int(^uint(0) >> 1)

	// Helper function to safely add two ints
	safeAdd := func(a, b int) int {
		// Check if addition would overflow
		if a > 0 && b > 0 && a > maxInt-b {
			return maxInt
		}
		if a < 0 && b < 0 && a < -maxInt-b {
			return -maxInt
		}
		return a + b
	}

	// Safely add each field
	u.Input = safeAdd(u.Input, other.Input)
	u.Output = safeAdd(u.Output, other.Output)
	u.Total = safeAdd(u.Total, other.Total)
}

// Stream represents a streaming response chunk.
type Stream struct {
	Delta     string     `json:"delta"`
	Done      bool       `json:"done"`
	Usage     *Usage     `json:"usage,omitempty"`      // Only when done
	ToolCalls []ToolCall `json:"tool_calls,omitempty"` // Tool calls when available
	Err       error      `json:"-"`
}

// EmbedRequest represents an embedding request.
type EmbedRequest struct {
	Text  string `json:"text"`
	Model string `json:"model,omitempty"`
}

// EmbedResponse represents an embedding response.
type EmbedResponse struct {
	Embedding []float32 `json:"embedding"`
	Model     string    `json:"model,omitempty"`
	Usage     Usage     `json:"usage,omitempty"`
}
