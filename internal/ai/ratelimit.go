// Package ai provides AI service abstraction
package ai

import (
	"context"
	"fmt"
	"sync"
	"time"
)

// RateLimiter provides simple rate limiting for AI requests.
// Uses a token bucket algorithm: requests consume tokens, tokens refill over time.
type RateLimiter struct {
	tokens     int           // Current available tokens
	maxTokens  int           // Maximum tokens in bucket
	refillRate time.Duration // Time to refill one token
	mu         sync.Mutex
	lastRefill time.Time
}

// NewRateLimiter creates a rate limiter with specified capacity and refill rate.
// Example: NewRateLimiter(10, time.Minute) allows 10 requests per minute.
func NewRateLimiter(maxTokens int, refillRate time.Duration) *RateLimiter {
	return &RateLimiter{
		tokens:     maxTokens,
		maxTokens:  maxTokens,
		refillRate: refillRate / time.Duration(maxTokens), // Rate per token
		lastRefill: time.Now(),
	}
}

// Allow checks if a request can proceed. Returns error if rate limit exceeded.
func (r *RateLimiter) Allow() error {
	r.mu.Lock()
	defer r.mu.Unlock()

	// Refill tokens based on elapsed time
	now := time.Now()
	elapsed := now.Sub(r.lastRefill)
	tokensToAdd := int(elapsed / r.refillRate)

	if tokensToAdd > 0 {
		r.tokens = min(r.tokens+tokensToAdd, r.maxTokens)
		r.lastRefill = now
	}

	// Check if we have tokens available
	if r.tokens <= 0 {
		return fmt.Errorf("rate limit exceeded, please try again later")
	}

	// Consume a token
	r.tokens--
	return nil
}

// rateLimitedClient wraps an AI client with rate limiting.
// This follows the decorator pattern to add rate limiting transparently.
// It implements both Client and Embedding interfaces if the underlying client supports them.
type rateLimitedClient struct {
	Client
	limiter *RateLimiter
}

// NewRateLimitedClient wraps a client with rate limiting.
func NewRateLimitedClient(client Client, limiter *RateLimiter) Client {
	return &rateLimitedClient{
		Client:  client,
		limiter: limiter,
	}
}

// Chat implements the Client interface with rate limiting.
func (c *rateLimitedClient) Chat(ctx context.Context, req *Request) (*Response, error) {
	if err := c.limiter.Allow(); err != nil {
		return nil, err
	}
	return c.Client.Chat(ctx, req)
}

// Stream implements the Client interface with rate limiting.
func (c *rateLimitedClient) Stream(ctx context.Context, req *Request) (<-chan Stream, error) {
	if err := c.limiter.Allow(); err != nil {
		return nil, err
	}
	return c.Client.Stream(ctx, req)
}

// Embed implements the Embedding interface with rate limiting.
// This method is only available if the underlying client implements Embedding.
func (c *rateLimitedClient) Embed(ctx context.Context, req *EmbedRequest) (*EmbedResponse, error) {
	if err := c.limiter.Allow(); err != nil {
		return nil, err
	}
	// Check if the underlying client supports embeddings
	if embeddingClient, ok := c.Client.(Embedding); ok {
		return embeddingClient.Embed(ctx, req)
	}
	return nil, fmt.Errorf("underlying client does not support embeddings")
}

// ServiceWithRateLimit creates an AI service with rate limiting applied to all providers.
// This is a convenience function that wraps all clients with rate limiters.
func ServiceWithRateLimit(clients map[Provider]Client, defaultProvider Provider, requestsPerMinute int, opts ...ServiceOption) (*Service, error) {
	// Wrap each client with rate limiting
	limitedClients := make(map[Provider]Client)
	for provider, client := range clients {
		limiter := NewRateLimiter(requestsPerMinute, time.Minute)
		limitedClients[provider] = NewRateLimitedClient(client, limiter)
	}

	return NewService(limitedClients, defaultProvider, opts...)
}
