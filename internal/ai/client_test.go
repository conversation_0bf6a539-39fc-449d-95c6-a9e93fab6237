package ai

import (
	"context"
	"encoding/json"
	"errors"
	"strings"
	"testing"
	"time"
)

// TestService_Chat tests the basic chat functionality
func TestService_Chat(t *testing.T) {
	tests := []struct {
		name      string
		request   *Request
		wantError bool
		errorMsg  string
	}{
		{
			name: "valid_request_with_single_message",
			request: &Request{
				Model: Claude35Sonnet,
				Messages: []Message{
					{Role: User, Content: "Hello"},
				},
				MaxTokens: 100,
			},
			wantError: false,
		},
		{
			name: "empty_messages_should_fail",
			request: &Request{
				Model:     Claude35Sonnet,
				Messages:  []Message{},
				MaxTokens: 100,
			},
			wantError: true,
			errorMsg:  "messages cannot be empty",
		},
		{
			name: "invalid_model_should_pass", // Model validation is not implemented
			request: &Request{
				Model: "invalid-model", // Invalid model string - but no validation
				Messages: []Message{
					{Role: User, Content: "Hello"},
				},
				MaxTokens: 100,
			},
			wantError: false, // No model validation, so it passes
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a test service with mock client
			service := &Service{
				clients: map[Provider]Client{
					Claude: &mockClient{
						chatFunc: func(ctx context.Context, req *Request) (*Response, error) {
							// Validate request
							if len(req.Messages) == 0 {
								return nil, ErrEmptyMessages
							}
							return &Response{
								Content: "Mock response",
								Model:   req.Model,
								Usage: Usage{
									Input:  10,
									Output: 5,
									Total:  15,
								},
							}, nil
						},
					},
				},
				defaultProvider: Claude,
				providerHealth: map[Provider]bool{
					Claude: true,
				},
			}

			ctx := context.Background()
			resp, err := service.Chat(ctx, tt.request)

			if tt.wantError {
				if err == nil {
					t.Errorf("expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("unexpected error: %v", err)
			}

			if resp == nil {
				t.Error("expected response but got nil")
			}
		})
	}
}

// TestService_ProviderFailover tests manual failover between providers
func TestService_ProviderFailover(t *testing.T) {
	failingClient := &mockClient{
		chatFunc: func(ctx context.Context, req *Request) (*Response, error) {
			return nil, &OpError{
				Provider: Claude,
				Op:       "chat",
				Err:      errors.New("connection refused"),
			}
		},
	}

	successClient := &mockClient{
		chatFunc: func(ctx context.Context, req *Request) (*Response, error) {
			return &Response{
				Content: "Backup provider response",
				Model:   Gemini15Pro,
			}, nil
		},
	}

	service, err := NewService(
		map[Provider]Client{
			Claude: failingClient,
			Gemini: successClient,
		},
		Claude,
	)
	if err != nil {
		t.Fatalf("failed to create service: %v", err)
	}

	ctx := context.Background()
	req := &Request{
		Model: Claude35Sonnet,
		Messages: []Message{
			{Role: User, Content: "Test failover"},
		},
		MaxTokens: 100,
	}

	// Need to fail 3 times to mark provider as unhealthy (maxFailures = 3)
	for i := 0; i < 3; i++ {
		_, err = service.Chat(ctx, req)
		if err == nil {
			t.Fatalf("expected request %d to fail", i+1)
		}
	}

	// Now Claude should be marked as unhealthy, next request should use Gemini
	req.Model = "" // Clear model to allow provider selection
	resp, err := service.Chat(ctx, req)
	if err != nil {
		t.Fatalf("expected request to succeed with fallback provider, got error: %v", err)
	}

	if resp.Content != "Backup provider response" {
		t.Errorf("expected response from backup provider, got: %s", resp.Content)
	}
}

// TestRequest_Validate tests request validation
func TestRequest_Validate(t *testing.T) {
	tests := []struct {
		name    string
		request Request
		wantErr bool
	}{
		{
			name: "valid_request",
			request: Request{
				Messages: []Message{{Role: User, Content: "Hello"}},
			},
			wantErr: false,
		},
		{
			name: "empty_messages",
			request: Request{
				Messages: []Message{},
			},
			wantErr: true,
		},
		{
			name: "empty_message_content",
			request: Request{
				Messages: []Message{{Role: User, Content: ""}},
			},
			wantErr: true,
		},
		{
			name: "invalid_role",
			request: Request{
				Messages: []Message{{Role: "invalid", Content: "Hello"}},
			},
			wantErr: true,
		},
		{
			name: "invalid_first_message_role",
			request: Request{
				Messages: []Message{{Role: Assistant, Content: "Hello"}},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.request.Validate()
			if (err != nil) != tt.wantErr {
				t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// mockClient implements Client interface for testing
type mockClient struct {
	chatFunc      func(context.Context, *Request) (*Response, error)
	streamFunc    func(context.Context, *Request) (<-chan Stream, error)
	embeddingFunc func(context.Context, *EmbedRequest) (*EmbedResponse, error)
}

func (m *mockClient) Chat(ctx context.Context, req *Request) (*Response, error) {
	if m.chatFunc != nil {
		return m.chatFunc(ctx, req)
	}
	return &Response{Content: "mock response"}, nil
}

func (m *mockClient) Stream(ctx context.Context, req *Request) (<-chan Stream, error) {
	if m.streamFunc != nil {
		return m.streamFunc(ctx, req)
	}
	ch := make(chan Stream, 1)
	ch <- Stream{Delta: "mock stream"}
	close(ch)
	return ch, nil
}

func (m *mockClient) Provider() Provider {
	return Claude
}

func (m *mockClient) Close() error {
	return nil
}

func (m *mockClient) Embed(ctx context.Context, req *EmbedRequest) (*EmbedResponse, error) {
	if m.embeddingFunc != nil {
		return m.embeddingFunc(ctx, req)
	}
	// Return a mock embedding
	return &EmbedResponse{
		Embedding: make([]float32, 1536),
	}, nil
}

func (m *mockClient) IsAvailable(ctx context.Context) bool {
	return true
}

// Example demonstrates how to use the AI service
func ExampleService_Chat() {
	// Create service with real clients
	service := &Service{
		clients: map[Provider]Client{
			// Initialize with real clients in production
		},
		defaultProvider: Claude,
	}

	// Create a chat request
	req := &Request{
		Model: Claude35Sonnet,
		Messages: []Message{
			{
				Role:    User,
				Content: "What is the capital of France?",
			},
		},
		MaxTokens:   100,
		Temperature: 0.7,
	}

	// Execute chat
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	resp, err := service.Chat(ctx, req)
	if err != nil {
		// Handle error
		return
	}

	// Use response
	_ = resp.Content
}

// Benchmark tests for AI types

// BenchmarkRequestValidation benchmarks request validation
func BenchmarkRequestValidation(b *testing.B) {
	req := &Request{
		Messages: []Message{
			{Role: User, Content: "Hello, this is a test message"},
			{Role: Assistant, Content: "I understand, how can I help?"},
			{Role: User, Content: "Tell me about Go benchmarking"},
		},
		MaxTokens:   1000,
		Temperature: 0.7,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = req.Validate()
	}
}

// BenchmarkMessageConstruction benchmarks building messages
func BenchmarkMessageConstruction(b *testing.B) {
	b.Run("Small", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = Message{
				Role:    User,
				Content: "Short message",
			}
		}
	})

	b.Run("Medium", func(b *testing.B) {
		content := strings.Repeat("This is a medium length message. ", 20)
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = Message{
				Role:    User,
				Content: content,
			}
		}
	})

	b.Run("Large", func(b *testing.B) {
		content := strings.Repeat("This is a large message for benchmarking. ", 200)
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = Message{
				Role:    User,
				Content: content,
			}
		}
	})
}

// BenchmarkUsageCalculation benchmarks token usage calculations
func BenchmarkUsageCalculation(b *testing.B) {
	usages := make([]Usage, 100)
	for i := range usages {
		usages[i] = Usage{
			Input:  i * 10,
			Output: i * 5,
			Total:  i * 15,
		}
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		total := Usage{}
		for _, u := range usages {
			total.Add(u)
		}
	}
}

// BenchmarkToolCallParsing benchmarks tool call parsing
func BenchmarkToolCallParsing(b *testing.B) {
	toolCalls := []ToolCall{
		{
			ID:   "call_123",
			Name: "search",
			Args: Arguments{
				"query": json.RawMessage(`"benchmark test"`),
				"limit": json.RawMessage(`10`),
			},
		},
		{
			ID:   "call_124",
			Name: "calculate",
			Args: Arguments{
				"expression": json.RawMessage(`"2 + 2"`),
				"precision":  json.RawMessage(`2`),
			},
		},
	}

	response := &Response{
		Content:   "Here are the results",
		ToolCalls: toolCalls,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// Simulate accessing tool calls
		for _, tc := range response.ToolCalls {
			_ = tc.ID
			_ = tc.Name
			_ = tc.Args
		}
	}
}
