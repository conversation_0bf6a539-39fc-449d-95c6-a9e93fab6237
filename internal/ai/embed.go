package ai

import (
	"context"
	"fmt"
)

// Embed generates text embeddings using the available AI provider.
//
// WHY: Service-level embedding because:
// 1. Not all providers support embeddings
// 2. Service can select appropriate provider
// 3. Consistent interface for memory system
func (s *Service) Embed(ctx context.Context, req *EmbedRequest) (*EmbedResponse, error) {
	// Set default model if not specified
	if req.Model == "" {
		req.Model = "gemini-embedding-001"
	}

	// Find any provider that supports embeddings
	provider := s.findEmbeddingProvider()
	if provider == "" {
		return nil, fmt.Errorf("no embedding provider available")
	}

	s.mu.RLock()
	client, exists := s.clients[provider]
	s.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("provider %s does not exist", provider)
	}

	// Check if client supports embeddings
	embedder, ok := client.(Embedding)
	if !ok {
		return nil, fmt.Errorf("provider %s does not support embeddings", provider)
	}

	// Send request
	response, err := embedder.Embed(ctx, req)
	if err != nil {
		// Mark provider as unhealthy if connection error
		if s.isConnectionError(err) {
			s.markProviderUnhealthy(provider)
		}
		return nil, fmt.Errorf("embedding failed (provider=%s): %w", provider, err)
	}

	return response, nil
}

// findEmbeddingProvider finds any provider that supports embeddings.
func (s *Service) findEmbeddingProvider() Provider {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// Prefer Gemini for embeddings if available
	if client, exists := s.clients[Gemini]; exists {
		if _, ok := client.(Embedding); ok && s.isProviderHealthy(Gemini) {
			return Gemini
		}
	}

	// Fall back to any other provider that supports embeddings
	for provider, client := range s.clients {
		if _, ok := client.(Embedding); ok && s.isProviderHealthy(provider) {
			return provider
		}
	}

	return ""
}

// EmbedTexts generates embeddings for multiple texts in batch.
//
// WHY: Batch processing for efficiency when dealing with multiple texts.
func (s *Service) EmbedTexts(ctx context.Context, texts []string, model string) ([][]float32, error) {
	embeddings := make([][]float32, 0, len(texts))

	for _, text := range texts {
		req := &EmbedRequest{
			Text:  text,
			Model: model,
		}

		resp, err := s.Embed(ctx, req)
		if err != nil {
			return nil, fmt.Errorf("embed text at index %d: %w", len(embeddings), err)
		}

		embeddings = append(embeddings, resp.Embedding)
	}

	return embeddings, nil
}
