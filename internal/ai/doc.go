// Package ai provides a unified interface for interacting with various AI providers.
//
// This package implements a provider-agnostic AI service that supports multiple
// backends (<PERSON>, <PERSON>, <PERSON>llama) while maintaining a consistent API. It follows
//
// # Architecture
//
// The package is structured around these core concepts:
//
//   - Service: The main entry point that manages multiple AI providers
//   - Client: Provider-specific implementations (<PERSON>, <PERSON>, etc.)
//   - Model: Strongly-typed model identifiers for each provider
//   - Request/Response: Consistent data structures across providers
//
// # Why This Design
//
// We chose this architecture for several reasons:
//
//  1. Provider Independence: Easy to switch between AI providers without changing application code
//  2. Failover Support: Automatic fallback to alternative providers on failure
//  3. Consistent Interface: Same API regardless of underlying provider
//  4. Explicit Error Handling: Clear error types for different failure modes
//  5. Resource Management: Proper connection pooling and cleanup
//
// # Usage
//
// Basic usage example:
//
//	// Initialize service with providers
//	service := ai.NewService(
//	    ai.WithClaude(apiKey),
//	    ai.With<PERSON><PERSON><PERSON>(api<PERSON>ey),
//	)
//	defer service.Close()
//
//	// Create a chat request
//	req := &ai.Request{
//	    Model: ai.Claude35Sonnet,
//	    Messages: []ai.Message{
//	        {Role: ai.User, Content: "Hello, how are you?"},
//	    },
//	    MaxTokens: 1000,
//	}
//
//	// Execute chat
//	resp, err := service.Chat(ctx, req)
//	if err != nil {
//	    // Handle error - check if it's a specific error type
//	    var aiErr *ai.Error
//	    if errors.As(err, &aiErr) {
//	        log.Printf("AI error from %s: %s", aiErr.Provider, aiErr.Message)
//	    }
//	    return err
//	}
//
// # Streaming
//
// For real-time responses, use the streaming API:
//
//	stream, err := service.Stream(ctx, req)
//	if err != nil {
//	    return err
//	}
//
//	for response := range stream {
//	    if response.Err != nil {
//	        return response.Err
//	    }
//	    fmt.Print(response.Delta)
//	}
//
// # Error Handling
//
// The package defines specific error types for common failures:
//
//   - RateLimitError: Provider rate limit exceeded
//   - AuthenticationError: Invalid API credentials
//   - ValidationError: Invalid request parameters
//   - NetworkError: Connection failures
//
// Always check error types to implement appropriate retry logic:
//
//	if err != nil {
//	    var aiErr *ai.Error
//	    if errors.As(err, &aiErr) && aiErr.Code == "rate_limit_error" {
//	        // Implement exponential backoff
//	        time.Sleep(backoffDuration)
//	        return retry(req)
//	    }
//	    return err
//	}
//
// # Best Practices
//
//  1. Always set appropriate timeouts using context
//  2. Handle streaming errors within the stream loop
//  3. Use specific models rather than generic identifiers
//  4. Validate requests before sending to avoid errors
//  5. Implement retry logic for transient failures
//
// # Performance Considerations
//
//   - Reuse Service instances (they're safe for concurrent use)
//   - Set appropriate MaxTokens to control costs
//   - Use streaming for better perceived performance
//   - Monitor token usage through Usage fields
//
// # Testing
//
// The package provides test utilities for mocking AI responses:
//
//	mockClient := ai.NewMockClient()
//	mockClient.SetResponse(&ai.Response{
//	    Content: "Test response",
//	})
//	service := ai.NewService(ai.WithClient(mockClient))
package ai
