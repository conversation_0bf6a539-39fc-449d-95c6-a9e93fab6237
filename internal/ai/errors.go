// Package ai provides error types for AI operations.
package ai

import (
	"errors"
	"fmt"
)

// OpError represents an operation error.
// Following os.PathError and net.OpError patterns.
type OpError struct {
	Op       string   // Operation: "chat", "embed", "stream"
	Provider Provider // Provider where error occurred
	Err      error    // Underlying error
}

// Error returns the error string.
func (e *OpError) Error() string {
	if e.Provider != "" {
		return fmt.Sprintf("%s %s: %v", e.Provider, e.Op, e.Err)
	}
	return fmt.Sprintf("%s: %v", e.Op, e.Err)
}

// Unwrap returns the underlying error.
func (e *OpError) Unwrap() error {
	return e.Err
}

// Timeout reports whether this error represents a timeout.
func (e *OpError) Timeout() bool {
	if e.Err == nil {
		return false
	}
	var timeoutErr interface{ Timeout() bool }
	return errors.As(e.Err, &timeoutErr) && timeoutErr.Timeout()
}

// Temporary reports whether this error is temporary.
func (e *OpError) Temporary() bool {
	if e.Err == nil {
		return false
	}
	var tempErr interface{ Temporary() bool }
	return errors.As(e.Err, &tempErr) && tempErr.Temporary()
}
