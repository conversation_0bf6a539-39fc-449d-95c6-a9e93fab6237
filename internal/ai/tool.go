package ai

import (
	"encoding/json"
	"fmt"
)

// Tool represents an available tool.
type Tool struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	InputSchema Schema `json:"input_schema"`
}

// Schema defines tool parameter schema.
type Schema struct {
	Type       string              `json:"type"` // Usually "object"
	Properties map[string]Property `json:"properties"`
	Required   []string            `json:"required,omitempty"`
}

// Property defines a parameter property.
type Property struct {
	Type        string   `json:"type"`
	Description string   `json:"description,omitempty"`
	Enum        []string `json:"enum,omitempty"`
	Default     any      `json:"default,omitempty"`
}

// ToolCall represents a tool invocation.
type ToolCall struct {
	ID   string    `json:"id"`
	Name string    `json:"name"`
	Args Arguments `json:"args"`
}

// ToolResult represents the result of tool execution.
type ToolResult struct {
	ID      string `json:"id"` // Matches ToolCall.ID
	Content string `json:"content"`
	Error   error  `json:"error,omitempty"`
}

// Arguments uses json.RawMessage for type-safe parameter handling.
// This follows encoding/json patterns for delayed parsing.
type Arguments map[string]json.RawMessage

// GetString extracts a string argument.
func (a Arguments) GetString(key string) (string, error) {
	raw, ok := a[key]
	if !ok {
		return "", fmt.Errorf("argument %q not found", key)
	}
	var s string
	if err := json.Unmarshal(raw, &s); err != nil {
		return "", fmt.Errorf("argument %q is not a string: %w", key, err)
	}
	return s, nil
}

// GetInt extracts an integer argument.
func (a Arguments) GetInt(key string) (int, error) {
	raw, ok := a[key]
	if !ok {
		return 0, fmt.Errorf("argument %q not found", key)
	}
	var i int
	if err := json.Unmarshal(raw, &i); err != nil {
		return 0, fmt.Errorf("argument %q is not an integer: %w", key, err)
	}
	return i, nil
}

// GetFloat extracts a float64 argument.
func (a Arguments) GetFloat(key string) (float64, error) {
	raw, ok := a[key]
	if !ok {
		return 0, fmt.Errorf("argument %q not found", key)
	}
	var f float64
	if err := json.Unmarshal(raw, &f); err != nil {
		return 0, fmt.Errorf("argument %q is not a number: %w", key, err)
	}
	return f, nil
}

// GetBool extracts a boolean argument.
func (a Arguments) GetBool(key string) (bool, error) {
	raw, ok := a[key]
	if !ok {
		return false, fmt.Errorf("argument %q not found", key)
	}
	var b bool
	if err := json.Unmarshal(raw, &b); err != nil {
		return false, fmt.Errorf("argument %q is not a boolean: %w", key, err)
	}
	return b, nil
}

// Get extracts an argument into a custom type.
func (a Arguments) Get(key string, v any) error {
	raw, ok := a[key]
	if !ok {
		return fmt.Errorf("argument %q not found", key)
	}
	return json.Unmarshal(raw, v)
}

// Set adds an argument of any type.
func (a Arguments) Set(key string, value any) error {
	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("marshal %q: %w", key, err)
	}
	a[key] = data
	return nil
}

// Has checks if an argument exists.
func (a Arguments) Has(key string) bool {
	_, ok := a[key]
	return ok
}

// Keys returns all argument keys.
func (a Arguments) Keys() []string {
	keys := make([]string, 0, len(a))
	for k := range a {
		keys = append(keys, k)
	}
	return keys
}

// GetTyped extracts a typed argument using generics.
// This provides compile-time type safety.
// Example: path, err := GetTyped[string](args, "path")
func GetTyped[T any](a Arguments, key string) (T, error) {
	var zero T
	raw, ok := a[key]
	if !ok {
		return zero, fmt.Errorf("argument %q not found", key)
	}

	var result T
	if err := json.Unmarshal(raw, &result); err != nil {
		return zero, fmt.Errorf("argument %q cannot be unmarshaled to %T: %w", key, zero, err)
	}
	return result, nil
}

// GetTypedOr returns the typed value or a default if not found.
// Example: encoding := GetTypedOr(args, "encoding", "utf-8")
func GetTypedOr[T any](a Arguments, key string, defaultValue T) T {
	value, err := GetTyped[T](a, key)
	if err != nil {
		return defaultValue
	}
	return value
}
