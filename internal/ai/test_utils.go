package ai

import (
	"sync"

	"github.com/koopa0/assistant-go/internal/platform/logger"
)

// mockLogger for testing
type mockLogger struct {
	mu         sync.Mutex
	warnCalled bool
	lastMsg    string
	lastKV     []any
}

func (m *mockLogger) Debug(msg string, keysAndValues ...any) {}
func (m *mockLogger) Info(msg string, keysAndValues ...any)  {}
func (m *mockLogger) Error(msg string, keysAndValues ...any) {}

func (m *mockLogger) Warn(msg string, keysAndValues ...any) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.warnCalled = true
	m.lastMsg = msg
	m.lastKV = keysAndValues
}

func (m *mockLogger) WithComponent(component string) logger.Logger {
	return m
}
