// Package ai provides AI client interfaces and implementations.
package ai

import (
	"context"
	"io"
)

// Client defines the interface for AI service providers.
// Implementations should provide provider-specific functionality.
type Client interface {
	// Chat sends a chat request and returns the response.
	Chat(ctx context.Context, req *Request) (*Response, error)

	// Stream sends a chat request and returns a stream of responses.
	Stream(ctx context.Context, req *Request) (<-chan Stream, error)

	// Provider returns the provider identifier.
	Provider() Provider

	// IsAvailable checks if the provider is currently available.
	IsAvailable(ctx context.Context) bool

	// Close closes the client and releases resources.
	io.Closer
}

// Embedding is an optional interface for providers that support embeddings.
// Use type assertion to check if a Client supports embeddings.
type Embedding interface {
	Embed(ctx context.Context, req *EmbedRequest) (*EmbedResponse, error)
}

// HybridClient represents AI providers that combine both conversational and embedding capabilities.
// This hybrid interface enables advanced features like semantic search, memory systems,
// and knowledge management by providing both chat and vector embedding functionality.
type Hybrid interface {
	Client
	Embedding
}
