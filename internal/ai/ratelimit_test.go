package ai

import (
	"context"
	"errors"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewRateLimiter(t *testing.T) {
	tests := []struct {
		name       string
		maxTokens  int
		refillRate time.Duration
		validate   func(*testing.T, *RateLimiter)
	}{
		{
			name:       "create_rate_limiter_10_per_minute",
			maxTokens:  10,
			refillRate: time.Minute,
			validate: func(t *testing.T, rl *RateLimiter) {
				assert.Equal(t, 10, rl.maxTokens)
				assert.Equal(t, 10, rl.tokens)
				assert.Equal(t, time.Minute/10, rl.refillRate)
			},
		},
		{
			name:       "create_rate_limiter_60_per_minute",
			maxTokens:  60,
			refillRate: time.Minute,
			validate: func(t *testing.T, rl *RateLimiter) {
				assert.Equal(t, 60, rl.maxTokens)
				assert.Equal(t, 60, rl.tokens)
				assert.Equal(t, time.Second, rl.refillRate)
			},
		},
		{
			name:       "create_rate_limiter_1_per_second",
			maxTokens:  1,
			refillRate: time.Second,
			validate: func(t *testing.T, rl *RateLimiter) {
				assert.Equal(t, 1, rl.maxTokens)
				assert.Equal(t, 1, rl.tokens)
				assert.Equal(t, time.Second, rl.refillRate)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rl := NewRateLimiter(tt.maxTokens, tt.refillRate)
			require.NotNil(t, rl)
			if tt.validate != nil {
				tt.validate(t, rl)
			}
		})
	}
}

func TestRateLimiter(t *testing.T) {
	t.Run("allows requests within limit", func(t *testing.T) {
		limiter := NewRateLimiter(3, time.Second)

		// Should allow 3 requests immediately
		for i := 0; i < 3; i++ {
			if err := limiter.Allow(); err != nil {
				t.Errorf("request %d should be allowed: %v", i+1, err)
			}
		}

		// 4th request should be denied
		if err := limiter.Allow(); err == nil {
			t.Error("4th request should be denied")
		}
	})

	t.Run("refills tokens over time", func(t *testing.T) {
		limiter := NewRateLimiter(2, 200*time.Millisecond)

		// Use up all tokens
		limiter.Allow()
		limiter.Allow()

		// Should be denied immediately
		if err := limiter.Allow(); err == nil {
			t.Error("should be rate limited")
		}

		// Wait for one token to refill
		time.Sleep(110 * time.Millisecond)

		// Should allow one more request
		if err := limiter.Allow(); err != nil {
			t.Error("should allow request after refill:", err)
		}

		// Should be denied again
		if err := limiter.Allow(); err == nil {
			t.Error("should be rate limited again")
		}
	})

	t.Run("full_refill", func(t *testing.T) {
		rl := NewRateLimiter(3, 150*time.Millisecond)

		// Use all tokens
		for i := 0; i < 3; i++ {
			assert.NoError(t, rl.Allow())
		}

		// Wait for full refill
		time.Sleep(160 * time.Millisecond)

		// Should be able to use all tokens again
		for i := 0; i < 3; i++ {
			err := rl.Allow()
			assert.NoError(t, err, "Request %d after refill should be allowed", i+1)
		}
	})

	t.Run("partial_refill", func(t *testing.T) {
		rl := NewRateLimiter(4, 200*time.Millisecond)

		// Use 3 out of 4 tokens
		for i := 0; i < 3; i++ {
			assert.NoError(t, rl.Allow())
		}

		// Wait for 2 tokens to refill
		time.Sleep(110 * time.Millisecond)

		// Should be able to use 3 tokens (1 remaining + 2 refilled)
		for i := 0; i < 3; i++ {
			err := rl.Allow()
			assert.NoError(t, err, "Request %d should be allowed", i+1)
		}

		// 4th should fail
		err := rl.Allow()
		assert.Error(t, err)
	})

	t.Run("concurrent_access", func(t *testing.T) {
		rl := NewRateLimiter(10, time.Second)

		var wg sync.WaitGroup
		successCount := 0
		var mu sync.Mutex

		// Launch 20 concurrent requests
		for i := 0; i < 20; i++ {
			wg.Add(1)
			go func() {
				defer wg.Done()
				if err := rl.Allow(); err == nil {
					mu.Lock()
					successCount++
					mu.Unlock()
				}
			}()
		}

		wg.Wait()

		// Exactly 10 should succeed
		assert.Equal(t, 10, successCount)
	})
}

func TestRateLimitedClient(t *testing.T) {
	// Create a mock client
	mockClient := &mockAIClient{
		chatResponse: &Response{
			Content: "test response",
		},
	}

	// Wrap with rate limiting (2 requests per second)
	limiter := NewRateLimiter(2, time.Second)
	client := NewRateLimitedClient(mockClient, limiter)

	ctx := context.Background()

	// First two requests should succeed
	for i := 0; i < 2; i++ {
		resp, err := client.Chat(ctx, &Request{})
		if err != nil {
			t.Errorf("request %d failed: %v", i+1, err)
		}
		if resp.Content != "test response" {
			t.Errorf("unexpected response: %s", resp.Content)
		}
	}

	// Third request should be rate limited
	_, err := client.Chat(ctx, &Request{})
	if err == nil {
		t.Error("expected rate limit error")
	}
}

func TestRateLimitedClient_Stream(t *testing.T) {
	t.Run("chat_stream_respects_rate_limit", func(t *testing.T) {
		streamCallCount := 0
		baseClient := &mockAIClient{
			chatResponse: &Response{Content: "response"},
		}
		baseClient.streamCallCount = &streamCallCount

		limiter := NewRateLimiter(1, time.Minute)
		rlClient := NewRateLimitedClient(baseClient, limiter)

		ctx := context.Background()
		req := &Request{
			Messages: []Message{{Role: User, Content: "test"}},
			Stream:   true,
		}

		// First call should succeed
		ch1, err1 := rlClient.Stream(ctx, req)
		assert.NoError(t, err1)
		assert.NotNil(t, ch1)

		// Drain the channel
		for range ch1 {
		}

		// Second call should be rate limited
		ch2, err2 := rlClient.Stream(ctx, req)
		assert.Error(t, err2)
		assert.Nil(t, ch2)
		assert.Contains(t, err2.Error(), "rate limit exceeded")

		// Only 1 call should have made it through
		assert.Equal(t, 1, streamCallCount)
	})

	t.Run("chat_stream_passes_through_errors", func(t *testing.T) {
		baseClient := &mockAIClient{
			chatError: errors.New("stream error"),
		}

		limiter := NewRateLimiter(10, time.Minute)
		rlClient := NewRateLimitedClient(baseClient, limiter)

		ctx := context.Background()
		req := &Request{
			Messages: []Message{{Role: User, Content: "test"}},
			Stream:   true,
		}

		ch, err := rlClient.Stream(ctx, req)
		// Since our mock returns error in Stream too
		assert.NoError(t, err) // Stream itself doesn't error
		assert.NotNil(t, ch)

		// But the channel should close immediately
		count := 0
		for range ch {
			count++
		}
		assert.Equal(t, 1, count) // Only the final message
	})
}

func TestRateLimitedClient_PassThroughMethods(t *testing.T) {
	// Test that other Client methods are passed through unchanged
	baseClient := &mockAIClient{
		chatResponse: &Response{Content: "test"},
	}

	limiter := NewRateLimiter(10, time.Minute)
	rlClient := NewRateLimitedClient(baseClient, limiter)

	// Test Provider
	assert.Equal(t, Claude, rlClient.Provider())

	// Test IsAvailable
	assert.True(t, rlClient.IsAvailable(context.Background()))

	// Test Close
	assert.NoError(t, rlClient.Close())

	// Test Embedding (if supported)
	ctx := context.Background()
	if embedClient, ok := rlClient.(Embedding); ok {
		embReq := &EmbedRequest{Text: "test"}
		embResp, err := embedClient.Embed(ctx, embReq)
		assert.NoError(t, err)
		assert.NotNil(t, embResp)
	}
}

func TestServiceWithRateLimit(t *testing.T) {
	tests := []struct {
		name              string
		clients           map[Provider]Client
		defaultProvider   Provider
		requestsPerMinute int
		opts              []ServiceOption
		wantError         bool
		errorContains     string
	}{
		{
			name: "successful_rate_limited_service",
			clients: map[Provider]Client{
				Claude: &mockAIClient{
					chatResponse: &Response{Content: "test"},
				},
			},
			defaultProvider:   Claude,
			requestsPerMinute: 60,
			wantError:         false,
		},
		{
			name:              "empty_clients",
			clients:           map[Provider]Client{},
			defaultProvider:   Claude,
			requestsPerMinute: 60,
			wantError:         true,
			errorContains:     "no AI clients provided",
		},
		{
			name: "with_options",
			clients: map[Provider]Client{
				Claude: &mockAIClient{
					chatResponse: &Response{Content: "test"},
				},
			},
			defaultProvider:   Claude,
			requestsPerMinute: 30,
			opts: []ServiceOption{
				WithHealthCheckInterval(1 * time.Minute),
			},
			wantError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service, err := ServiceWithRateLimit(tt.clients, tt.defaultProvider, tt.requestsPerMinute, tt.opts...)

			if tt.wantError {
				require.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
				assert.Nil(t, service)
			} else {
				require.NoError(t, err)
				require.NotNil(t, service)

				// Verify rate limiting is applied
				ctx := context.Background()
				req := &Request{
					Model:    Claude35Sonnet,
					Messages: []Message{{Role: User, Content: "Test"}},
				}

				// Should be able to make a request
				_, err := service.Chat(ctx, req)
				assert.NoError(t, err)

				// Cleanup
				service.Shutdown()
			}
		})
	}
}

func TestMinFunction(t *testing.T) {
	tests := []struct {
		a, b int
		want int
	}{
		{a: 1, b: 2, want: 1},
		{a: 2, b: 1, want: 1},
		{a: 5, b: 5, want: 5},
		{a: -1, b: 0, want: -1},
		{a: 0, b: -1, want: -1},
	}

	for _, tt := range tests {
		got := min(tt.a, tt.b)
		assert.Equal(t, tt.want, got)
	}
}

func BenchmarkRateLimiter_Allow(b *testing.B) {
	rl := NewRateLimiter(1000, time.Second)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = rl.Allow()
	}
}

func BenchmarkRateLimiter_AllowConcurrent(b *testing.B) {
	rl := NewRateLimiter(10000, time.Second)

	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			_ = rl.Allow()
		}
	})
}

// mockAIClient is a simple mock for testing
type mockAIClient struct {
	chatResponse    *Response
	chatError       error
	streamCallCount *int
}

func (m *mockAIClient) Chat(ctx context.Context, req *Request) (*Response, error) {
	if m.chatError != nil {
		return nil, m.chatError
	}
	return m.chatResponse, nil
}

func (m *mockAIClient) Stream(ctx context.Context, req *Request) (<-chan Stream, error) {
	if m.streamCallCount != nil {
		*m.streamCallCount++
	}
	ch := make(chan Stream, 1)
	if m.chatResponse != nil {
		ch <- Stream{
			Delta: m.chatResponse.Content,
			Done:  true,
		}
	} else {
		ch <- Stream{
			Delta: "",
			Done:  true,
		}
	}
	close(ch)
	return ch, nil
}

func (m *mockAIClient) Embed(ctx context.Context, req *EmbedRequest) (*EmbedResponse, error) {
	return &EmbedResponse{
		Embedding: make([]float32, 1536),
		Model:     "mock-model",
		Usage:     Usage{Input: 10, Output: 0, Total: 10},
	}, nil
}

func (m *mockAIClient) Provider() Provider {
	return Claude
}

func (m *mockAIClient) IsAvailable(ctx context.Context) bool {
	return true
}

func (m *mockAIClient) Close() error {
	return nil
}

// TestRateLimitedClient_Embedding tests that rate-limited clients preserve embedding capabilities
func TestRateLimitedClient_Embedding(t *testing.T) {
	tests := []struct {
		name         string
		client       Client
		wantEmbedErr bool
		checkEmbed   bool
	}{
		{
			name: "client with embedding support",
			client: &mockClient{
				embeddingFunc: func(ctx context.Context, req *EmbedRequest) (*EmbedResponse, error) {
					return &EmbedResponse{
						Embedding: []float32{0.1, 0.2, 0.3},
						Model:     "test-model",
						Usage:     Usage{Input: 10, Output: 0, Total: 10},
					}, nil
				},
			},
			wantEmbedErr: false,
			checkEmbed:   true,
		},
		{
			name:         "client without embedding support",
			client:       &mockClientWithoutEmbedding{},
			wantEmbedErr: true,
			checkEmbed:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create rate limiter
			limiter := NewRateLimiter(10, 60) // 10 requests per minute

			// Wrap client with rate limiting
			rateLimited := NewRateLimitedClient(tt.client, limiter)

			// Check if the rate-limited client implements Embedding
			embeddingClient, ok := rateLimited.(Embedding)
			if !ok {
				t.Fatal("rate-limited client should always implement Embedding")
			}

			// Test the embedding method
			if tt.checkEmbed {
				resp, err := embeddingClient.Embed(context.Background(), &EmbedRequest{
					Text: "test text",
				})

				if tt.wantEmbedErr {
					if err == nil {
						t.Error("expected error for client without embedding support")
					}
				} else {
					if err != nil {
						t.Errorf("unexpected error: %v", err)
					}
					if resp == nil {
						t.Error("expected non-nil response")
					}
				}
			}
		})
	}
}

// mockClientWithoutEmbedding is a mock that only implements Client interface
type mockClientWithoutEmbedding struct{}

func (m *mockClientWithoutEmbedding) Chat(ctx context.Context, req *Request) (*Response, error) {
	return &Response{Content: "test"}, nil
}

func (m *mockClientWithoutEmbedding) Stream(ctx context.Context, req *Request) (<-chan Stream, error) {
	ch := make(chan Stream, 1)
	ch <- Stream{Delta: "test", Done: true}
	close(ch)
	return ch, nil
}

func (m *mockClientWithoutEmbedding) Provider() Provider {
	return Provider("mock")
}

func (m *mockClientWithoutEmbedding) IsAvailable(ctx context.Context) bool {
	return true
}

func (m *mockClientWithoutEmbedding) Close() error {
	return nil
}
