// Package claude provides Anthropic Claude API client implementation
// Uses official anthropic-sdk-go
package claude

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/anthropics/anthropic-sdk-go"
	"github.com/anthropics/anthropic-sdk-go/option"

	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/koopa0/assistant-go/internal/platform/convert"
)

// Client implements the ai.Client interface
// Uses Anthropic official SDK
type Client struct {
	client       anthropic.Client
	defaultModel string
}

// New requires an Anthropic API key for authentication
func New(apiKey string) (*Client, error) {
	if apiKey == "" {
		return nil, errors.New("claude API key cannot be empty")
	}

	// Create anthropic client with API key and retries
	client := anthropic.NewClient(
		option.WithAPIKey(apiKey),
		option.WithMaxRetries(3),
		option.WithRequestTimeout(60*time.Second),
	)

	return &Client{
		client:       client,
		defaultModel: ai.Claude37Sonnet,
	}, nil
}

// NewWithTimeout allows configuring request timeout for long-running operations
func NewWithTimeout(apiKey string, timeout time.Duration) (*Client, error) {
	if apiKey == "" {
		return nil, errors.New("claude API key cannot be empty")
	}

	// Create anthropic client with API key and timeout
	client := anthropic.NewClient(
		option.WithAPIKey(apiKey),
		option.WithRequestTimeout(timeout),
	)

	return &Client{
		client:       client,
		defaultModel: ai.Claude35Sonnet,
	}, nil
}

// Chat implements the ai.Client interface Chat method
// Performs non-streaming conversation
func (c *Client) Chat(ctx context.Context, req *ai.Request) (*ai.Response, error) {
	// Build API request
	messages := c.convertMessages(req.Messages)

	// Create request parameters
	params := anthropic.MessageNewParams{
		Model:     c.convertModelToString(req.Model),
		Messages:  messages,
		MaxTokens: c.getMaxTokens(req.MaxTokens),
	}

	// Temperature is optional, so we'll set it separately
	// For now, we'll skip temperature to avoid the Opt type issue
	// The SDK should use a default value when temperature > 0

	// Set system prompt
	if req.System != "" {
		// According to SDK v1.4.0 docs, system prompt is set as an array of TextBlockParam
		params.System = []anthropic.TextBlockParam{
			{Text: req.System},
		}
	}

	// Note: Stop sequences are not part of the simplified Request type

	// Set tools (Function Call)
	if len(req.Tools) > 0 {
		params.Tools = c.convertTools(req.Tools)
	}

	// Send request
	message, err := c.client.Messages.New(ctx, params)
	if err != nil {
		// Log error for debugging (silent mode)
		return nil, fmt.Errorf("failed to generate response: %w", err)
	}

	// Validate response
	if message == nil {
		return nil, fmt.Errorf("received nil message from Claude API")
	}

	// Extract text content
	content := c.extractContent(*message)

	// Extract tool calls
	toolCalls := c.extractToolCalls(*message)

	// Build response
	usage := ai.Usage{
		Input:  0, // SDK may not provide these statistics
		Output: 0,
		Total:  0,
	}

	// If usage statistics are available
	if message.Usage.InputTokens > 0 {
		usage.Input = convert.SafeInt64ToInt(message.Usage.InputTokens)
		usage.Output = convert.SafeInt64ToInt(message.Usage.OutputTokens)
		usage.Total = usage.Input + usage.Output
	}

	return &ai.Response{
		Content:   content,
		Model:     req.Model,
		Usage:     usage,
		Provider:  ai.Claude,
		ToolCalls: toolCalls,
	}, nil
}

// Stream implements the ai.Client interface Stream method
// Performs streaming conversation
func (c *Client) Stream(ctx context.Context, req *ai.Request) (<-chan ai.Stream, error) {
	responseChan := make(chan ai.Stream)

	// Build API request
	messages := c.convertMessages(req.Messages)

	// Debug logging (re-enable if needed)
	// fmt.Printf("DEBUG: Stream called with %d messages\n", len(req.Messages))
	// fmt.Printf("  System prompt: %q\n", req.System)

	// Ensure we have at least one message
	if len(messages) == 0 {
		go func() {
			responseChan <- ai.Stream{
				Delta: "",
				Done:  true,
				Err:   fmt.Errorf("no messages provided for streaming"),
			}
			close(responseChan)
		}()
		return responseChan, nil
	}

	// Create request parameters
	params := anthropic.MessageNewParams{
		Model:     c.convertModelToString(req.Model),
		Messages:  messages,
		MaxTokens: c.getMaxTokens(req.MaxTokens),
	}

	// Set system prompt
	if req.System != "" {
		// According to SDK v1.4.0 docs, system prompt is set as an array of TextBlockParam
		params.System = []anthropic.TextBlockParam{
			{Text: req.System},
		}
	}

	// Note: Stop sequences are not part of the simplified Request type

	// Set tools (Function Call)
	if len(req.Tools) > 0 {
		params.Tools = c.convertTools(req.Tools)
	}

	// Start goroutine to handle streaming
	go func() {
		defer close(responseChan)

		// Debug logging for stream issues
		// Starting Claude streaming request
		// Model and message information available for debugging if needed

		// Send streaming request
		stream := c.client.Messages.NewStreaming(ctx, params)

		// Check for immediate errors
		if stream.Err() != nil {
			responseChan <- ai.Stream{
				Delta: "",
				Done:  true,
				Err:   fmt.Errorf("failed to create stream: %w", stream.Err()),
			}
			return
		}

		// Use Accumulate to handle stream for usage stats
		message := anthropic.Message{}

		// Keep track of sent content to avoid duplicates
		sentContent := ""

		// Process stream events
		eventCount := 0
		for stream.Next() {
			event := stream.Current()
			eventCount++
			// Processing stream event

			// Accumulate all events to build the message
			_ = message.Accumulate(event) // Accumulation errors handled by stream.Err()

			// Handle different event types
			switch evt := event.AsAny().(type) {
			case anthropic.ContentBlockDeltaEvent:
				// This is the actual text delta event
				// Processing content block delta
				if evt.Delta.Text != "" {
					responseChan <- ai.Stream{
						Delta: evt.Delta.Text,
						Done:  false,
					}
					sentContent += evt.Delta.Text
				}

			case anthropic.MessageDeltaEvent:
				// This typically contains usage info, not text
				// Message delta with usage info

			case anthropic.MessageStartEvent:
				// Message started - check what we're getting
				// Message started

			case anthropic.ContentBlockStartEvent:
				// Sometimes initial content is here
				// Content block starting
				if evt.ContentBlock.Text != "" {
					responseChan <- ai.Stream{
						Delta: evt.ContentBlock.Text,
						Done:  false,
					}
					sentContent += evt.ContentBlock.Text
				}

			case anthropic.MessageStopEvent:
				// Message ended, check if we need to extract content from accumulated message
				// Message ended, check for content

				// Always try to extract content from the accumulated message
				if len(message.Content) > 0 {
					// Check if this is a tool use response
					hasToolUse := false
					for _, content := range message.Content {
						if _, ok := content.AsAny().(anthropic.ToolUseBlock); ok {
							hasToolUse = true
							break
						}
					}

					if hasToolUse {
						// This is a tool use response, send tool calls
						toolCalls := c.extractToolCalls(message)
						if len(toolCalls) > 0 {
							// Send tool calls in stream
							responseChan <- ai.Stream{
								Delta:     "",
								Done:      false,
								ToolCalls: toolCalls,
							}
						}
					} else {
						// Normal text response
						for _, content := range message.Content {
							switch block := content.AsAny().(type) {
							case anthropic.TextBlock:
								// Processing content block
								if block.Text != "" && !strings.Contains(sentContent, block.Text) {
									responseChan <- ai.Stream{
										Delta: block.Text,
										Done:  false,
									}
									sentContent += block.Text
								}
							}
						}
					}
				}

				usage := ai.Usage{
					Input:  0,
					Output: 0,
					Total:  0,
				}

				if message.Usage.InputTokens > 0 {
					usage.Input = convert.SafeInt64ToInt(message.Usage.InputTokens)
					usage.Output = convert.SafeInt64ToInt(message.Usage.OutputTokens)
					usage.Total = usage.Input + usage.Output
				}

				// Stream complete
				responseChan <- ai.Stream{
					Delta: "",
					Done:  true,
					Usage: &usage,
				}

			case anthropic.ContentBlockStopEvent:
				// Content block stopped

			default:
				// Unknown event type
				// Unknown event type received
			}
		}

		// Stream complete
		// Final message details available for debugging if needed

		// Check for errors
		if err := stream.Err(); err != nil {
			// Stream error occurred
			responseChan <- ai.Stream{Err: err}
		}
	}()

	return responseChan, nil
}

// Embed implements the ai.Embedding interface Embed method
// Claude currently does not support embedding vectors
func (c *Client) Embed(ctx context.Context, req *ai.EmbedRequest) (*ai.EmbedResponse, error) {
	return nil, &ai.OpError{
		Op:       "embed",
		Provider: ai.Claude,
		Err:      errors.New("claude does not support embedding vector generation"),
	}
}

// Provider implements ai.Client interface
// Returns provider type
func (c *Client) Provider() ai.Provider {
	return ai.Claude
}

// IsAvailable checks if the provider is currently available
func (c *Client) IsAvailable(ctx context.Context) bool {
	// Simple availability check - try to validate the client
	// For Claude, we assume it's available if we have a client
	// Note: anthropic.Client is a value type, not a pointer
	return true
}

// Close implements ai.Client interface
// Closes the client
func (c *Client) Close() error {
	// anthropic.Client has no resources to close
	return nil
}

// Helper methods

// convertModelToString converts model to string
func (c *Client) convertModelToString(model string) anthropic.Model {
	switch model {
	case ai.Claude37Sonnet:
		return anthropic.ModelClaude3_7SonnetLatest
	case ai.Claude35Sonnet:
		return anthropic.ModelClaude3_5SonnetLatest
	case ai.Claude35Haiku:
		return anthropic.ModelClaude3_5HaikuLatest
	case ai.Claude3Haiku:
		return anthropic.ModelClaude_3_Haiku_20240307
	default:
		// Default to Claude 3.5 Sonnet
		return anthropic.ModelClaude3_5SonnetLatest
	}
}

// convertMessages converts message format
// Converts AI messages to Anthropic format
func (c *Client) convertMessages(messages []ai.Message) []anthropic.MessageParam {
	var anthropicMessages []anthropic.MessageParam

	for _, msg := range messages {
		// Skip system messages (will be set in system prompt)
		if msg.Role == ai.System {
			continue
		}

		// Create content blocks
		var contentBlocks []anthropic.ContentBlockParamUnion

		// Add text content
		if msg.Content != "" {
			contentBlocks = append(contentBlocks, anthropic.NewTextBlock(msg.Content))
		}

		// Add image content
		for _, img := range msg.Images {
			contentBlocks = append(contentBlocks, anthropic.NewImageBlockBase64(
				img.MimeType,
				string(img.Data),
			))
		}

		// Create message based on role
		switch msg.Role {
		case ai.User:
			anthropicMessages = append(anthropicMessages,
				anthropic.NewUserMessage(contentBlocks...))
		case ai.Assistant:
			// Assistant messages need special handling
			anthropicMessages = append(anthropicMessages,
				anthropic.NewAssistantMessage(contentBlocks...))
		}
	}

	return anthropicMessages
}

// extractContent extracts text content from response
func (c *Client) extractContent(message anthropic.Message) string {
	var result strings.Builder

	for _, content := range message.Content {
		// The content might be a TextBlock or ToolUseBlock
		switch block := content.AsAny().(type) {
		case anthropic.TextBlock:
			// Text content block
			result.WriteString(block.Text)
		case anthropic.ToolUseBlock:
			// Tool use block, skip for now
		default:
			// Unknown block type
		}
	}

	return result.String()
}

// getMaxTokens gets maximum token count
// Uses default value if not specified
func (c *Client) getMaxTokens(maxTokens int) int64 {
	// Clamp and validate max tokens first
	clamped := convert.ClampMaxTokens(maxTokens)
	return convert.SafeIntToInt64(clamped)
}

// convertTools converts tool parameter format
func (c *Client) convertTools(tools []ai.Tool) []anthropic.ToolUnionParam {
	result := make([]anthropic.ToolUnionParam, len(tools))

	for i, tool := range tools {
		// Build InputSchema
		var properties any
		var required []string

		// Parse tool.InputSchema
		// Convert properties to map[string]any
		if len(tool.InputSchema.Properties) > 0 {
			propsMap := make(map[string]any)
			for k, v := range tool.InputSchema.Properties {
				propMap := map[string]any{
					"type":        v.Type,
					"description": v.Description,
				}
				if len(v.Enum) > 0 {
					propMap["enum"] = v.Enum
				}
				if v.Default != nil {
					propMap["default"] = v.Default
				}
				propsMap[k] = propMap
			}
			properties = propsMap
		}
		required = tool.InputSchema.Required

		inputSchema := anthropic.ToolInputSchemaParam{
			Properties: properties,
			Required:   required,
		}

		toolParam := anthropic.ToolParam{
			Name:        tool.Name,
			Description: anthropic.String(tool.Description),
			InputSchema: inputSchema,
		}
		result[i] = anthropic.ToolUnionParam{
			OfTool: &toolParam,
		}
	}

	return result
}

// extractToolCalls extracts tool calls from response
func (c *Client) extractToolCalls(message anthropic.Message) []ai.ToolCall {
	var toolCalls []ai.ToolCall

	for _, content := range message.Content {
		switch c := content.AsAny().(type) {
		case anthropic.ToolUseBlock:
			// Convert c.Input (json.RawMessage) to Arguments
			args := make(ai.Arguments)

			// First unmarshal to a map to capture all fields
			var inputMap map[string]any
			if err := json.Unmarshal(c.Input, &inputMap); err != nil {
				// If unmarshaling fails, store the raw JSON
				args["error"] = c.Input
			} else {
				// Store each field as a raw JSON value
				for k, v := range inputMap {
					if raw, err := json.Marshal(v); err == nil {
						args[k] = json.RawMessage(raw)
					}
				}
			}

			toolCall := ai.ToolCall{
				ID:   c.ID,
				Name: c.Name,
				Args: args,
			}
			toolCalls = append(toolCalls, toolCall)
		}
	}

	return toolCalls
}
