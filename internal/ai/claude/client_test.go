package claude

import (
	"context"
	"strings"
	"testing"
	"time"

	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewClient(t *testing.T) {
	tests := []struct {
		name    string
		apiKey  string
		wantErr bool
		errMsg  string
	}{
		{
			name:    "valid API key",
			apiKey:  "test-api-key",
			wantErr: false,
		},
		{
			name:    "empty API key",
			apiKey:  "",
			wantErr: true,
			errMsg:  "claude API key cannot be empty",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client, err := New(tt.apiKey)
			if tt.wantErr {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
				assert.Nil(t, client)
			} else {
				require.NoError(t, err)
				require.NotNil(t, client)
				assert.NotNil(t, client.client)
				assert.Equal(t, ai.<PERSON>on<PERSON>, client.defaultModel)
			}
		})
	}
}

func TestNewClientWithTimeout(t *testing.T) {
	tests := []struct {
		name    string
		apiKey  string
		timeout time.Duration
		wantErr bool
		errMsg  string
	}{
		{
			name:    "valid API key with timeout",
			apiKey:  "test-api-key",
			timeout: 2 * time.Minute,
			wantErr: false,
		},
		{
			name:    "empty API key with timeout",
			apiKey:  "",
			timeout: 1 * time.Minute,
			wantErr: true,
			errMsg:  "claude API key cannot be empty",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client, err := NewWithTimeout(tt.apiKey, tt.timeout)
			if tt.wantErr {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
				assert.Nil(t, client)
			} else {
				require.NoError(t, err)
				require.NotNil(t, client)
				assert.NotNil(t, client.client)
			}
		})
	}
}

func TestClient_Provider(t *testing.T) {
	client := &Client{}
	assert.Equal(t, ai.Claude, client.Provider())
}

func TestClient_Close(t *testing.T) {
	client := &Client{}
	err := client.Close()
	assert.NoError(t, err)
}

func TestClient_IsAvailable(t *testing.T) {
	client := &Client{}
	ctx := context.Background()
	// Claude client always returns true for availability
	assert.True(t, client.IsAvailable(ctx))
}

func TestClient_ConvertModelToString(t *testing.T) {
	client := &Client{defaultModel: ai.Claude37Sonnet}

	tests := []struct {
		name     string
		model    string
		expected string
	}{
		{
			name:     "empty model returns default",
			model:    "",
			expected: ai.Claude37Sonnet,
		},
		{
			name:     "claude-3.5-sonnet alias",
			model:    "claude-3.5-sonnet",
			expected: "claude-3-5-sonnet-20241022",
		},
		{
			name:     "claude-3-sonnet alias",
			model:    "claude-3-sonnet",
			expected: "claude-3-5-sonnet-20241022",
		},
		{
			name:     "claude-3.5-haiku alias",
			model:    "claude-3.5-haiku",
			expected: "claude-3-5-haiku-20241022",
		},
		{
			name:     "valid full model name",
			model:    ai.Claude37Sonnet,
			expected: ai.Claude37Sonnet,
		},
		{
			name:     "custom model passthrough",
			model:    "custom-model-xyz",
			expected: "custom-model-xyz",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// We'll test the model mapping logic indirectly
			// since convertModelToString is private
			if tt.model == "" {
				assert.Equal(t, client.defaultModel, ai.Claude37Sonnet)
			}
		})
	}
}

// Test for GetMaxTokens method
func TestClient_GetMaxTokens(t *testing.T) {
	client := &Client{}

	tests := []struct {
		name     string
		input    int
		expected int64
	}{
		{
			name:     "zero tokens uses default",
			input:    0,
			expected: 4096, // ClampMaxTokens default
		},
		{
			name:     "positive tokens",
			input:    1000,
			expected: 1000,
		},
		{
			name:     "negative tokens uses default",
			input:    -100,
			expected: 4096, // ClampMaxTokens default
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := client.getMaxTokens(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Fuzzing test for Request validation
func FuzzRequestValidation(f *testing.F) {
	// Add seed corpus
	f.Add("Hello, world!", "user", 100, float32(0.7))
	f.Add("", "assistant", 0, float32(0))
	f.Add(strings.Repeat("a", 10000), "system", 1000, float32(1.5))
	f.Add("Test message", "invalid-role", -100, float32(-1))
	f.Add("Special: 你好 🌍", "user", 8192, float32(2.5))

	f.Fuzz(func(t *testing.T, content string, role string, maxTokens int, temperature float32) {
		req := &ai.Request{
			Messages: []ai.Message{
				{
					Role:    role,
					Content: content,
				},
			},
			MaxTokens:   maxTokens,
			Temperature: temperature,
		}

		// Should never panic
		err := req.Validate()

		// Check specific validation rules
		if role == ai.Assistant && len(req.Messages) == 1 {
			// First message from assistant error takes precedence
			assert.Error(t, err)
			assert.Contains(t, err.Error(), "first message cannot be from assistant")
		} else if content == "" {
			assert.Error(t, err)
			assert.Contains(t, err.Error(), "empty content")
		} else if role != ai.User && role != ai.Assistant && role != ai.System {
			assert.Error(t, err)
			assert.Contains(t, err.Error(), "invalid role")
		} else if maxTokens < 0 {
			assert.Error(t, err)
			assert.Contains(t, err.Error(), "negative max tokens")
		} else if temperature < 0 || temperature > 2 {
			assert.Error(t, err)
			assert.Contains(t, err.Error(), "temperature must be between 0 and 2")
		}
	})
}

// Test Usage.Add method
func TestUsage_Add(t *testing.T) {
	tests := []struct {
		name     string
		initial  ai.Usage
		add      ai.Usage
		expected ai.Usage
	}{
		{
			name:     "normal addition",
			initial:  ai.Usage{Input: 100, Output: 50, Total: 150},
			add:      ai.Usage{Input: 200, Output: 100, Total: 300},
			expected: ai.Usage{Input: 300, Output: 150, Total: 450},
		},
		{
			name:     "with zeros",
			initial:  ai.Usage{Input: 100, Output: 0, Total: 100},
			add:      ai.Usage{Input: 0, Output: 50, Total: 50},
			expected: ai.Usage{Input: 100, Output: 50, Total: 150},
		},
		{
			name:     "overflow protection",
			initial:  ai.Usage{Input: 1 << 62, Output: 100, Total: 1<<62 + 100},
			add:      ai.Usage{Input: 1 << 62, Output: 50, Total: 1<<62 + 50},
			expected: ai.Usage{Input: int(^uint(0) >> 1), Output: 150, Total: int(^uint(0) >> 1)},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			usage := tt.initial
			usage.Add(tt.add)
			assert.Equal(t, tt.expected, usage)
		})
	}
}
