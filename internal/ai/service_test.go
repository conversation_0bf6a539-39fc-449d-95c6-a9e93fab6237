package ai

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestServiceOptions(t *testing.T) {
	t.Run("WithHealthCheckInterval", func(t *testing.T) {
		opt := WithHealthCheckInterval(5 * time.Minute)
		service := &Service{}
		opt(service)
		assert.Equal(t, 5*time.Minute, service.healthCheckInterval)
	})

	t.Run("WithLogger", func(t *testing.T) {
		logger := &mockLogger{}
		opt := WithLogger(logger)
		service := &Service{}
		opt(service)
		assert.Equal(t, logger, service.logger)
	})
}

func TestNewService(t *testing.T) {
	t.Run("basic service creation", func(t *testing.T) {
		clients := map[Provider]Client{
			Claude: &mockClient{},
		}

		service, err := NewService(clients, Claude)
		require.NoError(t, err)
		require.NotNil(t, service)

		assert.Equal(t, <PERSON>, service.defaultProvider)
		assert.NotNil(t, service.clients)
		assert.NotNil(t, service.providerHealth)
		// Logger is optional, so it can be nil
	})

	t.Run("empty clients", func(t *testing.T) {
		_, err := NewService(map[Provider]Client{}, Claude)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "no AI clients provided")
	})

	t.Run("with options", func(t *testing.T) {
		logger := &mockLogger{}
		clients := map[Provider]Client{
			Claude: &mockClient{},
		}

		service, err := NewService(
			clients,
			Claude,
			WithLogger(logger),
			WithHealthCheckInterval(30*time.Second),
		)
		require.NoError(t, err)

		assert.Equal(t, logger, service.logger)
		assert.Equal(t, 30*time.Second, service.healthCheckInterval)
	})

	t.Run("initializes health status", func(t *testing.T) {
		clients := map[Provider]Client{
			Claude: &mockClient{},
			Gemini: &mockClient{},
		}

		service, err := NewService(clients, Claude)
		require.NoError(t, err)

		// All providers should start as healthy
		assert.True(t, service.providerHealth[Claude])
		assert.True(t, service.providerHealth[Gemini])
	})
}

func TestService_Close(t *testing.T) {
	closeCount := 0
	mockClient := &mockClient{
		chatFunc: func(ctx context.Context, req *Request) (*Response, error) {
			return &Response{}, nil
		},
	}
	// Override Close to count calls
	closableClient := &closableTestClient{
		Client: mockClient,
		closeFunc: func() error {
			closeCount++
			return nil
		},
	}

	service := &Service{
		clients: map[Provider]Client{
			Claude: closableClient,
		},
	}

	// Service doesn't have Close method, only clients do
	for _, client := range service.clients {
		if closer, ok := client.(interface{ Close() error }); ok {
			closer.Close()
		}
	}
	assert.Equal(t, 1, closeCount)
}

func TestGetAvailableProvider(t *testing.T) {
	t.Run("preferred provider is healthy", func(t *testing.T) {
		service := &Service{
			providerHealth: map[Provider]bool{
				Claude: true,
				Gemini: true,
			},
		}

		provider := service.getAvailableProvider(Claude)
		assert.Equal(t, Claude, provider)
	})

	t.Run("preferred provider is unhealthy", func(t *testing.T) {
		service := &Service{
			providerHealth: map[Provider]bool{
				Claude: false,
				Gemini: true,
			},
		}

		provider := service.getAvailableProvider(Claude)
		assert.Equal(t, Gemini, provider)
	})

	t.Run("no healthy providers", func(t *testing.T) {
		service := &Service{
			providerHealth: map[Provider]bool{
				Claude: false,
				Gemini: false,
			},
		}

		provider := service.getAvailableProvider(Claude)
		assert.Equal(t, Provider(""), provider)
	})
}

func TestGetFallbackProvider(t *testing.T) {
	t.Run("finds alternative provider", func(t *testing.T) {
		service := &Service{
			clients: map[Provider]Client{
				Claude: &mockClient{},
				Gemini: &mockClient{},
			},
			providerHealth: map[Provider]bool{
				Claude: true,
				Gemini: true,
			},
		}

		provider := service.getFallbackProvider(Claude)
		assert.Equal(t, Gemini, provider)
	})

	t.Run("no healthy alternative", func(t *testing.T) {
		service := &Service{
			clients: map[Provider]Client{
				Claude: &mockClient{},
				Gemini: &mockClient{},
			},
			providerHealth: map[Provider]bool{
				Claude: true,
				Gemini: false,
			},
		}

		provider := service.getFallbackProvider(Claude)
		assert.Equal(t, Provider(""), provider)
	})
}

func TestAdjustModelForProvider(t *testing.T) {
	service := &Service{}

	tests := []struct {
		name     string
		provider Provider
		model    string
		expected string
	}{
		{
			name:     "claude model for claude provider",
			provider: Claude,
			model:    Claude35Sonnet,
			expected: Claude35Sonnet,
		},
		{
			name:     "gemini model for claude provider",
			provider: Claude,
			model:    Gemini15Pro,
			expected: Claude35Sonnet,
		},
		{
			name:     "claude model for gemini provider",
			provider: Gemini,
			model:    Claude35Sonnet,
			expected: Gemini15Flash,
		},
		{
			name:     "gemini model for gemini provider",
			provider: Gemini,
			model:    Gemini15Pro,
			expected: Gemini15Pro,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.adjustModelForProvider(tt.provider, tt.model)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// closableTestClient wraps a client with a custom close function
type closableTestClient struct {
	Client
	closeFunc func() error
}

func (c *closableTestClient) Close() error {
	if c.closeFunc != nil {
		return c.closeFunc()
	}
	return nil
}

// Benchmark tests for Service

// BenchmarkServiceChat benchmarks the service chat method with failover
func BenchmarkServiceChat(b *testing.B) {
	ctx := context.Background()

	clients := map[Provider]Client{
		"primary": &mockClient{chatFunc: func(ctx context.Context, req *Request) (*Response, error) {
			return &Response{Content: "test response", Model: req.Model}, nil
		}},
		"secondary": &mockClient{chatFunc: func(ctx context.Context, req *Request) (*Response, error) {
			return &Response{Content: "backup response", Model: req.Model}, nil
		}},
	}

	service, err := NewService(clients, "primary")
	if err != nil {
		b.Fatalf("failed to create service: %v", err)
	}
	defer service.Close()

	req := &Request{
		Messages: []Message{
			{Role: User, Content: "Benchmark test message"},
		},
		MaxTokens: 100,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := service.Chat(ctx, req)
		if err != nil {
			b.Fatalf("chat failed: %v", err)
		}
	}
}

// BenchmarkServiceStream benchmarks streaming responses
func BenchmarkServiceStream(b *testing.B) {
	ctx := context.Background()

	ch := make(chan Stream, 10)
	go func() {
		defer close(ch)
		words := []string{"This", "is", "a", "test", "stream"}
		for i, word := range words {
			ch <- Stream{
				Delta: word + " ",
				Done:  i == len(words)-1,
			}
		}
	}()

	clients := map[Provider]Client{
		"streaming": &mockClient{
			streamFunc: func(ctx context.Context, req *Request) (<-chan Stream, error) {
				return ch, nil
			},
		},
	}

	service, err := NewService(clients, "streaming")
	if err != nil {
		b.Fatalf("failed to create service: %v", err)
	}
	defer service.Close()

	req := &Request{
		Messages: []Message{
			{Role: User, Content: "Stream benchmark test"},
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		ch, err := service.Stream(ctx, req)
		if err != nil {
			b.Fatalf("stream failed: %v", err)
		}

		// Consume the stream
		for range ch {
			// Just drain the channel
		}
	}
}

// BenchmarkProviderSelection benchmarks provider selection logic
func BenchmarkProviderSelection(b *testing.B) {
	clients := make(map[Provider]Client)
	for i := 0; i < 10; i++ {
		provider := Provider(string(rune('A' + i)))
		clients[provider] = &mockClient{}
	}

	service, err := NewService(clients, "A")
	if err != nil {
		b.Fatalf("failed to create service: %v", err)
	}
	defer service.Close()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		provider := service.getAvailableProvider(service.defaultProvider)
		if provider == "" {
			b.Fatal("no provider available")
		}
	}
}

// BenchmarkHealthCheck benchmarks health check operations
func BenchmarkHealthCheck(b *testing.B) {
	service := &Service{
		clients: map[Provider]Client{
			Claude: &mockClient{},
			Gemini: &mockClient{},
		},
		defaultProvider: Claude,
		providerHealth:  make(map[Provider]bool),
		healthStatus:    make(map[Provider]*providerHealthStatus),
	}

	service.initHealthCheck()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// Alternate between marking healthy and unhealthy
		if i%2 == 0 {
			service.markProviderHealthy(Claude)
		} else {
			service.markProviderUnhealthy(Claude)
		}
	}
}
