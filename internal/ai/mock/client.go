// Package mock provides mock implementations for testing
package mock

import (
	"context"

	"github.com/koopa0/assistant-go/internal/ai"
)

// Client is a mock implementation of ai.Client for testing
type Client struct {
	// Function fields allow tests to control behavior
	ChatFunc        func(ctx context.Context, req *ai.Request) (*ai.Response, error)
	StreamFunc      func(ctx context.Context, req *ai.Request) (<-chan ai.Stream, error)
	EmbedFunc       func(ctx context.Context, req *ai.EmbedRequest) (*ai.EmbedResponse, error)
	IsAvailableFunc func(ctx context.Context) bool

	// Provider to return
	ProviderValue ai.Provider

	// Track calls for assertions
	Calls []CallInfo
}

// CallInfo tracks method calls for test assertions
type CallInfo struct {
	Method string
	Args   []any
}

// Chat implements ai.Client
func (c *Client) Chat(ctx context.Context, req *ai.Request) (*ai.Response, error) {
	c.Calls = append(c.Calls, CallInfo{Method: "Chat", Args: []any{req}})

	if c.ChatFunc != nil {
		return c.ChatFunc(ctx, req)
	}

	// Default response
	return &ai.Response{
		Content:  "mock response",
		Model:    req.Model,
		Provider: c.ProviderValue,
		Usage: ai.Usage{
			Input:  10,
			Output: 5,
			Total:  15,
		},
	}, nil
}

// Stream implements ai.Client
func (c *Client) Stream(ctx context.Context, req *ai.Request) (<-chan ai.Stream, error) {
	c.Calls = append(c.Calls, CallInfo{Method: "Stream", Args: []any{req}})

	if c.StreamFunc != nil {
		return c.StreamFunc(ctx, req)
	}

	// Default streaming response
	ch := make(chan ai.Stream, 2)
	go func() {
		defer close(ch)
		ch <- ai.Stream{Delta: "mock ", Done: false}
		ch <- ai.Stream{Delta: "stream", Done: true}
	}()

	return ch, nil
}

// Embed implements ai.Embedding
func (c *Client) Embed(ctx context.Context, req *ai.EmbedRequest) (*ai.EmbedResponse, error) {
	c.Calls = append(c.Calls, CallInfo{Method: "Embed", Args: []any{req}})

	if c.EmbedFunc != nil {
		return c.EmbedFunc(ctx, req)
	}

	// Default embedding response
	return &ai.EmbedResponse{
		Embedding: []float32{0.1, 0.2, 0.3},
		Model:     "mock-embedding",
		Usage: ai.Usage{
			Input:  len(req.Text),
			Output: 0,
			Total:  len(req.Text),
		},
	}, nil
}

// Provider implements ai.Client
func (c *Client) Provider() ai.Provider {
	if c.ProviderValue != "" {
		return c.ProviderValue
	}
	return "mock"
}

// IsAvailable implements ai.Client
func (c *Client) IsAvailable(ctx context.Context) bool {
	c.Calls = append(c.Calls, CallInfo{Method: "IsAvailable", Args: nil})

	if c.IsAvailableFunc != nil {
		return c.IsAvailableFunc(ctx)
	}

	return true
}

// Close implements ai.Client
func (c *Client) Close() error {
	c.Calls = append(c.Calls, CallInfo{Method: "Close", Args: nil})
	return nil
}

// Reset clears recorded calls
func (c *Client) Reset() {
	c.Calls = nil
}

// NewClient creates a new mock client with default behavior
func NewClient() *Client {
	return &Client{
		ProviderValue: "mock",
	}
}
