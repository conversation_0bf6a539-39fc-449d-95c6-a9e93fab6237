package ai

import (
	"context"
	"testing"
)

// TestRateLimitedClient_Embedding tests that rate-limited clients preserve embedding capabilities
func TestRateLimitedClient_Embedding(t *testing.T) {
	tests := []struct {
		name         string
		client       Client
		wantEmbedErr bool
		checkEmbed   bool
	}{
		{
			name: "client with embedding support",
			client: &mockClient{
				embeddingFunc: func(ctx context.Context, req *EmbedRequest) (*EmbedResponse, error) {
					return &EmbedResponse{
						Embedding: []float32{0.1, 0.2, 0.3},
						Model:     "test-model",
						Usage:     Usage{Input: 10, Output: 0, Total: 10},
					}, nil
				},
			},
			wantEmbedErr: false,
			checkEmbed:   true,
		},
		{
			name:         "client without embedding support",
			client:       &mockClientWithoutEmbedding{},
			wantEmbedErr: true,
			checkEmbed:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create rate limiter
			limiter := NewRateLimiter(10, 60) // 10 requests per minute

			// Wrap client with rate limiting
			rateLimited := NewRateLimitedClient(tt.client, limiter)

			// Check if the rate-limited client implements Embedding
			embeddingClient, ok := rateLimited.(Embedding)
			if !ok {
				t.Fatal("rate-limited client should always implement Embedding")
			}

			// Test the embedding method
			if tt.checkEmbed {
				resp, err := embeddingClient.Embed(context.Background(), &EmbedRequest{
					Text: "test text",
				})

				if tt.wantEmbedErr {
					if err == nil {
						t.Error("expected error for client without embedding support")
					}
				} else {
					if err != nil {
						t.Errorf("unexpected error: %v", err)
					}
					if resp == nil {
						t.Error("expected non-nil response")
					}
				}
			}
		})
	}
}

// mockClientWithoutEmbedding is a mock that only implements Client interface
type mockClientWithoutEmbedding struct{}

func (m *mockClientWithoutEmbedding) Chat(ctx context.Context, req *Request) (*Response, error) {
	return &Response{Content: "test"}, nil
}

func (m *mockClientWithoutEmbedding) Stream(ctx context.Context, req *Request) (<-chan Stream, error) {
	ch := make(chan Stream, 1)
	ch <- Stream{Delta: "test", Done: true}
	close(ch)
	return ch, nil
}

func (m *mockClientWithoutEmbedding) Provider() Provider {
	return Provider("mock")
}

func (m *mockClientWithoutEmbedding) IsAvailable(ctx context.Context) bool {
	return true
}

func (m *mockClientWithoutEmbedding) Close() error {
	return nil
}
