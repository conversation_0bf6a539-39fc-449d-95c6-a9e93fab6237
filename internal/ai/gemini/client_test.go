package gemini

import (
	"context"
	"strings"
	"testing"

	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewClient(t *testing.T) {
	tests := []struct {
		name    string
		apiKey  string
		wantErr bool
		errMsg  string
	}{
		{
			name:    "empty API key",
			apiKey:  "",
			wantErr: true,
			errMsg:  "gemini API key cannot be empty",
		},
		// Note: Can't test valid API key without actual connection
		// This would require mocking the genai.NewClient function
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			client, err := New(ctx, tt.apiKey)
			if tt.wantErr {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
				assert.Nil(t, client)
			} else {
				// Can't test success case without mock
				t.Skip("Requires API key for integration test")
			}
		})
	}
}

func TestClient_Provider(t *testing.T) {
	client := &Client{}
	assert.Equal(t, ai.Gemini, client.Provider())
}

func TestClient_Close(t *testing.T) {
	client := &Client{}
	err := client.Close()
	assert.NoError(t, err)
}

func TestClient_IsAvailable(t *testing.T) {
	client := &Client{}
	ctx := context.Background()
	// Without actual client, it should return false
	assert.False(t, client.IsAvailable(ctx))
}

// Test model validation via the default model
func TestClient_DefaultModel(t *testing.T) {
	// Test that NewClient sets the correct default model
	// We can't directly test validateModel as it's private
	client := &Client{defaultModel: ai.Gemini15Pro}
	assert.Equal(t, ai.Gemini15Pro, client.defaultModel)
}

// Fuzzing test for message content handling
func FuzzMessageContent(f *testing.F) {
	// Add seed corpus
	f.Add("Hello world", "user")
	f.Add("", "assistant")
	f.Add(strings.Repeat("test ", 500), "user")
	f.Add("Special: 你好 🌍 \n\t\r", "assistant")
	f.Add("Multiple\n\nNewlines\n\nHere", "user")

	f.Fuzz(func(t *testing.T, content string, role string) {
		// Map role string to ai constants
		var aiRole string
		switch role {
		case "assistant":
			aiRole = ai.Assistant
		case "system":
			aiRole = ai.System
		default:
			aiRole = ai.User
		}

		msg := ai.Message{
			Role:    aiRole,
			Content: content,
		}

		// Test that message can be created without panic
		assert.NotNil(t, msg)
		assert.Equal(t, aiRole, msg.Role)
		assert.Equal(t, content, msg.Content)
	})
}

// Test AI Request validation
func TestRequest_Validate(t *testing.T) {
	tests := []struct {
		name    string
		request *ai.Request
		wantErr bool
		errMsg  string
	}{
		{
			name: "valid request",
			request: &ai.Request{
				Messages: []ai.Message{
					{Role: ai.User, Content: "Hello"},
				},
				MaxTokens:   100,
				Temperature: 0.7,
			},
			wantErr: false,
		},
		{
			name: "empty messages",
			request: &ai.Request{
				Messages: []ai.Message{},
			},
			wantErr: true,
			errMsg:  "empty messages",
		},
		{
			name: "first message from assistant",
			request: &ai.Request{
				Messages: []ai.Message{
					{Role: ai.Assistant, Content: "Hello"},
				},
			},
			wantErr: true,
			errMsg:  "first message cannot be from assistant",
		},
		{
			name: "empty content",
			request: &ai.Request{
				Messages: []ai.Message{
					{Role: ai.User, Content: ""},
				},
			},
			wantErr: true,
			errMsg:  "empty content",
		},
		{
			name: "invalid role",
			request: &ai.Request{
				Messages: []ai.Message{
					{Role: "invalid", Content: "test"},
				},
			},
			wantErr: true,
			errMsg:  "invalid role",
		},
		{
			name: "negative max tokens",
			request: &ai.Request{
				Messages: []ai.Message{
					{Role: ai.User, Content: "test"},
				},
				MaxTokens: -100,
			},
			wantErr: true,
			errMsg:  "negative max tokens",
		},
		{
			name: "temperature too high",
			request: &ai.Request{
				Messages: []ai.Message{
					{Role: ai.User, Content: "test"},
				},
				Temperature: 2.5,
			},
			wantErr: true,
			errMsg:  "temperature must be between 0 and 2",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.request.Validate()
			if tt.wantErr {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

// Test tool schema construction
func TestToolSchema(t *testing.T) {
	tool := ai.Tool{
		Name:        "search",
		Description: "Search for information",
		InputSchema: ai.Schema{
			Type: "object",
			Properties: map[string]ai.Property{
				"query": {
					Type:        "string",
					Description: "Search query",
				},
			},
			Required: []string{"query"},
		},
	}

	assert.Equal(t, "search", tool.Name)
	assert.Equal(t, "Search for information", tool.Description)
	assert.Equal(t, "object", tool.InputSchema.Type)
	assert.Contains(t, tool.InputSchema.Properties, "query")
	assert.Contains(t, tool.InputSchema.Required, "query")
}
