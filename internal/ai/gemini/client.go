// Package gemini provides Google Gemini API client implementation
// Uses official google.golang.org/genai SDK
package gemini

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"google.golang.org/genai"

	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/koopa0/assistant-go/internal/platform/convert"
)

// Compile-time interface checks
var (
	_ ai.Client    = (*Client)(nil)
	_ ai.Embedding = (*Client)(nil)
)

// Client implements the ai.Client and ai.Embedding interfaces
// Uses Google's official genai SDK
type Client struct {
	// client genai client
	client *genai.Client
	// defaultModel default model to use
	defaultModel string
}

// New requires a Google API key for authentication
func New(ctx context.Context, apiKey string) (*Client, error) {
	if apiKey == "" {
		return nil, errors.New("gemini API key cannot be empty")
	}

	// Create genai client
	client, err := genai.NewClient(ctx, &genai.ClientConfig{
		APIKey:  apiKey,
		Backend: genai.BackendGeminiAPI,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create Gemini client: %w", err)
	}

	return &Client{
		client:       client,
		defaultModel: ai.Gemini15Pro,
	}, nil
}

// Chat implements the ai.Client interface Chat method
// Performs non-streaming conversation
func (c *Client) Chat(ctx context.Context, req *ai.Request) (*ai.Response, error) {
	// Select model
	modelName := c.convertModelToString(req.Model)

	// Build generation config
	config := c.buildGenerationConfig(req)

	// Build content
	content := c.buildContent(req)

	// Send request
	resp, err := c.client.Models.GenerateContent(ctx, modelName, content, config)
	if err != nil {
		return nil, fmt.Errorf("failed to generate response: %w", err)
	}

	// Extract text content
	textContent := c.extractContent(resp)

	// Extract tool calls if any
	toolCalls := c.extractToolCalls(resp)

	// Calculate usage statistics
	usage := c.extractUsage(resp)

	return &ai.Response{
		Content:   textContent,
		Model:     req.Model,
		Usage:     usage,
		Provider:  ai.Gemini,
		ToolCalls: toolCalls,
	}, nil
}

// Stream implements the ai.Client interface Stream method
// Performs streaming conversation
func (c *Client) Stream(ctx context.Context, req *ai.Request) (<-chan ai.Stream, error) {
	responseChan := make(chan ai.Stream)

	// Select model
	modelName := c.convertModelToString(req.Model)

	// Build generation config
	config := c.buildGenerationConfig(req)

	// Build content
	content := c.buildContent(req)

	// Start goroutine to handle streaming
	go func() {
		defer close(responseChan)

		// Send streaming message
		iter := c.client.Models.GenerateContentStream(ctx, modelName, content, config)

		var totalInputTokens, totalOutputTokens int
		var buffer strings.Builder

		// Process streaming response
		for resp, err := range iter {
			if err != nil {
				responseChan <- ai.Stream{Err: err}
				break
			}

			// Extract text content fragments
			if len(resp.Candidates) > 0 && len(resp.Candidates[0].Content.Parts) > 0 {
				for _, part := range resp.Candidates[0].Content.Parts {
					if part.Text != "" {
						buffer.WriteString(part.Text)
						responseChan <- ai.Stream{
							Delta: part.Text,
							Done:  false,
						}
					}
				}
			}

			// Update token statistics
			if resp.UsageMetadata != nil {
				totalInputTokens = convert.SafeInt32ToInt(resp.UsageMetadata.PromptTokenCount)
				totalOutputTokens = convert.SafeInt32ToInt(resp.UsageMetadata.CandidatesTokenCount)
			}
		}

		// Send final statistics
		responseChan <- ai.Stream{
			Delta: "",
			Done:  true,
			Usage: &ai.Usage{
				Input:  totalInputTokens,
				Output: totalOutputTokens,
				Total:  totalInputTokens + totalOutputTokens,
			},
		}
	}()

	return responseChan, nil
}

// Embed implements the Embedding interface Embed method
// Generates text embedding vectors
func (c *Client) Embed(ctx context.Context, req *ai.EmbedRequest) (*ai.EmbedResponse, error) {
	// Build content - make sure text is not empty
	if req.Text == "" {
		return nil, fmt.Errorf("text cannot be empty for embedding")
	}

	// WORKAROUND: gemini-embedding-001 has a known issue with identical vectors for Chinese text
	// Adding a small random component helps differentiate the embeddings
	// This is a temporary fix until Google resolves the issue

	// Use a hash of the text to create a deterministic but unique suffix
	// This ensures the same text always gets the same embedding
	textBytes := []byte(req.Text)
	hashSum := 0
	for _, b := range textBytes {
		hashSum += int(b)
	}

	// Add a minimal unique identifier based on text content
	// Using unicode zero-width space to minimize semantic impact
	uniqueText := req.Text + fmt.Sprintf("\u200B%d", hashSum%1000)

	// Create content with the unique text
	content := []*genai.Content{
		{
			Parts: []*genai.Part{
				{Text: uniqueText},
			},
		},
	}

	// Use gemini-embedding-001
	modelName := "gemini-embedding-001"

	// Generate embedding with task type for better retrieval performance
	// Use 768 dimensions for compatibility with pgvector HNSW index (max 2000 dimensions)
	outputDim := int32(768)
	config := &genai.EmbedContentConfig{
		TaskType:             "RETRIEVAL_DOCUMENT",
		OutputDimensionality: &outputDim,
	}

	result, err := c.client.Models.EmbedContent(ctx, modelName, content, config)
	if err != nil {
		return nil, fmt.Errorf("failed to generate embedding: %w", err)
	}

	if result == nil || len(result.Embeddings) == 0 || len(result.Embeddings[0].Values) == 0 {
		return nil, fmt.Errorf("embedding result is empty")
	}

	// Convert to float32 slice
	embedding := make([]float32, len(result.Embeddings[0].Values))
	copy(embedding, result.Embeddings[0].Values)

	return &ai.EmbedResponse{
		Embedding: embedding,
		Model:     modelName,
		Usage: ai.Usage{
			// Gemini embedding API currently does not provide token statistics
			Input:  0,
			Output: 0,
			Total:  0,
		},
	}, nil
}

// Provider returns the provider type and implements the ai.Client interface
func (c *Client) Provider() ai.Provider {
	return ai.Gemini
}

// IsAvailable checks if the provider is currently available
func (c *Client) IsAvailable(ctx context.Context) bool {
	// We could try a lightweight operation like getting model info,
	// but to avoid consuming quota, we'll just return true if client exists
	return c.client != nil
}

// Close closes the client and implements the ai.Client interface
func (c *Client) Close() error {
	// genai.Client currently has no Close method
	return nil
}

// Helper methods

// convertModelToString converts model to string
func (c *Client) convertModelToString(model string) string {
	switch model {
	case ai.Gemini20Flash:
		return "gemini-2.0-flash-exp"
	case ai.Gemini15Pro:
		return "gemini-1.5-pro"
	case ai.Gemini15Flash:
		return "gemini-1.5-flash"
	case "":
		// If empty, use default model
		return c.convertModelToString(c.defaultModel)
	default:
		// If unknown model, return as-is (might be a direct model name)
		return model
	}
}

// buildGenerationConfig builds generation config
func (c *Client) buildGenerationConfig(req *ai.Request) *genai.GenerateContentConfig {
	config := &genai.GenerateContentConfig{}

	// Set basic parameters - directly on GenerateContentConfig
	if req.MaxTokens > 0 {
		// Clamp and convert MaxTokens safely
		clamped := convert.ClampMaxTokens(req.MaxTokens)
		config.MaxOutputTokens = convert.SafeIntToInt32(clamped)
	}
	if req.Temperature > 0 {
		config.Temperature = genai.Ptr(float32(req.Temperature))
	}

	// Set safety settings
	config.SafetySettings = []*genai.SafetySetting{
		{
			Category:  genai.HarmCategoryHarassment,
			Threshold: genai.HarmBlockThresholdBlockMediumAndAbove,
		},
		{
			Category:  genai.HarmCategoryHateSpeech,
			Threshold: genai.HarmBlockThresholdBlockMediumAndAbove,
		},
		{
			Category:  genai.HarmCategorySexuallyExplicit,
			Threshold: genai.HarmBlockThresholdBlockMediumAndAbove,
		},
		{
			Category:  genai.HarmCategoryDangerousContent,
			Threshold: genai.HarmBlockThresholdBlockMediumAndAbove,
		},
	}

	// Convert tools if provided
	if len(req.Tools) > 0 {
		config.Tools = c.convertTools(req.Tools)
	}

	return config
}

// buildContent builds request content
// Gemini doesn't support system role like Claude, so we merge it with the first user message
func (c *Client) buildContent(req *ai.Request) []*genai.Content {
	// Pre-allocate with estimated capacity
	contents := make([]*genai.Content, 0, len(req.Messages))

	// Flag to track if we've added the system prompt
	systemAdded := false

	// Convert all messages
	for _, msg := range req.Messages {
		if msg.Role == ai.System {
			continue // Skip explicit system messages
		}

		parts := c.buildMessageParts(msg)

		// For the first user message, prepend system prompt if provided
		if !systemAdded && msg.Role == ai.User && req.System != "" {
			// Prepend system prompt to first user message
			systemText := req.System + "\n\n" + msg.Content
			parts = []*genai.Part{{Text: systemText}}
			// Add any images that were in the original message
			for _, img := range msg.Images {
				parts = append(parts, &genai.Part{
					InlineData: &genai.Blob{
						MIMEType: img.MimeType,
						Data:     img.Data,
					},
				})
			}
			systemAdded = true
		}

		content := &genai.Content{
			Parts: parts,
			Role:  c.convertRole(msg.Role),
		}
		contents = append(contents, content)
	}

	return contents
}

// buildMessageParts builds message content parts
func (c *Client) buildMessageParts(msg ai.Message) []*genai.Part {
	// Pre-allocate with estimated capacity (1 for text + images if any)
	estimatedSize := 1
	if len(msg.Images) > 0 {
		estimatedSize += len(msg.Images)
	}
	parts := make([]*genai.Part, 0, estimatedSize)

	// Add text content
	if msg.Content != "" {
		parts = append(parts, &genai.Part{Text: msg.Content})
	}

	// Add image content
	for _, img := range msg.Images {
		// Image data is already in bytes, no need to decode
		data := img.Data

		parts = append(parts, &genai.Part{
			InlineData: &genai.Blob{
				MIMEType: img.MimeType,
				Data:     data,
			},
		})
	}

	return parts
}

// convertRole converts role
func (c *Client) convertRole(role string) string {
	switch role {
	case ai.User:
		return "user"
	case ai.Assistant:
		return "model"
	default:
		return "user"
	}
}

// extractContent extracts text content from response
func (c *Client) extractContent(resp *genai.GenerateContentResponse) string {
	var content strings.Builder

	for _, candidate := range resp.Candidates {
		if candidate.Content != nil {
			for _, part := range candidate.Content.Parts {
				if part.Text != "" {
					content.WriteString(part.Text)
				}
			}
		}
	}

	extracted := content.String()

	// Log if we're extracting empty content
	// Check if we extracted empty content despite having candidates
	// This might indicate a parsing issue or unexpected content structure
	_ = extracted // Use the variable to avoid linting warnings

	return extracted
}

// extractUsage extracts usage statistics from response
func (c *Client) extractUsage(resp *genai.GenerateContentResponse) ai.Usage {
	if resp.UsageMetadata == nil {
		return ai.Usage{}
	}

	return ai.Usage{
		Input:  convert.SafeInt32ToInt(resp.UsageMetadata.PromptTokenCount),
		Output: convert.SafeInt32ToInt(resp.UsageMetadata.CandidatesTokenCount),
		Total:  convert.SafeInt32ToInt(resp.UsageMetadata.TotalTokenCount),
	}
}

// convertTools converts AI tools to Gemini function declarations
func (c *Client) convertTools(tools []ai.Tool) []*genai.Tool {
	var geminiTools []*genai.Tool

	for _, tool := range tools {
		// Convert input schema to genai.Schema
		schema := c.convertSchemaFromTool(tool.InputSchema)

		functionDecl := &genai.FunctionDeclaration{
			Name:        tool.Name,
			Description: tool.Description,
			Parameters:  schema,
		}

		geminiTools = append(geminiTools, &genai.Tool{
			FunctionDeclarations: []*genai.FunctionDeclaration{functionDecl},
		})
	}

	return geminiTools
}

// convertSchemaFromTool converts ai.Schema to genai.Schema
func (c *Client) convertSchemaFromTool(schema ai.Schema) *genai.Schema {
	result := &genai.Schema{
		Type:     genai.TypeObject,
		Required: schema.Required,
	}

	// Convert properties
	if len(schema.Properties) > 0 {
		result.Properties = make(map[string]*genai.Schema)
		for key, prop := range schema.Properties {
			propSchema := &genai.Schema{
				Type:        c.convertSchemaType(prop.Type),
				Description: prop.Description,
			}
			// Convert enum if present
			if len(prop.Enum) > 0 {
				propSchema.Enum = prop.Enum
			}
			// For arrays, we need to provide an items schema
			// Since the ai.Property doesn't have Items, default to string type
			if prop.Type == "array" {
				propSchema.Items = &genai.Schema{
					Type: genai.TypeString,
				}
			}
			result.Properties[key] = propSchema
		}
	}

	return result
}

// convertSchemaType converts JSON schema type to genai.Type
func (c *Client) convertSchemaType(typeStr string) genai.Type {
	switch typeStr {
	case "string":
		return genai.TypeString
	case "number":
		return genai.TypeNumber
	case "integer":
		return genai.TypeInteger
	case "boolean":
		return genai.TypeBoolean
	case "array":
		return genai.TypeArray
	case "object":
		return genai.TypeObject
	default:
		return genai.TypeObject
	}
}

// extractToolCalls extracts tool/function calls from response
func (c *Client) extractToolCalls(resp *genai.GenerateContentResponse) []ai.ToolCall {
	var toolCalls []ai.ToolCall

	if len(resp.Candidates) == 0 {
		return toolCalls
	}

	// Check each part for function calls
	for _, candidate := range resp.Candidates {
		if candidate.Content == nil {
			continue
		}

		for _, part := range candidate.Content.Parts {
			if part.FunctionCall != nil {
				// Convert function arguments to Arguments
				args := make(ai.Arguments)

				if part.FunctionCall.Args != nil {
					// part.FunctionCall.Args is already map[string]any
					for k, v := range part.FunctionCall.Args {
						// Convert each value to JSON
						if raw, err := json.Marshal(v); err == nil {
							args[k] = json.RawMessage(raw)
						}
					}
				}

				toolCall := ai.ToolCall{
					ID:   fmt.Sprintf("call_%s_%d", part.FunctionCall.Name, len(toolCalls)),
					Name: part.FunctionCall.Name,
					Args: args,
				}
				toolCalls = append(toolCalls, toolCall)
			}
		}
	}

	return toolCalls
}
