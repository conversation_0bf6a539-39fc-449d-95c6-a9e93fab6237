// Package gemini provides health check optimizations
package gemini

// NOTE: IsAvailable method is implemented in client.go to avoid duplication

// UpdateHealthStatus updates health status (called after actual requests)
func (c *Client) UpdateHealthStatus(healthy bool) {
	// This method is kept for interface compatibility
	// but the implementation has been removed as the variables were unused
	_ = healthy
}
