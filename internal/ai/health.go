package ai

import (
	"context"
	"strings"
	"time"
)

// Provider health management
type providerHealthStatus struct {
	healthy   bool
	lastCheck time.Time
	failures  int
}

// Health check configuration
const (
	healthCheckInterval = 30 * time.Second
	maxFailures         = 3
	recoveryTime        = 60 * time.Second
)

// initHealthCheck initializes provider health monitoring
func (s *Service) initHealthCheck() {
	s.providerHealth = make(map[Provider]bool)
	s.healthStatus = make(map[Provider]*providerHealthStatus)

	// Initialize all providers as healthy
	s.mu.RLock()
	for provider := range s.clients {
		s.providerHealth[provider] = true
		s.healthStatus[provider] = &providerHealthStatus{
			healthy:   true,
			lastCheck: time.Now(),
			failures:  0,
		}
	}
	s.mu.RUnlock()
}

// startHealthCheck starts background health monitoring
func (s *Service) startHealthCheck(ctx context.Context) {
	ticker := time.NewTicker(healthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			s.checkAllProviders()
		}
	}
}

// checkAllProviders checks health of all providers
func (s *Service) checkAllProviders() {
	s.mu.RLock()
	providers := make([]Provider, 0, len(s.clients))
	for provider := range s.clients {
		providers = append(providers, provider)
	}
	s.mu.RUnlock()

	for _, provider := range providers {
		s.checkProviderHealth(provider)
	}
}

// checkProviderHealth checks a single provider's health
func (s *Service) checkProviderHealth(provider Provider) {
	s.healthMu.Lock()
	status, exists := s.healthStatus[provider]
	if !exists {
		status = &providerHealthStatus{
			healthy:   true,
			lastCheck: time.Now(),
			failures:  0,
		}
		s.healthStatus[provider] = status
	}
	s.healthMu.Unlock()

	// If provider was unhealthy, check if recovery time has passed
	if !status.healthy && time.Since(status.lastCheck) > recoveryTime {
		s.markProviderHealthy(provider)
		if s.logger != nil {
			s.logger.Info("Provider recovered",
				"provider", provider,
				"recovery_time", time.Since(status.lastCheck))
		}
	}
}

// isProviderHealthy checks if a provider is healthy
func (s *Service) isProviderHealthy(provider Provider) bool {
	s.healthMu.RLock()
	defer s.healthMu.RUnlock()

	// If no health status exists, assume healthy
	status, exists := s.healthStatus[provider]
	if !exists {
		return true
	}

	// Check if recovery time has passed for unhealthy providers
	if !status.healthy && time.Since(status.lastCheck) > recoveryTime {
		// Automatically try to recover
		go s.markProviderHealthy(provider)
		return true
	}

	return status.healthy
}

// markProviderUnhealthy marks a provider as unhealthy
func (s *Service) markProviderUnhealthy(provider Provider) {
	s.healthMu.Lock()
	defer s.healthMu.Unlock()

	status, exists := s.healthStatus[provider]
	if !exists {
		status = &providerHealthStatus{}
		s.healthStatus[provider] = status
	}

	status.failures++
	status.lastCheck = time.Now()

	if status.failures >= maxFailures {
		status.healthy = false
		s.providerHealth[provider] = false

		if s.logger != nil {
			s.logger.Error("Provider marked unhealthy",
				"provider", provider,
				"failures", status.failures)
		}
	}
}

// markProviderHealthy marks a provider as healthy
func (s *Service) markProviderHealthy(provider Provider) {
	s.healthMu.Lock()
	defer s.healthMu.Unlock()

	status, exists := s.healthStatus[provider]
	if !exists {
		status = &providerHealthStatus{}
		s.healthStatus[provider] = status
	}

	status.healthy = true
	status.failures = 0
	status.lastCheck = time.Now()
	s.providerHealth[provider] = true

	if s.logger != nil {
		s.logger.Info("Provider marked healthy", "provider", provider)
	}
}

// isConnectionError checks if an error is a connection error
func (s *Service) isConnectionError(err error) bool {
	if err == nil {
		return false
	}

	errStr := strings.ToLower(err.Error())
	connectionErrors := []string{
		"connection refused",
		"connection reset",
		"no such host",
		"timeout",
		"deadline exceeded",
		"eof",
		"broken pipe",
		"network is unreachable",
		"no route to host",
	}

	for _, pattern := range connectionErrors {
		if strings.Contains(errStr, pattern) {
			return true
		}
	}

	return false
}
