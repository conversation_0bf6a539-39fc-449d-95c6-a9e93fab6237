# SearXNG settings for assistant-go
# See: https://docs.searxng.org/admin/settings/settings.html

use_default_settings: true

general:
  debug: false
  instance_name: "Assistant SearXNG"

search:
  safe_search: 1
  autocomplete: 'duckduckgo'
  formats:
    - html
    - json

server:
  # Generate a secure secret key in production
  secret_key: "ultrasecretkey-change-this-in-production"
  limiter: false  # Disable rate limiting for internal use
  image_proxy: true

outgoing:
  request_timeout: 10.0
  useragent_suffix: "assistant-go"