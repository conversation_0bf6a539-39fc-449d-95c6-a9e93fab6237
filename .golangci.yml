# Comprehensive golangci-lint configuration with best practices
# This config requires golangci-lint v1.60.0 or later

run:
  timeout: 5m
  tests: true
  issues-exit-code: 1

linters:
  disable-all: true  # Start from scratch
  enable:
    # CRITICAL - Must fix
    - errcheck      # Check for unchecked errors
    - gosimple      # Simplify code  
    - govet         # Go vet examines Go source code
    - ineffassign   # Detects ineffectual assignments
    - staticcheck   # Staticcheck
    - typecheck     # Type-check Go code
    - gosec         # Security problems
    
    # IMPORTANT - Should fix
    - bodyclose     # Checks whether HTTP response bodies are closed
    - errorlint     # Find code that will cause problems with error wrapping
    - gofmt         # Check whether code was gofmt-ed
    - goimports     # Check import statements are formatted
    - unconvert     # Remove unnecessary type conversions
    - copyloopvar   # Checks for pointers to enclosing loop variables (replacement for exportloopref)
    - sqlclosecheck # Checks that sql.Rows and sql.Stmt are closed
    
    # MODERATE - Nice to have
    - misspell      # Finds commonly misspelled English words
    - goconst       # Finds repeated strings that could be replaced by a constant
    - gocyclo       # Computes cyclomatic complexity
    - durationcheck # Check for two durations multiplied together
    - noctx         # noctx finds sending http request without context
    - contextcheck  # Check whether the function uses a non-inherited context
    
    # Disabled - too pedantic for our needs
    # - revive        # Too many style complaints
    # - gocritic      # Too opinionated
    # - dupl          # Code duplication is sometimes OK
    # - stylecheck    # Too strict
    # - gofumpt       # Too strict formatting
    # - nakedret      # Sometimes naked returns are clearer
    # - prealloc      # Premature optimization
    # - unparam       # Too many false positives
    # - nolintlint    # We don't use many nolint directives

linters-settings:
  gosec:
    excludes:
      - G101 # Exclude hardcoded credentials check - too many false positives with SQL query names
    confidence: low
    
  errcheck:
    check-type-assertions: true
    check-blank: false  # Too many false positives
    exclude-functions:
      - fmt.Fprint
      - fmt.Fprintf
      - fmt.Fprintln
      - fmt.Print
      - fmt.Printf
      - fmt.Println
      - (os.File).Close
      - (io.ReadCloser).Close
      - (net/http.ResponseWriter).Write
      - os.Setenv
      - json.Marshal  # Rarely fails with valid structs
      - (database/sql.Rows).Close
      - (database/sql.Stmt).Close

  govet:
    enable-all: true
    disable:
      - fieldalignment  # Struct field alignment optimization is often premature

  gocyclo:
    min-complexity: 30  # Allow complex functions when necessary

  goconst:
    min-len: 3
    min-occurrences: 10  # Only flag really repetitive strings
    ignore-tests: true   # Don't enforce constants in tests

  misspell:
    locale: US

  unparam:
    check-exported: true

  # gocritic settings removed - linter disabled

  # dupl settings removed - linter disabled

  # prealloc settings removed - linter disabled

  # revive settings removed - linter disabled

  # nakedret settings removed - linter disabled

  # gofumpt settings removed - linter disabled

  # godot settings removed - linter disabled

  # dogsled settings removed - linter disabled

  # whitespace settings removed - linter disabled

  errorlint:
    errorf: true
    asserts: true
    comparison: true

  # thelper settings removed - linter disabled

  # stylecheck settings removed - linter disabled

  # gomodguard settings removed - linter disabled

issues:
  exclude-dirs:
    - vendor
    - examples
    - testdata
    - mocks
    - internal/storage/database/migrations

  exclude-rules:
    # Exclude some linters from running on tests files
    - path: _test\.go
      linters:
        - errcheck
        - goconst

    # Exclude generated files
    - path: internal/storage/database/sqlc
      linters:
        - goconst
        - gosec  # Generated code is safe

    # Exclude protobuf generated files
    - path: \.pb\.go$
      linters:
        - gosec  # Generated code is safe

    # Exclude linters for main package
    - path: cmd/
      linters:
        - gosec  # main packages may have different security patterns

    # Removed - godot linter disabled

    # Removed - revive linter disabled

    # Removed - gochecknoinits linter disabled

    # Removed - revive linter disabled

  # Maximum issues count per one linter. Set to 0 to disable
  max-issues-per-linter: 100

  # Maximum count of issues with the same text. Set to 0 to disable
  max-same-issues: 50
  
  # Show unique issues by line
  uniq-by-line: true

  # Fix found issues (if it's supported by the linter)
  fix: false

# Additional output settings
output:
  formats:
    - format: colored-line-number
  print-issued-lines: true
  print-linter-name: true
  sort-results: true