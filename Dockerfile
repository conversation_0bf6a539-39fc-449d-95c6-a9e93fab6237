# Build stage
FROM golang:1.24.5-alpine3.21 AS builder

# Install build dependencies
RUN apk add --no-cache git make

# Set working directory
WORKDIR /build

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -ldflags="-w -s" -o assistant ./cmd/assistant

# Final stage
FROM alpine:3.19

# Install runtime dependencies
RUN apk add --no-cache \
    ca-certificates \
    tzdata \
    curl

# Create non-root user
RUN addgroup -g 1000 assistant && \
    adduser -D -u 1000 -G assistant assistant

# Set working directory
WORKDIR /app

# Copy binary from builder
COPY --from=builder /build/assistant .

# Copy migration files
COPY --from=builder /build/internal/storage/database/migrations ./migrations

# Change ownership
RUN chown -R assistant:assistant /app

# Switch to non-root user
USER assistant

# Set terminal environment for proper color support
ENV TERM=xterm-256color

# Expose port
EXPOSE 8200

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8200/health || exit 1

# Run the application
ENTRYPOINT ["./assistant"]
