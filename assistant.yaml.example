# Assistant Configuration Example
# Copy this file to assistant.yaml and update with your values

app:
  mode: production
  language: zh-TW  # Interface language: en, zh-TW, ja, etc.

database:
  # For Docker Compose, use: ********************************************/assistant?sslmode=disable
  # For local development, use: postgresql://postgres:postgres@localhost:5432/assistant?sslmode=disable
  url: ********************************************/assistant?sslmode=disable
  max_connections: 30
  min_connections: 5

server:
  address: :8200  # Default port for the assistant

ai:
  provider: claude  # claude or gemini
  claude_api_key: your-claude-api-key-here
  gemini_api_key: your-gemini-api-key-here  # Required for embeddings

owner:
  name: Your Name
  email: <EMAIL>
  preferences:
    language: en      # User's preferred language for responses
    timezone: UTC     # User's timezone
    theme: dark       # Interface theme preference

observability:
  log_level: info

tools:
  # For Docker Compose, use: http://searxng:8080
  # For local development, use: http://localhost:8888
  searxng_url: http://searxng:8080
  filesystem:
    allowed_dirs:
      - .
      - /tmp
    enable_write: true
    create_dirs: true

# MCP (Model Context Protocol) Configuration
# Enable dynamic tool loading through MCP servers
# mcp:
#   enabled: true                    # Enable MCP integration
#   client_name: "assistant-go"      # Client identifier
#   client_version: "1.0.0"          # Client version
#   auto_discover: true              # Auto-discover tools from servers
#   discovery_interval: 5m           # Re-discovery interval
#   timeout: 30s                     # Connection timeout
#
#   servers:
#     # Filesystem access server
#     - name: filesystem
#       command: mcp-server-filesystem
#       args: ["--root", "/Users/<USER>/Documents"]
#       env:
#         LOG_LEVEL: info
#       auto_connect: true
#
#     # GitHub integration
#     - name: github
#       command: npx
#       args: ["-y", "@modelcontextprotocol/server-github"]
#       env:
#         GITHUB_TOKEN: "${GITHUB_TOKEN}"  # Reference env var
#       auto_connect: false  # Connect manually when needed
#
#     # Memory server for enhanced context
#     - name: memory
#       command: npx
#       args: ["-y", "@modelcontextprotocol/server-memory"]
#       auto_connect: true
#
#     # SQLite database access
#     - name: sqlite
#       command: mcp-server-sqlite
#       args: ["--db", "/path/to/database.db", "--read-only"]
#       auto_connect: false
#
#     # Custom server example
#     - name: custom
#       command: /usr/local/bin/my-mcp-server
#       args: ["--config", "/etc/mcp/server.conf"]
#       env:
#         API_KEY: "${MY_API_KEY}"
#         DEBUG: "false"
#       auto_connect: true
