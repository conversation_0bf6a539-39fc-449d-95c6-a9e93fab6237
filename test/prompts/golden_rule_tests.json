{"version": "1.0", "description": "Test suite for the Golden Rule - temporal context acquisition", "test_cases": [{"id": "golden-rule-001", "name": "Version query should trigger time check first", "description": "When asked about software versions, the assistant should check current time first", "input": "What's new in golang 1.25?", "expected": {"should_check_time": true, "should_search": true, "tool_calls": [{"name": "current_time", "order": 0}, {"name": "web_search", "order": 1}]}, "tags": ["golden-rule", "version-query", "temporal-context"]}, {"id": "golden-rule-002", "name": "Simple greeting should not trigger time check", "description": "Basic conversational greetings don't need temporal context", "input": "Hello, how are you?", "expected": {"should_check_time": false, "should_search": false, "forbidden_patterns": ["Let me check", "I'll search"]}, "tags": ["golden-rule", "greeting", "conversation"]}, {"id": "golden-rule-003", "name": "Current events query requires time context", "description": "Questions about current events should check time first", "input": "What's happening in the tech industry today?", "expected": {"should_check_time": true, "should_search": true, "tool_calls": [{"name": "current_time", "order": 0}]}, "tags": ["golden-rule", "current-events", "temporal-context"]}, {"id": "search-001", "name": "Direct search request", "description": "Explicit search requests should be honored", "input": "Search for information about Claude 3 Opus", "expected": {"should_check_time": true, "should_search": true, "response_patterns": ["Claude 3 Opus", "Anthropic"]}, "tags": ["search", "direct-request"]}, {"id": "search-002", "name": "Version uncertainty should trigger search", "description": "When uncertain about version information, search instead of denying", "input": "Tell me about Python 4.0 features", "expected": {"should_search": true, "forbidden_patterns": ["doesn't exist", "no such version", "not released"]}, "tags": ["search", "version-query", "service-first"]}, {"id": "memory-001", "name": "Personal preference query", "description": "Questions about user preferences should use memory, not search", "input": "What's my favorite programming language?", "expected": {"should_check_time": false, "should_search": false, "response_patterns": ["don't have", "memory", "tell me"]}, "tags": ["memory", "personal"]}, {"id": "file-001", "name": "File operation request", "description": "File operations should use appropriate tools", "input": "List all Python files in the current directory", "expected": {"should_check_time": false, "should_search": false, "tool_calls": [{"name": "file_operations", "parameters": {"pattern": "*.py"}}]}, "tags": ["file-operations", "tools"]}, {"id": "ui-001", "name": "Form request with permission", "description": "UI form should only be shown after asking permission", "input": "I want to fill out my profile information", "expected": {"should_check_time": false, "should_search": false, "response_patterns": ["Would you like", "form", "permission"], "forbidden_patterns": ["<ui_form>"]}, "tags": ["ui-tools", "permission"]}]}