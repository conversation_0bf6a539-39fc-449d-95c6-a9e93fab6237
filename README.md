<h1 align="center">Assistant-Go</h1>

<p align="center">
  <strong>The AI assistant with a persistent, self-organizing brain.</strong>
  <br />
  Built in Go for high performance, privacy, and true long-term memory.
</p>

<p align="center">
  <a href="https://go.dev"><img src="https://img.shields.io/badge/Go-1.24.5+-00ADD8?style=flat&logo=go" alt="Go Version"></a>
  <a href="https://goreportcard.com/report/github.com/koopa0/assistant-go"><img src="https://goreportcard.com/badge/github.com/koopa0/assistant-go" alt="Go Report Card"></a>
  <a href="https://github.com/koopa0/assistant-go/actions/workflows/ci.yml"><img src="https://github.com/koopa0/assistant-go/actions/workflows/ci.yml/badge.svg" alt="CI Status"></a>
  <a href="https://opensource.org/licenses/MIT"><img src="https://img.shields.io/badge/License-MIT-yellow.svg" alt="License: MIT"></a>
</p>

---

<img align="right" width="200px" src="docs/assets/logo.png" alt="Assistant-Go Logo">

**Assistant-Go** is not just another chatbot. It's a stateful AI companion engineered to solve the fundamental problem of digital amnesia in modern AI. It features a sophisticated **Cognitive Memory Architecture** that allows it to learn from conversations, build a dynamic knowledge graph, and autonomously maintain its memory to ensure it remains coherent and relevant over time.

Forget the limitations of context windows. Assistant-Go builds its understanding in a persistent PostgreSQL database, using `pgvector` for semantic search. It doesn't just store what you say; it **understands, consolidates, and reasons** about information, proactively resolving conflicts and managing its own knowledge lifecycle.

<p align="center">
<strong>🧠 Cognitive Architecture</strong> • <strong>🔧 Extensible Tools</strong> • <strong>🚀 High Performance</strong> • <strong>🔒 Privacy-First</strong> • <strong>🌍 Multi-Language</strong>
</p>

## ✨ Core Features

- **🧠 Cognitive Memory System**: The heart of the assistant. A complete knowledge lifecycle management system that goes far beyond simple conversation history.
  - **Intelligent Fact Extraction**: Uses LLMs to automatically parse conversations and structure them into typed memories (facts, preferences, schedules, etc.).
  - **Tiered Semantic Deduplication**: A key innovation. Instead of a binary check, it uses a graded approach to similarity, preventing data loss while eliminating redundancy:
    - `> 0.98 Similarity (Identical)`: Redundant memory is deactivated.
    - `0.90 - 0.98 Similarity (Highly Similar)`: Memories are intelligently merged, combining their metadata.
    - `0.80 - 0.90 Similarity (Related)`: A `similar_to` relationship is created in the knowledge graph, preserving both distinct memories.
  - **Autonomous Knowledge Curation**: A background `Consolidator` service constantly works to keep the knowledge base healthy by merging related concepts, archiving old information, and resolving contradictions.
  - **Dynamic Knowledge Graph**: Automatically builds a rich, queryable graph of relationships between memories based on shared entities, topics, and semantic similarity.
  - **Graph-Aware Retrieval**: The search mechanism traverses the knowledge graph to pull in not just direct matches, but also contextually relevant information, providing the LLM with a far richer context.
  - **Resilient & Asynchronous**: All memory operations are handled in a robust background queue, ensuring that assistant responses are always fast and no information is ever lost.

- **🔧 Extensible Tool System**:
  - **Built-in Tools**: Comes with essential tools for file operations, web search, and time utilities.
  - **MCP Integration**: Seamlessly connect external tools via the Model Context Protocol (MCP) without changing a single line of code.
  - **Natural Language Access**: All tools are available through natural conversation.

- **🤖 Multi-Provider AI & Local-First Support**:
  - **Claude (Anthropic)**, **Gemini (Google)**: Support for top-tier models with automatic failover.
  - **Ollama**: Full support for running local models, ensuring 100% privacy and offline capability.

- **💻 Modern CLI Experience**:
  - **Interactive Chat**: A beautiful and intuitive terminal UI powered by Bubble Tea.
  - **Guided Setup**: A first-run wizard makes configuration and personalization effortless.
  - **Multi-language**: Native support for English, Traditional Chinese, and Japanese.

## 🚀 Quick Start: Docker Compose (Recommended)

The easiest way to get started is with Docker Compose, which handles all dependencies (PostgreSQL, pgvector, SearXNG for web search) automatically.

**Prerequisites:** Docker and Docker Compose.

1.  **Clone the repository:**

    ```bash
    git clone https://github.com/koopa0/assistant-go.git
    cd assistant-go
    ```

2.  **Run the setup script:**
    ```bash
    ./chat.sh      # On macOS/Linux
    ./chat.bat     # On Windows
    ```

That's it! The script will:

- Create your `assistant.yaml` configuration file.
- Prompt you to add your AI provider API keys if they are missing.
- Start all required services in the background (`docker-compose up -d`).
- Connect you directly to the assistant's chat interface.

### Data Persistence with Docker

- Your PostgreSQL data (memories, conversations) is stored in a Docker volume named `postgres-data` and will persist across container restarts.
- To backup your data: `docker-compose exec postgres pg_dump -U postgres assistant > backup.sql`
- To restore: `docker-compose exec -T postgres psql -U postgres assistant < backup.sql`

## 💡 How The Memory Works: A Lifecycle Example

To understand what makes Assistant-Go special, consider this scenario:

1.  **Initial Input**: You tell the assistant, `"My favorite coffee is a flat white."`
    - **Extraction**: The system identifies this as a `preference` memory.
    - **Storage**: A new memory is created with its vector embedding.

2.  **Second Input**: A week later, you mention, `"I love drinking a good flat white in the morning."`
    - **Extraction**: Another `preference` memory is created.

3.  **Autonomous Consolidation**: In the background, the `Consolidator` runs.
    - **Detection**: It finds these two memories are semantically similar with a score of `~0.92`.
    - **Action**: Because the score is in the "merge" threshold, it combines them into a single, more detailed memory: `"User's favorite coffee is a flat white, which they enjoy in the morning."` The original, less-detailed memories are deactivated.

4.  **Retrieval**: You ask, `"What's my usual coffee order?"`
    - **Search**: The system performs a vector search for your query and finds the consolidated memory with a high relevance score.
    - **Response**: The assistant confidently answers, "Your usual coffee order is a flat white, which you seem to enjoy in the morning."

This continuous cycle of extraction, consolidation, and retrieval allows the assistant to build a robust and nuanced understanding over time.

## 🏛️ Architecture Overview

The project follows a clean, modular architecture designed for maintainability and separation of concerns.

```
assistant-go/
├── cmd/                  # Application entry points (main.go)
├── internal/
│   ├── ai/               # AI provider clients (Claude, Gemini, Ollama)
│   ├── assistant/        # Core assistant orchestrator and chat logic
│   ├── cli/              # Terminal UI (Bubble Tea) and command-line setup
│   ├── conversation/     # Conversation history management
│   ├── memory/           # 🧠 The core Cognitive Memory Architecture
│   ├── mcp/              # Model Context Protocol for external tools
│   ├── platform/         # Shared utilities (config, logger, shutdown handling)
│   ├── prompt/           # Prompt templating and management
│   ├── storage/          # Database layer (PostgreSQL, pgvector, sqlc)
│   └── tool/             # Built-in tool implementations and manager
└── configs/              # Configuration files (sqlc.yaml, etc.)
```

## 🧑‍💻 Contributing

Contributions are highly welcome! This project uses a comprehensive `Makefile` to streamline development.

1.  Fork the repository.
2.  Create your feature branch (`git checkout -b feature/AmazingFeature`).
3.  Commit your changes (`git commit -m 'Add some AmazingFeature'`).
4.  Run checks: `make ci` (includes formatting, linting, and testing).
5.  Push to the branch (`git push origin feature/AmazingFeature`).
6.  Open a Pull Request.

Please see `CONTRIBUTING.md` for more detailed guidelines.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
