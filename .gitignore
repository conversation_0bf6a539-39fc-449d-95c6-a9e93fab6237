# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories
vendor/

# Go workspace file
go.work
go.work.sum
docker-compose.searxng.yml

# Build output
/bin/
/dist/

# Environment files
.env
.env.local
.env.production

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
*~
.DS_Store
.claude/
CLAUDE.md

# Debug files
debug.test
debug.out

# Temporary files
*.tmp
*.temp
*.log

# Coverage reports
coverage.html
coverage.out
*.cover

# Docker
.docker/
docker-compose.override.yml
backups/

# Local configuration
config.local.yaml
configs/local.yaml

# Database files
*.db
*.sqlite
*.sqlite3

# Assistant-specific
.assistant_history
.goassistant_history*
/assistant
assistant.yaml

# Demo and validation scripts (not for public repo)
demo_cli.sh
scripts/test-demo.sh
scripts/validate-implementuuidation.sh

# Local documentation (not for public repo)
**/README-zh.md
.docs
.test
.CLAUDE-ARCHITECTURE.md
.CLAUDE.md
.MCP.md
.prompt.md
.Assistant.md
Assistant.md

# Profiling files
*.pprof
default.pgo
