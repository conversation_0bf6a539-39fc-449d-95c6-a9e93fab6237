services:
  assistant:
    build:
      context: .
      dockerfile: Dockerfile
      # Force rebuild every time
      cache_from: []
      # Add build args to invalidate cache
      args:
        - BUILDKIT_INLINE_CACHE=0
    environment:
      # Database URL - uses internal service name 'postgres'
      - DATABASE_URL=********************************************/assistant?sslmode=disable
      # Config file location
      - ASSISTANT_CONFIG=/app/assistant.yaml
    volumes:
      # Mount the config file from host
      - ./assistant.yaml:/app/assistant.yaml:ro
    ports:
      - "8200:8200"
    depends_on:
      postgres:
        condition: service_healthy
      searxng:
        condition: service_started
    restart: unless-stopped
    # Keep container running for exec access since assistant needs interactive input
    entrypoint: ["/bin/sh", "-c", "while true; do sleep 1000; done"]

  postgres:
    image: pgvector/pgvector:pg16
    environment:
      - POSTGRES_DB=assistant
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init.sql:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  searxng:
    image: searxng/searxng:latest
    environment:
      - SEARXNG_BASE_URL=http://searxng:8080/
    volumes:
      - ./searxng:/etc/searxng:ro
    restart: unless-stopped

volumes:
  postgres-data:
