// assistant is a command-line AI assistant with memory capabilities.
package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"github.com/koopa0/assistant-go/internal/ai"
	"github.com/koopa0/assistant-go/internal/cli"
	"github.com/koopa0/assistant-go/internal/platform/config"
	"github.com/koopa0/assistant-go/internal/platform/logger"
	"github.com/koopa0/assistant-go/internal/platform/server"
	uitools "github.com/koopa0/assistant-go/internal/tool/ui"
)

// earlyError prints error messages before logger is initialized
func earlyError(format string, args ...any) {
	fmt.Fprintf(os.Stderr, format, args...)
}

func main() {
	ctx := context.Background()

	// No .env file support - configuration comes from assistant.yaml only

	// Load configuration from assistant.yaml
	cfg, err := config.Load()
	if err != nil {
		earlyError("Failed to load configuration: %v\n", err)
		os.Exit(1)
	}

	// Setup initial logging based on context
	// Check if input is being piped
	stat, _ := os.Stdin.Stat()
	isPiped := (stat.Mode() & os.ModeCharDevice) == 0

	// For direct assistant execution (no subcommands) or piped input, use silent mode
	if isPiped || len(os.Args) == 1 || (len(os.Args) == 2 && (os.Args[1] == "-h" || os.Args[1] == "--help" || os.Args[1] == "-v" || os.Args[1] == "--version")) {
		// Silent mode for direct chat and piped input
		_ = os.Setenv("LOG_LEVEL", "error") // Only show errors, not fatal which hides everything
		_ = os.Setenv("WIRE_LOG_LEVEL", "error")
		logger.SetGlobal(logger.NewDiscardLogger())
	} else {
		// For verbose mode, show info logs
		if len(os.Args) >= 2 && (os.Args[1] == "--verbose" || os.Args[1] == "-v") {
			_ = os.Setenv("LOG_LEVEL", "info")
			logger.SetGlobal(logger.NewFromEnv())
		} else {
			// Normal mode - only errors
			_ = os.Setenv("LOG_LEVEL", "error")
			logger.SetGlobal(logger.NewFromEnv())
		}
	}

	// Run first-time setup if needed
	if err := cli.RunSetupIfNeeded(cfg); err != nil {
		// Logger is now available
		log := logger.Global()
		if log != nil {
			log.Error("Setup failed", "error", err)
		} else {
			earlyError("Setup failed: %v\n", err)
		}
		os.Exit(1)
	}

	// Initialize and run application
	if err := run(ctx, cfg); err != nil {
		// Logger should be available
		log := logger.Global()
		if log != nil {
			log.Error("Application error", "error", err)
		} else {
			earlyError("Error: %v\n", err)
		}
		os.Exit(1)
	}
}

func run(ctx context.Context, cfg *config.Config) error {
	// Check for version flag early to avoid unnecessary initialization
	for _, arg := range os.Args[1:] {
		if arg == "--version" || arg == "-v" {
			fmt.Println("Assistant v0.1.0")
			return nil
		}
	}

	// Parse command line flags - only support verbose mode
	verbose := false
	for _, arg := range os.Args[1:] {
		if arg == "-v" || arg == "--verbose" {
			verbose = true
			break
		}
	}

	// Create server with all dependencies initialized
	// Always use default config search (./assistant.yaml in current directory)
	srv, err := server.New(server.Config{
		Verbose:       verbose,
		DisableColors: os.Getenv("NO_COLOR") != "",
		DisableMemory: false,
	})
	if err != nil {
		return fmt.Errorf("failed to create server: %w", err)
	}

	log := srv.GetLogger()
	assistantService := srv.GetAssistant()

	// Register UI tools after assistant is created
	if toolMgr := assistantService.GetToolManager(); toolMgr != nil {
		termOutput := uitools.NewTerminalOutput(os.Stdout)
		if termOutput.IsInteractive() {
			if registerErr := uitools.RegisterUITools(toolMgr, termOutput); registerErr != nil {
				log.Warn("failed to register UI tools", "error", registerErr)
			} else {
				log.Debug("UI tools registered successfully")
			}
		}
	}

	// Initialize CLI
	srvConfig := srv.GetConfig()
	db := srv.GetDatabase()
	// The CLI needs access to the AI service for the session
	// Try to cast the AI client to *ai.Service
	var aiService *ai.Service
	if client := srv.GetAIService(); client != nil {
		// Try type assertion
		if svc, ok := client.(*ai.Service); ok {
			aiService = svc
		}
	}
	cliCmd, err := cli.NewSimpleRootCmd(&srvConfig, aiService, db, assistantService, log)
	if err != nil {
		return fmt.Errorf("create CLI: %w", err)
	}

	// Set up signal handling
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Run CLI in background
	errChan := make(chan error, 1)
	go func() {
		errChan <- cliCmd.Execute()
	}()

	// Wait for signal or error
	select {
	case <-sigChan:
		return srv.Shutdown(ctx)
	case err := <-errChan:
		if err != nil {
			return fmt.Errorf("CLI error: %w", err)
		}
		return srv.Shutdown(ctx)
	}
}
