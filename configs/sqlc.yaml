version: "2"
sql:
  - engine: "postgresql"
    queries: "../internal/storage/database/queries"
    schema: 
      - "../internal/storage/database/migrations/001_initial_schema.up.sql"
    gen:
      go:
        package: "sqlc"
        out: "../internal/storage/database/sqlc"
        sql_package: "pgx/v5"
        emit_json_tags: true
        emit_prepared_queries: true
        emit_interface: true
        emit_exact_table_names: false
        emit_empty_slices: true
        emit_exported_queries: true
        emit_result_struct_pointers: true
        emit_params_struct_pointers: false
        emit_methods_with_db_argument: false
        emit_pointers_for_null_types: false
        emit_enum_valid_method: true
        emit_all_enum_values: true
        overrides:
          # 時間欄位
          - column: "*.created_at"
            go_type: "time.Time"
          - column: "*.updated_at"
            go_type: "time.Time"
          - column: "*.last_message_at"
            go_type:
              import: "database/sql"
              type: "NullTime"
          - column: "*.last_accessed_at"
            go_type:
              import: "database/sql"
              type: "NullTime"
          - column: "*.completed_at"
            go_type:
              import: "database/sql"
              type: "NullTime"
          - column: "*.due_date"
            go_type:
              import: "database/sql"
              type: "NullTime"
          # JSON 欄位
          - column: "*.metadata"
            go_type: "encoding/json.RawMessage"
          - column: "*.input"
            go_type: "encoding/json.RawMessage"
          - column: "*.output"
            go_type: "encoding/json.RawMessage"
          - column: "*.value"
            go_type: "encoding/json.RawMessage"
          - column: "*.backup_data"
            go_type: "encoding/json.RawMessage"
          - column: "*.attributes"
            go_type: "encoding/json.RawMessage"
          - column: "*.entities"
            go_type: "encoding/json.RawMessage"
          - column: "*.context"
            go_type: "encoding/json.RawMessage"
          # 向量欄位
          - column: "*.embedding"
            go_type:
              import: "github.com/pgvector/pgvector-go"
              package: "pgvector"
              type: "Vector"
          # 陣列欄位
          - column: "tasks.labels"
            go_type:
              type: "[]string"
          - column: "memories.merged_from"
            go_type:
              type: "[]uuid.UUID"
          - column: "memories.keywords"
            go_type:
              type: "[]string"
          - column: "memory_consolidations.original_memory_ids"
            go_type:
              type: "[]uuid.UUID"
          # Memory system fields
          - column: "memories.accessed_at"
            go_type:
              import: "database/sql"
              type: "NullTime"
          - column: "memory_consolidations.processed_at"
            go_type:
              import: "database/sql"
              type: "NullTime"