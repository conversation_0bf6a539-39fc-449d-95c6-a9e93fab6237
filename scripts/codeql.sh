#!/bin/bash
# Quick CodeQL security scan

set -e

echo "=== Quick CodeQL Security Scan ==="
echo "Checking if CodeQL is installed..."

if ! command -v codeql &> /dev/null; then
    echo "CodeQL CLI is not installed."
    echo ""
    echo "To install CodeQL CLI:"
    echo "1. Visit: https://github.com/github/codeql-cli-binaries/releases"
    echo "2. Download the appropriate bundle for your OS"
    echo "3. Extract and add to PATH"
    echo ""
    echo "Quick install for macOS (Intel):"
    echo "  brew install --cask codeql"
    echo ""
    echo "Quick install for macOS (Apple Silicon) or Linux:"
    echo "  curl -L https://github.com/github/codeql-cli-binaries/releases/latest/download/codeql-osx64.zip -o codeql.zip"
    echo "  unzip codeql.zip && rm codeql.zip"
    echo "  export PATH=\"\$PATH:\$(pwd)/codeql\""
    exit 1
fi

echo "CodeQL is installed. Running security scan..."
echo ""

# Just run a quick security scan without creating database
echo "Note: For a quick scan, we'll use Go's built-in security tools instead."
echo ""

# Run gosec for security scanning
if command -v gosec &> /dev/null; then
    echo "Running gosec security scanner..."
    gosec -fmt json -severity medium ./... 2>/dev/null | jq -r '.Issues[] | "[\(.severity)] \(.file):\(.line) - \(.rule_id): \(.details)"' || echo "No security issues found by gosec"
else
    echo "Installing gosec..."
    go install github.com/securego/gosec/v2/cmd/gosec@latest
    echo "Running gosec security scanner..."
    gosec -fmt json -severity medium ./... 2>/dev/null | jq -r '.Issues[] | "[\(.severity)] \(.file):\(.line) - \(.rule_id): \(.details)"' || echo "No security issues found by gosec"
fi

echo ""
echo "=== Security scan complete ==="
