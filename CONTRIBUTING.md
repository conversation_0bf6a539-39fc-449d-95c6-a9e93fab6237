# Contributing to <PERSON>-<PERSON>

Thank you for your interest in contributing to Assistant-Go! This document provides guidelines and information for contributors.

## Development Setup

### Prerequisites

- Go 1.24.4 or higher
- PostgreSQL 14+ with pgvector extension
- golangci-lint for code quality checks
- sqlc for SQL code generation

### Initial Setup

```bash
# Clone the repository
git clone https://github.com/koopa0/assistant-go.git
cd assistant-go

# Install development tools
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
go install github.com/sqlc-dev/sqlc/cmd/sqlc@latest

# Setup database
createdb assistant_db
psql assistant_db -c "CREATE EXTENSION IF NOT EXISTS vector;"

# Run migrations
make migrate-up

# Install dependencies
go mod download

# Run tests to verify setup
make test
```

## Understanding the Codebase

### Configuration System

The configuration system has been simplified to use direct YAML unmarshaling:

- **Config Structure**: The `Config` struct in `internal/platform/config/config.go` now includes yaml tags for direct YAML parsing
- **Simplified Loading**: `LoadFromYAML` in `internal/platform/config/yaml.go` directly unmarshals YAML into the Config struct
- **Configuration Location**: Main configuration file is located at `configs/assistant.yaml` (not in project root)
- **No Intermediate Structs**: We eliminated the separate YAMLConfig struct for cleaner code

This follows Go's philosophy of simplicity and reduces unnecessary abstraction.

### Go Module Replace Directives

The `go.mod` file contains several `replace` directives that are critical for the project to build correctly:

```go
replace (
    // Pin vertexai to v0.12.0 to avoid internal/support package conflicts
    cloud.google.com/go/vertexai => cloud.google.com/go/vertexai v0.12.0

    // Pin aiplatform to compatible version
    cloud.google.com/go/aiplatform => cloud.google.com/go/aiplatform v1.89.0

    // Ensure consistent OpenTelemetry versions
    go.opentelemetry.io/otel => go.opentelemetry.io/otel v1.37.0
    go.opentelemetry.io/otel/metric => go.opentelemetry.io/otel/metric v1.37.0

    // Other version pins...
)
```

**Why these replacements are necessary:**

1. **vertexai v0.12.0**: Later versions introduced breaking changes in the internal package structure that cause compilation errors. This version is the last stable release that works with our dependency tree.

2. **Consistent versions**: Google Cloud packages often have complex interdependencies. The replace directives ensure all packages use compatible versions, preventing "multiple versions of package" errors.

3. **OpenTelemetry alignment**: Different packages may require different versions of OpenTelemetry. We pin to specific versions to ensure compatibility.

**Important**: Do not remove or modify these replace directives without thorough testing. If you need to update a dependency, ensure all related packages are updated together.

### Project Structure

```
assistant-go/
├── cmd/assistant/       # Main application entry point
├── configs/            # Configuration files
│   ├── assistant.yaml  # Main configuration (copy from .example)
│   ├── assistant.yaml.example  # Configuration template
│   ├── sqlc.yaml       # SQL code generation config
│   └── docker-compose.searxng.yml  # Search service config
├── internal/           # Private application code
│   ├── ai/            # AI provider implementations
│   ├── assistant/     # Core assistant logic
│   ├── cli/          # Command-line interface
│   ├── memory/       # Memory and learning system
│   ├── mcp/          # Model Context Protocol
│   ├── platform/     # Platform utilities and shared code
│   │   ├── config/   # Configuration management
│   │   ├── convert/  # Type conversion utilities
│   │   ├── env/      # Environment variable handling
│   │   ├── logger/   # Logging infrastructure
│   │   └── shutdown/ # Graceful shutdown handling
│   └── tool/         # Tool implementations
├── migrations/        # Database migrations
├── scripts/          # Development scripts
└── docs/            # Documentation
```

### Key Design Principles

1. **Follow Go's Philosophy**
   - Clear is better than clever
   - Explicit error handling
   - Minimal abstraction
   - Composition over inheritance

2. **Database-First Design**
   - Use sqlc for type-safe SQL
   - Write migrations for schema changes
   - Test database operations thoroughly

3. **Interface-Based Design**
   - Define interfaces where implementations may vary
   - Accept interfaces, return concrete types
   - Keep interfaces small

## Making Changes

### Workflow

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Run tests (`make test`)
5. Run linters (`make lint`)
6. Commit with descriptive message
7. Push to your fork
8. Open a Pull Request

### Code Standards

#### Go Code

- Follow standard Go formatting (`gofmt`)
- Use meaningful variable and function names
- Add comments for exported functions
- Handle errors explicitly
- Write tests for new functionality

#### SQL Code

- Write migrations for schema changes
- Use sqlc for generating Go code from SQL
- Follow naming conventions (snake_case for columns)
- Add appropriate indexes
- Document complex queries

#### Testing

- Write table-driven tests
- Test both success and error cases
- Use testify for assertions
- Mock external dependencies
- Aim for >80% code coverage

### Commit Messages

Follow conventional commit format:

```
type(scope): description

[optional body]

[optional footer]
```

Types:

- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Test additions or corrections
- `chore`: Maintenance tasks

Example:

```
feat(memory): add schedule extraction from natural language

- Parse recurring patterns like "every Tuesday"
- Extract time information from casual mentions
- Store schedules with timezone awareness

Closes #123
```

## Testing

### Running Tests

```bash
# Run all tests
make test

# Run with coverage
make test-coverage

# Run specific package tests
go test ./internal/memory/...

# Run with race detection
go test -race ./...

# Run integration tests (requires database)
make test-integration
```

### Writing Tests

Example test structure:

```go
func TestMemoryExtraction(t *testing.T) {
    tests := []struct {
        name    string
        input   string
        want    []Memory
        wantErr bool
    }{
        {
            name:  "extract meeting time",
            input: "Meeting with Bob at 3pm tomorrow",
            want: []Memory{
                {Type: MemoryTypeSchedule, Content: "Meeting with Bob at 3pm tomorrow"},
            },
            wantErr: false,
        },
        // More test cases...
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            got, err := ExtractMemories(tt.input)
            if (err != nil) != tt.wantErr {
                t.Errorf("ExtractMemories() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            assert.Equal(t, tt.want, got)
        })
    }
}
```

## Adding New Features

### Adding a New Tool

1. Create a new package in `internal/tool/`
2. Implement the `tool.Tool` interface
3. Register the tool in `internal/tool/register.go`
4. Add tests
5. Update documentation

### Adding a New AI Provider

1. Create a new package in `internal/ai/`
2. Implement the `ai.Client` interface
3. Add provider configuration in `internal/config/`
4. Add integration in `internal/ai/service.go`
5. Add tests and documentation

### Database Changes

1. Create a new migration:
   ```bash
   make migrate-create name=add_new_feature
   ```
2. Write up and down migrations
3. Test migrations:
   ```bash
   make migrate-up
   make migrate-down
   make migrate-up
   ```
4. Update sqlc queries if needed
5. Regenerate code:
   ```bash
   make generate
   ```

## Documentation

- Update README.md for user-facing changes
- Update code comments for API changes
- Add examples for new features
- Update configuration examples

## Code Review Process

1. **Self Review**: Check your own code first
2. **Tests Pass**: Ensure all tests pass
3. **Linting**: No linting errors
4. **Documentation**: Updated as needed
5. **Commits**: Clean commit history

## Release Process

1. Version tags follow semantic versioning (v1.2.3)
2. Update CHANGELOG.md
3. Create GitHub release with notes
4. Build binaries for supported platforms

## Getting Help

- Open an issue for bugs or feature requests
- Use discussions for questions
- Check existing issues before creating new ones
- Join our community chat (coming soon)

## License

By contributing, you agree that your contributions will be licensed under the MIT License.
