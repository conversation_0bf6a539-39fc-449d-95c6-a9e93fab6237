@echo off
REM Chat launcher for Assistant-Go on Windows

echo Checking requirements...

REM Check if Docker is installed
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not installed
    echo Please install Docker Desktop from https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

REM Check if Docker Compose is available
docker compose version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose is not available
    echo Please update Docker Desktop to the latest version
    pause
    exit /b 1
)

REM Check if assistant.yaml exists
if not exist assistant.yaml (
    echo Configuration file not found
    echo.
    echo Creating assistant.yaml from template...
    copy assistant.yaml.example assistant.yaml >nul
    echo Created assistant.yaml
    echo.
    echo Please edit assistant.yaml and add your API keys:
    echo   - claude_api_key: your-key-here
    echo   - gemini_api_key: your-key-here
    echo.
    echo Then run chat.bat again
    pause
    exit /b 1
)

REM Check if API keys are configured
findstr "your-claude-api-key-here your-gemini-api-key-here" assistant.yaml >nul
if not errorlevel 1 (
    echo API keys not configured
    echo.
    echo Please edit assistant.yaml and add your API keys:
    echo   - claude_api_key: your-actual-key
    echo   - gemini_api_key: your-actual-key
    echo.
    pause
    exit /b 1
)

REM Check if services are running
docker compose ps --services --filter "status=running" 2>nul | findstr assistant >nul
if errorlevel 1 (
    echo Starting Assistant services...
    docker compose up -d
    
    echo Waiting for services to start...
    timeout /t 5 >nul
)

REM Launch the chat interface
echo Connecting to Assistant...
echo.
docker compose exec assistant ./assistant