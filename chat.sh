#!/bin/bash
# Simple chat launcher for Assistant-Go

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker is not installed${NC}"
    echo "Please install Docker Desktop from https://www.docker.com/products/docker-desktop"
    exit 1
fi

# Check if Docker Compose is available
if ! docker compose version &> /dev/null 2>&1; then
    echo -e "${RED}❌ Docker Compose is not available${NC}"
    echo "Please update Docker Desktop to the latest version"
    exit 1
fi

# Check if assistant.yaml exists
if [ ! -f "assistant.yaml" ]; then
    echo -e "${YELLOW}⚠️  Configuration file not found${NC}"
    echo ""
    echo "Creating assistant.yaml from template..."
    cp assistant.yaml.example assistant.yaml
    echo -e "${GREEN}✓ Created assistant.yaml${NC}"
    echo ""
    echo -e "${YELLOW}Please edit assistant.yaml and add your API keys:${NC}"
    echo "  - claude_api_key: your-key-here"
    echo "  - gemini_api_key: your-key-here"
    echo ""
    echo "Then run ./chat.sh again"
    exit 1
fi

# Check if API keys are configured
if grep -q "your-claude-api-key-here\|your-gemini-api-key-here" assistant.yaml; then
    echo -e "${YELLOW}⚠️  API keys not configured${NC}"
    echo ""
    echo "Please edit assistant.yaml and add your API keys:"
    echo "  - claude_api_key: your-actual-key"
    echo "  - gemini_api_key: your-actual-key"
    echo ""
    exit 1
fi

# Check if services are running
if ! docker compose ps --services --filter "status=running" 2>/dev/null | grep -q "assistant"; then
    echo -e "${YELLOW}Starting Assistant services...${NC}"
    
    # Check if port 8200 is in use
    if lsof -Pi :8200 -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${RED}❌ Port 8200 is already in use${NC}"
        echo "Please stop the service using port 8200 or change the port in docker-compose.yml"
        exit 1
    fi
    
    # Force rebuild only if source code has changed
    # Use --no-build to skip rebuild if you want faster startup
    echo -e "${YELLOW}Building and starting services...${NC}"
    docker compose up -d --build

    # Wait for services to be ready
    echo -n "Waiting for services to start"
    for i in {1..30}; do
        if docker compose ps --services --filter "status=running" 2>/dev/null | grep -q "assistant"; then
            echo -e " ${GREEN}✓${NC}"
            break
        fi
        echo -n "."
        sleep 1
    done
    echo ""
fi

# Launch the chat interface
echo -e "${GREEN}Connecting to Assistant...${NC}"
echo ""
# Use environment variable to override config file database URL
# The -it flags ensure interactive terminal mode
docker compose exec -it -e "DATABASE_URL=********************************************/assistant?sslmode=disable" assistant ./assistant