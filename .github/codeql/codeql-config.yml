name: "Assistant Go CodeQL Config"

# Specify the paths to include in the analysis
paths:
  - internal
  - cmd
  - pkg

# Paths to ignore
paths-ignore:
  - internal/storage/database/sqlc  # Generated code
  - "**/*_test.go"                  # Optionally exclude tests
  - "**/mocks/**"                   # Mock files
  - "**/testdata/**"                # Test data
  - "**/*.pb.go"                    # Protocol buffer generated files

# Specify query suites to run
queries:
  - uses: security-and-quality
  - uses: security-extended

# Additional queries
packs:
  - codeql/go-queries

# Query filters - adjust severity
query-filters:
  - exclude:
      id: go/log-injection  # We've already fixed this
  - exclude:
      id: go/incomplete-hostname-regexp  # If not applicable
  - include:
      severity:
        - error
        - warning
        - recommendation