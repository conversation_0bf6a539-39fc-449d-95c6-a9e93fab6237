name: Security

on:
  push:
    branches: [main]
  schedule:
    # Weekly security scan
    - cron: '0 6 * * 1'

env:
  GO_VERSION: "1.24.4"

permissions:
  contents: read
  security-events: write

jobs:
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: ${{ env.GO_VERSION }}

      - name: Run gosec
        run: |
          go install github.com/securego/gosec/v2/cmd/gosec@latest
          
          echo "Running gosec security scan..."
          # Run gosec - it returns exit code 1 if issues found, which is normal
          gosec -conf .gosec.json -fmt sarif -out gosec.sarif ./... || GOSEC_EXIT_CODE=$?
          
          echo "Gosec exit code: ${GOSEC_EXIT_CODE:-0}"
          
          # Check if SARIF file was created successfully
          if [ -f gosec.sarif ]; then
            echo "✓ Gosec scan completed successfully"
            if [ "${GOSEC_EXIT_CODE:-0}" -eq 1 ]; then
              echo "Security issues were found and will be uploaded to the Security tab"
            fi
            # Exit 0 because finding issues is not a failure - they'll be reviewed in Security tab
            exit 0
          else
            echo "✗ Error: gosec failed to create SARIF file"
            exit 1
          fi

      - name: Upload SARIF file
        if: always()
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: gosec.sarif
        continue-on-error: true

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: go
          config-file: ./.github/codeql/codeql-config.yml

      - name: Autobuild
        uses: github/codeql-action/autobuild@v3

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3