name: CI

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

env:
  GO_VERSION: "1.24.4"

jobs:
  # Quick quality checks - fail fast
  quality:
    name: Code Quality
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: ${{ env.GO_VERSION }}
          cache: true

      - name: Check Go formatting
        run: |
          if [ "$(gofmt -s -l . | wc -l)" -gt 0 ]; then
            echo "Go code is not formatted properly:"
            gofmt -s -l .
            exit 1
          fi

      - name: Check Go modules
        run: |
          go mod download
          go mod verify
          go mod tidy
          git diff --exit-code go.mod go.sum || \
            (echo "go.mod/go.sum are not up to date. Run 'go mod tidy'" && exit 1)

  # Linting with golangci-lint
  lint:
    name: Lint
    runs-on: ubuntu-latest
    needs: quality
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: ${{ env.GO_VERSION }}

      - name: Run golangci-lint
        uses: golangci/golangci-lint-action@v6
        with:
          version: latest
          args: --timeout=5m

  # Unit tests with coverage - TEMPORARILY DISABLED
  # TODO: Re-enable once unit tests are complete
  # test:
  #   name: Test
  #   runs-on: ubuntu-latest
  #   needs: quality
  #   services:
  #     postgres:
  #       image: pgvector/pgvector:pg15
  #       env:
  #         POSTGRES_DB: assistant_test
  #         POSTGRES_USER: test
  #         POSTGRES_PASSWORD: test
  #       options: >-
  #         --health-cmd pg_isready
  #         --health-interval 10s
  #         --health-timeout 5s
  #         --health-retries 5
  #       ports:
  #         - 5432:5432
  #
  #   steps:
  #     - name: Checkout code
  #       uses: actions/checkout@v4
  #
  #     - name: Set up Go
  #       uses: actions/setup-go@v5
  #       with:
  #         go-version: ${{ env.GO_VERSION }}
  #         cache: true
  #
  #     - name: Run tests
  #       env:
  #         DATABASE_URL: "postgresql://test:test@localhost:5432/assistant_test?sslmode=disable"
  #         TEST_DATABASE_URL: "postgresql://test:test@localhost:5432/assistant_test?sslmode=disable"
  #         CI: "true"  # Skip flaky network tests
  #       run: |
  #         set -e
  #         echo "Running tests with race detector..."
  #         go test -v -race -coverprofile=coverage.out -timeout=10m ./...
  #         echo "Test coverage: $(go tool cover -func=coverage.out | grep total | awk '{print $3}')"
  #
  #     - name: Upload coverage
  #       uses: codecov/codecov-action@v4
  #       with:
  #         file: ./coverage.out
  #         flags: unittests
  #         token: ${{ secrets.CODECOV_TOKEN }}

  # Build binaries for multiple platforms
  build:
    name: Build
    runs-on: ubuntu-latest
    needs: [lint]  # Temporarily removed test dependency
    strategy:
      matrix:
        include:
          - {os: linux, arch: amd64}
          - {os: linux, arch: arm64}
          - {os: darwin, arch: amd64}
          - {os: darwin, arch: arm64}
          - {os: windows, arch: amd64}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: ${{ env.GO_VERSION }}
          cache: true

      - name: Build binary
        env:
          GOOS: ${{ matrix.os }}
          GOARCH: ${{ matrix.arch }}
          CGO_ENABLED: 0
        run: |
          mkdir -p dist
          EXT=""
          if [ "${{ matrix.os }}" = "windows" ]; then
            EXT=".exe"
          fi
          
          # Build with version info
          VERSION=${GITHUB_SHA::8}
          BUILD_TIME=$(date -u '+%Y-%m-%d_%H:%M:%S')
          LDFLAGS="-w -s -X main.version=${VERSION} -X main.buildTime=${BUILD_TIME}"
          
          go build -ldflags="${LDFLAGS}" \
            -o "dist/assistant-${{ matrix.os }}-${{ matrix.arch }}${EXT}" \
            ./cmd/assistant

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: assistant-${{ matrix.os }}-${{ matrix.arch }}
          path: dist/assistant-*
          retention-days: 7

  # Docker build (only on main branch) - TEMPORARILY DISABLED
  # TODO: Re-enable once Dockerfile is created
  # docker:
  #   name: Docker Build
  #   runs-on: ubuntu-latest
  #   needs: [lint]  # Temporarily removed test dependency
  #   if: github.event_name == 'push' && github.ref == 'refs/heads/main'
  #   permissions:
  #     contents: read
  #     packages: write
  #     
  #   steps:
  #     - name: Checkout code
  #       uses: actions/checkout@v4
  #
  #     - name: Set up Docker Buildx
  #       uses: docker/setup-buildx-action@v3
  #
  #     - name: Login to GitHub Container Registry
  #       uses: docker/login-action@v3
  #       with:
  #         registry: ghcr.io
  #         username: ${{ github.actor }}
  #         password: ${{ secrets.GITHUB_TOKEN }}
  #
  #     - name: Extract metadata
  #       id: meta
  #       uses: docker/metadata-action@v5
  #       with:
  #         images: ghcr.io/${{ github.repository }}
  #         tags: |
  #           type=ref,event=branch
  #           type=sha,prefix={{branch}}-
  #           type=raw,value=latest,enable={{is_default_branch}}
  #
  #     - name: Build and push
  #       uses: docker/build-push-action@v6
  #       with:
  #         context: .
  #         platforms: linux/amd64,linux/arm64
  #         push: true
  #         tags: ${{ steps.meta.outputs.tags }}
  #         labels: ${{ steps.meta.outputs.labels }}
  #         cache-from: type=gha
  #         cache-to: type=gha,mode=max
  #         build-args: |
  #           VERSION=${{ github.sha }}
  #           BUILD_TIME=${{ github.event.head_commit.timestamp }}