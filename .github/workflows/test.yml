name: Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  GO_VERSION: '1.24'

jobs:
  test:
    name: Unit Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: pgvector/pgvector:pg17
        env:
          POSTGRES_USER: testuser
          POSTGRES_PASSWORD: testpass
          POSTGRES_DB: testdb
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Cache Go modules
      uses: actions/cache@v4
      with:
        path: ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: Install dependencies
      run: go mod download

    - name: Run tests with coverage
      env:
        TEST_DATABASE_URL: postgres://testuser:testpass@localhost:5432/testdb?sslmode=disable
      run: |
        go test -v -race -coverprofile=coverage.out -covermode=atomic ./...
        go tool cover -func=coverage.out

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v4
      with:
        file: ./coverage.out
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

  lint:
    name: Lint
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Cache Go modules
      uses: actions/cache@v4
      with:
        path: ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: Install golangci-lint
      run: |
        curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v1.61.0

    - name: Run linters
      run: |
        golangci-lint run ./...
        go vet ./...

  security:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Run gosec
      run: |
        go install github.com/securego/gosec/v2/cmd/gosec@latest
        gosec -fmt=json -out=gosec-report.json ./... || true

    - name: Upload gosec report
      uses: actions/upload-artifact@v4
      with:
        name: gosec-report
        path: gosec-report.json

  benchmark:
    name: Benchmarks
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    services:
      postgres:
        image: pgvector/pgvector:pg17
        env:
          POSTGRES_USER: testuser
          POSTGRES_PASSWORD: testpass
          POSTGRES_DB: testdb
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - name: Checkout code (PR)
      uses: actions/checkout@v4

    - name: Checkout code (base)
      uses: actions/checkout@v4
      with:
        ref: ${{ github.base_ref }}
        path: base

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Run benchmarks (base)
      env:
        TEST_DATABASE_URL: postgres://testuser:testpass@localhost:5432/testdb?sslmode=disable
      working-directory: base
      run: |
        go test -bench=. -benchmem -count=3 -run=^$ ./... > ../base-benchmarks.txt

    - name: Run benchmarks (PR)
      env:
        TEST_DATABASE_URL: postgres://testuser:testpass@localhost:5432/testdb?sslmode=disable
      run: |
        go test -bench=. -benchmem -count=3 -run=^$ ./... > pr-benchmarks.txt

    - name: Compare benchmarks
      run: |
        go install golang.org/x/perf/cmd/benchstat@latest
        benchstat base-benchmarks.txt pr-benchmarks.txt > benchmark-comparison.txt
        cat benchmark-comparison.txt

    - name: Comment PR with benchmark results
      uses: actions/github-script@v7
      if: github.event_name == 'pull_request'
      with:
        script: |
          const fs = require('fs');
          const comparison = fs.readFileSync('benchmark-comparison.txt', 'utf8');
          
          const comment = `## Benchmark Results
          
          <details>
          <summary>Benchmark comparison</summary>
          
          \`\`\`
          ${comparison}
          \`\`\`
          
          </details>`;
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });

  fuzz:
    name: Fuzz Tests
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Run fuzz tests
      run: |
        # Run each fuzz test for a short time
        go test -fuzz=FuzzRequestValidation -fuzztime=30s ./internal/ai/claude
        go test -fuzz=FuzzMessageContent -fuzztime=30s ./internal/ai/gemini
        go test -fuzz=FuzzExecuteInput -fuzztime=30s ./internal/tool
        go test -fuzz=FuzzMarkdownToHTML -fuzztime=30s ./internal/platform/convert

  build:
    name: Build
    runs-on: ubuntu-latest
    strategy:
      matrix:
        os: [linux, darwin, windows]
        arch: [amd64, arm64]
        exclude:
          - os: windows
            arch: arm64
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Build binary
      env:
        GOOS: ${{ matrix.os }}
        GOARCH: ${{ matrix.arch }}
      run: |
        output_name="assistant-${{ matrix.os }}-${{ matrix.arch }}"
        if [ "${{ matrix.os }}" = "windows" ]; then
          output_name="${output_name}.exe"
        fi
        go build -o "$output_name" ./cmd/assistant

    - name: Upload artifacts
      uses: actions/upload-artifact@v4
      with:
        name: assistant-${{ matrix.os }}-${{ matrix.arch }}
        path: assistant-*